<template>
  <GlassCard :width="width" :aspectRatio="true" :ratio="aspectRatio" @click="handleClick">
    <div class="image-container">
      <img :src="src" :alt="alt" class="preview-image" :class="{ 'loading': isLoading }" @load="handleImageLoaded" />
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
      <div class="image-info">
        <slot name="info">
          <div v-if="showType" class="image-type">{{ typeText }}</div>
        </slot>
      </div>
    </div>
  </GlassCard>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "ImagePreviewCard",
});
</script>

<script setup lang="ts">
import { ref, computed } from "vue";
import GlassCard from "./GlassCard.vue";

const props = defineProps({
  // 图片源URL
  src: {
    type: String,
    required: true,
  },
  // 图片替代文本
  alt: {
    type: String,
    default: "预览图片",
  },
  // 图片类型
  type: {
    type: String,
    default: "wallpaper",
    validator: (value: string) => ["wallpaper", "icon"].includes(value),
  },
  // 是否显示类型标签
  showType: {
    type: Boolean,
    default: true,
  },
  // 卡片宽度
  width: {
    type: String,
    default: "100%",
  },
  // 图片宽度
  imageWidth: {
    type: Number,
    default: 0,
  },
  // 图片高度
  imageHeight: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["click", "loaded"]);

// 图片加载状态
const isLoading = ref(true);

// 计算宽高比
const aspectRatio = computed(() => {
  if (props.imageWidth && props.imageHeight) {
    return props.imageWidth / props.imageHeight;
  }
  return props.type === "wallpaper" ? 16 / 9 : 1;
});

// 类型文本
const typeText = computed(() => {
  return props.type === "wallpaper" ? "壁纸" : "图标";
});

// 处理图片加载完成
const handleImageLoaded = () => {
  isLoading.value = false;
  emit("loaded");
};

// 处理点击事件
const handleClick = () => {
  emit("click", props.src);
};
</script>

<style scoped>
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 10px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.preview-image.loading {
  opacity: 0;
}

.image-container:hover .preview-image {
  transform: scale(1.05);
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  font-size: 14px;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.image-container:hover .image-info {
  opacity: 1;
}

.image-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 