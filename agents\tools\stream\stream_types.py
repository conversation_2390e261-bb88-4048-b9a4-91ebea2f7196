from typing import Dict, List, Optional, Union, Any
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime
import json

class StreamEventType(str, Enum):
    """流式事件类型枚举"""
    TOKEN = "token"
    THINKING_STEP = "thinking_step"
    CODE_BLOCK = "code_block"
    TOOL_CALL = "tool_call"
    COMPLETION = "completion"
    ERROR = "error"
    STATUS = "status"

@dataclass
class TokenPayload:
    """Token事件载荷"""
    text: str

@dataclass
class ThinkingStepPayload:
    """思考步骤事件载荷"""
    icon: str  
    text: str
    progress: Optional[float] = None

@dataclass
class CodeBlockPayload:
    """代码块事件载荷"""
    language: str
    code: str

@dataclass
class ToolCallPayload:
    """工具调用事件载荷"""
    tool_name: str
    tool_input: Dict[str, Any]
    tool_output: Optional[Dict[str, Any]] = None

@dataclass
class CompletionPayload:
    """完成事件载荷"""
    text: str  # 完整的生成内容

@dataclass
class ErrorPayload:
    """错误事件载荷"""
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None

@dataclass
class StatusPayload:
    """状态事件载荷"""
    status: str
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

@dataclass
class StreamEvent:
    """流式事件通用结构"""
    type: StreamEventType
    agent_id: str
    session_id: str
    timestamp: float  # 使用时间戳
    payload: Union[
        TokenPayload, 
        ThinkingStepPayload, 
        CodeBlockPayload,
        ToolCallPayload,
        CompletionPayload,
        ErrorPayload,
        StatusPayload
    ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "type": self.type,
            "agentId": self.agent_id,
            "sessionId": self.session_id,
            "timestamp": self.timestamp,
            "payload": asdict(self.payload)
        }
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "StreamEvent":
        """从字典创建事件"""
        event_type = data["type"]
        payload_data = data["payload"]
        
        # 根据事件类型创建对应的载荷对象
        if event_type == StreamEventType.TOKEN:
            payload = TokenPayload(**payload_data)
        elif event_type == StreamEventType.THINKING_STEP:
            payload = ThinkingStepPayload(**payload_data)
        elif event_type == StreamEventType.CODE_BLOCK:
            payload = CodeBlockPayload(**payload_data)
        elif event_type == StreamEventType.TOOL_CALL:
            payload = ToolCallPayload(**payload_data)
        elif event_type == StreamEventType.COMPLETION:
            payload = CompletionPayload(**payload_data)
        elif event_type == StreamEventType.ERROR:
            payload = ErrorPayload(**payload_data)
        elif event_type == StreamEventType.STATUS:
            payload = StatusPayload(**payload_data)
        else:
            raise ValueError(f"未知的事件类型: {event_type}")
        
        return cls(
            type=event_type,
            agent_id=data["agentId"],
            session_id=data["sessionId"],
            timestamp=data["timestamp"],
            payload=payload
        )
    
    @classmethod
    def from_json(cls, json_str: str) -> "StreamEvent":
        """从JSON字符串创建事件"""
        data = json.loads(json_str)
        return cls.from_dict(data)

# 流式事件创建工厂函数
def create_token_event(
    agent_id: str, 
    session_id: str, 
    text: str
) -> StreamEvent:
    """创建Token事件"""
    return StreamEvent(
        type=StreamEventType.TOKEN,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=TokenPayload(text=text)
    )

def create_thinking_step_event(
    agent_id: str,
    session_id: str,
    icon: str,
    text: str,
    progress: Optional[float] = None
) -> StreamEvent:
    """创建思考步骤事件"""
    return StreamEvent(
        type=StreamEventType.THINKING_STEP,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=ThinkingStepPayload(
            icon=icon,
            text=text,
            progress=progress
        )
    )

def create_code_block_event(
    agent_id: str,
    session_id: str,
    language: str,
    code: str
) -> StreamEvent:
    """创建代码块事件"""
    return StreamEvent(
        type=StreamEventType.CODE_BLOCK,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=CodeBlockPayload(
            language=language,
            code=code
        )
    )

def create_tool_call_event(
    agent_id: str,
    session_id: str,
    tool_name: str,
    tool_input: Dict[str, Any],
    tool_output: Optional[Dict[str, Any]] = None
) -> StreamEvent:
    """创建工具调用事件"""
    return StreamEvent(
        type=StreamEventType.TOOL_CALL,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=ToolCallPayload(
            tool_name=tool_name,
            tool_input=tool_input,
            tool_output=tool_output
        )
    )

def create_completion_event(
    agent_id: str,
    session_id: str,
    text: str
) -> StreamEvent:
    """创建完成事件"""
    return StreamEvent(
        type=StreamEventType.COMPLETION,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=CompletionPayload(text=text)
    )

def create_error_event(
    agent_id: str,
    session_id: str,
    error_type: str,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> StreamEvent:
    """创建错误事件"""
    return StreamEvent(
        type=StreamEventType.ERROR,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=ErrorPayload(
            error_type=error_type,
            message=message,
            details=details
        )
    )

def create_status_event(
    agent_id: str,
    session_id: str,
    status: str,
    message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> StreamEvent:
    """创建状态事件"""
    return StreamEvent(
        type=StreamEventType.STATUS,
        agent_id=agent_id,
        session_id=session_id,
        timestamp=datetime.now().timestamp(),
        payload=StatusPayload(
            status=status,
            message=message,
            details=details
        )
    ) 