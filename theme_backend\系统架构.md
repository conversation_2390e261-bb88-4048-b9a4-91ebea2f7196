# 总体架构设计

## 系统架构
### 大模型
1. 大语言模型
   - Qwen2.5-32B-Instruct-GPTQ-Int4
   - 用于提示词优化和扩展

2. 扩散模型
   - 差异扩散模型: shuttle3.1，SDXL,SD1.5
   - VAE: ae.sft，sdxl_vae.safetensors，sd1.5_vae.safetensors
   - CLIP: t5xxl_fp8_e4m3fn + clip_l.safetensors，sdxl_clip.safetensors，sd1.5_clip.safetensors
   - 超分模型: RealESRGAN_x2plus.pth，4x-UltraSharp.pth
   - 图像识别模型: WD14Tagger，promptgen_base_v2.0
   - 模型加速技术: hyper
   - 风格迁移技术: IPAdapter
   - 风格化技术方案：lora,ConDelta
   - controlnet: controlnet-union，controlnet-canny，controlnet-depth，controlnet-tile，controlnet-color,MistoLine，sdxl_plus_promax

3. 预处理模型
   - AnyLine预处理器
   - DepthAnythingV2深度估计
   - CannyEdgePreprocessor边缘检测
   - TilePreprocessor平铺预处理


### 服务组件

1. 工作流引擎
   - ComfyUI工作流系统
   - 支持API访问

2. Web API服务层
   - 基于FastAPI框架构建的RESTful API服务
   - 提供主题管理、配置和自动化接口
   - 支持异步处理和WebSocket实时通信

3. 容器化部署架构
   - 使用Docker容器技术实现应用隔离
   - Docker Compose实现多容器编排
   - 支持开发环境和生产环境的差异化配置

4. 存储层
   - 文件系统存储主题资源
   - 支持大规模主题包管理

5. 数据库层
   - 使用mysql数据库实现数据存储
   - 支持数据备份和恢复

6. 消息队列
   - 使用RabbitMQ消息队列实现任务调度
   - 支持任务的异步处理和实时监控

7. Haproxy
   - 负载均衡
   - 反向代理
   - Comfyui集群
   - 接口集群

8. 任务状态管理
   - 基于Redis的分布式任务状态管理
   - 支持任务进度跟踪和状态监控
   - 实现任务中断和恢复机制

## 功能架构

### 主题生成流程
#### 主题生成总线流程:

1. 接收API请求
   - 接收用户提交的主题生成任务
   - 解析任务参数和配置信息
   - 生成唯一的任务ID

2. 任务调度
   - 任务进入队列管理系统
   - 根据系统负载进行任务分发
   - 支持并行处理多个生成任务

3. 资源准备
   - 加载所需的模型资源
   - 准备工作目录和临时文件
   - 初始化ComfyUI工作流

4. 执行生成
   - 调用comfyui服务进行对应任务生成
   - 等待任务完成
   - 传递任务ID到下一个环节，记录日志  

5. 结果处理
   - 保存生成的主题资源
   - 更新任务状态
   - 调用方通过任务ID获取结果

6. 异常处理
   - 捕获和记录错误信息
   - 支持任务重试机制
   - 清理临时资源

7. 任务中断处理
   - 支持通过API手动终止任务
   - 清除ComfyUI队列并中断当前执行任务
   - 取消所有相关的异步任务
   - 自动终止等待图像生成的循环


#### 完整生成流程:
1. 壁纸生成
2. UI主题生成
3. UI切分
4. UI细节优化
5. 卡片内容生成
6. 卡片内容取色
7. 图像格式转换
8. 资源压缩

### 壁纸生成
壁纸生成流程基于ComfyUI工作流实现:

1. 输入提示词
2. 使用Qwen2.5-32B-Instruct增强提示词
3. 调用shuttle3.1模型进行采样生成壁纸
4. 使用RealESRGAN_x2plus进行超分
5. 保存壁纸到指定任务ID目录

### UI资源生成与处理
UI资源生成与处理流程包含三个主要阶段：

1. UI主题生成
   基于ComfyUI工作流的主题生成：
   - 图标预处理
     - LoadImagesFromPath加载source_icon和source_mask资源
     - ImageToMask转换遮罩为alpha通道
     - Cut By Mask进行精确裁剪
     - ImageScale缩放到1024x616标准尺寸
   - 混合壁纸和图标UI界面,进行预处理
     - AnyLine
     - DepthAnythingV2
     - CannyEdgePreprocessor
     - TilePreprocessor
   - 差异扩散模型
     - 基础模型：SDXL
      多阶段的细节处理流程：
   - 风格优化
     - promptgen_base_v2.0提取特征标签
     - IPAdapter进行风格迁移
     - 使用lora,ConDelta进行风格优化，使用hyper进行加速
2. UI资源切分
   基于设计规范的智能切分：
   - 智能切分
     - 读取风格界面
     - 使用Cut By Mask按遮罩进行精确裁剪
     - 通过For Loop批量处理多个组件
   - 资源生成
     - 导出不同状态组件
     - 按照映射字典重命名图标
       - icon_0 -> com_tinnove_mediacenter
       - icon_1 -> com_autopai_caller
       - 等等...
     - 保持PNG格式以保留透明通道
     - 生成资源映射
     - 根据taskID生成输出路径,传递到下一个环境
3. UI细节优化
   支持基于comfyui集群并发处理
   多阶段的细节处理流程：
   - 风格优化
     - WD14Tagger提取特征标签
     - 风格LoRA，ConDelta叠加细化
   - 差异扩散模型
     - 基础模型：SD1.5
   - 预处理与ControlNe
     - Canny
     - Depth
     - Tile
     - Color
   - 采样与生成
     - dpmpp_2m_sde采样器
     - 15步精细采样
     - 双阶段KSampler处理
     - 4x-UltraSharp超分辨率
   - 后期处理
     - 遮罩裁剪
     - 保存到指定任务ID的releases目录
### 卡片内容生成
卡片内容生成流程基于ComfyUI工作流实现，主要包含以下步骤：

1. 图像加载与预处理
   - 从source_content目录加载原始内容图片
   - 从wallpaper目录加载对应壁纸图片
   - 通过AnyLineArtPreprocessor_aux进行线稿提取
   - 使用SAMPreprocessor进行图像分割

2. 模型与控制器配置
   - 使用SDXL作为基础模型
   - 通过使用contentnet进行整体控制
     - MistoLine
     - controlnet-union
     - sdxl_plus_promax

3. 生成过程
   - 混合壁纸和页面
   - 使用promptgen_base_v2.0模型生成详细描述
   - 通过IPAdapte进行双重风格迁移
   - KSampler进行两阶段采样
     - 第一阶段:完整重绘
     - 第二阶段:局部优化
   - 使用4x-UltraSharp.pth进行超分辨率处理

4. 保存与部署
   - 保存到指定任务ID的content目录

### 卡片内容取色
智能取色流程基于PIL实现，包含以下步骤：

1. 图像加载与预处理
   - 加载卡片内容页面
   - 确保图像为RGB模式
   - 对关键区域坐标进行取色
2. 颜色处理
   - RGB转十六进制格式
   - 支持单点取色和多点取色
3. 结果输出
   - 生成colors.json配置文件
   - 保存到releases目录
   - 支持API访问

### 图像格式转换
基于PIL的图像格式转换流程：

1. 转换前准备
   - 创建壁纸备份目录wallpaper_backup
   - 复制原始壁纸文件以确保安全

2. 壁纸PNG转JPG处理
   - 仅处理wallpaper目录下的图片
   - 处理透明通道
     - RGBA模式：创建白色背景
     - LA模式：转换为RGB
     - P模式：处理透明度信息
   - 保持目录结构
   - 95%质量等级保存

3. 错误处理机制
   - 单文件转换失败不影响整体
   - 记录转换成功和失败数量
   - 失败时自动回滚到备份

4. 清理与完成
   - 转换成功则删除原PNG文件
   - 全部成功则删除备份
   - 部分失败则恢复备份
   - 通过API返回详细结果

5. 图标处理
   - 保持图标PNG格式不变
   - 根据映射字典重命名
   - 保留透明通道
   - 支持批量处理

### 图像压缩
智能压缩流程：
1. 分析图像特征
2. 选择最优压缩算法
3. 平衡质量和大小
4. 批量处理优化

### 任务中断与恢复机制

1. 任务中断流程
   - 通过API接口触发任务中断
   - 清除ComfyUI队列中的待处理任务
   - 中断正在执行的ComfyUI工作流
   - 取消所有相关的异步任务
   - 更新任务状态为已终止（TERMINATED）
   - 释放相关资源

2. 图像生成中断处理
   - 检测ComfyUI任务状态
   - 识别已中断或不存在的任务
   - 自动退出等待图像生成的循环
   - 向调用方返回中断状态
   - 防止资源泄漏和无限等待

3. 恢复机制
   - 支持从保存点重新启动任务
   - 保留已生成的中间资源
   - 提供部分结果访问


## 技术架构

1. 后端技术栈：
   - Python 3.11+
   - FastAPI 框架
   - Uvicorn ASGI服务器
   - WebSocket 实时通信
   - Pydantic 数据验证
   - ComfyUI 工作流引擎
   - httpx 异步HTTP客户端
   - Redis 分布式状态管理

2. 部署技术：
   - Docker 容器化
   - Docker Compose 容器编排
   - Gunicorn 生产级WSGI服务器
   - ComfyUI 集群部署
   - Haproxy 负载均衡

3. 开发工具和环境：
   - 支持跨平台（Windows/Linux）
   - 开发/生产环境分离
   - 环境变量配置
   - ComfyUI 工作流开发环境

4. 性能优化：
   - 异步处理
   - 非Windows系统uvloop优化
   - 文件异步处理
   - ComfyUI 并行处理优化
   - Haproxy 反向代理优化
   - 任务队列优先级管理

5. 错误处理与恢复：
   - 全局异常捕获
   - 任务重试机制
   - 资源清理与回收
   - 日志记录与监控
   - 任务中断信号处理
