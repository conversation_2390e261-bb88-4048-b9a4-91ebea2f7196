#!/usr/bin/env python
"""
工具类型修复脚本
修复AutoGen工具类型问题
"""

import os
import sys
import json
import logging
import asyncio
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 确保当前目录在路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入提示词增强代理
from prompt_enhancer_agent import PromptEnhancerAgent, AssistantAgent

# 修补AssistantAgent类的创建方法，以处理字典格式的工具
def monkey_patch_assistant():
    """
    修补AssistantAgent类的创建方法，确保工具格式正确
    """
    # 保存原始的__init__方法
    original_init = AssistantAgent.__init__
    
    # 创建新的__init__方法
    def patched_init(self, name, system_message=None, llm_config=None, 
                   tools=None, max_consecutive_auto_reply=None, 
                   is_termination_msg=None, human_input_mode=None, 
                   code_execution_config=None, default_auto_reply=None, 
                   description=None, model_client=None, reflect_on_tool_use=False):
        # 处理工具格式
        formatted_tools = None
        if tools:
            try:
                # 检查工具类型
                if isinstance(tools, list) and tools and isinstance(tools[0], dict):
                    # 工具已经是字典格式，保持不变
                    formatted_tools = tools
                elif isinstance(tools, list):
                    # 尝试将工具对象转换为字典
                    formatted_tools = []
                    for tool in tools:
                        if isinstance(tool, dict):
                            formatted_tools.append(tool)
                        else:
                            # 尝试转换为字典
                            try:
                                tool_dict = tool.__dict__ if hasattr(tool, '__dict__') else tool
                                formatted_tools.append(tool_dict)
                            except Exception as e:
                                logger.warning(f"无法转换工具 {tool} 为字典: {str(e)}")
            except Exception as e:
                logger.error(f"处理工具格式时出错: {str(e)}")
                # 如果转换失败，使用空工具列表
                formatted_tools = []
        
        # 调用原始的__init__方法，但使用格式化后的工具
        original_init(self, name=name, system_message=system_message, llm_config=llm_config,
                    tools=formatted_tools, max_consecutive_auto_reply=max_consecutive_auto_reply,
                    is_termination_msg=is_termination_msg, human_input_mode=human_input_mode,
                    code_execution_config=code_execution_config, default_auto_reply=default_auto_reply,
                    description=description, model_client=model_client, 
                    reflect_on_tool_use=reflect_on_tool_use)
    
    # 替换原始方法
    AssistantAgent.__init__ = patched_init
    logger.info("已修补AssistantAgent类，添加工具格式处理功能")

# 修补PromptEnhancerAgent类的create_assistant方法
def monkey_patch_prompt_enhancer():
    """
    修补PromptEnhancerAgent类的create_assistant方法
    """
    # 保存原始方法
    original_create_assistant = PromptEnhancerAgent.create_assistant
    
    # 创建新方法
    async def patched_create_assistant(self):
        """
        创建提示词增强助手代理，并确保工具格式正确
        """
        # 确保MCP工具已初始化
        if not self.mcp_tools:
            await self.init_mcp_tools()
        
        # 格式化工具
        formatted_tools = []
        if self.mcp_tools:
            for tool in self.mcp_tools:
                if isinstance(tool, dict):
                    formatted_tools.append(tool)
                else:
                    try:
                        tool_dict = tool.__dict__ if hasattr(tool, '__dict__') else tool
                        formatted_tools.append(tool_dict)
                    except Exception as e:
                        logger.warning(f"无法转换工具: {str(e)}")
        
        # 替换工具列表
        self.mcp_tools = formatted_tools
        
        # 调用原始方法
        return await original_create_assistant(self)
    
    # 替换方法
    PromptEnhancerAgent.create_assistant = patched_create_assistant
    logger.info("已修补PromptEnhancerAgent类，添加工具格式处理功能")

def apply_patches():
    """应用所有修补"""
    monkey_patch_assistant()
    monkey_patch_prompt_enhancer()
    logger.info("已应用所有修补")

if __name__ == "__main__":
    apply_patches()
    logger.info("工具类型修复完成") 