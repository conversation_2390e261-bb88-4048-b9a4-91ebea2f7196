{"1": {"inputs": {"ckpt_name": "sd1.5/AWPainting_v1.5.safetensors", "prompt": "[none]", "example": "[none]"}, "class_type": "CheckpointLoader|pysssss", "_meta": {"title": "Checkpoint Loader 🐍"}}, "2": {"inputs": {"lora_name": "功能/Hyper-SD15-1step-lora.safetensors", "strength_model": 1, "strength_clip": 1, "prompt": "[none]", "example": "[none]", "model": ["1", 0], "clip": ["1", 1]}, "class_type": "LoraLoader|pysssss", "_meta": {"title": "<PERSON><PERSON> 🐍"}}, "8": {"inputs": {"text": ["41", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "9": {"inputs": {"text": ["42", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "14": {"inputs": {"samples": ["22", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "20": {"inputs": {"upscale_method": "nearest-exact", "width": 512, "height": 512, "crop": "disabled", "image": ["44", 0]}, "class_type": "ImageScale", "_meta": {"title": "缩放图像"}}, "21": {"inputs": {"pixels": ["20", 0], "vae": ["1", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "22": {"inputs": {"seed": 389677771544087, "steps": 6, "cfg": 1, "sampler_name": "euler_cfg_pp", "scheduler": "ddim_uniform", "denoise": 0.6, "model": ["2", 0], "positive": ["8", 0], "negative": ["9", 0], "latent_image": ["21", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "41": {"inputs": {"String": "embedding:il/lazypos, 1girl", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "正面提示词"}}, "42": {"inputs": {"String": "(CyberRealistic_Negative-neg), (worst quality:2), (low quality:2), (bad-hands-5, lowres, bad anatomy, deformed, missing fingers, extra digit, fewer digits, cropped,watermark,username),(blurry:1.4), (opened mouth:1.4), easynegative, unreal engine, (nsfw:1.4) (rainbow:1.4), (teeth:1.4), (crowd:1.4)", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "44": {"inputs": {"image": "0.jpg"}, "class_type": "LoadImage", "_meta": {"title": "上传图像"}}, "45": {"inputs": {"filename_prefix": "tuya", "images": ["14", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}