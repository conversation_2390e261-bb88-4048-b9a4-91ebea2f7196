import React from 'react';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-dark-900 text-white">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <h1 className="text-4xl font-bold text-center mb-12">AI创作系统</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* 文生壁纸卡片 */}
          <div className="glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">文生壁纸</h3>
            <p className="text-gray-300 text-center text-lg mb-6">通过文字描述智能生成高清壁纸，支持多种分辨率和风格定制</p>
            <button className="mt-auto glass-card bg-blue-500/20 hover:bg-blue-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
              立即创建
            </button>
          </div>
          
          {/* 文生主题卡片 */}
          <div className="glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-purple-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">文生主题</h3>
            <p className="text-gray-300 text-center text-lg mb-6">一键生成完整UI主题，包含壁纸、图标、字体与配色方案</p>
            <button className="mt-auto glass-card bg-purple-500/20 hover:bg-purple-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
              立即创建
            </button>
          </div>
          
          {/* 文生视频卡片 */}
          <div className="glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-green-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">文生视频</h3>
            <p className="text-gray-300 text-center text-lg mb-6">基于文字描述生成动态视频内容，支持多种场景与风格</p>
            <Link to="/video-generation" className="mt-auto glass-card bg-green-500/20 hover:bg-green-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg inline-block">
              立即创建
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage; 