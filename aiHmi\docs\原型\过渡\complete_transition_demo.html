<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI 完整过渡效果演示</title>
    
    <!-- 外部依赖 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* 主容器 */
        .demo-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        /* 头部导航 */
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
            list-style: none;
        }
        
        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        /* 英雄区域 */
        .hero-section {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
        
        /* 演示网格 */
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .demo-card:hover::before {
            left: 100%;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .card-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .card-effect {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        /* 控制面板 */
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .control-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .control-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .control-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .control-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            color: white;
            font-size: 0.9rem;
        }
        
        .control-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .control-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        /* 快捷键提示 */
        .shortcuts {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .shortcuts-title {
            color: white;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
        }
        
        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }
        
        .shortcut-key {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
        }
        
        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            top: 50%;
            right: 2rem;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 1rem;
            color: white;
            font-size: 0.9rem;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .status-indicator.show {
            opacity: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .control-grid {
                grid-template-columns: 1fr;
            }
            
            .shortcuts-grid {
                grid-template-columns: 1fr;
            }
            
            .status-indicator {
                position: static;
                transform: none;
                margin: 1rem;
            }
        }
        
        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            pointer-events: auto;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div>正在执行过渡效果...</div>
        </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicator" id="statusIndicator">
        <div id="statusText">准备就绪</div>
    </div>
    
    <div class="demo-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="#" class="logo">
                    <i class="fas fa-car"></i> AI HMI 过渡演示
                </a>
                <nav>
                    <ul class="nav-links">
                        <li><a href="#demo">演示</a></li>
                        <li><a href="#controls">控制</a></li>
                        <li><a href="#shortcuts">快捷键</a></li>
                        <li><a href="../README.md" target="_blank">文档</a></li>
                    </ul>
                </nav>
            </div>
        </header>
        
        <!-- 主内容 -->
        <main class="main-content">
            <!-- 英雄区域 -->
            <section class="hero-section fade-in-up">
                <h1 class="hero-title">AI HMI 过渡特效系统</h1>
                <p class="hero-subtitle">
                    体验下一代车载人机界面的流畅过渡效果。支持页面快照捕获、多种蒙版切换动画，
                    为不同设计风格提供专属的过渡体验。点击下方卡片或使用快捷键开始探索。
                </p>
            </section>
            
            <!-- 演示网格 -->
            <section id="demo" class="demo-grid fade-in-up">
                <div class="demo-card" data-page="../1_natural_commute.html" data-effect="sliding_panel">
                    <i class="card-icon fas fa-leaf" style="color: #10b981;"></i>
                    <h3 class="card-title">自然通勤</h3>
                    <p class="card-description">清新自然的界面设计，配合流畅的滑动面板过渡效果，营造舒适的驾驶氛围。</p>
                    <span class="card-effect">滑动面板</span>
                </div>
                
                <div class="demo-card" data-page="../2_cyberpunk_drive.html" data-effect="angled_panel">
                    <i class="card-icon fas fa-robot" style="color: #8b5cf6;"></i>
                    <h3 class="card-title">赛博朋克归途</h3>
                    <p class="card-description">未来科技感的赛博朋克风格，搭配动感的斜切面板过渡，展现科技魅力。</p>
                    <span class="card-effect">斜切面板</span>
                </div>
                
                <div class="demo-card" data-page="../3_glassmorphism_wait.html" data-effect="circle_expand">
                    <i class="card-icon fas fa-gem" style="color: #06b6d4;"></i>
                    <h3 class="card-title">玻璃拟态等待</h3>
                    <p class="card-description">优雅的玻璃拟态设计语言，配合圆形扩展过渡，呈现精致的视觉体验。</p>
                    <span class="card-effect">圆形扩展</span>
                </div>
                
                <div class="demo-card" data-page="../4_neumorphism_rainy.html" data-effect="curtain_open">
                    <i class="card-icon fas fa-cloud-rain" style="color: #64748b;"></i>
                    <h3 class="card-title">新拟态雨天</h3>
                    <p class="card-description">温和的新拟态风格设计，幕布开启过渡效果营造温馨的雨天驾驶场景。</p>
                    <span class="card-effect">幕布开启</span>
                </div>
                
                <div class="demo-card" data-page="../5_kawaii_family_trip.html" data-effect="ripple_expand">
                    <i class="card-icon fas fa-heart" style="color: #f59e0b;"></i>
                    <h3 class="card-title">可爱家庭旅行</h3>
                    <p class="card-description">温馨可爱的家庭出行界面，波纹扩散过渡效果增添趣味性和亲和力。</p>
                    <span class="card-effect">波纹扩散</span>
                </div>
                
                <div class="demo-card" data-page="integrated_demo.html" data-effect="fade">
                    <i class="card-icon fas fa-palette" style="color: #ec4899;"></i>
                    <h3 class="card-title">集成演示</h3>
                    <p class="card-description">查看原始的集成演示页面，对比不同的过渡效果实现方案。</p>
                    <span class="card-effect">淡入淡出</span>
                </div>
            </section>
            
            <!-- 控制面板 -->
            <section id="controls" class="control-panel fade-in-up">
                <h2 class="control-title">
                    <i class="fas fa-sliders-h"></i>
                    过渡效果控制
                </h2>
                <div class="control-grid">
                    <div class="control-item">
                        <label class="control-label">过渡时长 (秒)</label>
                        <input type="range" class="control-input" id="durationSlider" min="0.5" max="3" step="0.1" value="1.2">
                        <span id="durationValue">1.2s</span>
                    </div>
                    
                    <div class="control-item">
                        <label class="control-label">快照质量</label>
                        <select class="control-input" id="qualitySelect">
                            <option value="0.5">低质量 (快速)</option>
                            <option value="0.8" selected>标准质量</option>
                            <option value="1.0">高质量 (慢速)</option>
                        </select>
                    </div>
                    
                    <div class="control-item">
                        <label class="control-label">调试模式</label>
                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" id="debugToggle" checked>
                            <span>启用调试信息</span>
                        </label>
                    </div>
                    
                    <div class="control-item">
                        <label class="control-label">快照功能</label>
                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" id="snapshotToggle" checked>
                            <span>启用页面快照</span>
                        </label>
                    </div>
                    
                    <div class="control-item">
                        <button class="control-button" id="clearHistoryBtn">
                            <i class="fas fa-trash"></i> 清理快照历史
                        </button>
                    </div>
                    
                    <div class="control-item">
                        <button class="control-button" id="testTransitionBtn">
                            <i class="fas fa-play"></i> 测试过渡效果
                        </button>
                    </div>
                </div>
            </section>
            
            <!-- 快捷键说明 -->
            <section id="shortcuts" class="shortcuts fade-in-up">
                <h3 class="shortcuts-title">
                    <i class="fas fa-keyboard"></i>
                    键盘快捷键
                </h3>
                <div class="shortcuts-grid">
                    <div class="shortcut-item">
                        <span>自然通勤</span>
                        <span class="shortcut-key">Ctrl + 1</span>
                    </div>
                    <div class="shortcut-item">
                        <span>赛博朋克归途</span>
                        <span class="shortcut-key">Ctrl + 2</span>
                    </div>
                    <div class="shortcut-item">
                        <span>玻璃拟态等待</span>
                        <span class="shortcut-key">Ctrl + 3</span>
                    </div>
                    <div class="shortcut-item">
                        <span>新拟态雨天</span>
                        <span class="shortcut-key">Ctrl + 4</span>
                    </div>
                    <div class="shortcut-item">
                        <span>可爱家庭旅行</span>
                        <span class="shortcut-key">Ctrl + 5</span>
                    </div>
                    <div class="shortcut-item">
                        <span>返回演示页面</span>
                        <span class="shortcut-key">Ctrl + 0</span>
                    </div>
                </div>
            </section>
        </main>
    </div>
    
    <!-- 引入增强过渡管理器 -->
    <script src="enhanced_transition_manager.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化增强过渡管理器
            const transitionManager = new EnhancedTransitionManager({
                enableDebug: true,
                enableSnapshots: true,
                snapshotQuality: 0.8,
                transitionDuration: 1.2
            });
            
            // 存储到全局变量
            window.transitionManager = transitionManager;
            
            // 获取DOM元素
            const loadingOverlay = document.getElementById('loadingOverlay');
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const durationSlider = document.getElementById('durationSlider');
            const durationValue = document.getElementById('durationValue');
            const qualitySelect = document.getElementById('qualitySelect');
            const debugToggle = document.getElementById('debugToggle');
            const snapshotToggle = document.getElementById('snapshotToggle');
            const clearHistoryBtn = document.getElementById('clearHistoryBtn');
            const testTransitionBtn = document.getElementById('testTransitionBtn');
            
            // 状态显示函数
            function showStatus(message, duration = 3000) {
                statusText.textContent = message;
                statusIndicator.classList.add('show');
                setTimeout(() => {
                    statusIndicator.classList.remove('show');
                }, duration);
            }
            
            // 显示加载遮罩
            function showLoading() {
                loadingOverlay.classList.add('show');
            }
            
            // 隐藏加载遮罩
            function hideLoading() {
                loadingOverlay.classList.remove('show');
            }
            
            // 绑定演示卡片点击事件
            document.querySelectorAll('.demo-card').forEach(card => {
                card.addEventListener('click', function() {
                    const page = this.dataset.page;
                    const effect = this.dataset.effect;
                    
                    if (page && effect) {
                        showLoading();
                        showStatus(`正在切换到: ${this.querySelector('.card-title').textContent}`);
                        
                        // 计算点击位置（用于圆形扩展效果）
                        const rect = this.getBoundingClientRect();
                        const options = {
                            centerX: ((rect.left + rect.width / 2) / window.innerWidth) * 100,
                            centerY: ((rect.top + rect.height / 2) / window.innerHeight) * 100,
                            duration: parseFloat(durationSlider.value)
                        };
                        
                        transitionManager.transitionToPage(page, effect, options)
                            .then(() => {
                                hideLoading();
                                showStatus('过渡完成');
                            })
                            .catch(error => {
                                hideLoading();
                                showStatus('过渡失败: ' + error.message, 5000);
                                console.error('Transition failed:', error);
                            });
                    }
                });
            });
            
            // 控制面板事件绑定
            durationSlider.addEventListener('input', function() {
                const value = parseFloat(this.value);
                durationValue.textContent = value + 's';
                transitionManager.options.transitionDuration = value;
            });
            
            qualitySelect.addEventListener('change', function() {
                transitionManager.options.snapshotQuality = parseFloat(this.value);
                showStatus(`快照质量已设置为: ${this.options[this.selectedIndex].text}`);
            });
            
            debugToggle.addEventListener('change', function() {
                transitionManager.options.enableDebug = this.checked;
                showStatus(`调试模式: ${this.checked ? '已启用' : '已禁用'}`);
            });
            
            snapshotToggle.addEventListener('change', function() {
                transitionManager.options.enableSnapshots = this.checked;
                showStatus(`页面快照: ${this.checked ? '已启用' : '已禁用'}`);
            });
            
            clearHistoryBtn.addEventListener('click', function() {
                transitionManager.clearSnapshotHistory();
                showStatus('快照历史已清理');
            });
            
            testTransitionBtn.addEventListener('click', function() {
                const effects = ['sliding_panel', 'angled_panel', 'circle_expand', 'curtain_open', 'ripple_expand', 'fade'];
                const randomEffect = effects[Math.floor(Math.random() * effects.length)];
                
                showStatus(`测试过渡效果: ${randomEffect}`);
                
                // 创建测试内容
                const testContent = document.createElement('div');
                testContent.style.cssText = `
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 2rem;
                    font-weight: bold;
                `;
                testContent.textContent = `测试效果: ${randomEffect}`;
                
                const options = {
                    centerX: 50,
                    centerY: 50,
                    duration: parseFloat(durationSlider.value)
                };
                
                transitionManager.executeTransition(
                    transitionManager.createPlaceholderSnapshot(),
                    testContent,
                    randomEffect,
                    options
                ).then(() => {
                    setTimeout(() => {
                        transitionManager.cleanupTransition();
                        showStatus('测试完成');
                    }, 2000);
                });
            });
            
            // 添加返回演示页面的快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === '0') {
                    e.preventDefault();
                    showStatus('返回演示页面');
                    window.location.href = 'complete_transition_demo.html';
                }
            });
            
            // 页面加载完成提示
            setTimeout(() => {
                showStatus('AI HMI 过渡演示已就绪，点击卡片或使用快捷键开始体验');
            }, 1000);
            
            // 添加页面可见性变化监听
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // 页面隐藏时暂停动画
                    if (window.gsap) {
                        gsap.globalTimeline.pause();
                    }
                } else {
                    // 页面显示时恢复动画
                    if (window.gsap) {
                        gsap.globalTimeline.resume();
                    }
                }
            });
            
            // 性能监控
            if (transitionManager.options.enableDebug) {
                setInterval(() => {
                    const historyCount = transitionManager.getSnapshotHistory().length;
                    console.log(`[Performance] Snapshot history count: ${historyCount}`);
                }, 30000);
            }
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            if (window.transitionManager && window.transitionManager.options.enableDebug) {
                document.getElementById('statusText').textContent = '发生错误: ' + e.message;
                document.getElementById('statusIndicator').classList.add('show');
            }
        });
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (window.transitionManager) {
                window.transitionManager.destroy();
            }
        });
    </script>
</body>
</html>