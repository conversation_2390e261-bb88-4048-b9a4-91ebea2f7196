from minio import Minio
from minio.error import S3Error
from flask import send_file
from PIL import  Image, ImageDraw, ImageFont
from io import BytesIO
import io
import logging
import mimetypes

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# MinIO 服务器的地址
minio_endpoint = "106.63.5.45:9000"
# MinIO 服务器的访问密钥
access_key = "admin"
# MinIO 服务器的私密密钥
secret_key = "Fsti#2024"
bucket_name = "changan"

# 创建 MinIO 客户端实例
minio_client = Minio(minio_endpoint, access_key, secret_key, secure=False)

def minioGetPresigned_url(bucketName, objectName):
    presigned_url = minio_client.presigned_get_object(bucketName, objectName)
    return presigned_url

def minioGet(bucketName, objectName):
    data_stream = minio_client.get_object(bucketName, objectName)
    # 根据文件类型设置正确的MIME类型
    mimetype, _ = mimetypes.guess_type(objectName)
    if not mimetype:
        mimetype = 'application/octet-stream'  # 如果无法猜测类型，则使用默认的二进制流类型
    return send_file(data_stream, mimetype=mimetype)
    # return send_file(data_stream, mimetype='image/png')

def list_files(bucket_name, suffix, prefix=''):
    png_files = []
    # 列出指定路径下的所有对象
    objects = list(minio_client.list_objects(bucket_name, prefix=prefix, recursive=False))
    for obj in objects:
        if obj.object_name.lower().endswith(suffix):
            png_files.append(obj.object_name)
    return png_files

def async_put_file(param):
    objectName = param.get('objectName')
    fileType = param.get('fileType', 'jpg').lower()  # 默认文件类型为jpg，并转换为小写
    bucketName = param.get('bucketName',bucket_name)
    fileStream = param.get('fileStream')
    if not fileStream:
        raise ValueError("文件流不能为空")
    logger.info(f'开始文件：{objectName}，文件类型：{fileType}')

    found = minio_client.bucket_exists(bucketName)
    if not found:
        logger.info(f'桶{bucketName}不存在')
        # 桶不存在，创建桶
        try :
            minio_client.make_bucket(bucketName)
            logger.info(f'创建桶{bucketName}成功')
        except Exception as e:
            logger.error(f'创建桶{bucketName}失败：{e}') 
    try:
        # 获取文件数据流
        response_content = fileStream.read()
        fileStream.close()

        if fileType == 'jpg' or fileType == 'png':
            # 使用WebP格式存储图片
            with Image.open(BytesIO(response_content)) as img:
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='webp')
                img_bytes.seek(0)
                mimetype = 'image/webp'
        elif fileType == 'json':
            img_bytes = BytesIO(response_content)
            mimetype = 'application/json'
        elif fileType == 'mp4':
            # 可以考虑压缩MP4文件，这里假设不压缩，直接使用二进制流
            img_bytes = BytesIO(response_content)
            mimetype = 'video/mp4'
        elif fileType == 'zip':
            # 可以尝试重新压缩ZIP文件，这里假设不重新压缩，直接使用二进制流
            img_bytes = BytesIO(response_content)
            mimetype = 'application/zip'
        else:
            raise ValueError(f'未知的文件类型: {fileType}')

        logger.info(f'存储文件中：{objectName}，文件类型：{fileType}')
        # 存储对象到MinIO
        minio_client.put_object(bucketName, objectName, img_bytes, -1, part_size=5*1024*1024, content_type=mimetype)
        logger.info(f'成功存储文件：{objectName}，文件类型：{fileType}')

        return objectName
    except S3Error as e:
        print(str(e))
        logger.warning(f'存储文件异常 S3Error：{str(e)}')
        raise e
    except Exception as e:
        logger.error(f"存储文件异常: {str(e)}")
        raise e

def del_object(bucketName,objectName):
    # 检查存储桶是否存在
    found = minio_client.bucket_exists(bucket_name)
    if  found:
        # 删除对象
        minio_client.remove_object(bucketName, objectName)
