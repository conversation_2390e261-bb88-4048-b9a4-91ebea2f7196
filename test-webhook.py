#!/usr/bin/env python3
"""
简单的webhook测试脚本
用于测试n8n工作流的webhook功能
"""

import requests
import json
import sys

def test_webhook():
    """测试webhook功能"""
    
    # webhook URL - 使用测试URL
   # webhook_url = "http://************:7869/webhook/ai-hmi-scene-layout-v2"

    # webhook URL - 使用基础LLM
    webhook_url = "http://************:7869/webhook-test/ai-hmi-basic-llm"
    #webhook_url = "http://************:7869/webhook/ai-hmi-basic"
    
    # 测试数据
    test_data = {
        "scene_description": "早高峰家庭通勤：我需要先送孩子毛毛去幼儿园，然后独自前往公司。孩子在车上会问各种问题，需要播放教育视频。送完孩子后我要去麦当劳买早餐，然后赶10点的重要会议。"
    }
    
    print(f"🚀 测试webhook: {webhook_url}")
    print(f"📝 发送数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("-" * 50)
    
    try:
        # 发送POST请求
        response = requests.post(
            webhook_url,
            json=test_data,
            headers={
                "Content-Type": "application/json"
            },
            timeout=60  # 60秒超时
        )
        
        print(f"✅ 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("🎉 成功响应:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                # 如果有HTML代码，保存到文件
                if 'htmlCode' in result and result['htmlCode']:
                    with open('generated_layout.html', 'w', encoding='utf-8') as f:
                        f.write(result['htmlCode'])
                    print("\n💾 HTML代码已保存到 generated_layout.html")
                    
            except json.JSONDecodeError:
                print("📄 响应内容 (非JSON):")
                print(response.text)
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时 (60秒)")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误 - 无法连接到webhook")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"💥 未知错误: {e}")

if __name__ == "__main__":
    test_webhook()
