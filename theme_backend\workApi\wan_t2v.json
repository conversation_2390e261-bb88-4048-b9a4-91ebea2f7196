{"11": {"inputs": {"model_name": "umt5-xxl-enc-bf16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "fp8_e4m3fn"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "Load WanVideo T5 TextEncoder"}}, "16": {"inputs": {"positive_prompt": "flat 2d animation style，一位未来战士，身着融合了中国传统盔甲元素的机甲，站在未来的城市废墟中，缓步走来。中景，镜头缓缓拉近，展现机甲的细节。周身环绕着粒子特效，充满科技感、史诗感和力量感。\n\n", "negative_prompt": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", "force_offload": true, "t5": ["11", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "提示词输入"}}, "22": {"inputs": {"model": "Wan2_1-T2V-14B_fp8_e4m3fn.safetensors", "base_precision": "bf16", "quantization": "fp8_e4m3fn", "load_device": "offload_device", "attention_mode": "sageattn", "block_swap_args": ["39", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "27": {"inputs": {"steps": 20, "cfg": 6, "shift": 5, "seed": 844755487243750, "force_offload": true, "scheduler": "dpm++", "riflex_freq_index": 0, "denoise_strength": 1, "model": ["22", 0], "text_embeds": ["16", 0], "image_embeds": ["37", 0], "feta_args": ["44", 0], "teacache_args": ["42", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "28": {"inputs": {"enable_vae_tiling": true, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "vae": ["38", 0], "samples": ["27", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "30": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "Wan/WanVideo2_1_T2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["28", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "37": {"inputs": {"width": 832, "height": 480, "num_frames": 81}, "class_type": "WanVideoEmptyEmbeds", "_meta": {"title": "WanVideo Empty Embeds"}}, "38": {"inputs": {"model_name": "Wan2_1_VAE_bf16.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "39": {"inputs": {"blocks_to_swap": 10, "offload_img_emb": false, "offload_txt_emb": false}, "class_type": "WanVideoBlockSwap", "_meta": {"title": "WanVideo BlockSwap"}}, "42": {"inputs": {"rel_l1_thresh": 0.25, "start_step": 1, "end_step": -1, "cache_device": "offload_device", "use_coefficients": true}, "class_type": "WanVideoTeaCache", "_meta": {"title": "WanVideo TeaCache"}}, "44": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}}