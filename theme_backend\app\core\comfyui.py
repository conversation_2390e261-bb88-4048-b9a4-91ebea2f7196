import aiohttp
import json
import asyncio
import websockets
from typing import Dict, Any, Optional
from .config import settings

class ComfyUIClient:
    def __init__(self):
        self.base_url = settings.COMFYUI_BASE_URL
        self.ws_url = settings.COMFYUI_WS_URL
        
    async def queue_prompt(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """将工作流提交到ComfyUI队列"""
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/prompt", json=workflow) as response:
                return await response.json()
    
    async def get_image(self, filename: str) -> bytes:
        """获取生成的图片"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/view?filename={filename}") as response:
                return await response.read()
    
    async def wait_for_completion(self, prompt_id: str) -> Dict[str, Any]:
        """等待工作流完成并返回结果"""
        async with websockets.connect(self.ws_url) as websocket:
            while True:
                message = await websocket.recv()
                data = json.loads(message)
                
                if "type" not in data:
                    continue
                    
                if data["type"] == "execution_complete" and data.get("data", {}).get("prompt_id") == prompt_id:
                    return data["data"]
                    
                if data["type"] == "execution_error":
                    raise Exception(f"Workflow execution error: {data.get('data', {}).get('error', 'Unknown error')}")
    
    async def process_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """处理完整的工作流程"""
        # 提交工作流到队列
        queue_response = await self.queue_prompt(workflow)
        prompt_id = queue_response["prompt_id"]
        
        # 等待工作流完成
        result = await self.wait_for_completion(prompt_id)
        
        return result

    async def get_history(self) -> Dict[str, Any]:
        """获取历史记录"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/history") as response:
                return await response.json()

    async def interrupt_workflow(self) -> Dict[str, Any]:
        """中断当前工作流"""
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/interrupt") as response:
                return await response.json()

comfy_client = ComfyUIClient() 