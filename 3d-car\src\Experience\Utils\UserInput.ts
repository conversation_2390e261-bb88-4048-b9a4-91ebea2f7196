import type Experience from "../Experience";
import { ApiService } from "./ApiService";

export class UserInput {
    container: HTMLDivElement;
    input: HTMLInputElement;
    button: HTMLButtonElement;
    experience: Experience;
    private apiService: ApiService;
    private isLoading: boolean = false;

    constructor(experience: Experience) {
        this.experience = experience;
        this.container = document.createElement('div');
        this.input = document.createElement('input');
        this.button = document.createElement('button');
        this.apiService = ApiService.getInstance();

        this.init();
    }

    init() {
        const container = this.container;
        const input = this.input;

        // 设置容器样式
        container.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 10px;
            zIndex: 10000;
        `;

        // 设置输入框样式
        input.type = 'text';
        input.placeholder = '输入内容';
        input.style.cssText = `
            padding: 12px 20px;
            backgroundColor: rgba(20, 20, 20, 0.7);
            backdropFilter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            borderRadius: 30px;
            color: white;
            cursor: text;
            fontSize: 14px;
            fontWeight: bold;
            width: 300px;
            boxShadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
            outline: none;
        `;

        // 添加webkit前缀
        (input.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';

        // 添加输入框焦点效果
        input.addEventListener('focus', () => {
            input.style.backgroundColor = 'rgba(38, 214, 233, 0.1)';
            input.style.border = '1px solid rgba(38, 214, 233, 0.3)';
            input.style.boxShadow = '0 4px 15px rgba(38, 214, 233, 0.2)';
        });

        input.addEventListener('blur', () => {
            input.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
            input.style.border = '1px solid rgba(255, 255, 255, 0.1)';
            input.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.3)';
        });

        // 设置按钮样式
        const button = this.button;
        button.textContent = '生成车衣';

        const buttonStyles = {
            padding: '12px 20px',
            backgroundColor: 'rgba(147, 51, 234, 0.7)',
            backdropFilter: 'blur(5px)',
            color: 'white',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '30px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
            transition: 'all 0.2s ease',
            minWidth: '100px',
            position: 'relative',
        };

        Object.assign(button.style, buttonStyles as any);
        // 添加webkit前缀
        (button.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';

        // 添加悬停效果
        button.addEventListener('mouseenter', () => {
            if (!this.isLoading) {
                button.style.backgroundColor = 'rgba(168, 85, 247, 0.8)';
                button.style.transform = 'translateY(-2px)';
            }
        });

        button.addEventListener('mouseleave', () => {
            if (!this.isLoading) {
                button.style.backgroundColor = 'rgba(147, 51, 234, 0.7)';
                button.style.transform = 'translateY(0)';
            }
        });

        // 点击事件 - 处理输入的内容
        button.addEventListener('click', async () => {
            if (this.isLoading) return;
            
            const inputValue = this.input.value.trim();
            if (!inputValue) {
                alert('请输入内容');
                return;
            }

            this.setLoading(true);
            try {
                const result = await this.apiService.generateCarSkin(inputValue);
                if (result.success && result.data) {
                    // 触发应用赛车贴图事件
                    this.experience.params.prompt = inputValue;
                    
                    // 设置hasSkin为true并保存当前皮肤URL
                    this.experience.params.hasSkin = true;
                    this.experience.params.currentSkinUrl = result.data.image_url;
                    
                    // 应用新的车衣
                    if (this.experience.world.car) {
                        this.experience.world.car.applySkin(result.data.image_url);
                    }
                    
                    console.log('生成成功:', result.data);
                } else {
                    alert(result.error || '生成失败，请重试');
                }
            } catch (error) {
                console.error('生成失败:', error);
                alert('生成失败，请重试');
            } finally {
                this.setLoading(false);
            }
        });

        // 将输入框和按钮添加到容器中
        container.appendChild(input);
        container.appendChild(button);

        // 将容器添加到DOM中
        document.body.appendChild(container);

        console.log('Input Button component added to the DOM');
    }

    private setLoading(loading: boolean) {
        this.isLoading = loading;
        const button = this.button;
        
        if (loading) {
            button.textContent = '生成中...';
            button.style.backgroundColor = 'rgba(147, 51, 234, 0.4)';
            button.style.cursor = 'not-allowed';
            button.style.opacity = '0.7';
        } else {
            button.textContent = '生成车衣';
            button.style.backgroundColor = 'rgba(147, 51, 234, 0.7)';
            button.style.cursor = 'pointer';
            button.style.opacity = '1';
        }
    }

    dispose() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}
