# MCP会话存储服务设计

## 1. 概述

MCP（Model Context Protocol）会话存储服务是一个独立于WebSocket的服务，负责将AI交互会话信息存储到持久化存储（MongoDB）和临时缓存（Redis）中。该服务遵循MCP协议，可以被AI代理（Agent）直接调用，确保会话的可追溯性和可恢复性。

## 2. 系统架构

```
┌─────────────────┐       ┌───────────────┐       ┌────────────────┐
│                 │       │               │       │                │
│  WebSocket服务  │───────│  Agent服务    │───────│  MCP服务接口   │
│                 │       │               │       │                │
└─────────────────┘       └───────┬───────┘       └────────┬───────┘
                                  │                        │
                                  │                        │
                                  │                        ▼
                                  │               ┌────────────────┐
                                  │               │                │
                                  │               │  Redis(临时)   │
                                  │               │                │
                                  │               └────────┬───────┘
                                  │                        │
                                  │                        │
                                  ▼                        ▼
                          ┌────────────────┐      ┌────────────────┐
                          │                │      │                │
                          │  用户会话记录  │      │  MongoDB(永久) │
                          │                │      │                │
                          └────────────────┘      └────────────────┘
```

## 3. 功能设计

### 3.1 核心功能

1. **会话存储**：
   - 存储会话ID、用户信息、提示词、增强结果等数据
   - 跟踪会话的状态变化和生命周期

2. **双层存储策略**：
   - Redis：存储活跃会话，支持快速访问和实时更新
   - MongoDB：存储历史会话，支持长期查询和分析

3. **用户关联**：
   - 每个会话关联到特定用户（当前默认admin）
   - 预留用户权限和个性化设置扩展

4. **服务独立性**：
   - MCP服务完全独立于WebSocket服务
   - 仅通过Agent服务进行间接交互
   - 不直接处理WebSocket连接和消息推送

### 3.2 数据模型

#### Redis存储结构

```json
{
  "session:{session_id}": {
    "user_id": "admin",  // 预留用户ID，默认admin
    "prompt": "原始提示词",
    "status": "running|completed|error",
    "created_at": 1623456789,
    "last_updated": 1623456999,
    "ttl": 86400  // 24小时过期
  },
  
  "session:{session_id}:messages": [
    {
      "timestamp": 1623456790,
      "event_type": "thinking",
      "content": "思考过程内容"
    },
    {
      "timestamp": 1623456800,
      "event_type": "content",
      "content": "生成的内容片段",
      "is_final": false
    },
    {
      "timestamp": 1623456900,
      "event_type": "content",
      "content": "最终增强提示词",
      "is_final": true
    }
  ]
}
```

#### MongoDB存储结构

```json
{
  "_id": "ObjectId",
  "session_id": "唯一会话ID",
  "user_id": "admin",  // 预留用户ID，默认admin
  "prompt": "原始提示词",
  "result": "最终增强提示词",
  "status": "completed|error",
  "created_at": "2023-06-12T10:00:00Z",
  "completed_at": "2023-06-12T10:02:00Z",
  "duration_ms": 120000,
  "messages": [
    {
      "timestamp": "2023-06-12T10:00:10Z",
      "event_type": "thinking",
      "content": "思考过程内容"
    },
    {
      "timestamp": "2023-06-12T10:01:00Z",
      "event_type": "content",
      "content": "生成的内容片段",
      "is_final": false
    },
    {
      "timestamp": "2023-06-12T10:02:00Z",
      "event_type": "content",
      "content": "最终增强提示词",
      "is_final": true
    }
  ],
  "metadata": {
    "device": "browser/mobile",
    "browser": "Chrome/Firefox/Safari",
    "os": "Windows/MacOS/iOS",
    "ip_address": "127.0.0.1"  // 匿名化处理或加密存储
  }
}
```

## 4. MCP服务接口

### 4.1 MCP工具定义

MCP服务提供以下工具，遵循Model Context Protocol规范：

1. `create_session` - 创建新会话
2. `update_session` - 更新会话状态和内容
3. `add_message` - 添加新消息到会话
4. `complete_session` - 标记会话完成
5. `get_session` - 获取会话信息
6. `list_user_sessions` - 列出用户历史会话

### 4.2 接口示例

```json
{
  "tools": [
    {
      "name": "create_session",
      "description": "创建新的会话记录",
      "parameters": {
        "type": "object",
        "properties": {
          "user_id": {
            "type": "string",
            "description": "用户ID，默认为admin"
          },
          "prompt": {
            "type": "string",
            "description": "原始提示词"
          },
          "metadata": {
            "type": "object",
            "description": "会话元数据"
          }
        },
        "required": ["prompt"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "创建的会话ID"
          },
          "status": {
            "type": "string",
            "description": "会话状态"
          }
        }
      }
    },
    {
      "name": "add_message",
      "description": "向会话添加新消息",
      "parameters": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "会话ID"
          },
          "event_type": {
            "type": "string",
            "description": "事件类型：thinking, content, code, error"
          },
          "content": {
            "type": "string",
            "description": "消息内容"
          },
          "is_final": {
            "type": "boolean",
            "description": "是否为最终消息"
          }
        },
        "required": ["session_id", "event_type", "content"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "success": {
            "type": "boolean"
          },
          "message_id": {
            "type": "string"
          }
        }
      }
    },
    {
      "name": "complete_session",
      "description": "标记会话完成并持久化",
      "parameters": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "会话ID"
          },
          "result": {
            "type": "string",
            "description": "最终结果"
          },
          "status": {
            "type": "string",
            "description": "完成状态: completed, error"
          }
        },
        "required": ["session_id", "status"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "success": {
            "type": "boolean"
          }
        }
      }
    }
  ]
}
```

## 5. 数据流程

1. **会话创建流程**：
   - WebSocket服务通知Agent创建新会话
   - Agent调用MCP服务的`create_session`工具
   - MCP服务在Redis中创建临时会话记录
   - 会话ID返回给Agent，然后传递给WebSocket服务

2. **消息添加流程**：
   - 每当Agent生成新的思考或内容
   - Agent直接调用MCP服务的`add_message`工具
   - 消息追加到Redis中的会话记录
   - Agent同时通过WebSocket服务将消息推送给前端

3. **会话完成流程**：
   - 当提示词增强完成或出错时
   - Agent调用MCP服务的`complete_session`工具
   - MCP服务将完整会话从Redis导出到MongoDB
   - Redis中的会话设置过期时间（24小时）
   - Agent通过WebSocket服务通知前端会话完成

4. **会话查询流程**：
   - 活跃会话从Redis中查询
   - 历史会话从MongoDB中查询

## 6. 安全考虑

1. **数据隔离**：确保不同用户的会话数据严格隔离
2. **权限控制**：预留基于用户角色的访问控制机制
3. **数据保护**：敏感信息加密存储
4. **合规性**：遵循数据保护法规，提供数据删除机制
5. **审计日志**：记录重要操作的审计日志

## 7. 扩展性考虑

1. **多用户支持**：
   - 当前默认用户为"admin"
   - 预留用户认证和授权机制
   - 支持未来实现完整的用户管理系统

2. **数据分析**：
   - 会话数据可用于分析用户行为和偏好
   - 支持提示词效果评估和改进

3. **集群部署**：
   - Redis集群支持高可用性
   - MongoDB分片支持水平扩展

## 8. 实现示例

### MCP服务端点定义

```python
from mcp_protocol import MCPServer, Tool, Resource

# 创建MCP服务
session_storage_server = MCPServer(name="session_storage")

# 注册工具
@session_storage_server.tool
async def create_session(user_id: str = "admin", prompt: str = "", metadata: dict = None):
    """创建新的会话记录"""
    session_id = str(uuid.uuid4())
    timestamp = time.time()
    
    # Redis存储临时会话
    redis_client.hset(
        f"session:{session_id}",
        mapping={
            "user_id": user_id,
            "prompt": prompt,
            "status": "created",
            "created_at": timestamp,
            "last_updated": timestamp
        }
    )
    redis_client.expire(f"session:{session_id}", 86400)  # 24小时过期
    
    return {
        "session_id": session_id,
        "status": "created"
    }

@session_storage_server.tool
async def add_message(session_id: str, event_type: str, content: str, is_final: bool = False):
    """向会话添加新消息"""
    if not redis_client.exists(f"session:{session_id}"):
        return {"success": False, "error": "Session not found"}
    
    message = {
        "timestamp": time.time(),
        "event_type": event_type,
        "content": content,
        "is_final": is_final
    }
    
    # 添加消息到Redis列表
    redis_client.rpush(f"session:{session_id}:messages", json.dumps(message))
    redis_client.hset(f"session:{session_id}", "last_updated", time.time())
    
    return {"success": True, "message_id": str(uuid.uuid4())}

@session_storage_server.tool
async def complete_session(session_id: str, result: str = "", status: str = "completed"):
    """标记会话完成并持久化到MongoDB"""
    if not redis_client.exists(f"session:{session_id}"):
        return {"success": False, "error": "Session not found"}
    
    # 获取会话数据
    session_data = redis_client.hgetall(f"session:{session_id}")
    messages_data = redis_client.lrange(f"session:{session_id}:messages", 0, -1)
    messages = [json.loads(m) for m in messages_data]
    
    # 更新Redis中的会话状态
    redis_client.hset(
        f"session:{session_id}", 
        mapping={
            "status": status,
            "result": result,
            "completed_at": time.time()
        }
    )
    
    # 构建MongoDB文档
    mongo_doc = {
        "session_id": session_id,
        "user_id": session_data.get("user_id", "admin"),
        "prompt": session_data.get("prompt", ""),
        "result": result,
        "status": status,
        "created_at": datetime.fromtimestamp(float(session_data.get("created_at", 0))),
        "completed_at": datetime.utcnow(),
        "duration_ms": int((time.time() - float(session_data.get("created_at", 0))) * 1000),
        "messages": messages
    }
    
    # 存储到MongoDB
    mongo_client.sessions.insert_one(mongo_doc)
    
    return {"success": True}

### Agent调用MCP服务示例

```python
# 在Agent服务中
class PromptEnhancerAgent:
    def __init__(self, websocket_manager):
        self.mcp_client = MCPClient(MCP_SERVER_URL)
        self.websocket_manager = websocket_manager
    
    async def enhance_prompt(self, session_id, prompt, user_id="admin"):
        # 创建会话
        result = await self.mcp_client.call_tool(
            "create_session",
            {
                "user_id": user_id,
                "prompt": prompt
            }
        )
        
        # 开始生成增强提示词
        try:
            # 记录思考过程
            await self.mcp_client.call_tool(
                "add_message",
                {
                    "session_id": session_id,
                    "event_type": "thinking",
                    "content": "分析提示词需求..."
                }
            )
            
            # 同时通过WebSocket发送给前端
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "thinking",
                    "data": {
                        "content": "分析提示词需求...",
                        "is_final": False
                    }
                }
            )
            
            # 模拟生成过程
            enhanced_prompt = f"增强版: {prompt}"
            
            # 记录最终结果
            await self.mcp_client.call_tool(
                "add_message",
                {
                    "session_id": session_id,
                    "event_type": "content",
                    "content": enhanced_prompt,
                    "is_final": True
                }
            )
            
            # 完成会话
            await self.mcp_client.call_tool(
                "complete_session",
                {
                    "session_id": session_id,
                    "result": enhanced_prompt,
                    "status": "completed"
                }
            )
            
            # 通过WebSocket发送结果
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "content",
                    "data": {
                        "content": enhanced_prompt,
                        "is_final": True
                    }
                }
            )
            
            return enhanced_prompt
            
        except Exception as e:
            # 记录错误
            await self.mcp_client.call_tool(
                "add_message",
                {
                    "session_id": session_id,
                    "event_type": "error",
                    "content": str(e)
                }
            )
            
            # 标记会话为错误状态
            await self.mcp_client.call_tool(
                "complete_session",
                {
                    "session_id": session_id,
                    "status": "error"
                }
            )
            
            # 通过WebSocket发送错误
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "error",
                    "data": {
                        "content": f"处理过程中出错: {str(e)}",
                        "is_final": True
                    }
                }
            )
            
            raise
```

## 9. 部署配置

### 9.1 依赖

```
# MCP服务依赖
mcp-protocol>=1.0.0
redis>=4.0.0
pymongo>=4.0.0
```

### 9.2 环境变量

```
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# MongoDB配置
MONGO_URI=mongodb://localhost:27017
MONGO_DB=theme_backend
MONGO_COLLECTION=sessions

# MCP服务配置
MCP_PORT=19220
MCP_HOST=0.0.0.0
``` 