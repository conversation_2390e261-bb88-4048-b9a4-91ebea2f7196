import logging
import os
import datetime
from typing import Any, Dict, List, Optional, Union

from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, PyMongoError

# MongoDB配置
MONGO_HOST = "************"
MONGO_PORT = 37817
MONGO_USERNAME = "admin"
MONGO_PASSWORD = "kLKe3NFM4RZMgXhA"
MONGO_DB_NAME = "admin"

# 常量定义
SESSIONS_COLLECTION = "sessions"
MESSAGES_COLLECTION = "messages"
CONTEXT_COLLECTION = "context"

logger = logging.getLogger(__name__)

class MongoClient:
    """MongoDB客户端封装，提供会话和消息存储功能"""
    
    _instance = None
    _client = None
    _db = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MongoClient, cls).__new__(cls)
            cls._instance._init_connection()
        return cls._instance
    
    def _init_connection(self):
        """初始化MongoDB连接"""
        try:
            logger.info("尝试连接MongoDB...")
            # 创建MongoDB连接
            if MONGO_USERNAME and MONGO_PASSWORD:
                connection_string = f"mongodb://{MONGO_USERNAME}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB_NAME}?authSource=admin"
            else:
                connection_string = f"mongodb://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB_NAME}"
            
            logger.info(f"MongoDB连接字符串: {connection_string}")
            self._client = MongoClient(connection_string)
            
            # 检查连接
            self._client.admin.command('ping')
            
            # 获取数据库
            self._db = self._client[MONGO_DB_NAME]
            
            # 创建索引
            self._ensure_indexes()
            
            logger.info("MongoDB连接成功")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {str(e)}")
            self._client = None
            self._db = None
    
    def _ensure_indexes(self):
        """确保必要的索引存在"""
        if not self._db:
            return
        
        # 会话集合索引
        sessions_collection = self._db['sessions']
        sessions_collection.create_index([("sessionId", ASCENDING)], unique=True)
        sessions_collection.create_index([("userId", ASCENDING)])
        sessions_collection.create_index([("lastActiveTime", DESCENDING)])
        
        # 消息集合索引
        messages_collection = self._db['messages']
        messages_collection.create_index([("sessionId", ASCENDING), ("timestamp", ASCENDING)])
        messages_collection.create_index([("agentId", ASCENDING)])
        messages_collection.create_index([("type", ASCENDING)])
        
        # 上下文集合索引
        contexts_collection = self._db['contexts']
        contexts_collection.create_index([("sessionId", ASCENDING), ("agentId", ASCENDING), ("name", ASCENDING)], unique=True)
        contexts_collection.create_index([("lastUpdated", ASCENDING)])
        # TTL索引，自动过期
        contexts_collection.create_index([("expires_at", ASCENDING)], expireAfterSeconds=0)
    
    @property
    def db(self) -> Optional[Database]:
        """获取数据库实例"""
        if not self._db and self._client:
            try:
                self._db = self._client[MONGO_DB_NAME]
            except Exception as e:
                logger.error(f"获取数据库实例失败: {str(e)}")
        return self._db
    
    def get_collection(self, collection_name: str) -> Optional[Collection]:
        """获取集合"""
        if not self.db:
            return None
        return self.db[collection_name]
    
    def create_session(self, session_id: str, user_id: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        在MongoDB中创建会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            metadata: 会话元数据
            
        Returns:
            bool: 是否创建成功
        """
        try:
            logging.info(f"🔄 MongoDB创建会话: session_id={session_id}, user_id={user_id}")
            logging.info(f"📝 元数据: {metadata}")
            
            # 构建会话文档
            session_doc = {
                "session_id": session_id,
                "user_id": user_id,
                "status": "active",
                "created_at": datetime.datetime.now(),
                "updated_at": datetime.datetime.now(),
                "metadata": metadata or {}
            }
            
            # 检查MongoDB连接和集合
            logging.info(f"👉 检查MongoDB连接: {self._client}")
            db = self._client[MONGO_DB_NAME]
            collection = db['sessions']
            logging.info(f"👉 使用集合: {MONGO_DB_NAME}.sessions")
            
            # 插入文档
            logging.info(f"👉 插入会话文档")
            result = collection.insert_one(session_doc)
            
            # 检查结果
            success = result.acknowledged
            logging.info(f"🔙 插入结果: acknowledged={success}, inserted_id={result.inserted_id}")
            
            return success
        except Exception as e:
            logging.error(f"❌ MongoDB创建会话异常: {str(e)}")
            logging.exception("详细异常信息:")
            return False
    
    def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新会话信息
        
        Args:
            session_id: 会话ID
            updates: 更新字段
        
        Returns:
            bool: 是否更新成功
        """
        try:
            sessions = self.get_collection('sessions')
            if not sessions:
                return False
            
            # 始终更新最后活动时间
            updates["lastActiveTime"] = datetime.datetime.now()
            
            result = sessions.update_one(
                {"sessionId": session_id},
                {"$set": updates}
            )
            
            return result.modified_count > 0
        except PyMongoError as e:
            logger.error(f"更新会话失败: {str(e)}")
            return False
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
        
        Returns:
            Optional[Dict[str, Any]]: 会话信息
        """
        try:
            sessions = self.get_collection('sessions')
            if not sessions:
                return None
            
            return sessions.find_one({"sessionId": session_id})
        except PyMongoError as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None
    
    def add_message(self, session_id: str, agent_id: str, message_type: str, 
                   content: Dict[str, Any], parent_message_id: Optional[str] = None,
                   metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        添加消息记录
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            message_type: 消息类型
            content: 消息内容
            parent_message_id: 父消息ID
            metadata: 消息元数据
        
        Returns:
            Optional[str]: 消息ID，失败则返回None
        """
        try:
            messages = self.get_collection('messages')
            if not messages:
                return None
            
            # 更新会话最后活动时间
            self.update_session(session_id, {})
            
            # 创建消息文档
            message_doc = {
                "sessionId": session_id,
                "agentId": agent_id,
                "type": message_type,
                "content": content,
                "timestamp": datetime.datetime.now(),
                "parentMessageId": parent_message_id,
                "metadata": metadata or {}
            }
            
            result = messages.insert_one(message_doc)
            if result.acknowledged:
                return str(result.inserted_id)
            return None
        except PyMongoError as e:
            logger.error(f"添加消息失败: {str(e)}")
            return None
    
    def get_messages(self, session_id: str, limit: int = 50, 
                    message_type: Optional[str] = None,
                    agent_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取会话消息
        
        Args:
            session_id: 会话ID
            limit: 限制消息数量
            message_type: 过滤消息类型
            agent_id: 过滤智能体ID
        
        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        try:
            messages = self.get_collection('messages')
            if not messages:
                return []
                
            # 构建查询条件
            query = {"sessionId": session_id}
            if message_type:
                query["type"] = message_type
            if agent_id:
                query["agentId"] = agent_id
                
            # 查询消息，按时间倒序
            cursor = messages.find(query).sort("timestamp", DESCENDING).limit(limit)
            
            # 转换为列表并返回
            return list(cursor)
        except PyMongoError as e:
            logger.error(f"获取消息失败: {str(e)}")
            return []
    
    def save_context(self, session_id: str, agent_id: str, name: str, 
                    value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        保存上下文数据
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            name: 上下文名称
            value: 上下文值
            ttl: 生存时间（秒）
            
        Returns:
            bool: 是否保存成功
        """
        try:
            contexts = self.get_collection('contexts')
            if not contexts:
                return False
                
            # 计算过期时间
            now = datetime.datetime.now()
            expires_at = None
            if ttl:
                expires_at = now + datetime.timedelta(seconds=ttl)
                
            # 更新或插入
            result = contexts.update_one(
                {
                    "sessionId": session_id,
                    "agentId": agent_id,
                    "name": name
                },
                {
                    "$set": {
                        "value": value,
                        "lastUpdated": now,
                        "expires_at": expires_at
                    }
                },
                upsert=True
            )
            
            return result.acknowledged
        except PyMongoError as e:
            logger.error(f"保存上下文失败: {str(e)}")
            return False
    
    def get_context(self, session_id: str, agent_id: str, name: str) -> Optional[Dict[str, Any]]:
        """
        获取上下文数据
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            name: 上下文名称
            
        Returns:
            Optional[Dict[str, Any]]: 上下文数据
        """
        try:
            contexts = self.get_collection('contexts')
            if not contexts:
                return None
                
            # 查询上下文
            context_doc = contexts.find_one({
                "sessionId": session_id,
                "agentId": agent_id,
                "name": name
            })
            
            if context_doc:
                return context_doc.get("value")
            return None
        except PyMongoError as e:
            logger.error(f"获取上下文失败: {str(e)}")
            return None
    
    def delete_context(self, session_id: str, agent_id: str, name: str) -> bool:
        """
        删除上下文数据
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            name: 上下文名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            contexts = self.get_collection('contexts')
            if not contexts:
                return False
                
            # 删除上下文
            result = contexts.delete_one({
                "sessionId": session_id,
                "agentId": agent_id,
                "name": name
            })
            
            return result.deleted_count > 0
        except PyMongoError as e:
            logger.error(f"删除上下文失败: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试MongoDB连接
        
        Returns:
            bool: 是否连接成功
        """
        try:
            if not self._client:
                self._init_connection()
                
            if self._client:
                self._client.admin.command('ping')
                return True
            return False
        except Exception as e:
            logger.error(f"MongoDB连接测试失败: {str(e)}")
            return False 