<template>
  <div class="app gradient-bg min-h-screen">
    <!-- 顶部导航 -->
    <nav class="glass py-4 px-6 mb-6 flex items-center justify-between">
      <div class="flex items-center">
        <i class="ri-palette-line text-primary text-2xl mr-2"></i>
        <span class="text-xl font-bold text-white">超维智能座舱数字全息系统 </span>
      </div>

      <div class="hidden md:flex space-x-6">
        <a href="#" class="text-gray-300 hover:text-white">首页</a>
        <a href="#" class="text-gray-300 hover:text-white">主题库</a>
        <a href="#" class="text-gray-300 hover:text-white">配置</a>
        <a href="#" class="text-gray-300 hover:text-white">关于</a>
      </div>
      <button class="btn btn-outline px-3 py-1.5" @click="$router.push('/demo-videos')">
        <i class="ri-moon-line mr-1"></i> 演示视频
      </button>
    </nav>

    <!-- 主要内容区 -->
    <main>
      <PageTransition>
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </PageTransition>
    </main>

    <!-- 页脚 -->
    <footer class="glass mt-10 py-6 px-6 text-center text-gray-400">
      <p>© 2025 主题生成系统 - 版权所有</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { PageTransition } from "./components";
import { useRouter } from "vue-router";

const router = useRouter();
</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Inter", "Arial", sans-serif;
  background-color: #0f172a;
  color: #e2e8f0;
  min-height: 100vh;
}

.app-container {
  width: 100%;
  min-height: 100vh;
}
</style> 