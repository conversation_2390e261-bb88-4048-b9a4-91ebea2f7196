from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import json
import traceback
from pathlib import Path
import httpx
import aiofiles
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    logger
)

router = APIRouter()
server_name="car_texture_server"
class CarTextureRequest(BaseModel):
    prompt: str
    task_id: str

def build_release_path(task_id: str) -> str:
    """构建发布路径（相对路径，用于32节点）"""
    return f"changan/mainLine_{task_id}/releases/car_texture/car_texture"

def build_car_texture_workflow(prompt: str, task_id: str) -> dict:
    """构建车衣纹理生成工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("cat_texture.json")
        
        # 设置提示词
        if "51" not in workflow:
            logger.error("工作流中找不到节点51")
            raise HTTPException(status_code=500, detail="Node 51 not found in workflow")
            
        workflow["51"]["inputs"]["prompt"] = f"{prompt}，画面居中展开，不要出现前面没有提到的元素，使用极简的背景"
        logger.info(f"设置车衣风格提示词: {prompt}")

        # 设置保存路径
        if "32" not in workflow:
            logger.error("工作流中找不到节点32")
            raise HTTPException(status_code=500, detail="Node 32 not found in workflow")
        
        release_path = build_release_path(task_id)
        workflow["32"]["inputs"]["filename_prefix"] = release_path
        logger.info(f"设置保存路径: {release_path}")
            
        return workflow, "32"  # 32是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建车衣工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/car-texture", tags=["car texture"])
async def generate_car_texture(request: CarTextureRequest):
    """SU7车衣生成接口
    
    根据用户输入的提示词，生成车辆纹理效果图。
    
    Args:
        request (CarTextureRequest): 包含prompt和task_id的请求体
        
    Returns:
        dict: 包含prompt_id和完整的图片URL
    """
    try:
        logger.info(f"\n========== 开始生成车衣纹理 ==========")
        logger.info(f"车衣风格提示词: {request.prompt}")
        logger.info(f"任务ID: {request.task_id}")

        if not request.prompt or not request.task_id:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 构建工作流
        workflow, output_node_id = build_car_texture_workflow(
            request.prompt,
            request.task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        subfolder = "car_texture"
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的车衣纹理URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{request.task_id}/releases/car_texture"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_path = save_dir / "car_texture.png"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                logger.info(f"车衣纹理已保存到: {image_path}")
            except Exception as e:
                logger.error(f"保存车衣纹理失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save car texture: {str(e)}")

        # 构建公开访问URL
        public_url = f"http://106.63.5.45:9077/ui_source/mainLine_{request.task_id}/releases/car_texture/car_texture.png"
        logger.info(f"公开访问URL: {public_url}")

        logger.info(f"========== 车衣纹理生成完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "image_url": public_url,
            "comfyui_url": image_url  # 添加ComfyUI原始URL
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理车衣纹理生成请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))
