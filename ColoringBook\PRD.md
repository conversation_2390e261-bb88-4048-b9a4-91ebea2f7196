### **魔法画册生成器 - 产品需求文档 (PRD)**

**版本:** 2.1
**日期:** 2025年7月15日
**作者:** Gemini

#### 1. 项目概述

**1.1. 项目愿景**
本项目旨在创造一个不仅仅是工具，更是一次奇妙旅程的“魔法画册生成器”。我们的目标是构建一个能唤起吉卜力工作室动画电影中那种手绘、有机和梦幻般美学的在线应用。用户将在这里感受到AI技术与手作质感魔法般的融合，将想象力转化为充满艺术感的涂色线稿，开启一段充满奇迹和创造力的体验。

**1.2. 目标用户**
*   **核心用户:** 热爱吉卜力美学、富有想象力和创造力的所有年龄段用户。
*   **扩展用户:** 寻找高质量、个性化涂色材料的家长、教师和艺术爱好者。

**1.3. 核心体验**
*   **沉浸感:** 从打开页面的第一刻起，用户就仿佛进入了一个吉卜力风格的幻想世界。
*   **魔法感:** AI图像生成的过程被包装成一种“魔法施咒”，而非冰冷的技术操作。
*   **手作质感:** 所有的UI元素和交互都旨在模拟传统、手工制作的温暖感觉。

---

#### 2. 视觉与交互设计 (核心)

**2.1. 核心美学：吉卜力之梦**
*   **灵感来源:** 深深植根于吉卜力工作室的艺术风格，追求其梦幻、自然、手绘和充满善意的世界观。
*   **关键词:** 水彩、手绘、有机、自然、魔法、温暖、怀旧。

**2.2. 配色方案：自然的调色盘**
*   **主色调:** 采用柔和的水彩风格配色，以唤起吉卜力风景的宁静感。
    *   **田园绿:** `#A3B899` (类似龙猫森林的颜色)
    *   **天空蓝:** `#B2C8D3` (清澈的天空)
    *   **大地色:** `#D4C2AD` (温暖的土壤和木材)
*   **强调色:** 提取自标志性电影，用于关键交互元素。
    *   **龙猫绿:** `#5B8E7D` (用于确认、生成等积极操作)
    *   **千与千寻红:** `#C75D5D` (用于删除、警告等需要注意的操作)
    *   **哈尔蓝:** `#5A7A9E` (用于选中、链接等状态)
*   **背景纹理:** 页面背景应模拟**水彩纸**或传统动画背景，带有微妙的颗粒感和不均匀的纹理。可以使用CSS渐变或无缝平铺的SVG/PNG图案实现。

**2.3. 界面组件：手作的温度**
*   **形状与边框:** 抛弃僵硬的直线和完美的几何形状。所有UI组件（按钮、卡片、输入框）都应具有**圆润、有机的形状**，边框带有轻微的、自然的不规则感，如同手绘勾勒。
*   **材质感:** 组件应感觉像是用天然材料（木头、纸张、布料）制成，而非数字化的塑料或玻璃。

**2.4. 排版：故事书的字体**
*   **标题/重要文本:** 采用一款具有手写风格、但仍清晰易读的字体（例如 Google Fonts 的 `ZCOOL KuaiLe` 或 `Ma Shan Zheng`）。
*   **正文/常规文本:** 使用一款干净、圆润的无衬线字体（例如 `Nunito` 或 `Quicksand`），确保可读性。

**2.5. 动画与过渡：魔法的轨迹**
*   **核心理念:** 动画应模仿手绘过程，感觉流畅、自然、充满生命力。
*   **具体应用:**
    *   **页面加载/过渡:** 模仿水彩在纸上绽放或云朵飘动的效果。
    *   **按钮悬停/点击:** 出现柔和的光晕或轻微的“呼吸”效果。
    *   **加载动画:** **一群可爱的煤灰精灵（或类似的小精灵）在屏幕上忙碌地“绘制”图像**，取代传统的旋转加载器。

---

#### 3. 产品功能与界面设计

**3.1. F1: 魔法卷轴 (提示词输入)**
*   **F1.1:** 提示词输入区域被设计成一个展开的**老旧卷轴**或**艺术家的画板**。当用户点击输入时，卷轴会优雅地展开。

**3.2. F2: 魔法道具 (参数配置)**
*   **F2.1 (比例):** 图片比例选项被设计成一组**古朴的木制或石制符文**，用户点击以选择。
*   **F2.2 (数量):** 数量选择器是一个带有复古刻度盘的旋钮。

**3.3. F3: 施展魔法 (生成与展示)**
*   **F3.1:** “生成图片”按钮是一个醒目的、镶嵌着“龙猫绿”宝石的魔法护符。
*   **F3.2:** 加载动画为一群忙碌的**煤灰精灵**。
*   **F3.3:** 生成的图片被展示在**类似动画赛璐珞或彩绘木框**的卡片中。画廊整体布局模仿**故事板**或**艺术家的作品集**，卡片排列略显随意，而非严格对齐的网格。

**3.4. F4 & F5: 画册整理 (管理与打印)**
*   **F4.1 (选择):** “选择模式”的开关是一个**萤火虫开关**，打开后会有柔和的光晕。
*   **F4.2 (打印):** “打印”按钮是一个盖着邮戳的信封图标。

**3.5. F6: 魔法源泉配置 (后台设置)**
*   **F6.1 (入口):** 在界面角落（例如右下角）添加一个**古朴的钥匙 🗝️** 图标作为设置入口。
*   **F6.2 (设置面板):** 点击钥匙后，从屏幕侧边或底部平滑地“拉出”一个**炼金术士的抽屉**作为设置面板。
*   **F6.3 (模式选择):** 面板内提供“魔法路径”选择，UI为两个可点击的**发光符文**（单选）。
    *   **选项A: 魔法阵模式 (ComfyUI):** 连接到自定义的魔法阵 (需要提供工作流)。
    *   **选项B: 契约模式 (API):** 与外部服务签订一个简单的魔法契约。（此为预留接口）
*   **F6.4 (魔法阵配置):** 当选择“魔法阵模式”时，显示以下选项：
    *   **魔法阵地址:** 风格化的输入框，用于填写ComfyUI服务器地址。
    *   **上传魔法卷轴:** 一个文件上传按钮，允许用户上传ComfyUI工作流 (Workflow JSON)。
*   **F6.5 (契约配置):** 当选择“契约模式”时，显示以下选项：
    *   **契约地址:** 风格化的输入框，用于填写API地址。
    *   **说明:** 文本提示“此为未来功能预留接口，敬请期待更多魔法的降临。”
*   **F6.6 (保存设置):** 一个**“封印设置”**按钮，设计成**蜡封图章**的样式。点击后，配置被保存，设置面板收回。
*   **F6.7 (设置面板草图):**

    ```
    .-----------------------------------------------------.
    |                  ~* 魔法源泉配置 *~                 |
    |-----------------------------------------------------|
    |                                                     |
    |  魔法路径:                                          |
    |    .----------------.      .----------------.       |
    |   ( (*) 魔法阵模式 )    (  ( ) 契约模式   )       |
    |    '----------------'      '----------------'       |
    |      (发光符文 F6.3)                                |
    |                                                     |
    |  魔法阵地址:                                        |
    |  .--------------------------------------------.     |
    |  | http://127.0.0.1:8188                |     |
    |  '--------------------------------------------'     |
    |                                                     |
    |  魔法卷轴:                                          |
    |  .--------------------------------------------.     |
    |  | [ 上传卷轴... ] (workflow.json)          |     |
    |  '--------------------------------------------'     |
    |                                                     |
    |                               .---------------.     |
    |                               | ( 蜡封印章 )  |     |
    |                               |  封印设置     |     |
    |                               '---------------'     |
    |                                  (F6.6)             |
    '-----------------------------------------------------'
    ```

**3.6. 界面概念草图 (ASCII Art)**

```
+--------------------------------------------------------------------------------+
|                           ~* 魔法画册生成器 *~                               |
| (背景: 水彩纸纹理)                                                             |
+--------------------------------------------------------------------------------+
|                                                                                |
|   /~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\   |
|  /     [ 魔法卷轴: 一只飞翔在云端的龙猫...                       ] (F1)      \  |
| /________________________________________________________________________\ |
|                                                                                |
| [木制符文]  ( 1:1 )  [ 4:3 ]  [ 3:4 ] ... (F2.1)                               |
|                                                                                |
| [复古旋钮] ( 1 v ) (F2.2)                                                      |
|                                                                                |
|                               +-----------------+                              |
|                               |   (施展魔法 ✨)   | (F3.1)                         |
|                               +-----------------+                              |
|                                                                                |
|--------------------------------------------------------------------------------|
| (加载时: 屏幕中央出现一群工作的煤灰精灵 F3.2)                                  |
|--------------------------------------------------------------------------------|
|                                                                                |
| [萤火虫开关: (o) 选择] (F4.1)      [信封: 🖨️ 打印] (F4.2)      [钥匙: 🗝️] (F6.1) |
|                                                                                |
|   .-----------------.      .-----------------.      .-----------------.        |
|  (  /----\         )    (  /----\         )    (  /----\         )       |
| (   |      |        )  (   |      |        )  (   |      |        )      |
| (   | 图片 |        )  (   | 图片 |        )  (   | 图片 |        )      |
| (   |      |        )  (   |      |        )  (   |      |        )      |
|  (  \----/         )    (  \----/         )    (  \----/         )       |
|   '-----------------'      '-----------------'      '-----------------'        |
|    (赛璐珞画框 F3.3)                                                           |
|                                                                                |
+--------------------------------------------------------------------------------+
```

#### 4. 技术实现概要

*   **前端:**
    *   **HTML/CSS:** 结构将保持简洁，但CSS会变得非常关键。大量使用 `border-radius`, `box-shadow`, `transform` 和 `filter` 来创造有机形状和手绘感。背景纹理将通过CSS实现。
    *   **字体:** 从 Google Fonts 引入指定的 `ZCOOL KuaiLe` (标题) 和 `Nunito` (正文) 字体。
    *   **动画:** 主要使用CSS的 `transition` 和 `animation`。复杂的加载动画（煤灰精灵）可能需要使用SVG动画或一个轻量级的JavaScript动画库。
    *   **JavaScript:**
        *   逻辑保持不变，但需要配合新的CSS类来实现视觉状态的切换。
        *   **新增:** 使用 `localStorage` 在用户本地持久化保存“魔法源泉”的配置（所选模式、地址、JSON内容等），以便下次访问时自动加载。
*   **后端:** 后端逻辑需要变得灵活，能够根据前端传递的配置（是ComfyUI还是API）来调用不同的服务。
    *   **模式一 (ComfyUI):** 通过`theme_backend`中的新接口，调用指定的`lineart.json`工作流来生成图片。
    *   **模式二 (API):** 预留调用外部API的接口。

#### 5. 在线涂鸦/填色功能
*此部分的功能逻辑保持不变，但其UI（工具栏、按钮等）也需要全面遵循以上定义的吉卜力视觉风格。*