{"5": {"inputs": {"width": 1920, "height": 1080, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent"}}, "6": {"inputs": {"text": ["39", 0], "clip": ["11", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "9": {"inputs": {"filename_prefix": "changan/mainLine_111/releases/wallpaper/wallpaper", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "10": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "11": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["5", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器(高级)"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "normal", "steps": 8, "denoise": 1, "model": ["30", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基础调度器"}}, "22": {"inputs": {"model": ["30", 0], "conditioning": ["37", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基础引导"}}, "25": {"inputs": {"noise_seed": 893455665019559}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "30": {"inputs": {"unet_name": "shuttle31Aesthetic_v10.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "35": {"inputs": {"prompt": "扁平风格,西湖风光,扁平插画,中国风", "model_name": "Qwen2.5-32B-Instruct-GPTQ-Int4", "base_url": "http://106.63.100.60:7868/v1", "api_key": "1", "is_enable": true, "seed": 1024}, "class_type": "mini_flux_prompt", "_meta": {"title": "☁️Mini FLUX Prompt Generator"}}, "37": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "39": {"inputs": {"text_1": ["35", 0], "text_2": "", "text_3": "", "text_4": ""}, "class_type": "LayerUtility: Text<PERSON>oin", "_meta": {"title": "文本合并"}}}