<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D车辆展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                            600: '#475569',
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            overflow: auto;
            background-color: #0f172a;
            background-image: 
                radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
                radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
                radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-attachment: fixed;
            color: white;
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        
        canvas { 
            width: 100%;
            height: 100%;
            display: block;
        }
        
        @keyframes pulse-animation {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .animate-pulse-custom {
            animation: pulse-animation 2s infinite ease-in-out;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        .feature-card {
            transition: all 0.4s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
        }

        .bg-gradient-tech {
            background: linear-gradient(125deg, #0ea5e9, #7c3aed, #3b82f6);
            background-size: 200% 200%;
            animation: gradientBG 15s ease infinite;
        }
        
        @keyframes gradientBG {
            0% {background-position: 0% 50%;}
            50% {background-position: 100% 50%;}
            100% {background-position: 0% 50%;}
        }
    </style>
</head>
<body class="dark">

<div class="fixed top-0 left-0 w-full z-10">
    <div class="glass-card py-5 px-10 flex justify-between items-center">
        <div class="flex items-center">
            <div class="h-12 w-12 rounded-full bg-gradient-tech flex items-center justify-center float-animation">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                    <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h2.05a2.5 2.5 0 014.9 0H19a1 1 0 001-1v-4a1 1 0 00-1-1h-8a1 1 0 00-.8.4L8.25 8 6.2 4.4A1 1 0 005.4 4H3z" />
                </svg>
            </div>
            <h1 class="ml-4 text-3xl font-bold text-white">3D车辆展示系统</h1>
        </div>
        <div class="flex items-center">
            <button class="glass-card px-5 py-3 rounded-lg mr-4 hover:bg-purple-700/30 transition-all text-lg">
                查看说明
            </button>
            <button class="glass-card px-5 py-3 rounded-lg hover:bg-purple-700/30 transition-all text-lg">
                更多模型
            </button>
        </div>
    </div>
</div>

<!-- 3D车辆容器 -->
<div id="car-container" class="w-full h-screen"></div>

<!-- 控制面板 -->
<div class="fixed bottom-10 left-1/2 transform -translate-x-1/2 z-10">
    <div class="glass-card rounded-xl p-6 w-[800px]">
        <h2 class="text-2xl font-bold text-white mb-6">车辆控制面板</h2>
        
        <div class="grid grid-cols-2 gap-8">
            <!-- 左侧控制 -->
            <div class="space-y-6">
                <div>
                    <label class="block text-gray-300 text-lg mb-3">旋转速度</label>
                    <div class="flex items-center">
                        <div class="flex-grow relative">
                            <div class="h-3 bg-dark-700 rounded-full">
                                <div id="rotation-speed-bar" class="h-full w-1/2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                            </div>
                            <div id="rotation-speed-handle" class="absolute h-6 w-6 bg-white rounded-full -top-1.5 border-2 border-purple-600 left-1/2 transform -translate-x-1/2 shadow cursor-pointer"></div>
                        </div>
                        <span id="rotation-speed-value" class="text-base text-white ml-4 min-w-[60px] text-center">50%</span>
                    </div>
                </div>
                
                <div>
                    <label class="block text-gray-300 text-lg mb-3">粒子数量</label>
                    <div class="flex items-center">
                        <div class="flex-grow relative">
                            <div class="h-3 bg-dark-700 rounded-full">
                                <div id="particle-count-bar" class="h-full w-3/4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                            </div>
                            <div id="particle-count-handle" class="absolute h-6 w-6 bg-white rounded-full -top-1.5 border-2 border-purple-600 left-3/4 transform -translate-x-1/2 shadow cursor-pointer"></div>
                        </div>
                        <span id="particle-count-value" class="text-base text-white ml-4 min-w-[60px] text-center">1500</span>
                    </div>
                </div>
                
                <div>
                    <label class="block text-gray-300 text-lg mb-3">车辆颜色</label>
                    <div class="flex space-x-3">
                        <div data-color="#ef4444" class="h-10 w-10 rounded-lg bg-red-500 cursor-pointer ring-2 ring-transparent hover:ring-white"></div>
                        <div data-color="#3b82f6" class="h-10 w-10 rounded-lg bg-blue-500 cursor-pointer ring-2 ring-transparent hover:ring-white"></div>
                        <div data-color="#10b981" class="h-10 w-10 rounded-lg bg-green-500 cursor-pointer ring-2 ring-transparent hover:ring-white"></div>
                        <div data-color="#8b5cf6" class="h-10 w-10 rounded-lg bg-purple-500 cursor-pointer ring-2 ring-white"></div>
                        <div data-color="#f59e0b" class="h-10 w-10 rounded-lg bg-yellow-500 cursor-pointer ring-2 ring-transparent hover:ring-white"></div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧控制 -->
            <div class="space-y-6">
                <div>
                    <label class="block text-gray-300 text-lg mb-3">特效类型</label>
                    <div class="grid grid-cols-3 gap-3">
                        <div data-effect="particles" class="py-2 px-3 bg-purple-600/30 text-purple-300 rounded-lg text-center text-sm cursor-pointer ring-2 ring-purple-500">粒子漂浮</div>
                        <div data-effect="trail" class="py-2 px-3 bg-dark-700/50 text-gray-300 rounded-lg text-center text-sm cursor-pointer hover:bg-purple-600/30 hover:text-purple-300">尾迹效果</div>
                        <div data-effect="explosion" class="py-2 px-3 bg-dark-700/50 text-gray-300 rounded-lg text-center text-sm cursor-pointer hover:bg-purple-600/30 hover:text-purple-300">爆炸粒子</div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-gray-300 text-lg mb-3">背景样式</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div data-background="space" class="py-2 px-3 bg-purple-600/30 text-purple-300 rounded-lg text-center text-sm cursor-pointer ring-2 ring-purple-500">太空背景</div>
                        <div data-background="matrix" class="py-2 px-3 bg-dark-700/50 text-gray-300 rounded-lg text-center text-sm cursor-pointer hover:bg-purple-600/30 hover:text-purple-300">矩阵风格</div>
                    </div>
                </div>
                
                <div class="pt-4">
                    <button id="reset-button" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 rounded-lg transition duration-300 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                        </svg>
                        重置设置
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Three.js库 -->
<script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>

<script>
    // 场景初始化
    let scene, camera, renderer, car, particles, particleSystem;
    let rotationSpeed = 0.005;
    let particleCount = 1500;
    let carColor = '#8b5cf6'; // 默认紫色
    let effectType = 'particles';
    let backgroundType = 'space';
    
    // 初始化场景
    function init() {
        // 创建场景
        scene = new THREE.Scene();
        
        // 创建相机
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.z = 5;
        camera.position.y = 2;
        
        // 创建渲染器
        const container = document.getElementById('car-container');
        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.shadowMap.enabled = true;
        container.appendChild(renderer.domElement);
        
        // 添加轨道控制
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        
        // 添加环境光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        
        // 添加方向光（类似阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);
        
        // 加载模型 - 使用一个简单的立方体代替（实际使用时替换为GLTF模型）
        loadCar();
        
        // 创建粒子系统
        createParticleSystem();
        
        // 添加窗口调整大小事件
        window.addEventListener('resize', onWindowResize, false);
        
        // 控制元素初始化
        initControls();
        
        // 开始动画循环
        animate();
    }
    
    // 加载车辆模型
    function loadCar() {
        // 注意：在实际场景中，使用以下代码加载真实的GLTF模型
        // const loader = new THREE.GLTFLoader();
        // loader.load('path/to/car.glb', function(gltf) {
        //     car = gltf.scene;
        //     car.scale.set(0.5, 0.5, 0.5);
        //     scene.add(car);
        // });
        
        // 临时使用立方体代替车模型
        const geometry = new THREE.BoxGeometry(1, 0.5, 2);
        const material = new THREE.MeshPhongMaterial({ 
            color: carColor,
            shininess: 100
        });
        car = new THREE.Mesh(geometry, material);
        car.castShadow = true;
        car.receiveShadow = true;
        
        // 添加车轮
        const wheelGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.1, 32);
        wheelGeometry.rotateZ(Math.PI/2);
        const wheelMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
        
        const wheel1 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel1.position.set(0.5, -0.3, 0.7);
        car.add(wheel1);
        
        const wheel2 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel2.position.set(-0.5, -0.3, 0.7);
        car.add(wheel2);
        
        const wheel3 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel3.position.set(0.5, -0.3, -0.7);
        car.add(wheel3);
        
        const wheel4 = new THREE.Mesh(wheelGeometry, wheelMaterial);
        wheel4.position.set(-0.5, -0.3, -0.7);
        car.add(wheel4);
        
        // 添加车窗
        const windshieldGeometry = new THREE.PlaneGeometry(0.9, 0.4);
        const glassMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x88ccff, 
            transparent: true,
            opacity: 0.7,
            shininess: 100
        });
        
        const windshield = new THREE.Mesh(windshieldGeometry, glassMaterial);
        windshield.position.set(0, 0.2, 0.5);
        windshield.rotation.x = -Math.PI / 4;
        car.add(windshield);
        
        scene.add(car);
    }
    
    // 创建粒子系统
    function createParticleSystem() {
        if (particleSystem) {
            scene.remove(particleSystem);
        }
        
        const geometry = new THREE.BufferGeometry();
        const vertices = [];
        const sizes = [];
        const colors = [];
        
        const color1 = new THREE.Color(0x8b5cf6); // 紫色
        const color2 = new THREE.Color(0x3b82f6); // 蓝色
        
        for (let i = 0; i < particleCount; i++) {
            // 生成围绕车辆的随机粒子位置
            const x = (Math.random() - 0.5) * 10;
            const y = (Math.random() - 0.5) * 10;
            const z = (Math.random() - 0.5) * 10;
            
            vertices.push(x, y, z);
            
            // 随机粒子大小
            sizes.push(Math.random() * 0.05 + 0.01);
            
            // 混合两种颜色
            const mixedColor = color1.clone().lerp(color2, Math.random());
            colors.push(mixedColor.r, mixedColor.g, mixedColor.b);
        }
        
        geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        geometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        
        // 自定义着色器材质
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.7
        });
        
        particleSystem = new THREE.Points(geometry, particleMaterial);
        scene.add(particleSystem);
    }
    
    // 窗口大小调整
    function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    // 动画循环
    function animate() {
        requestAnimationFrame(animate);
        
        // 旋转车辆
        if (car) {
            car.rotation.y += rotationSpeed;
        }
        
        // 旋转粒子系统
        if (particleSystem) {
            particleSystem.rotation.y += rotationSpeed * 0.2;
            
            // 粒子漂浮动效
            const positions = particleSystem.geometry.attributes.position.array;
            for (let i = 0; i < positions.length; i += 3) {
                positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * 0.001;
            }
            particleSystem.geometry.attributes.position.needsUpdate = true;
        }
        
        renderer.render(scene, camera);
    }
    
    // 初始化控制面板功能
    function initControls() {
        // 旋转速度滑块
        const rotationSpeedHandle = document.getElementById('rotation-speed-handle');
        const rotationSpeedBar = document.getElementById('rotation-speed-bar');
        const rotationSpeedValue = document.getElementById('rotation-speed-value');
        
        rotationSpeedHandle.addEventListener('mousedown', function(e) {
            document.addEventListener('mousemove', moveRotationSpeed);
            document.addEventListener('mouseup', function() {
                document.removeEventListener('mousemove', moveRotationSpeed);
            });
        });
        
        function moveRotationSpeed(e) {
            const parent = rotationSpeedBar.parentElement;
            const rect = parent.getBoundingClientRect();
            let percent = (e.clientX - rect.left) / rect.width;
            percent = Math.max(0, Math.min(1, percent));
            
            rotationSpeedBar.style.width = (percent * 100) + '%';
            rotationSpeedHandle.style.left = (percent * 100) + '%';
            rotationSpeedValue.textContent = Math.round(percent * 100) + '%';
            
            // 更新旋转速度
            rotationSpeed = percent * 0.01;
        }
        
        // 粒子数量滑块
        const particleCountHandle = document.getElementById('particle-count-handle');
        const particleCountBar = document.getElementById('particle-count-bar');
        const particleCountValue = document.getElementById('particle-count-value');
        
        particleCountHandle.addEventListener('mousedown', function(e) {
            document.addEventListener('mousemove', moveParticleCount);
            document.addEventListener('mouseup', function() {
                document.removeEventListener('mousemove', moveParticleCount);
            });
        });
        
        function moveParticleCount(e) {
            const parent = particleCountBar.parentElement;
            const rect = parent.getBoundingClientRect();
            let percent = (e.clientX - rect.left) / rect.width;
            percent = Math.max(0, Math.min(1, percent));
            
            particleCountBar.style.width = (percent * 100) + '%';
            particleCountHandle.style.left = (percent * 100) + '%';
            
            // 更新粒子数量 (范围：100-3000)
            particleCount = Math.round(100 + percent * 2900);
            particleCountValue.textContent = particleCount;
            
            // 重新创建粒子系统
            createParticleSystem();
        }
        
        // 车辆颜色选择
        const colorButtons = document.querySelectorAll('[data-color]');
        colorButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除其他按钮的选中状态
                colorButtons.forEach(btn => btn.classList.remove('ring-white'));
                
                // 添加当前按钮的选中状态
                this.classList.add('ring-white');
                
                // 更新车辆颜色
                carColor = this.dataset.color;
                if (car) {
                    car.material.color.set(carColor);
                }
            });
        });
        
        // 特效类型选择
        const effectButtons = document.querySelectorAll('[data-effect]');
        effectButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除其他按钮的选中状态
                effectButtons.forEach(btn => {
                    btn.classList.remove('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                    btn.classList.add('bg-dark-700/50', 'text-gray-300');
                });
                
                // 添加当前按钮的选中状态
                this.classList.remove('bg-dark-700/50', 'text-gray-300');
                this.classList.add('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                
                // 更新特效类型
                effectType = this.dataset.effect;
                
                // 暂未实现不同特效类型的切换逻辑
                // 实际项目中可以在这里实现不同粒子效果的切换
            });
        });
        
        // 背景样式选择
        const backgroundButtons = document.querySelectorAll('[data-background]');
        backgroundButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除其他按钮的选中状态
                backgroundButtons.forEach(btn => {
                    btn.classList.remove('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                    btn.classList.add('bg-dark-700/50', 'text-gray-300');
                });
                
                // 添加当前按钮的选中状态
                this.classList.remove('bg-dark-700/50', 'text-gray-300');
                this.classList.add('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                
                // 更新背景类型
                backgroundType = this.dataset.background;
                
                // 暂未实现不同背景类型的切换逻辑
                // 实际项目中可以在这里实现不同背景效果的切换
            });
        });
        
        // 重置按钮
        document.getElementById('reset-button').addEventListener('click', function() {
            // 重置旋转速度
            rotationSpeed = 0.005;
            rotationSpeedBar.style.width = '50%';
            rotationSpeedHandle.style.left = '50%';
            rotationSpeedValue.textContent = '50%';
            
            // 重置粒子数量
            particleCount = 1500;
            particleCountBar.style.width = '75%';
            particleCountHandle.style.left = '75%';
            particleCountValue.textContent = '1500';
            
            // 重置车辆颜色
            carColor = '#8b5cf6';
            colorButtons.forEach(btn => {
                btn.classList.remove('ring-white');
                if (btn.dataset.color === '#8b5cf6') {
                    btn.classList.add('ring-white');
                }
            });
            
            if (car) {
                car.material.color.set(carColor);
            }
            
            // 重置特效类型
            effectType = 'particles';
            effectButtons.forEach(btn => {
                btn.classList.remove('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                btn.classList.add('bg-dark-700/50', 'text-gray-300');
                
                if (btn.dataset.effect === 'particles') {
                    btn.classList.remove('bg-dark-700/50', 'text-gray-300');
                    btn.classList.add('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                }
            });
            
            // 重置背景类型
            backgroundType = 'space';
            backgroundButtons.forEach(btn => {
                btn.classList.remove('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                btn.classList.add('bg-dark-700/50', 'text-gray-300');
                
                if (btn.dataset.background === 'space') {
                    btn.classList.remove('bg-dark-700/50', 'text-gray-300');
                    btn.classList.add('ring-2', 'ring-purple-500', 'bg-purple-600/30', 'text-purple-300');
                }
            });
            
            // 重新创建粒子系统
            createParticleSystem();
        });
    }
    
    // 初始化场景
    init();
</script>

</body>
</html>
