from fastapi import APIRouter, HTTPException, BackgroundTasks, Path, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import asyncio
from enum import Enum
import time
import uuid
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
from .text_to_image import generate_image, TextToImageRequest
from .ui_generation import generate_ui_path
from .ui_split import split_ui_path
from .icon_refine import refine_icon
from .common import logger
from ..core.config import settings
from ..core.redis_client import RedisClient
from ..core.mysql_util import connect_PBDB,update_data,insert_data_and_get_id
from ..core.minio_util import minioGet, minioGetPresigned_url, list_files
from datetime import datetime

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SUCCESS = "success"
    TERMINATED = "terminated"  # 添加终止状态

class PipelineRequest(BaseModel):
    prompt: str
    task_id: str  # 添加task_id字段
    style: str

@dataclass
class SubTask:
    name: str                      # 任务名称
    weight: float                  # 任务权重（进度占比）
    parallel: bool = False         # 是否可以并行执行
    max_workers: int = 1          # 并行任务数
    depends_on: List[str] = None  # 依赖的任务

class PipelineManager:
    def __init__(self):
        # 初始化时不再需要本地变量存储
        # 测试Redis连接
        if not RedisClient.test_connection():
            logger.error("Redis连接测试失败，将无法使用状态管理功能!")
        # 存储运行中的任务
        self.running_tasks = {}
        # 存储任务及其子任务之间的关系 {task_id: {subtask_id1, subtask_id2, ...}}
        self.related_tasks = {}
        # 存储所有运行中的子任务 {subtask_id: task}
        self.running_subtasks = {}
        
    def register_task(self, task_id: str, subtasks: List[SubTask]):
        """注册任务及其子任务"""
        # 构建任务信息
        task_info = {
            "status": TaskStatus.PENDING.value,
            "progress": 0,
            "current_step": subtasks[0].name if subtasks else None,
            "start_time": time.time(),
            "error": None,
            "result_urls": [],
            "subtasks": {}
        }
        
        total_weight = sum(task.weight for task in subtasks)
        for task in subtasks:
            task_info["subtasks"][task.name] = {
                "status": TaskStatus.PENDING.value,
                "progress": 0,
                "weight": task.weight / total_weight,
                "parallel": task.parallel,
                "max_workers": task.max_workers,
                "depends_on": task.depends_on or [],
                "result_url": None
            }
        
        # 存储到Redis
        success = RedisClient.set_task_info(task_id, task_info)
        if not success:
            logger.error(f"无法注册任务到Redis: {task_id}")
            
        # 初始化关联任务集合
        self.related_tasks[task_id] = set()
        
        # 注册当前进程
        try:
            from ..core.process_manager import register_current_process
            register_current_process(task_id)
            logger.info(f"已注册当前进程到任务 {task_id}")
        except Exception as e:
            logger.error(f"注册进程失败: {str(e)}")
    
    def register_running_task(self, task_id: str, task):
        """注册一个运行中的异步任务"""
        self.running_tasks[task_id] = task
        logger.info(f"注册运行中任务: {task_id}")
    
    def register_subtask(self, main_task_id: str, subtask_id: str, task):
        """
        注册一个运行中的子任务，并将其与主任务关联
        
        Args:
            main_task_id: 主任务ID
            subtask_id: 子任务ID (生成的唯一标识)
            task: 异步任务对象
        """
        # 存储子任务
        self.running_subtasks[subtask_id] = task
        
        # 关联到主任务
        if main_task_id in self.related_tasks:
            self.related_tasks[main_task_id].add(subtask_id)
            logger.info(f"已关联子任务 {subtask_id} 到主任务 {main_task_id}")
        else:
            # 如果主任务不存在，创建关联集合
            self.related_tasks[main_task_id] = {subtask_id}
            logger.info(f"创建主任务 {main_task_id} 关联集合并添加子任务 {subtask_id}")
    
    def cancel_running_task(self, task_id: str):
        """取消运行中的异步任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            if not task.done() and not task.cancelled():
                logger.info(f"正在取消任务: {task_id}")
                task.cancel()
                return True
            else:
                logger.info(f"任务 {task_id} 已完成或已取消")
        else:
            logger.warning(f"未找到运行中的任务: {task_id}")
        return False
    
    async def cancel_all_related_tasks(self, task_id: str):
        """
        取消与主任务关联的所有子任务
        
        Args:
            task_id: 主任务ID
            
        Returns:
            int: 取消的任务数量
        """
        cancelled_count = 0
        
        # 取消关联的子任务
        if task_id in self.related_tasks:
            subtask_ids = list(self.related_tasks[task_id])
            for subtask_id in subtask_ids:
                if subtask_id in self.running_subtasks:
                    subtask = self.running_subtasks[subtask_id]
                    if not subtask.done() and not subtask.cancelled():
                        logger.info(f"正在取消子任务: {subtask_id} (属于主任务 {task_id})")
                        subtask.cancel()
                        cancelled_count += 1
                    
                    # 移除子任务
                    del self.running_subtasks[subtask_id]
            
            # 清空关联集合
            self.related_tasks[task_id].clear()
            logger.info(f"已取消主任务 {task_id} 的 {cancelled_count} 个子任务")
        
        return cancelled_count
    
    def check_task_cancellation(self, task_id: str):
        """
        检查任务是否被取消/终止
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 如果任务被取消或终止，返回True
        """
        task_info = self.get_task_info(task_id)
        if not task_info:
            return False
        
        status = task_info.get("status")
        return status == TaskStatus.TERMINATED.value

    def update_subtask_progress(self, task_id: str, subtask_name: str, progress: float, result_url: str = None):
        """更新子任务进度和结果URL"""
        # 从Redis获取任务信息
        task_info = RedisClient.get_task_info(task_id)
        if not task_info or "subtasks" not in task_info or subtask_name not in task_info["subtasks"]:
            logger.error(f"找不到任务信息或子任务: {task_id}, {subtask_name}")
            return
            
        # 更新子任务信息
        subtask = task_info["subtasks"][subtask_name]
        old_progress = subtask["progress"]
        subtask["progress"] = progress
        logger.info(f"子任务进度更新: {subtask_name} - {old_progress:.2f}% -> {progress:.2f}%")
        
        if result_url:
            subtask["result_url"] = result_url
            if isinstance(result_url, list):
                task_info["result_urls"].extend(result_url)
                logger.info(f"添加多个结果URL: {len(result_url)} 个")
            else:
                task_info["result_urls"].append(result_url)
                logger.info(f"添加结果URL: {result_url}")
        
        # 更新总进度
        old_total_progress = task_info["progress"]
        total_progress = sum(
            st["progress"] * st["weight"] 
            for st in task_info["subtasks"].values()
        )
        task_info["progress"] = round(total_progress, 2)
        logger.info(f"总进度更新: {old_total_progress:.2f}% -> {task_info['progress']:.2f}%")

        # 更新当前执行的步骤
        current_step = None
        for name, info in task_info["subtasks"].items():
            if info["progress"] < 100:
                current_step = name
                break
        
        if current_step:
            old_step = task_info.get("current_step")
            task_info["current_step"] = current_step
            if old_step != current_step:
                logger.info(f"当前步骤更新: {old_step} -> {current_step}")

        # 只有在所有任务完成且当前状态不是FAILED时才更新状态为COMPLETED
        all_completed = all(
            st["progress"] == 100 
            for st in task_info["subtasks"].values()
        )
        
        if all_completed and task_info["status"] not in [TaskStatus.FAILED.value, TaskStatus.TERMINATED.value]:
            old_status = task_info["status"]
            task_info["status"] = TaskStatus.COMPLETED.value
            logger.info(f"任务状态更新: {old_status} -> {TaskStatus.COMPLETED.value}")

        connection = None
        try:
            connection = connect_PBDB()
            logger.error(f"更新任务信息到数据库 ")
            logger.error(task_info["status"])
            logger.error(task_info["progress"])
            logger.error(task_info["current_step"])
            logger.error(task_info["result_urls"])
            logger.error(task_id)
            update_data(connection,'theme_task_log',
                        {'task_status':task_info["status"],
                         'progress':task_info["progress"],
                         'current_step':task_info["current_step"],
                         'result_urls':str(task_info["result_urls"]),
                         'update_time':datetime.now()},"task_id='{}'".format(task_id))

        except Exception as e:
            logger.error(f"更新任务信息到数据库失败: {str(e)}")
        finally:
            if connection:
                connection.close()

        # 将更新后的信息保存回Redis
        success = RedisClient.update_task_info(task_id, task_info)
        if not success:
            logger.error(f"更新任务信息到Redis失败: {task_id}")
        else:
            logger.info(f"成功更新任务信息到Redis: {task_id}")
            
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        return RedisClient.get_task_info(task_id)
    
    def set_task_status(self, task_id: str, status: TaskStatus, error: str = None):
        """设置任务状态"""
        task_info = RedisClient.get_task_info(task_id)
        if not task_info:
            logger.error(f"找不到任务信息: {task_id}")
            return False
            
        updates = {"status": status.value}
        if error:
            updates["error"] = error
            
        return RedisClient.update_task_info(task_id, updates)
        
    def task_exists(self, task_id: str) -> bool:
        """检查任务是否存在"""
        return RedisClient.get_task_info(task_id) is not None

    async def execute_parallel_subtask(self, task_id: str, subtask_name: str, func, total_items: int, style: str, *args):
        """执行并行子任务"""
        task_info = RedisClient.get_task_info(task_id)
        if not task_info or "subtasks" not in task_info or subtask_name not in task_info["subtasks"]:
            logger.error(f"找不到任务信息或子任务: {task_id}, {subtask_name}")
            return
            
        subtask = task_info["subtasks"][subtask_name]
        tasks = []
        
        async def process_item(index: int):
            try:
                # 设置10分钟超时
                result = await asyncio.wait_for(
                    func(style, index, task_id),
                    timeout=600  # 10分钟 = 600秒
                )
                progress = ((index + 1) / total_items) * 100
                self.update_subtask_progress(
                    task_id, 
                    subtask_name, 
                    progress,
                    result.get("image_url")
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"处理项目 {index} 超时（超过10分钟）")
                raise HTTPException(status_code=500, detail=f"Processing item {index} timed out after 10 minutes")
            except Exception as e:
                logger.error(f"处理项目 {index} 时出错: {str(e)}")
                raise

        # 创建所有任务
        for i in range(total_items):
            tasks.append(asyncio.create_task(process_item(i)))
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

router = APIRouter()
pipeline_manager = PipelineManager()

def get_subtasks() -> List[SubTask]:
    """获取子任务列表"""
    return [
        SubTask("wallpaper_generation", weight=20),
        SubTask("ui_generation", weight=15, depends_on=["wallpaper_generation"]),
        SubTask("ui_split", weight=15, depends_on=["ui_generation"]),
        SubTask(
            "parallel_tasks", 
            weight=30,  # 调整回原来的权重
            parallel=True,
            max_workers=settings.MAX_PARALLEL_WORKERS + 1,
            depends_on=["ui_split"]
        ),
        # SubTask(
        #     "color_extraction",
        #     weight=5,
        #     depends_on=["parallel_tasks"]
        # ),
        SubTask(
            "convert_to_jpg",
            weight=10,
            depends_on=["parallel_tasks"]
        ),
        SubTask(
            "create_archive",
            weight=5,
            depends_on=["convert_to_jpg"]
        )
    ]

async def execute_parallel_tasks(task_id: str, subtask_name: str, style: str):
    """执行并行任务组"""
    try:
        # 检查任务是否已经被终止
        if pipeline_manager.check_task_cancellation(task_id):
            logger.info(f"任务 {task_id} 已被终止，不执行并行任务")
            return False
        
        # 创建任务列表
        tasks = []
        failed_tasks = []
        success_tasks = []

        connection = None
        try:
            connection = connect_PBDB()
            update_data(connection,'theme_task_log',
                    {'refine_style':style},"task_id='{}'".format(task_id))
        except Exception as e:
            logger.error(f"更新风格信息到数据库失败: {str(e)}")
        finally:
            if connection:
                connection.close()

        # 添加35个图标细化任务
        for i in range(15):
            # 生成子任务ID
            subtask_id = f"{task_id}_icon_{i}"
            
            # 创建异步任务
            task = asyncio.create_task(
                asyncio.wait_for(
                    refine_icon(style, i, task_id),
                    timeout=1200  # 20分钟超时
                )
            )
            
            # 注册子任务
            pipeline_manager.register_subtask(task_id, subtask_id, task)
            
            # 添加到任务列表
            tasks.append(task)
            logger.info(f"已创建并注册图标细化子任务 {subtask_id}")
        
        # 添加内容生成任务
        # from .content import generate_content, ContentRequest
        # content_subtask_id = f"{task_id}_content"
        # content_task = asyncio.create_task(
        #     asyncio.wait_for(
        #         generate_content(ContentRequest(task_id=task_id)),
        #         timeout=1200  # 20分钟超时
        #     )
        # )
        #
        # # 注册内容生成子任务
        # pipeline_manager.register_subtask(task_id, content_subtask_id, content_task)
        # tasks.append(content_task)
        # logger.info(f"已创建并注册内容生成子任务 {content_subtask_id}")
        
        total_tasks = len(tasks)  # 应该是36个任务
        completed_tasks = 0
        
        # 使用as_completed来处理任务，这样可以在每个任务完成时立即更新进度
        pending = set(tasks)
        termination_check_counter = 0
        
        while pending:
            # 每5个任务检查一次任务是否被终止
            termination_check_counter += 1
            if termination_check_counter >= 5:
                termination_check_counter = 0
                if pipeline_manager.check_task_cancellation(task_id):
                    logger.info(f"任务 {task_id} 已被终止，取消所有剩余的并行子任务")
                    for task in pending:
                        task.cancel()
                    break
            
            # 等待下一个完成的任务
            done, pending = await asyncio.wait(
                pending, 
                return_when=asyncio.FIRST_COMPLETED,
                timeout=10  # 添加10秒超时，以便定期检查任务是否被终止
            )
            
            # 如果超时但没有任务完成，继续下一轮循环
            if not done:
                logger.debug("并行任务等待超时，继续检查")
                continue
            
            # 处理完成的任务
            for task in done:
                try:
                    task_result = task.result()
                    if task_result:  # 确保任务结果有效
                        completed_tasks += 1
                        success_tasks.append(task_result)
                    else:
                        failed_tasks.append("Task returned no result")
                        completed_tasks += 1
                except asyncio.TimeoutError:
                    logger.error("任务执行超时（超过20分钟）")
                    failed_tasks.append("Task timed out after 20 minutes")
                    completed_tasks += 1
                except asyncio.CancelledError:
                    logger.info("任务被取消")
                    failed_tasks.append("Task was cancelled")
                    completed_tasks += 1
                except Exception as e:
                    logger.error(f"并行任务执行出错: {str(e)}")
                    failed_tasks.append(str(e))
                    completed_tasks += 1
                
                # 每完成一个任务就更新进度
                current_progress = (completed_tasks / total_tasks) * 100
                logger.info(f"并行任务进度更新: {completed_tasks}/{total_tasks} = {current_progress:.2f}%")
                
                # 检查任务是否被终止
                if pipeline_manager.check_task_cancellation(task_id):
                    logger.info(f"任务 {task_id} 已被终止，不再更新进度")
                    break
                
                # 更新Redis中的进度信息
                pipeline_manager.update_subtask_progress(
                    task_id,
                    subtask_name,
                    current_progress,
                    task_result.get("image_url") if task_result else None
                )
        
        # 取消所有未完成的任务
        for task in pending:
            task.cancel()
            failed_tasks.append("Task was cancelled")
        
        # 再次检查任务是否被终止
        if pipeline_manager.check_task_cancellation(task_id):
            logger.info(f"任务 {task_id} 已被终止，不更新最终进度")
            return False
        
        # 更新最终进度
        pipeline_manager.update_subtask_progress(
            task_id,
            subtask_name,
            100  # 无论成功失败，进度都设为100%以继续下一步
        )
        
        # 记录错误信息但继续执行
        if failed_tasks:
            error_msg = f"Some tasks failed: {'; '.join(failed_tasks[:5])}" + (f" (and {len(failed_tasks)-5} more)" if len(failed_tasks) > 5 else "")
            logger.error(error_msg)
            # 不将整体任务状态设置为失败，只记录错误
            logger.warning(f"并行任务部分失败，但将继续执行后续步骤: {len(failed_tasks)} 失败，{len(success_tasks)} 成功")
        
        # 如果任务已终止，返回False
        if pipeline_manager.check_task_cancellation(task_id):
            return False
        
        # 如果有成功的任务，进行图标重命名
        if len(success_tasks) > 0:
            try:
                from ..core.DictUtil import get_icon_mapping
                from pathlib import Path
                import os
                
                # 获取图标映射字典
                icon_mapping = get_icon_mapping()
                
                # 构建图标目录路径
                icon_dir = Path("public/ui_source") / f"mainLine_{task_id}/releases/icon"
                if icon_dir.exists():
                    # 遍历目录中的所有PNG文件
                    for file in icon_dir.glob("*.png"):
                        # 从文件名中提取图标序号
                        try:
                            # 提取数字部分（例如从 "icon_1.png" 提取 "1"）
                            icon_num = int(''.join(filter(str.isdigit, file.stem)))
                            old_name = f"icon_{icon_num}"
                            
                            # 如果在映射字典中存在对应关系
                            if old_name in icon_mapping:
                                new_name = f"{icon_mapping[old_name]}.png"
                                new_path = file.parent / new_name
                                
                                # 如果目标文件已存在，先删除
                                if new_path.exists():
                                    os.remove(new_path)
                                    
                                # 重命名文件
                                os.rename(file, new_path)
                                logger.info(f"重命名图标: {file.name} -> {new_name}")
                            else:
                                logger.warning(f"图标 {old_name} 在映射字典中未找到对应关系")
                        except Exception as e:
                            logger.error(f"重命名图标 {file.name} 时出错: {str(e)}")
                else:
                    logger.error(f"图标目录不存在: {icon_dir}")
            except Exception as e:
                logger.error(f"图标重命名过程出错: {str(e)}")
                # 记录错误但不影响后续流程
        
        # 即使有任务失败，只要有成功的任务就返回True让流程继续执行
        result = len(success_tasks) > 0
        logger.info(f"并行任务组执行结果: {result} (成功: {len(success_tasks)}, 失败: {len(failed_tasks)})")
        return result
        
    except Exception as e:
        error_msg = f"并行任务组执行失败: {str(e)}"
        logger.error(error_msg)
        # 更新状态但不立即设置为FAILED，让整个流程决定最终状态
        pipeline_manager.update_subtask_progress(
            task_id,
            subtask_name,
            100  # 确保即使出错也设置进度为100%以允许流程继续
        )
        return False

async def rename_icons(task_id: str):
    """重命名图标任务"""
    try:
        from ..core.DictUtil import get_icon_mapping
        from pathlib import Path
        import os
        
        # 获取图标映射字典
        icon_mapping = get_icon_mapping()
        
        # 构建图标目录路径
        icon_dir = Path("public/ui_source") / f"mainLine_{task_id}/releases/icon"
        if not icon_dir.exists():
            raise Exception(f"图标目录不存在: {icon_dir}")

        renamed_count = 0
        total_icons = len(list(icon_dir.glob("*.png")))
        
        # 遍历目录中的所有PNG文件
        for file in icon_dir.glob("*.png"):
            try:
                # 提取数字部分（例如从 "icon_1.png" 提取 "1"）
                icon_num = int(''.join(filter(str.isdigit, file.stem)))
                old_name = f"icon_{icon_num}"
                
                # 如果在映射字典中存在对应关系
                if old_name in icon_mapping:
                    new_name = f"{icon_mapping[old_name]}.png"
                    new_path = file.parent / new_name
                    
                    # 如果目标文件已存在，先删除
                    if new_path.exists():
                        os.remove(new_path)
                        
                    # 重命名文件
                    os.rename(file, new_path)
                    renamed_count += 1
                    logger.info(f"重命名图标: {file.name} -> {new_name}")
                else:
                    logger.warning(f"图标 {old_name} 在映射字典中未找到对应关系")
            except Exception as e:
                logger.error(f"重命名图标 {file.name} 时出错: {str(e)}")
        
        return {
            "renamed_count": renamed_count,
            "total_icons": total_icons
        }
        
    except Exception as e:
        logger.error(f"图标重命名过程出错: {str(e)}")
        raise

async def process_pipeline(task_id: str, prompt: str, style: str):
    """处理任务链"""
    try:
        # 注册当前进程
        try:
            from ..core.process_manager import register_current_process
            register_current_process(task_id)
            logger.info(f"已注册主流程进程到任务 {task_id}")
        except Exception as e:
            logger.error(f"注册流程进程失败: {str(e)}")
            
        # 设置状态为运行中
        if not pipeline_manager.set_task_status(task_id, TaskStatus.RUNNING):
            logger.error(f"无法设置任务状态到Redis: {task_id}")
            raise
        
        # 添加检查函数，用于检查任务是否被终止
        def is_task_terminated():
            """检查任务是否被终止"""
            return pipeline_manager.check_task_cancellation(task_id)
        
        # 1. 生成壁纸
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行壁纸生成")
                return
                
            wallpaper_result = await generate_image(TextToImageRequest(
                prompt=prompt,
                task_id=task_id
            ))
            
            # 再次检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，不更新壁纸生成进度")
                return
                
            pipeline_manager.update_subtask_progress(
                task_id, 
                "wallpaper_generation", 
                100,
                f"/ui_source/mainLine_{task_id}/releases/wallpaper/wallpaper.png"
            )
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，壁纸生成步骤终止")
            return
        except Exception as e:
            logger.error(f"生成壁纸失败: {str(e)}")
            # 当壁纸生成失败时，如果任务已被终止，直接返回
            if is_task_terminated():
                return
            pipeline_manager.set_task_status(task_id, TaskStatus.FAILED, str(e))
            raise
        
        # 2. UI生成
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行UI生成")
                return
                
            ui_result = await generate_ui_path(task_id)
            
            # 再次检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，不更新UI生成进度")
                return
                
            pipeline_manager.update_subtask_progress(
                task_id, 
                "ui_generation", 
                100,
                ui_result.get("image_url")
            )
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，UI生成步骤终止")
            return
        except Exception as e:
            logger.error(f"UI生成失败: {str(e)}")
            # 当UI生成失败时，如果任务已被终止，直接返回
            if is_task_terminated():
                return
            pipeline_manager.set_task_status(task_id, TaskStatus.FAILED, str(e))
            raise
        
        # 3. UI切分
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行UI切分")
                return
                
            split_result = await split_ui_path(task_id)
            
            # 再次检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，不更新UI切分进度")
                return
                
            pipeline_manager.update_subtask_progress(
                task_id, 
                "ui_split", 
                100,
                split_result.get("image_urls")
            )
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，UI切分步骤终止")
            return
        except Exception as e:
            logger.error(f"UI切分失败: {str(e)}")
            # 当UI切分失败时，如果任务已被终止，直接返回
            if is_task_terminated():
                return
            pipeline_manager.set_task_status(task_id, TaskStatus.FAILED, str(e))
            raise
        
        # 4.0. 通过模型获取图标细化风格
        try:
                
            from .util import get_style_from_prompt
            logger.info("通过模型获取图标细化风格")
            refine_style = await get_style_from_prompt(prompt)
            logger.info(f"选择图标细化风格: {refine_style}")
        except Exception as e:
            refine_style = "jianzhi"

        # 4. 并行执行图标细化内容生成
        parallel_success = True
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行并行任务")
                return
                
            parallel_success = await execute_parallel_tasks(task_id, "parallel_tasks", refine_style)
            logger.info(f"并行任务执行完成，结果: {parallel_success}")
            if not parallel_success:
                logger.warning("并行任务全部失败，但仍将继续执行后续步骤")
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，并行任务步骤终止")
            return
        except Exception as e:
            logger.error(f"并行任务执行异常: {str(e)}")
            # 记录错误但继续执行后续步骤
            parallel_success = False
        
        # 最后一次检查任务是否被终止
        if is_task_terminated():
            logger.info(f"任务 {task_id} 已被终止，停止后续所有处理")
            return

        # 5. 提取颜色
        # try:
        #     # 检查任务是否被终止
        #     if is_task_terminated():
        #         logger.info(f"任务 {task_id} 已被终止，停止执行提取颜色")
        #         return
        #
        #     from .util import extract_colors
        #     logger.info("开始执行提取颜色步骤")
        #     colors = await extract_colors(task_id)
        #
        #     # 再次检查任务是否被终止
        #     if is_task_terminated():
        #         logger.info(f"任务 {task_id} 已被终止，不更新颜色提取进度")
        #         return
        #
        #     pipeline_manager.update_subtask_progress(
        #         task_id,
        #         "color_extraction",
        #         100
        #     )
        #     logger.info("颜色提取完成")
        # except asyncio.CancelledError:
        #     logger.info(f"任务 {task_id} 被取消，提取颜色步骤终止")
        #     return
        # except Exception as e:
        #     logger.error(f"提取颜色失败: {str(e)}")
        #     # 记录错误但继续执行
        #     colors_success = False
        # else:
        #     colors_success = True
        #
        # # 检查任务是否被终止
        # if is_task_terminated():
        #     logger.info(f"任务 {task_id} 已被终止，停止后续处理")
        #     return
            
        # 6. 转换PNG为JPG
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行PNG转JPG")
                return
                
            from .util import convert_task_images_to_jpg
            logger.info("开始执行PNG转JPG步骤")
            result = await convert_task_images_to_jpg(task_id)
            
            # 再次检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，不更新PNG转JPG进度")
                return
                
            pipeline_manager.update_subtask_progress(
                task_id,
                "convert_to_jpg",
                100 if result["status"] == "success" else 50
            )
            logger.info(f"PNG转JPG完成: {result['status']}")
            jpg_success = result["status"] == "success"
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，PNG转JPG步骤终止")
            return
        except Exception as e:
            logger.error(f"PNG转JPG失败: {str(e)}")
            # 记录错误但继续执行
            jpg_success = False

        # 检查任务是否被终止
        if is_task_terminated():
            logger.info(f"任务 {task_id} 已被终止，停止后续处理")
            return
            
        # 7. 重命名图标（在打包前执行）
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行图标重命名")
                return
                
            from .util import rename_task_icons
            logger.info("开始执行图标重命名步骤")
            rename_result = await rename_task_icons(task_id)
            logger.info(f"图标重命名完成: {rename_result['renamed_count']}/{rename_result['total_icons']}")
            rename_success = rename_result['renamed_count'] > 0
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，图标重命名步骤终止")
            return
        except Exception as e:
            logger.error(f"图标重命名失败: {str(e)}")
            # 记录错误但继续执行
            rename_success = False

        # 检查任务是否被终止
        if is_task_terminated():
            logger.info(f"任务 {task_id} 已被终止，停止后续处理")
            return
            
        # 8. 创建发布包
        try:
            # 检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，停止执行创建发布包")
                return
                
            from .util import create_release_archive
            logger.info("开始执行创建发布包步骤")
            archive_path = await create_release_archive(task_id)
            
            # 再次检查任务是否被终止
            if is_task_terminated():
                logger.info(f"任务 {task_id} 已被终止，不更新创建发布包进度")
                return
                
            pipeline_manager.update_subtask_progress(
                task_id,
                "create_archive",
                100
            )
            logger.info(f"发布包创建成功: {archive_path}")
            archive_success = True
        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} 被取消，创建发布包步骤终止")
            return
        except Exception as e:
            logger.error(f"创建发布包失败: {str(e)}")
            archive_success = False
        
        # 最终检查任务是否被终止
        if is_task_terminated():
            logger.info(f"任务 {task_id} 已被终止，不更新最终状态")
            return
            
        # 最终状态判断逻辑
        # 如果所有关键步骤都成功，设置为SUCCESS
        if parallel_success and jpg_success and rename_success and archive_success:
            logger.info(f"任务 {task_id} 全部步骤成功完成，设置状态为SUCCESS")
            pipeline_manager.set_task_status(task_id, TaskStatus.SUCCESS)
        else:
            # 至少有一个步骤失败，但整体流程完成
            error_msg = "部分步骤执行失败，但主要流程已完成"
            logger.warning(f"任务 {task_id} {error_msg}")
            pipeline_manager.set_task_status(task_id, TaskStatus.SUCCESS, error_msg)
            
    except asyncio.CancelledError:
        logger.info(f"任务 {task_id} 被取消")
        # 确保任务状态已更新为TERMINATED
        task_info = pipeline_manager.get_task_info(task_id)
        if task_info and task_info.get("status") != TaskStatus.TERMINATED.value:
            pipeline_manager.set_task_status(task_id, TaskStatus.TERMINATED, "Task cancelled")
    except Exception as e:
        # 只有在没有更具体的错误处理时才设置通用错误
        logger.error(f"任务链处理过程中发生未处理异常: {str(e)}")
        task_info = pipeline_manager.get_task_info(task_id)
        if task_info and task_info.get("status") != TaskStatus.FAILED.value:
            pipeline_manager.set_task_status(task_id, TaskStatus.FAILED, str(e))

@router.post("/pipeline")
async def start_pipeline(request: PipelineRequest):
    connection = None
    """启动任务链"""
    try:
        connection = connect_PBDB()
        task_id = request.task_id
        
        # 注册任务
        subtasks = get_subtasks()
        pipeline_manager.register_task(task_id, subtasks)
        
        # 启动任务链处理（包括壁纸生成）
        task = asyncio.create_task(process_pipeline(task_id, request.prompt, request.style))
        
        # 注册任务到运行中的任务列表
        pipeline_manager.register_running_task(task_id, task)
        logId = insert_data_and_get_id(connection,'theme_task_log',
                                       {'task_id':request.task_id,
                                        'query':request.prompt,
                                        'task_type':'theme',
                                        'task_status':TaskStatus.PENDING.value,
                                        'progress':0,
                                        'current_step':subtasks[0].name if subtasks else None,
                                        'create_time':datetime.now(),
                                        'update_time':datetime.now()}
                                       )
        return {
            "task_id": task_id,
            "message": "Pipeline started",
            "status": pipeline_manager.get_task_info(task_id)["status"]
        }
    except Exception as e:
        logger.error(f"启动任务链失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if connection:
            connection.close()

@router.get("/pipeline/status/{task_id}")
async def get_pipeline_status(task_id: str):
    """获取任务进度"""
    if not pipeline_manager.task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = pipeline_manager.get_task_info(task_id)

    # 添加步骤详细信息
    steps = [
        {"name": "wallpaper_generation", "status": "completed"},
        {"name": "ui_generation", "status": "running"},
        # ... 其他步骤状态 ...
    ]

    return {
        "status": task_info["status"],
        "progress": task_info["progress"],
        "current_step": task_info["current_step"],
        "steps": steps,
        "error": task_info.get("error")
    }

# 图像预览接口
@router.get("/preview/{task_id}/{category}")
async def get_previews(
    task_id: str = Path(..., description="任务ID"),
    category: str = Path(..., description="预览类别，支持wallpaper或icon")
):
    """
    获取指定任务的壁纸或图标预览图片列表
    
    Args:
        task_id: 任务ID
        category: 预览类别，wallpaper或icon
        
    Returns:
        预览图片信息列表
    """
    # 验证category参数
    if category not in ["wallpaper", "icon"]:
        raise HTTPException(status_code=400, detail="Invalid category. Must be 'wallpaper' or 'icon'")
    
    import os
    import glob
    
    # 添加更多路径模式以提高匹配成功率
    possible_paths = [
        # 标准格式：mainLine_{task_id}_时间戳/releases/{category}
        f"public/ui_source/mainLine_{task_id}_20*/releases/{category}",
        # 不带mainLine前缀
        f"public/ui_source/{task_id}_20*/releases/{category}",
        # 不带releases子目录
        f"public/ui_source/mainLine_{task_id}_20*/{category}",
        # 完全匹配给定的taskID目录（针对已知的精确目录）
        f"public/ui_source/mainLine_{task_id}*/releases/{category}",
        # 精确匹配指定的任务ID
        f"public/ui_source/mainLine_03f2b605273e44a1_20250306152737/releases/{category}",
    ]
    
    # 尝试所有可能的路径模式
    matched_dirs = []
    for path_pattern in possible_paths:
        matched = glob.glob(path_pattern)
        if matched:
            matched_dirs.extend(matched)
            break
    
    if not matched_dirs:
        raise HTTPException(status_code=404, detail=f"No {category} directory found for task {task_id}")
    
    # 获取最新的目录
    latest_dir = max(matched_dirs, key=os.path.getctime)
    
    print(f"找到目录: {latest_dir}")  # 调试信息
    
    # 获取目录中的所有图片文件
    image_extensions = ["*.jpg", "*.png", "*.jpeg", "*.webp"]
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(latest_dir, ext)))
    
    if not image_files:
        raise HTTPException(status_code=404, detail=f"No images found in {category} directory for task {task_id}")
    
    # 构建预览信息，更新URL前缀为v1版本
    base_url = "/api/v1/preview-image"
    previews = []
    
    for idx, file_path in enumerate(image_files):
        file_name = os.path.basename(file_path)
        # 获取图片尺寸
        try:
            from PIL import Image
            img = Image.open(file_path)
            width, height = img.size
            img.close()
        except Exception:
            width, height = 0, 0
            
        previews.append({
            "id": idx + 1,
            "file_name": file_name,
            "url": f"{base_url}/{task_id}/{category}/{file_name}",
            "width": width,
            "height": height,
            "type": category
        })
    
    return JSONResponse(content={"previews": previews})

# 获取单张预览图片
@router.get("/preview-image/{task_id}/{category}/{file_name}")
async def get_preview_image(
    task_id: str = Path(..., description="任务ID"),
    category: str = Path(..., description="预览类别，支持wallpaper或icon"),
    file_name: str = Path(..., description="文件名")
):
    """
    获取指定任务的单张预览图片
    
    Args:
        task_id: 任务ID
        category: 预览类别，wallpaper或icon
        file_name: 图片文件名
        
    Returns:
        图片文件
    """
    # 验证category参数
    if category not in ["wallpaper", "icon"]:
        raise HTTPException(status_code=400, detail="Invalid category. Must be 'wallpaper' or 'icon'")
    
    import os
    import glob
    
    # 添加更多路径模式以提高匹配成功率
    possible_paths = [
        # 标准格式：mainLine_{task_id}_时间戳/releases/{category}
        f"public/ui_source/mainLine_{task_id}_20*/releases/{category}",
        # 不带mainLine前缀
        f"public/ui_source/{task_id}_20*/releases/{category}",
        # 不带releases子目录
        f"public/ui_source/mainLine_{task_id}_20*/{category}",
        # 完全匹配给定的taskID目录（针对已知的精确目录）
        f"public/ui_source/mainLine_{task_id}*/releases/{category}",
        # 精确匹配指定的任务ID
        f"public/ui_source/mainLine_03f2b605273e44a1_20250306152737/releases/{category}",
    ]
    
    # 尝试所有可能的路径模式
    matched_dirs = []
    for path_pattern in possible_paths:
        matched = glob.glob(path_pattern)
        if matched:
            matched_dirs.extend(matched)
            break
    
    if not matched_dirs:
        raise HTTPException(status_code=404, detail=f"No {category} directory found for task {task_id}")
    
    # 获取最新的目录
    latest_dir = max(matched_dirs, key=os.path.getctime)
    
    print(f"获取图片的目录: {latest_dir}")  # 调试信息
    
    # 构建文件路径
    file_path = os.path.join(latest_dir, file_name)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Image not found")
    
    return FileResponse(file_path)

@router.get("/preview-minio/{task_id}/{category}")
async def get_previews_minio(
        task_id: str = Path(..., description="任务ID"),
        category: str = Path(..., description="预览类别，支持wallpaper或icon")
):
    """
    获取指定任务的壁纸或图标预览图片列表

    Args:
        task_id: 任务ID
        category: 预览类别，wallpaper或icon

    Returns:
        预览图片信息列表
    """
    # 验证category参数
    if category not in ["wallpaper", "icon"]:
        raise HTTPException(status_code=400, detail="Invalid category. Must be 'wallpaper' or 'icon'")
    bucket_name = 'changan-theme'
    image_files = []
    if category and category == 'wallpaper':
        image_files = list_files(bucket_name, 'jpg', prefix=f'{task_id}/')
    if category and category == 'icon':
        image_files = list_files(bucket_name, 'png', prefix=f'{task_id}/')
    print(image_files)
    # for ext in image_extensions:
    #     image_files.extend(glob.glob(os.path.join(latest_dir, ext)))

    if not image_files:
        raise HTTPException(status_code=404, detail=f"No images found in {category} directory for task {task_id}")

    import os
    from PIL import Image
    import io
    previews = []
    for idx, file_path in enumerate(image_files):
        # MinIO中的路径可能与本地文件路径格式不同，这里假设file_path是MinIO中的对象名称
        file_name = os.path.basename(file_path)

        # 获取图片尺寸
        try:
            # 下载图片对象
            data_stream = minioGet(bucket_name, file_path)
            image_data = data_stream.data
            data_stream.close()
            data_stream.release_conn()

            # 使用 Pillow 打开图片并获取尺寸
            image = Image.open(io.BytesIO(image_data))
            width, height = image.size
            image.close()
        except Exception as e:
            logger.error(f"获取图片尺寸失败: {str(e)}")
            width, height = 0, 0

        previews.append({
            "id": idx + 1,
            "file_name": file_name,
            "url": minioGetPresigned_url(bucket_name, file_path),  # 使用MinIO生成预签名URL
            "width": width,
            "height": height,
            "type": "jpg" if category == 'wallpaper' else "png"  # 这里假设所有文件都是PNG类型
        })

    return JSONResponse(content={"previews": previews})

@router.get("/preview-image-minio/{task_id}/{category}/{file_name}")
async def get_preview_image_minio(
        task_id: str = Path(..., description="任务ID"),
        category: str = Path(..., description="预览类别，支持wallpaper或icon"),
        file_name: str = Path(..., description="文件名")
):
    # 验证category参数
    if category not in ["wallpaper", "icon"]:
        raise HTTPException(status_code=400, detail="Invalid category. Must be 'wallpaper' or 'icon'")
    if category and category == 'wallpaper':
        return minioGetPresigned_url('changan-theme', f"{task_id}/wallpaper.jpg")
    if category and category == 'icon':
        return minioGetPresigned_url('changan-theme', f"{task_id}/{file_name}")
