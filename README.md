# 文生视频系统

本项目提供了一个完整的文生视频生成系统，包括前端界面和后端API服务。该系统允许用户通过文字描述生成高质量的视频内容，支持各种风格和场景。

## 系统架构

系统由以下组件组成：

1. **theme_ui**: 前端用户界面，使用React和Tailwind CSS构建
2. **base_backend**: 视频生成后端服务，处理视频生成请求
3. **theme_backend**: 原有的主题生成后端服务

## 功能特性

- 通过文字描述生成长达3分钟的视频
- 支持调整分辨率、帧率、帧数等参数
- 实时显示生成进度
- 可配置多个后端服务URL
- 暗色模式界面，使用玻璃拟态设计

## 安装与运行

### 前端 (theme_ui)

1. 进入theme_ui目录
   ```bash
   cd theme_ui
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 运行开发服务器
   ```bash
   npm run dev
   ```

### 后端 (base_backend)

1. 进入base_backend目录
   ```bash
   cd base_backend
   ```

2. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 启动服务器
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8001 --reload
   ```

或者使用Docker:
   ```bash
   docker-compose up -d
   ```

## 使用说明

1. 打开浏览器访问前端地址 (默认为 http://localhost:3000)
2. 点击首页上的"文生视频"卡片
3. 填写视频描述和参数
4. 点击"开始生成视频"按钮
5. 实时查看生成进度
6. 生成完成后，可以预览和下载视频

## 配置多后端

该系统支持配置多个后端服务地址：

1. 在文生视频页面，点击右上角"设置"按钮
2. 输入新的后端URL (默认为 http://127.0.0.1:8001)
3. 点击"保存设置"按钮

## 技术栈

- 前端: React, TypeScript, Tailwind CSS, React Router, Axios
- 后端: FastAPI, Python, uvicorn
- 容器化: Docker, Docker Compose

# theme



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin http://************:8090/duanzj063/theme.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](http://************:8090/duanzj063/theme/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

### 车衣贴图(Decal)实现技术

项目中的车衣贴图（例如芙宁娜主题的decal.png）采用Three.js的Decal系统实现，主要技术点如下：

1. **贴花(Decal)技术原理**：
   ```typescript
   // 创建贴花几何体 - 将2D贴图精确投影到3D曲面
   const decalGeometry = new THREE.DecalGeometry(
     carBodyMesh, // 目标网格
     position,    // 贴花位置
     orientation, // 贴花方向
     size         // 贴花尺寸
   );
   
   // 创建贴花材质
   const decalMaterial = new THREE.MeshPhongMaterial({
     map: textureLoader.load('texture/decal.png'),
     transparent: true,
     opacity: 1,
     polygonOffset: true,
     polygonOffsetFactor: -1,
     depthTest: true,
     depthWrite: false
   });
   
   // 创建贴花网格并添加到场景
   const decalMesh = new THREE.Mesh(decalGeometry, decalMaterial);
   scene.add(decalMesh);
   ```

2. **主题识别与贴图加载**：
   ```typescript
   // 根据URL hash参数识别不同主题
   const theme = window.location.hash.substring(1); // 例如"furina"
   
   // 根据主题加载对应贴图资源
   if (theme === "furina") {
     // 加载芙宁娜主题贴图
     this.loadFurinaDecals();
   }
   ```

3. **贴花位置与旋转映射**：
   ```typescript
   // 贴花定位数据结构
   const decalPositions = [
     { 
       position: new THREE.Vector3(-2.1, 0.8, 0.5),
       rotation: new THREE.Euler(0, Math.PI / 2, 0),
       size: new THREE.Vector3(0.8, 0.8, 0.1)
     },
     // 更多贴花位置...
   ];
   
   // 为每个定位创建对应贴花
   decalPositions.forEach(data => {
     this.createDecal(carBodyMesh, decalTexture, data);
   });
   ```

4. **UV跟随与变形处理**：
   ```typescript
   // 贴花网格会跟随车身网格的变形
   // 通过设置贴花与车身的父子关系实现同步变形
   carBodyMesh.add(decalMesh);
   
   // 或通过手动更新位置方式
   function updateDecalPositions() {
     decals.forEach(decal => {
       // 跟随车身网格更新位置和旋转
       decal.position.copy(decal.userData.originalPosition);
       decal.rotation.copy(decal.userData.originalRotation);
       // 应用车身网格的变换
       decal.applyMatrix4(carBodyMesh.matrixWorld);
     });
   }
   ```

特殊主题版本（如芙宁娜版本）通过URL hash参数(#furina)触发，系统加载专用的装饰贴图资源，并通过精确的贴花定位将角色图像应用到车身的特定部位，实现风格化的车衣效果。贴花系统保证了图像能够正确贴合到车身曲面，并在车辆旋转和变形时保持正确的位置关系。
