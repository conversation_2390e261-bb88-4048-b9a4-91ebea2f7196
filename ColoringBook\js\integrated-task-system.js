/**
 * 集成任务系统 - 将增强的任务管理器与UI进度组件集成
 * 
 * 功能特性：
 * 1. 集成EnhancedTaskManager和TaskProgressUI
 * 2. 提供统一的任务管理接口
 * 3. 处理任务生成和进度显示
 * 4. 管理任务结果和错误处理
 */

class IntegratedTaskSystem {
    constructor() {
        // 初始化任务管理器
        this.taskManager = new EnhancedTaskManager({
            maxConcurrentTasks: 3,
            maxRetries: 3,
            basePollingInterval: 2000,
            maxPollingAttempts: 60
        });

        // 初始化进度UI
        this.progressUI = new TaskProgressUI('taskProgressContainer', {
            showDetailedProgress: true,
            showPerformanceMetrics: true,
            updateInterval: 1000,
            maxVisibleTasks: 10
        });

        // 初始化增强画廊
        this.gallery = new EnhancedGallery('gallery-grid', {
            enableLazyLoading: true,
            enablePreloading: true,
            retryAttempts: 3,
            retryDelay: 1000,
            animationDuration: 300
        });

        // 连接任务管理器和进度UI
        this.progressUI.setTaskManager(this.taskManager);

        // 绑定事件处理器
        this.setupEventHandlers();

        // 状态管理
        this.isGenerating = false;
        this.currentTasks = [];
    }

    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        // 任务开始事件
        this.taskManager.on('taskStart', (taskKey, taskInfo) => {
            console.log(`[IntegratedTaskSystem] 任务开始: ${taskKey}`);
        });

        // 任务进度更新事件
        this.taskManager.on('taskProgress', (taskId, taskInfo) => {
            console.log(`[IntegratedTaskSystem] 任务进度: ${taskId} - ${taskInfo.progress}%`);
        });

        // 任务完成事件
        this.taskManager.on('taskComplete', (taskId, result) => {
            console.log(`[IntegratedTaskSystem] 任务完成: ${taskId}`);
            this.handleTaskComplete(taskId, result);
        });

        // 任务失败事件
        this.taskManager.on('taskFail', (taskId, error) => {
            console.error(`[IntegratedTaskSystem] 任务失败: ${taskId}`, error);
            this.handleTaskFail(taskId, error);
        });
    }

    /**
     * 开始生成任务
     * @param {string} prompt - 提示词
     * @param {string} ratio - 图片比例
     * @param {number} count - 生成数量
     * @returns {Promise<Array>} - 生成结果
     */
    async startGeneration(prompt, ratio, count) {
        if (this.isGenerating) {
            console.warn('[IntegratedTaskSystem] 正在生成中，跳过重复请求');
            return [];
        }

        if (!prompt || prompt.trim() === '') {
            throw new Error('请输入提示词');
        }

        if (count < 1 || count > 10) {
            throw new Error('生成数量必须在1-10之间');
        }

        this.isGenerating = true;

        try {
            // 创建任务数组
            const tasks = Array.from({ length: count }, (_, index) => ({
                prompt: prompt.trim(),
                index: index
            }));

            // 创建选项对象，包含ratio和count
            const options = {
                ratio: ratio,
                count: count // 使用传入的count参数
            };

            console.log(`[IntegratedTaskSystem] 开始生成 ${count} 个任务，比例: ${ratio}`);

            // 使用增强的任务管理器处理任务
            const results = await this.taskManager.startTasksWithQueueManagement(tasks, options);

            console.log(`[IntegratedTaskSystem] 生成完成，成功: ${results.length}/${count}`);

            return results;

        } catch (error) {
            console.error('[IntegratedTaskSystem] 生成失败:', error);
            throw error;
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * 处理任务完成
     * @param {string} taskId - 任务ID
     * @param {Object} result - 任务结果
     */
    handleTaskComplete(taskId, result) {
        console.log('[IntegratedTaskSystem] 处理任务完成:', taskId, result);
        
        if (result && result.image_urls && result.image_urls.length > 0) {
            // 为每个图片URL创建图片卡片
            result.image_urls.forEach((imageUrl, index) => {
                this.createImageCard({
                    ...result,
                    image_url: imageUrl,
                    index: index
                });
            });
            
            // 显示成功消息
            const count = result.image_urls.length;
            this.showSuccessMessage(`魔法施展成功！${count}张新的画作已添加到画廊`);
        } else {
            console.warn('[IntegratedTaskSystem] 任务完成但没有图片URL:', result);
        }
    }

    /**
     * 处理任务失败
     * @param {string} taskId - 任务ID
     * @param {Error} error - 错误信息
     */
    handleTaskFail(taskId, error) {
        // 显示详细的任务失败信息
        this.showTaskFailureDetails(taskId, error.message);
    }

    /**
     * 创建图片卡片
     * @param {Object} result - 任务结果
     */
    createImageCard(result) {
        if (!this.gallery) {
            console.error('[IntegratedTaskSystem] 画廊未初始化');
            return;
        }

        // 使用增强画廊添加图片
        const imageId = this.gallery.addImage({
            url: result.image_url,
            metadata: {
                taskId: result.task_id,
                prompt: result.prompt,
                index: result.index || 0,
                createdAt: new Date().toISOString()
            }
        });

        console.log(`[IntegratedTaskSystem] 图片已添加到画廊: ${imageId}`);
        return imageId;
    }

    /**
     * 更新打印按钮状态 (委托给画廊处理)
     */
    updatePrintButtonState() {
        if (this.gallery) {
            this.gallery.updatePrintButtonState();
        }
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    showSuccessMessage(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg bg-green-100 border-l-4 border-green-400 text-green-700 transform transition-all duration-300 translate-x-full';
        
        successDiv.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 text-xl mr-3">✨</div>
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                </div>
                <button class="close-btn ml-2 text-xl hover:opacity-70 transition-opacity">&times;</button>
            </div>
        `;

        document.body.appendChild(successDiv);

        // 显示动画
        setTimeout(() => {
            successDiv.classList.remove('translate-x-full');
        }, 100);

        // 关闭按钮事件
        successDiv.querySelector('.close-btn').addEventListener('click', () => {
            this.hideNotification(successDiv);
        });

        // 自动隐藏
        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                this.hideNotification(successDiv);
            }
        }, 3000);
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     * @param {string} type - 错误类型
     */
    showErrorMessage(message, type = 'UNKNOWN_ERROR') {
        // 移除现有的错误提示
        const existingError = document.querySelector('.error-notification');
        if (existingError) {
            existingError.remove();
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full';
        
        // 根据错误类型设置样式
        let bgColor, icon;
        switch (type) {
            case 'NETWORK_ERROR':
                bgColor = 'bg-red-100 border-red-400 text-red-700';
                icon = '🌐';
                break;
            case 'SERVER_ERROR':
                bgColor = 'bg-orange-100 border-orange-400 text-orange-700';
                icon = '⚠️';
                break;
            case 'TIMEOUT_ERROR':
                bgColor = 'bg-yellow-100 border-yellow-400 text-yellow-700';
                icon = '⏰';
                break;
            case 'VALIDATION_ERROR':
                bgColor = 'bg-blue-100 border-blue-400 text-blue-700';
                icon = '📝';
                break;
            default:
                bgColor = 'bg-gray-100 border-gray-400 text-gray-700';
                icon = '❌';
        }

        errorDiv.className += ` ${bgColor} border-l-4`;
        
        errorDiv.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 text-xl mr-3">${icon}</div>
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                </div>
                <button class="close-btn ml-2 text-xl hover:opacity-70 transition-opacity">&times;</button>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // 显示动画
        setTimeout(() => {
            errorDiv.classList.remove('translate-x-full');
        }, 100);

        // 关闭按钮事件
        errorDiv.querySelector('.close-btn').addEventListener('click', () => {
            this.hideNotification(errorDiv);
        });

        // 自动隐藏
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                this.hideNotification(errorDiv);
            }
        }, 5000);
    }

    /**
     * 显示详细的任务失败信息
     * @param {string} taskId - 任务ID
     * @param {string} error - 错误信息
     */
    showTaskFailureDetails(taskId, error) {
        const detailDiv = document.createElement('div');
        detailDiv.className = 'task-failure-notification fixed bottom-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg bg-red-50 border-l-4 border-red-400 text-red-700 transform transition-all duration-300 translate-y-full';
        
        detailDiv.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 text-lg mr-3">⚠️</div>
                <div class="flex-1">
                    <p class="font-medium text-sm">任务失败详情</p>
                    <p class="text-xs opacity-75 mt-1">任务ID: ${taskId.substring(0, 8)}...</p>
                    <p class="text-xs mt-1">${error}</p>
                </div>
                <button class="close-btn ml-2 text-lg hover:opacity-70 transition-opacity">&times;</button>
            </div>
        `;

        document.body.appendChild(detailDiv);

        // 显示动画
        setTimeout(() => {
            detailDiv.classList.remove('translate-y-full');
        }, 100);

        // 关闭按钮事件
        detailDiv.querySelector('.close-btn').addEventListener('click', () => {
            detailDiv.classList.add('translate-y-full');
            setTimeout(() => {
                if (document.body.contains(detailDiv)) {
                    detailDiv.remove();
                }
            }, 300);
        });

        // 自动隐藏
        setTimeout(() => {
            if (document.body.contains(detailDiv)) {
                detailDiv.classList.add('translate-y-full');
                setTimeout(() => {
                    if (document.body.contains(detailDiv)) {
                        detailDiv.remove();
                    }
                }, 300);
            }
        }, 8000);
    }

    /**
     * 隐藏通知
     * @param {HTMLElement} notification - 通知元素
     */
    hideNotification(notification) {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.remove();
            }
        }, 300);
    }

    /**
     * 获取任务统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        return this.taskManager.getProgressStats();
    }

    /**
     * 获取实时统计信息
     * @returns {Object} - 实时统计信息
     */
    getRealTimeStats() {
        return this.taskManager.getRealTimeStats();
    }

    /**
     * 取消所有任务
     * @param {string} reason - 取消原因
     */
    cancelAllTasks(reason = '用户取消') {
        return this.taskManager.cancelAllTasks(reason);
    }

    /**
     * 重置任务系统
     */
    reset() {
        this.taskManager.reset();
        this.progressUI.hide();
        this.isGenerating = false;
        this.currentTasks = [];
    }

    /**
     * 销毁任务系统
     */
    destroy() {
        this.taskManager.reset();
        this.progressUI.destroy();
    }
}

// 导出集成任务系统类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IntegratedTaskSystem;
} else if (typeof window !== 'undefined') {
    window.IntegratedTaskSystem = IntegratedTaskSystem;
}