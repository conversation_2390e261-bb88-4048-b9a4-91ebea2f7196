import os
import asyncio
import uuid
import json
import time
import logging
from typing import Dict, Any, Optional, List

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.ui import Console

# 修改导入方式以兼容不同环境
try:
    # 先尝试相对导入（在模块内使用）
    from .event_handler import PromptEnhancerEventHandler
except ImportError:
    try:
        # 再尝试从项目路径导入（在直接运行时）
        from theme_backend.agent.event_handler import PromptEnhancerEventHandler
    except ImportError:
        # 最后尝试从当前目录导入（在测试脚本中使用）
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        from agent.event_handler import PromptEnhancerEventHandler

# 配置日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 获取模型客户端
def get_model_client():
    """获取配置好的模型客户端"""
    logger.info("初始化模型客户端 - Qwen2.5-32B-Instruct-AWQ")
    print("[模型] 初始化模型客户端 - Qwen2.5-32B-Instruct-AWQ")
    return OpenAIChatCompletionClient(
        model="Qwen2.5-32B-Instruct-AWQ",  
        api_key="1",
        base_url="http://27.159.93.61:8007/v1/",
        model_capabilities={
            "json_output": True,
            "vision": False,
            "function_calling": True,
        },
        extra_body={
            # 使用原始响应模式，保留reasoning_content (思考内容)
            "raw_response": True
        },
        response_wrapper=process_qwq_response
    )

# QwQ模型响应处理函数
def process_qwq_response(response):
    """处理QwQ模型的特殊响应格式，保留思考内容"""
    try:
        logger.debug(f"处理模型响应: {str(response)[:200]}...")
        print(f"[模型] 接收到原始响应: {str(response)[:200]}...")
        
        # 检查响应中是否包含思考内容
        if isinstance(response, dict) and "choices" in response:
            choices = response.get("choices", [])
            if choices and len(choices) > 0:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                reasoning_content = message.get("reasoning_content")
                
                # 如果有reasoning_content，将其添加为<think>标签内容
                if reasoning_content:
                    logger.debug(f"找到思考内容，长度: {len(reasoning_content)} 字符")
                    print(f"[模型] 找到思考内容，长度: {len(reasoning_content)} 字符")
                    content = f"<think>{reasoning_content}</think>\n\n{content}"
                    # 更新响应中的内容
                    choices[0]["message"]["content"] = content
                    print(f"[模型] 更新后的内容长度: {len(content)} 字符")
        
        return response
    except Exception as e:
        logger.error(f"处理QwQ响应时出错: {e}")
        print(f"[模型] 处理响应出错: {e}")
        return response

# WebSocket消息管理器
class WebSocketManager:
    """管理活跃的WebSocket连接并发送消息"""
    
    def __init__(self):
        self.active_connections: Dict[str, Any] = {}
        logger.info("WebSocket管理器已初始化")
    
    def connect(self, session_id: str, websocket):
        """添加新的WebSocket连接"""
        self.active_connections[session_id] = websocket
        logger.info(f"WebSocket管理器: 添加连接 {session_id}, 当前连接数: {len(self.active_connections)}")
        
    def disconnect(self, session_id: str):
        """移除WebSocket连接"""
        if session_id in self.active_connections:
            logger.info(f"WebSocket管理器: 移除连接 {session_id}")
            del self.active_connections[session_id]
        else:
            logger.warning(f"WebSocket管理器: 尝试移除不存在的连接 {session_id}")
    
    async def send_message(self, session_id: str, message: Dict[str, Any]):
        """向特定会话的WebSocket连接发送消息"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            try:
                # 记录要发送的消息内容摘要
                msg_type = message.get("event_type", "unknown")
                content_preview = "无内容"
                if "data" in message and "content" in message["data"]:
                    content = message["data"]["content"]
                    content_preview = content[:50] + "..." if len(content) > 50 else content
                
                logger.debug(f"发送WebSocket消息: 会话={session_id}, 类型={msg_type}, 内容={content_preview}")
                # 同时打印到控制台，方便调试
                print(f"[WebSocket输出] 类型: {msg_type}, 内容: {content_preview}")
                
                await websocket.send_json(message)
                return True
            except Exception as e:
                logger.error(f"发送WebSocket消息失败: 会话={session_id}, 错误={str(e)}")
                return False
        else:
            logger.warning(f"尝试向不存在的会话发送消息: {session_id}")
            return False

# 提示词增强Agent
class PromptEnhancerAgent:
    """提示词增强Agent，使用AutoGen框架和流式输出"""
    
    def __init__(self, websocket_manager: Any, config: Dict[str, Any] = None):
        self.websocket_manager = websocket_manager
        self.config = config or {}
        logger.info("初始化提示词增强Agent")
        print("[Agent] 初始化提示词增强Agent")
        
        # 初始化模型客户端
        try:
            logger.info("正在获取模型客户端...")
            print("[Agent] 正在获取模型客户端...")
            self.model_client = get_model_client()
            logger.info("模型客户端初始化成功")
            print("[Agent] 模型客户端初始化成功")
        except Exception as e:
            logger.error(f"模型客户端初始化失败: {e}")
            print(f"[Agent] 模型客户端初始化失败: {e}")
            raise
        
        # 创建增强Agent
        try:
            logger.info("正在创建增强Agent...")
            print("[Agent] 正在创建增强Agent...")
            self.enhancer_agent = AssistantAgent(
                name="prompt_enhancer",
                model_client=self.model_client,
                system_message=self._get_system_message(),
                model_client_stream=True  # 启用流式输出
            )
            logger.info("增强Agent创建成功")
            print("[Agent] 增强Agent创建成功")
            
            # 删除团队模式，简化逻辑
            # 注释掉创建评审Agent和编辑Agent的代码
            
        except Exception as e:
            logger.error(f"创建Agent组件时出错: {e}")
            print(f"[Agent] 创建Agent组件时出错: {e}")
            raise
    
    def _get_system_message(self, agent_type: str = "prompt_enhancer") -> str:
        """获取Agent的系统消息"""
        if agent_type == "prompt_enhancer":
            return """你是一个专业的提示词增强专家，负责将简短的初始提示词转换为清晰、简洁的AI绘画提示词。
输出格式必须是一个简单的句子，不超过100字
"""
    
    async def enhance_prompt(self, session_id: str, prompt: str, user_id: str = "admin"):
        """增强提示词的主函数"""
        # 创建事件处理器
        event_handler = PromptEnhancerEventHandler(self.websocket_manager, session_id)
        logger.info(f"开始处理提示词增强: 会话={session_id}, 提示词={prompt[:30]}...")
        print(f"[Agent] 开始处理提示词增强: 会话={session_id}, 提示词={prompt[:30]}...")
        
        try:
            # 初始任务描述
            task = f"增强以下提示词，使其更加详细、专业和有创意：\n\n{prompt}"
            
            # 发送开始消息
            logger.info(f"发送初始思考消息: 会话={session_id}")
            print(f"[Agent] 发送初始思考消息: 会话={session_id}")
            await event_handler.on_thinking("开始分析提示词...")
            
            # 获取流式输出
            logger.info(f"开始运行Agent处理提示词: 会话={session_id}")
            print(f"[Agent] 开始运行Agent处理提示词: 会话={session_id}")
            
            # 确保enhancer_agent已初始化
            if not hasattr(self, 'enhancer_agent') or self.enhancer_agent is None:
                error_msg = "模型客户端或增强Agent未初始化"
                logger.error(error_msg)
                print(f"[Agent错误] {error_msg}")
                await event_handler.on_error(error_msg)
                return None
            
            # 使用run_stream获取流式输出
            try:
                stream = self.enhancer_agent.run_stream(task=task)
                print(f"[流式输出] 成功创建流式输出对象")
            except Exception as stream_error:
                error_msg = f"创建流式输出时出错: {str(stream_error)}"
                logger.error(error_msg)
                print(f"[Agent错误] {error_msg}")
                await event_handler.on_error(error_msg)
                return None
            
            # 直接控制台输出流式内容
            print(f"\n[流式输出开始] 会话={session_id}, 提示词={prompt[:30]}...")
            
            # 用于存储最终结果
            final_result = None
            
            # 处理流式输出并发送消息
            try:
                message_count = 0
                async for message in stream:
                    message_count += 1
                    # 输出消息类型和原始内容以便调试
                    print(f"[消息类型] {type(message)}")
                    
                    # 转换消息为字符串并打印
                    message_str = str(message)
                    print(f"[原始流式消息 {message_count}] {message_str[:200]}..." if len(message_str) > 200 else f"[原始流式消息 {message_count}] {message_str}")
                    
                    # 如果是ChatCompletionMessage类型，尝试获取content
                    if hasattr(message, "content"):
                        message_str = message.content
                        print(f"[消息内容属性] {message_str[:200]}..." if len(message_str) > 200 else f"[消息内容属性] {message_str}")
                    
                    # 判断是否为思考过程
                    is_thinking = False
                    if message_count < 5 or ("思考" in message_str or "分析" in message_str or "考虑" in message_str):
                        is_thinking = True

                    # 处理消息并发送
                    if is_thinking:
                        # 将思考内容包装在<think>标签中
                        wrapped_message = f"<think>{message_str}</think>"
                        await event_handler.on_thinking(wrapped_message)
                    else:
                        # 不是思考过程的内容作为最终输出结果处理
                        await event_handler.on_content(message_str, is_final=False)
                    
                    # 保存最新消息为最终结果
                    final_result = message_str
                    
                    # 检测是否最终消息
                    is_final = "提示词增强完成" in message_str
                    
                    # 如果是最终消息，记录完成
                    if is_final:
                        print(f"[流式输出] 检测到最终消息标记")
                
                print(f"[流式输出结束] 会话={session_id}, 共收到 {message_count} 条消息")
                logger.info(f"提示词增强完成: 会话={session_id}, 原始提示词={prompt[:30]}...")
                print(f"[Agent] 提示词增强完成: 会话={session_id}")
                
                if message_count == 0:
                    print(f"[警告] 没有收到任何流式消息！")
                    await event_handler.on_error("未收到任何模型响应")
                    return None
                
                # 保存最终消息为结果
                if final_result:
                    for msg in message.messages:
                        if msg.source == "prompt_enhancer":
                            final_result = msg.content
                    # 保留最终结果的内容
                    await event_handler.on_content(final_result, is_final=True)
                    # 发送最终完成信号
                    await event_handler.on_message("提示词增强过程已完成", "prompt_enhancer", True)
                    return final_result
                else:
                    error_msg = "未能获取最终结果"
                    logger.error(error_msg)
                    print(f"[Agent错误] {error_msg}")
                    await event_handler.on_error(error_msg)
                    return None
                
            except Exception as stream_process_error:
                error_msg = f"处理流式输出时出错: {str(stream_process_error)}"
                logger.error(error_msg)
                print(f"[Agent错误] {error_msg}")
                import traceback
                print(f"[错误堆栈] {traceback.format_exc()}")
                await event_handler.on_error(error_msg)
                return None
            
        except Exception as e:
            # 记录错误
            error_message = f"提示词增强过程中出错: {str(e)}"
            logger.error(f"{error_message}")
            print(f"[Agent] 错误: {error_message}")
            import traceback
            trace = traceback.format_exc()
            logger.error(trace)
            print(f"[Agent] 错误堆栈: {trace}")
            
            # 发送错误消息
            logger.info(f"向前端发送错误消息: 会话={session_id}")
            print(f"[Agent] 向前端发送错误消息: 会话={session_id}")
            await event_handler.on_error(error_message)
            
            # 重新抛出异常让上层处理
            return None

# 任务管理器
class AgentTaskManager:
    """管理提示词增强Agent任务"""
    
    def __init__(self, websocket_manager: WebSocketManager, config: Dict[str, Any] = None):
        self.websocket_manager = websocket_manager
        self.config = config or {}
        self.tasks = {}  # session_id -> task info
        self.lock = asyncio.Lock()
        logger.info("初始化任务管理器")
        try:
            self.agent = PromptEnhancerAgent(websocket_manager, config)
            logger.info("提示词增强Agent初始化成功")
        except Exception as e:
            logger.error(f"提示词增强Agent初始化失败: {e}")
            raise
    
    async def start_task(self, session_id: str, prompt: str, user_id: str = "admin"):
        """启动一个提示词增强任务"""
        logger.info(f"请求启动任务: 会话={session_id}, 提示词={prompt[:30]}...")
        
        async with self.lock:
            # 创建任务
            logger.info(f"创建异步任务: 会话={session_id}")
            task = asyncio.create_task(
                self._run_agent(session_id, prompt, user_id)
            )
            
            # 记录任务信息
            self.tasks[session_id] = {
                "task": task,
                "status": "running",
                "start_time": time.time(),
                "prompt": prompt,
                "user_id": user_id
            }
            logger.info(f"任务已创建并启动: 会话={session_id}")
            
            return session_id
    
    async def stop_task(self, session_id: str):
        """安全停止任务"""
        logger.info(f"请求停止任务: 会话={session_id}")
        
        async with self.lock:
            if session_id in self.tasks and self.tasks[session_id]["status"] == "running":
                # 更新状态
                self.tasks[session_id]["status"] = "stopping"
                logger.info(f"任务状态更新为stopping: 会话={session_id}")
                
                try:
                    # 取消任务
                    task = self.tasks[session_id]["task"]
                    logger.info(f"正在取消任务: 会话={session_id}")
                    task.cancel()
                    
                    # 等待任务完成取消
                    try:
                        await asyncio.wait_for(task, timeout=2.0)
                        logger.info(f"任务成功取消: 会话={session_id}")
                    except asyncio.TimeoutError:
                        logger.warning(f"任务取消超时: 会话={session_id}")
                    except asyncio.CancelledError:
                        logger.info(f"任务已成功取消: 会话={session_id}")
                    
                    self.tasks[session_id]["status"] = "stopped"
                    logger.info(f"任务状态更新为stopped: 会话={session_id}")
                except Exception as e:
                    logger.error(f"停止任务时出错: 会话={session_id}, 错误={str(e)}")
                    self.tasks[session_id]["status"] = "error"
                    logger.info(f"任务状态更新为error: 会话={session_id}")
    
    async def _run_agent(self, session_id: str, prompt: str, user_id: str = "admin"):
        """运行Agent的包装函数，包含异常处理"""
        logger.info(f"开始执行Agent任务: 会话={session_id}")
        
        try:
            # 运行提示词增强Agent
            logger.info(f"调用提示词增强Agent: 会话={session_id}")
            result = await self.agent.enhance_prompt(session_id, prompt, user_id)
            
            # 更新任务状态
            if session_id in self.tasks:
                self.tasks[session_id]["status"] = "completed"
                self.tasks[session_id]["result"] = result
                self.tasks[session_id]["end_time"] = time.time()
                logger.info(f"任务完成，状态更新为completed: 会话={session_id}")
            
            return result
        except asyncio.CancelledError:
            # 正常取消，清理资源
            logger.info(f"Agent任务被取消: 会话={session_id}")
            raise  # 重新抛出以便调用者知道任务被取消
        except Exception as e:
            # 捕获所有其他异常
            logger.error(f"Agent任务执行出错: 会话={session_id}, 错误={str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 更新任务状态
            if session_id in self.tasks:
                self.tasks[session_id]["status"] = "error"
                self.tasks[session_id]["error"] = str(e)
                self.tasks[session_id]["end_time"] = time.time()
                logger.info(f"任务状态更新为error: 会话={session_id}")
            
            # 尝试通知前端（如果WebSocket还在）
            try:
                logger.info(f"向前端发送错误通知: 会话={session_id}")
                await self.websocket_manager.send_message(
                    session_id,
                    {
                        "event_type": "error",
                        "data": {
                            "message": f"处理过程中出错: {str(e)}",
                            "is_final": True
                        }
                    }
                )
            except Exception as notify_error:
                # 忽略发送错误的错误
                logger.error(f"发送错误通知失败: 会话={session_id}, 错误={str(notify_error)}")
            
            return None

# 测试函数
async def test_enhance_prompt():
    """测试Agent功能的函数 - 直接使用流式输出示例"""
    logger.info("开始测试Agent功能")
    print("[测试] 开始测试Agent功能")
    
    # 创建模拟WebSocket管理器
    class MockWebSocketManager:
        async def send_message(self, session_id, message):
            print(f"[测试WebSocket] 发送消息: session_id={session_id}, 类型={message.get('event_type')}")
            if "data" in message and "content" in message["data"]:
                content = message["data"]["content"]
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"[测试WebSocket内容] {preview}")
            return True
    
    # 创建提示词增强Agent
    agent = PromptEnhancerAgent(MockWebSocketManager())
    
    # 测试提示词
    prompt = "一个站在山顶的人"
    session_id = "test-session"
    
    print(f"[测试] 原始提示词: {prompt}")
    print(f"[测试] 会话ID: {session_id}")
    
    # 执行提示词增强
    try:
        result = await agent.enhance_prompt(session_id, prompt)
        print(f"[测试] 增强结果: {result}")
    except Exception as e:
        print(f"[测试] 执行出错: {e}")

# 当直接运行此文件时执行测试
if __name__ == "__main__":
    logger.info("直接执行promptAgent.py，启动测试")
    print("[主程序] 直接执行promptAgent.py，启动测试")
    asyncio.run(test_enhance_prompt())
