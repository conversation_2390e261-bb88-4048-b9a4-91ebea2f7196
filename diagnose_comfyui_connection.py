#!/usr/bin/env python3
"""
ComfyUI连接诊断脚本
专门用于诊断魔法画册后端与ComfyUI的连接问题
"""

import requests
import json
import time
import asyncio
import httpx
from typing import Dict, Any

# 配置
BACKEND_API = "http://127.0.0.1:8100"
COMFYUI_API = "http://27.159.93.61:8193"

class ComfyUIConnectionDiagnostic:
    def __init__(self):
        self.results = {}
    
    def test_comfyui_direct_connection(self):
        """直接测试ComfyUI连接"""
        print("🔍 测试ComfyUI直接连接...")
        try:
            # 测试基本连接
            response = requests.get(f"{COMFYUI_API}/", timeout=10)
            print(f"✅ ComfyUI基本连接成功: HTTP {response.status_code}")
            
            # 测试系统状态
            system_response = requests.get(f"{COMFYUI_API}/system_stats", timeout=10)
            if system_response.status_code == 200:
                stats = system_response.json()
                print(f"📊 ComfyUI系统状态: {json.dumps(stats, indent=2)}")
            
            # 测试队列状态
            queue_response = requests.get(f"{COMFYUI_API}/queue", timeout=10)
            if queue_response.status_code == 200:
                queue_data = queue_response.json()
                print(f"📋 ComfyUI队列状态: 运行中={len(queue_data.get('queue_running', []))}, 等待中={len(queue_data.get('queue_pending', []))}")
            
            self.results['comfyui_direct'] = True
            return True
            
        except requests.exceptions.ConnectRefused:
            print("❌ ComfyUI连接被拒绝 - ComfyUI可能未启动")
            self.results['comfyui_direct'] = False
            return False
        except requests.exceptions.Timeout:
            print("❌ ComfyUI连接超时 - 服务响应缓慢")
            self.results['comfyui_direct'] = False
            return False
        except Exception as e:
            print(f"❌ ComfyUI连接失败: {e}")
            self.results['comfyui_direct'] = False
            return False
    
    def test_backend_comfyui_config(self):
        """测试后端ComfyUI配置"""
        print("\n🔧 检查后端ComfyUI配置...")
        try:
            # 读取后端配置文件
            config_paths = [
                "theme_backend/app/config.py",
                "theme_backend/config.py",
                "theme_backend/.env"
            ]
            
            for config_path in config_paths:
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'COMFYUI' in content or '8188' in content:
                            print(f"📄 发现配置文件: {config_path}")
                            # 提取相关配置行
                            lines = content.split('\n')
                            config_lines = [line for line in lines if 'COMFYUI' in line or '8188' in line]
                            for line in config_lines:
                                print(f"   {line.strip()}")
                except FileNotFoundError:
                    continue
            
            self.results['backend_config'] = True
            return True
            
        except Exception as e:
            print(f"❌ 配置检查失败: {e}")
            self.results['backend_config'] = False
            return False
    
    async def test_async_connection(self):
        """测试异步连接（模拟后端的连接方式）"""
        print("\n🔄 测试异步连接（模拟后端）...")
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 测试基本连接
                response = await client.get(f"{COMFYUI_API}/")
                print(f"✅ 异步连接成功: HTTP {response.status_code}")
                
                # 测试POST请求（模拟工作流提交）
                test_payload = {
                    "prompt": {
                        "1": {
                            "inputs": {"text": "test"},
                            "class_type": "CLIPTextEncode"
                        }
                    }
                }
                
                post_response = await client.post(
                    f"{COMFYUI_API}/prompt",
                    json=test_payload,
                    timeout=30.0
                )
                print(f"✅ 异步POST测试: HTTP {post_response.status_code}")
                
                if post_response.status_code == 200:
                    result = post_response.json()
                    print(f"📝 POST响应: {json.dumps(result, indent=2)}")
                
                self.results['async_connection'] = True
                return True
                
        except httpx.ReadTimeout:
            print("❌ 异步连接读取超时 - 这是后端日志中显示的错误")
            self.results['async_connection'] = False
            return False
        except httpx.ConnectTimeout:
            print("❌ 异步连接超时")
            self.results['async_connection'] = False
            return False
        except Exception as e:
            print(f"❌ 异步连接失败: {e}")
            self.results['async_connection'] = False
            return False
    
    def test_backend_api_status(self):
        """测试后端API状态"""
        print("\n🎯 测试后端API状态...")
        try:
            # 测试后端基本连接
            response = requests.get(f"{BACKEND_API}/", timeout=10)
            print(f"✅ 后端API连接成功: HTTP {response.status_code}")
            
            # 测试涂色书端点
            test_payload = {"prompt": "测试连接"}
            start_response = requests.post(
                f"{BACKEND_API}/api/v1/coloring-book/start",
                json=test_payload,
                timeout=30
            )
            
            if start_response.status_code == 200:
                data = start_response.json()
                task_id = data.get('task_id')
                print(f"✅ 后端任务创建成功: {task_id}")
                
                # 等待一下再检查状态
                time.sleep(2)
                status_response = requests.get(f"{BACKEND_API}/api/v1/coloring-book/status/{task_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"📊 任务状态: {status_data.get('status')}")
                    if status_data.get('error'):
                        print(f"⚠️ 任务错误: {status_data.get('error')}")
                else:
                    print(f"⚠️ 状态查询失败: HTTP {status_response.status_code}")
            else:
                print(f"❌ 后端任务创建失败: HTTP {start_response.status_code}")
                print(f"错误详情: {start_response.text}")
            
            self.results['backend_api'] = True
            return True
            
        except Exception as e:
            print(f"❌ 后端API测试失败: {e}")
            self.results['backend_api'] = False
            return False
    
    def test_network_connectivity(self):
        """测试网络连通性"""
        print("\n🌐 测试网络连通性...")
        try:
            import socket
            
            # 测试ComfyUI端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', 8188))
            sock.close()
            
            if result == 0:
                print("✅ ComfyUI端口8188可访问")
            else:
                print("❌ ComfyUI端口8188不可访问")
            
            # 测试后端端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', 8100))
            sock.close()
            
            if result == 0:
                print("✅ 后端端口8100可访问")
            else:
                print("❌ 后端端口8100不可访问")
            
            self.results['network'] = True
            return True
            
        except Exception as e:
            print(f"❌ 网络连通性测试失败: {e}")
            self.results['network'] = False
            return False
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("📋 ComfyUI连接诊断报告")
        print("="*60)
        
        issues = []
        recommendations = []
        
        if not self.results.get('comfyui_direct', False):
            issues.append("ComfyUI服务不可访问")
            recommendations.append("1. 确保ComfyUI已启动并运行在端口8188")
            recommendations.append("2. 检查ComfyUI启动日志是否有错误")
        
        if not self.results.get('async_connection', False):
            issues.append("异步连接超时")
            recommendations.append("3. 增加httpx客户端的超时时间")
            recommendations.append("4. 检查ComfyUI是否有大量排队任务导致响应缓慢")
        
        if not self.results.get('backend_api', False):
            issues.append("后端API调用失败")
            recommendations.append("5. 检查后端日志中的详细错误信息")
            recommendations.append("6. 验证后端ComfyUI配置是否正确")
        
        if issues:
            print("🚨 发现的问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            
            print("\n💡 建议的解决方案:")
            for rec in recommendations:
                print(f"   {rec}")
        else:
            print("✅ 所有连接测试通过！")
        
        print("\n🔧 快速修复步骤:")
        print("1. 重启ComfyUI服务")
        print("2. 检查防火墙设置")
        print("3. 增加后端超时配置")
        print("4. 检查系统资源使用情况")

async def main():
    """主诊断函数"""
    print("🚀 开始ComfyUI连接诊断")
    print("="*50)
    
    diagnostic = ComfyUIConnectionDiagnostic()
    
    # 执行所有测试
    tests = [
        diagnostic.test_network_connectivity,
        diagnostic.test_comfyui_direct_connection,
        diagnostic.test_backend_comfyui_config,
        diagnostic.test_backend_api_status,
    ]
    
    for test in tests:
        test()
        time.sleep(1)
    
    # 异步测试
    await diagnostic.test_async_connection()
    
    # 生成报告
    diagnostic.generate_diagnosis_report()

if __name__ == "__main__":
    asyncio.run(main())