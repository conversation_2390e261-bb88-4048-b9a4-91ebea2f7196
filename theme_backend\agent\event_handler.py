"""
提示词增强事件处理器
处理Agent生成过程中的事件，并通过WebSocket转发到前端
"""

from typing import Dict, Any, Optional
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class PromptEnhancerEventHandler:
    """处理提示词增强过程中的事件，并通过WebSocket推送到前端"""
    
    def __init__(self, websocket_manager, session_id: str):
        """
        初始化事件处理器
        
        Args:
            websocket_manager: WebSocket管理器实例
            session_id: 会话ID
        """
        self.websocket_manager = websocket_manager
        self.session_id = session_id
        self.in_thinking_mode = False  # 跟踪是否在思考模式中
        logger.info(f"初始化事件处理器: 会话={session_id}")
    
    def _process_content(self, content: str) -> Dict[str, Any]:
        # 检查是否为思考块(包含<think>标签的内容)
        thinking_match = re.search(r'<think>(.*?)</think>', content, re.DOTALL)
        
        if thinking_match:
            thinking_content = thinking_match.group(1).strip()
            # 从原始内容中移除<think>标签及其内容，保留其他部分作为实际输出
            actual_content = re.sub(r'<think>.*?</think>\s*', '', content, flags=re.DOTALL).strip()
            
            logger.debug(f"检测到思考块: 会话={self.session_id}, 思考内容长度={len(thinking_content)}, 实际内容长度={len(actual_content)}")
            return {
                "has_thinking": True,
                "thinking": thinking_content,
                "content": actual_content
            }
        
        # 检查是否为单独的思考块(某些自动生成的消息)
        if "</think>" in content:
            self.in_thinking_mode = False
            # 分割思考内容和实际内容
            parts = content.split("</think>", 1)
            thinking_content = parts[0].strip()
            # 如果存在思考结束后的内容，则作为实际内容
            actual_content = parts[1].strip() if len(parts) > 1 else ""
            
            logger.debug(f"检测到思考块结束: 会话={self.session_id}, 思考内容长度={len(thinking_content)}, 实际内容长度={len(actual_content)}")
            return {
                "has_thinking": True,
                "thinking": thinking_content,
                "content": actual_content
            }
            
        if "<think>" in content:
            self.in_thinking_mode = True
            thinking_content = content.replace("<think>", "").strip()
            logger.debug(f"检测到思考块开始: 会话={self.session_id}, 思考内容长度={len(thinking_content)}")
            return {
                "has_thinking": True,
                "thinking": thinking_content,
                "content": ""
            }
            
        # 如果当前处于思考模式，所有内容都作为思考处理
        if self.in_thinking_mode:
            logger.debug(f"思考模式中的普通内容: 会话={self.session_id}, 内容长度={len(content)}")
            return {
                "has_thinking": True,
                "thinking": content,
                "content": ""
            }
            
        # 普通内容
        logger.debug(f"普通内容: 会话={self.session_id}, 内容长度={len(content)}")
        return {
            "has_thinking": False,
            "thinking": "",
            "content": content
        }
    
    async def on_message(self, message: str, agent_name: str = None, is_final: bool = False):
        """
        处理消息事件
        
        Args:
            message: 消息内容
            agent_name: 代理名称
            is_final: 是否最终消息
        """
        # 处理消息内容
        processed = self._process_content(message)
        
        # 确定事件类型
        event_type = None
        if processed["has_thinking"]:
            # 有思考内容，发送思考事件
            if processed["thinking"]:
                event_type = "thinking"
                # 发送思考内容
                await self.websocket_manager.send_message(
                    self.session_id,
                    {
                        "event_type": event_type,
                        "data": {
                            "content": processed["thinking"],
                            "agent": agent_name,
                            "is_final": False
                        }
                    }
                )
            
            # 如果也有实际内容，发送内容事件
            if processed["content"]:
                event_type = "content"
                # 发送实际内容
                await self.websocket_manager.send_message(
                    self.session_id,
                    {
                        "event_type": event_type,
                        "data": {
                            "content": processed["content"],
                            "agent": agent_name,
                            "is_final": is_final
                        }
                    }
                )
            
            # 记录日志
            thinking_preview = processed['thinking'][:50] + "..." if len(processed['thinking']) > 50 else processed['thinking']
            content_preview = processed['content'][:50] + "..." if len(processed['content']) > 50 else processed['content']
            logger.info(f"消息处理: 会话={self.session_id}, 思考={thinking_preview}, 内容={content_preview}")
            return
            
        # 普通消息处理
        event_type = "content" if not is_final else "result"
        
        logger.info(f"发送内容: 会话={self.session_id}, 类型={event_type}, 内容长度={len(processed['content'])}, 最终消息={is_final}")
        content_preview = processed['content'][:100] + "..." if len(processed['content']) > 100 else processed['content']
        print(f"[内容事件] 类型={event_type}, 最终={is_final}")
        print(f"[内容数据] {content_preview}")
        
        # 发送消息到WebSocket
        await self.websocket_manager.send_message(
            self.session_id,
            {
                "event_type": event_type,
                "data": {
                    "content": processed["content"],
                    "agent": agent_name,
                    "is_final": is_final
                }
            }
        )
    
    async def on_thinking(self, content: str):
        """处理思考过程事件"""
        logger.info(f"思考过程事件: 会话={self.session_id}, 内容长度={len(content)}")
        print(f"[思考过程] 会话={self.session_id}, 内容长度={len(content)}")
        await self.on_message(content, "prompt_enhancer", False)
    
    async def on_content(self, content: str, is_final: bool = False):
        """处理内容生成事件"""
        logger.info(f"内容生成事件: 会话={self.session_id}, 内容长度={len(content)}, 最终内容={is_final}")
        print(f"[内容生成] 会话={self.session_id}, 内容长度={len(content)}, 最终={is_final}")
        await self.on_message(content, "editor", is_final)
    
    async def on_error(self, error: str):
        """处理错误事件"""
        logger.error(f"错误事件: 会话={self.session_id}, 错误={error}")
        print(f"[错误事件] 会话={self.session_id}, 错误={error}")
        await self.websocket_manager.send_message(
            self.session_id,
            {
                "event_type": "error",
                "data": {
                    "message": error,
                    "is_final": True
                }
            }
        ) 