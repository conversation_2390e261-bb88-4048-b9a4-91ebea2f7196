from fastapi import APIRouter, HTTPException
import requests
from pydantic import BaseModel, Field
from datetime import datetime
from ..core.mysql_util import connect_PBDB,update_data,insert_data_and_get_id
from ..core.minio_util import async_put_file
from .common import logger
import io
import json

router = APIRouter()

kolor_api = "http://************:15010/generate"

class TextToImageRequest(BaseModel):
    prompt: str
    task_id: str = Field(..., description="任务ID，用于文件保存和标识")

# 定义text_to_image接口
@router.post("/text-to-image", summary="文本生成壁纸", description="根据文本描述生成壁纸")
async def text_to_image(request: TextToImageRequest):
    connection = None
    try:
        connection = connect_PBDB()
        logger.info(f"\n========== 开始生成壁纸 ==========")
        logger.info(f"提示词: {request.prompt}")
        logger.info(f"任务ID: {request.task_id}")
        # 从请求体中获取文本描述
        text_description = request.prompt

        if not text_description:
            raise HTTPException(status_code=400, detail="文本描述不能为空")
        logId = insert_data_and_get_id(connection,'theme_task_log',
                               {'task_id':request.task_id,
                                'query':request.prompt,
                                'task_type':'image',
                                'task_status':'running',
                                'create_time':datetime.now(),
                                'update_time':datetime.now()}
                               )
        # 构建请求负载
        payload = {
            "query": text_description,
            "has_baidu_censor": False
        }

        # 调用远程HTTP接口
        response = requests.post(f"{kolor_api}", json=payload)
        response.raise_for_status()  # 如果响应状态码不是200，会抛出异常

        # 解析响应数据
        response_data = response.json()
        image_url = response_data.get('image_url')
        logger.info(f"文本生成壁纸返回: {json.dumps(response_data, indent=2)}")

        if not image_url:
            raise HTTPException(status_code=500, detail="生成图像URL为空")

        logger.info(f"成功生成图像URL: {image_url}")
        logger.info(f"上传图像: {image_url}")
        image_response = requests.get(image_url)
        image_response.raise_for_status()
        image_content = image_response.content

        # 将图片内容保存为BytesIO
        img_bytes = io.BytesIO(image_content)
        img_bytes.seek(0)
        objectName = request.task_id + f"/{request.task_id}.png"

        async_put_file({
            'bucketName': 'changan-image',
            'objectName': objectName,
            'fileType': 'png',
            'fileStream': img_bytes
        })
        logger.info(f"完成上传图像")

        # 返回生成的图像URL
        return {"image_url": image_url}
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用text_to_image接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
    finally:
        update_data(connection,'theme_task_log',
                    {'task_status': 'success' if image_url and image_url != '' else 'failed',
                     'result_urls': objectName if objectName else image_url,
                     'update_time':datetime.now()},"task_id='{}'".format(request.task_id))
        if connection:
            connection.close()