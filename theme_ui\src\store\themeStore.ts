import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

interface TaskState {
    status: string;
    progress: number;
    currentStep: string;
    error: string | null;
}

export const useThemeStore = defineStore('theme', () => {
    // 状态
    const taskId = ref<string | null>(null);
    const taskState = ref<TaskState>({
        status: 'pending',
        progress: 0,
        currentStep: '',
        error: null
    });

    // 计算属性
    const isTaskRunning = computed(() => {
        return ['pending', 'running'].includes(taskState.value.status);
    });

    const canDownload = computed(() => {
        return taskState.value.status === 'success' || taskState.value.status === 'completed';
    });

    const downloadUrl = computed(() => {
        return taskId.value ? `/api/theme/download/${taskId.value}` : '';
    });

    // 方法
    const startTask = async (prompt: string) => {
        try {
            // 模拟API调用
            taskId.value = 'task_' + Date.now();
            taskState.value = {
                status: 'running',
                progress: 0,
                currentStep: 'wallpaper_generation',
                error: null
            };

            // 模拟任务进度
            simulateProgress();
        } catch (error: any) {
            taskState.value.status = 'failed';
            taskState.value.error = error.message || '任务启动失败';
        }
    };

    const terminateTask = async () => {
        // 模拟终止任务
        taskState.value.status = 'terminated';
        taskState.value.error = '任务已手动终止';
    };

    const resetState = () => {
        taskId.value = null;
        taskState.value = {
            status: 'pending',
            progress: 0,
            currentStep: '',
            error: null
        };
    };

    // 模拟任务进度
    const simulateProgress = () => {
        const steps = [
            'wallpaper_generation',
            'ui_generation',
            'ui_split',
            'parallel_tasks',
            'color_extraction',
            'convert_to_jpg',
            'create_archive'
        ];

        let currentStepIndex = 0;
        let progress = 0;

        const interval = setInterval(() => {
            if (taskState.value.status !== 'running') {
                clearInterval(interval);
                return;
            }

            progress += 1;
            taskState.value.progress = progress;

            // 更新当前步骤
            if (progress % 15 === 0 && currentStepIndex < steps.length - 1) {
                currentStepIndex++;
                taskState.value.currentStep = steps[currentStepIndex];
            }

            // 完成任务
            if (progress >= 100) {
                taskState.value.status = 'success';
                taskState.value.progress = 100;
                clearInterval(interval);
            }
        }, 300);
    };

    return {
        taskId,
        taskState,
        isTaskRunning,
        canDownload,
        downloadUrl,
        startTask,
        terminateTask,
        resetState
    };
}); 