<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">创建新主题</h1>
      </div>

      <!-- 生成状态面板 -->
      <GlassPanel v-if="generationStatus" class="p-6 mb-8 transition-all duration-300">
        <div class="flex items-center justify-between">
          <div class="flex-1 mr-4">
            <h3 class="text-lg font-semibold text-purple-200 mb-2">
              生成进度 -
              <span :class="{
                'text-red-400': generationStatus.status === 'terminating' || generationStatus.status === 'terminated',
                'text-green-400': generationStatus.status === 'completed' || generationStatus.status === 'success'
              }">
                {{ 
                  generationStatus.status === 'terminating' ? '取消中...' : 
                  generationStatus.status === 'terminated' ? '已取消' : 
                  generationStatus.status === 'completed' || generationStatus.status === 'success' ? '已完成' :
                  generationStatus.status 
                }}
              </span>
            </h3>

            <div class="relative pt-1" v-if="generationStatus.status !== 'terminated'">
              <div class="flex mb-2 items-center justify-between">
                <div>
                  <span class="text-xs font-semibold inline-block text-purple-200">
                    {{ generationProgress.toFixed(0) }}%
                  </span>
                </div>
                <div class="text-right">
                  <span class="text-xs font-semibold inline-block text-purple-200">
                    {{ completedSteps }} / {{ totalSteps }} 步骤
                  </span>
                </div>
              </div>
              <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-dark-700">
                <div :style="{ width: generationProgress + '%' }" :class="{
                       'bg-gradient-to-r from-purple-500 to-blue-500': generationStatus.status !== 'terminating' && generationStatus.status !== 'terminated',
                       'bg-gradient-to-r from-red-500 to-red-300': generationStatus.status === 'terminating' || generationStatus.status === 'terminated'
                     }" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center transition-all duration-300"></div>
              </div>
            </div>

            <div v-if="generationStatus.status === 'terminated'" class="mt-4 bg-red-900/30 p-4 rounded-lg">
              <p class="text-red-300 text-center">任务已被取消</p>
              <p class="text-gray-300 text-sm text-center mt-2">您可以回到上一步重新开始生成</p>
              <div class="flex justify-center mt-4">
                <button @click="resetGeneration" class="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white font-medium hover:opacity-90 transition-opacity">
                  重新生成
                </button>
              </div>
            </div>

            <!-- 生成资源预览 -->
            <div v-if="generatedResources.length" class="mt-4">
              <h4 class="text-sm font-semibold text-purple-200 mb-2">已生成资源</h4>
              <div class="flex flex-wrap gap-2">
                <div v-for="(resource, index) in generatedResources" :key="index" class="w-16 h-16 rounded-lg bg-cover bg-center border border-purple-500/30 hover:border-purple-500 transition-colors" :style="{ backgroundImage: `url(${resource.thumbnail})` }"></div>
              </div>
            </div>

            <!-- 在进度面板中添加当前步骤显示 -->
            <div class="mb-4">
              <span class="text-purple-200 text-sm">当前步骤：</span>
              <span class="text-white font-medium">
                {{ stepTranslations[generationStatus?.current_step] || '准备中' }}
              </span>
            </div>

            <!-- 或者在进度条下方添加 -->
            <div class="mt-2 text-sm text-purple-200">
              正在执行：{{ stepTranslations[generationStatus?.current_step] || '初始化任务' }}
            </div>

            <!-- 壁纸预览区域 -->
            <div v-if="wallpaperPreviews.length > 0" class="mt-6 bg-gray-900/30 p-4 rounded-lg glass-panel">
              <h4 class="text-sm font-semibold text-purple-200 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                壁纸预览
              </h4>
              <div class="grid grid-cols-3 gap-4">
                <div v-for="(wallpaper, index) in wallpaperPreviews.slice(0, 3)" :key="index" class="glass-preview-card wallpaper-card" @click="openImageViewer(wallpaperPreviews.map(wp => wp.url), index)">
                  <div class="preview-img-container">
                    <img :src="wallpaper.url" :alt="wallpaper.file_name" class="preview-img" @load="handleImageLoad" @error="handleImageError(index)" />
                  </div>
                  <div class="preview-card-info">
                    <span class="preview-filename">{{ wallpaper.file_name }}</span>
                  </div>
                </div>
              </div>
              <div v-if="wallpaperPreviews.length > 3" class="text-center mt-4">
                <span class="text-xs text-purple-200 bg-purple-500/20 px-3 py-1 rounded-full hover:bg-purple-500/30 transition-colors cursor-pointer" @click="openAllWallpapers">
                  查看全部 {{ wallpaperPreviews.length }} 张壁纸
                </span>
              </div>
            </div>

            <!-- 图标预览区域 -->
            <div v-if="iconPreviews.length > 0" class="mt-4 glass-panel p-4 rounded-lg">
              <h4 class="text-sm font-semibold text-purple-200 mb-4 flex items-center justify-between">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 2a2 2 0 100 4 2 2 0 000-4zm-3 6a3 3 0 116 0v1h1a2 2 0 012 2v1a2 2 0 01-2 2H6a2 2 0 01-2-2v-1a2 2 0 012-2h1V8z" clip-rule="evenodd" />
                  </svg>
                  图标预览
                  <span v-if="generationStatus?.current_step === 'parallel_tasks'" class="ml-2 text-xs bg-blue-500/30 text-blue-300 px-2 py-0.5 rounded-full">
                    实时生成中 ({{ iconPreviews.length }}/35)
                  </span>
                  <span v-if="lastIconRefreshTime > 0" class="ml-2 text-xs text-gray-400">
                    最后更新: {{ new Date(lastIconRefreshTime).toLocaleTimeString() }}
                  </span>
                </div>
                <button @click="loadIconPreviews(currentTaskId)" class="px-3 py-1 bg-blue-800 hover:bg-blue-700 text-white rounded text-xs transition-colors flex items-center" :disabled="loadingIcons">
                  <svg v-if="loadingIcons" class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                  </svg>
                  {{ loadingIcons ? '刷新中...' : '刷新图标' }}
                </button>
              </h4>
              <div class="grid grid-cols-4 gap-3">
                <div v-for="(icon, index) in showAllIcons ? iconPreviews : iconPreviews.slice(0, 8)" :key="icon.id || index" class="glass-preview-card icon-card" @click="openImageViewer(iconPreviews.map(ic => ic.url), index)">
                  <div class="preview-img-container">
                    <img :src="icon.url" :alt="icon.file_name" class="preview-img" @load="handleImageLoad" @error="handleImageError(index)" />
                  </div>
                </div>
              </div>
              <div v-if="iconPreviews.length > 8" class="text-center mt-3">
                <button @click="showAllIcons = !showAllIcons" class="text-xs text-purple-200 bg-purple-500/20 px-3 py-1 rounded-full hover:bg-purple-500/30 transition-colors cursor-pointer flex items-center mx-auto">
                  <svg v-if="!showAllIcons" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                  </svg>
                  {{ showAllIcons ? '折叠图标' : `展开全部 ${iconPreviews.length} 个图标` }}
                </button>
              </div>
            </div>

            <!-- 图标加载中状态 -->
            <div v-if="loadingIcons && iconPreviews.length === 0" class="mt-4 bg-blue-900/30 text-blue-300 p-4 rounded-lg flex items-center">
              <svg class="animate-spin h-5 w-5 mr-3 text-blue-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>正在加载图标预览...</span>
            </div>

            <!-- 图标生成中提示 -->
            <div v-else-if="iconPreviews.length === 0 && generationStatus?.current_step === 'parallel_tasks'" class="mt-4 bg-blue-900/30 text-blue-300 p-4 rounded-lg flex items-center justify-between">
              <div class="flex items-center">
                <svg class="animate-spin h-5 w-5 mr-3 text-blue-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>图标正在生成中，将在生成后显示...</span>
              </div>
              <button @click="loadIconPreviews(currentTaskId)" class="ml-2 px-3 py-1 bg-blue-800 hover:bg-blue-700 text-white rounded text-xs transition-colors flex items-center" :disabled="loadingIcons">
                <svg v-if="loadingIcons" class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ loadingIcons ? '刷新中...' : '手动刷新' }}
              </button>
            </div>

            <!-- 图标加载错误状态 -->
            <div v-else-if="iconLoadError && generationStatus?.current_step === 'parallel_tasks'" class="mt-4 bg-amber-900/30 text-amber-300 p-4 rounded-lg flex items-center justify-between">
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                <span class="flex-1">图标加载失败，请手动刷新重试。</span>
              </div>
              <button @click="loadIconPreviews(currentTaskId)" class="ml-2 px-3 py-1 bg-amber-800 hover:bg-amber-700 text-white rounded text-xs transition-colors flex items-center" :disabled="loadingIcons">
                <svg v-if="loadingIcons" class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ loadingIcons ? '刷新中...' : '手动刷新' }}
              </button>
            </div>

            <!-- 手动加载按钮 -->
            <div v-if="wallpaperPreviews.length === 0 && generationProgress > 30" class="mt-4 flex justify-center">
              <button @click="forceLoadWallpapers" class="fancy-button" :disabled="loadingPreviews || (!canSafelyLoadWallpaper && !isWallpaperGenCompleted)" :class="{'opacity-50 cursor-not-allowed': !canSafelyLoadWallpaper && !isWallpaperGenCompleted}">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ loadingPreviews ? '加载中...' : !canSafelyLoadWallpaper ? '等待UI生成阶段' : '手动加载预览' }}
              </button>
            </div>

            <!-- 添加查看预览按钮 -->
            <button v-if="generationStatus.status === 'completed' || generationStatus.status === 'success'" @click="goToPreview" class="mt-4 fancy-button">
              <span>查看预览</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <button v-if="generationStatus.status === 'running'" @click="cancelGeneration" class="px-4 py-2 text-sm text-red-400 hover:text-red-300 transition-colors">
            取消生成
          </button>
        </div>
      </GlassPanel>

      <!-- 主要内容 -->
      <GlassPanel class="p-10">

        <h2 class="text-3xl font-bold mb-10 text-center bg-gradient-to-r from-purple-500 via-blue-500 to-blue-400 bg-clip-text text-transparent animate-gradient drop-shadow-[0_4px_12px_rgba(139,92,246,0.4)]">
          描述您想要的主题
        </h2>

        <!-- 文本输入区域 -->
        <div class="mb-8">
          <textarea v-model="themeDescription" class="w-full p-5 bg-dark-800/50 border border-gray-700 focus:border-purple-500 rounded-lg text-white focus:outline-none resize-none h-40 text-lg" placeholder="描述您想要的主题风格，例如：'帮我生成一个中国风的西湖风光的主题'"></textarea>
        </div>

        <!-- 试一试标签区域 -->
        <div class="mb-6">
          <p class="text-gray-300 text-sm mb-3">试一试：</p>
          <div class="flex flex-wrap gap-3">
            <button 
              v-for="(tag, index) in suggestionTags" 
              :key="index"
              @click="applySuggestion(tag)"
              class="px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 rounded-full transition-colors duration-200 border border-purple-500/30 hover:border-purple-500/50"
            >
              {{ tag }}
            </button>
          </div>
        </div>

        <!-- 测试模式开关 -->
        <div class="mb-6 bg-dark-800/50 p-4 rounded-lg border border-purple-500/30">
          <div class="flex items-center">
            <input type="checkbox" id="testMode" v-model="isTestMode" class="w-5 h-5 text-purple-500 bg-dark-900 rounded border-gray-600 focus:ring-purple-500 focus:ring-opacity-25" />
            <label for="testMode" class="ml-3 text-white">测试模式（使用本地文件模拟生成流程）</label>
          </div>
          <p class="mt-2 text-sm text-gray-400">启用后将使用本地样例文件模拟主题生成过程，不会实际调用后端API</p>
        </div>

        <!-- 参考样式 -->
        <div class="mb-10">
          <p class="text-gray-300 text-lg mb-4">图标样式</p>
          <div class="grid grid-cols-3 gap-6">
            <div v-for="(style, index) in referenceStyles" :key="index" class="glass-preview-card h-40 cursor-pointer transition-all duration-300 overflow-hidden" :class="selectedStyle === index ? 'ring-2 ring-purple-500 ring-offset-2 ring-offset-dark-800' : ''" @click="selectedStyle = index">
              <div class="preview-img-container h-full">
                <img :src="style.url" :alt="style.name" class="preview-img object-cover w-full h-full" />
              </div>
              <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent">
                <span class="text-white font-medium text-sm">{{ style.name }}</span>
              </div>
              <!-- 选中特效 -->
              <!-- 后端自动选择风格 <div v-if="selectedStyle === index" class="checkmark"></div> -->
            </div>
          </div>
        </div>

        <!-- 生成按钮 -->
        <button class="w-full glass-card bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-5 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg text-xl" @click="startGeneration">
          开始生成
        </button>
      </GlassPanel>

      <!-- 添加查看进度按钮 -->
      <div v-if="currentTaskId && !generationStatus" class="fixed bottom-4 right-4">
        <button @click="checkProgress" class="glass-card px-6 py-3 rounded-full text-white font-semibold flex items-center space-x-2 hover:scale-105 transition-transform">
          <span>查看进度</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- 进度弹窗 -->
      <div v-if="showProgressModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="glass-card p-8 rounded-xl max-w-lg w-full mx-4">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">生成进度</h3>
            <button @click="showProgressModal = false" class="text-gray-400 hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <ProgressBar :progress="generationProgress" :completed-steps="completedSteps" :total-steps="totalSteps" />

          <ResourcePreview v-if="generatedResources.length" :resources="generatedResources" />

          <!-- 在进度弹窗中添加步骤列表 -->
          <div class="mb-6">
            <h4 class="text-purple-200 mb-2">执行步骤：</h4>
            <div class="space-y-2">
              <div v-for="(step, index) in generationStatus?.steps" :key="index" class="flex items-center text-sm">
                <span class="w-5 h-5 rounded-full mr-2 flex items-center justify-center" :class="{
                    'bg-green-500': step.status === 'completed',
                    'bg-purple-500 animate-pulse': step.status === 'running',
                    'bg-gray-600': step.status === 'pending'
                  }">
                  <span v-if="step.status === 'completed'" class="text-white text-xs">✓</span>
                </span>
                <span class="text-gray-300">
                  {{ stepTranslations[step.name] || step.name }}
                </span>
              </div>
            </div>
          </div>

          <!-- 在弹窗中添加手动加载预览按钮 -->
          <div v-if="wallpaperPreviews.length === 0 && generationProgress > 30" class="mt-4 flex justify-center">
            <button @click="forceLoadWallpapers" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center transition-colors" :disabled="loadingPreviews || (!canSafelyLoadWallpaper && !isWallpaperGenCompleted)" :class="{'opacity-50 cursor-not-allowed': !canSafelyLoadWallpaper && !isWallpaperGenCompleted}">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              {{ loadingPreviews ? '加载中...' : !canSafelyLoadWallpaper ? '等待UI生成阶段' : '手动加载预览' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 最后的结尾处添加图片查看器组件 -->
      <ImageViewer v-model="showImageViewer" :images="viewerImages" :initialIndex="currentImageIndex" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, computed } from "vue";
import { defineAsyncComponent } from "vue";
import {
  ThemeService,
  type ThemeGenerationStatus,
  type PreviewResponse,
} from "@/services/themeService";
import { useRouter } from "vue-router";
import { themeApi } from "@/api/themeApi";
import { API_CONFIG } from "@/config/api";

const GlassPanel = defineAsyncComponent(
  () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
  () => import("../components/ProgressBar.vue")
);
const ResourcePreview = defineAsyncComponent(
  () => import("../components/ResourcePreview.vue")
);
const ImageViewer = defineAsyncComponent(
  () => import("../components/ImageViewer.vue")
);

// 添加状态类型
interface GenerationStatus {
  status: "pending" | "running" | "completed" | "failed";
  progress: number;
  resources: Array<{
    type: string;
    url: string;
    thumbnail: string;
  }>;
  current_step?: string;
}

// 响应式状态
const themeDescription = ref("");
const generationSpeed = ref(50);
const selectedStyle = ref(0);
const currentTaskId = ref<string | null>(null);
const generationStatus = ref<ThemeGenerationStatus | null>(null);
const generationProgress = ref(0);
const completedSteps = ref(0);
const totalSteps = ref(0);
const generatedResources = ref<Array<any>>([]);
const showProgressModal = ref(false);
let statusPollInterval: number | null = null;
const router = useRouter();

// 预览图片
const wallpaperPreviews = ref<Array<any>>([]);
const iconPreviews = ref<Array<any>>([]);
const loadingPreviews = ref(false);
const loadingIcons = ref(false);
const loadError = ref(false);
const iconLoadError = ref(false);
const loadedImages = ref<Set<number>>(new Set());

// 参考样式数据
const referenceStyles = [
  {
    url: "/images/国风剪纸.png", // 放在public/images目录下的本地图片
    name: "中国风",
    style: "jianzhi",
  },
  {
    url: "/images/冰.png", // 放在public/images目录下的本地图片
    name: "冰晶风",
    style: "bing",
  },
  {
    url: "/images/敦煌.png", // 放在public/images目录下的本地图片
    name: "敦煌风",
    style: "dunhuang",
  },
];

// 步骤中文映射
const stepTranslations = {
  wallpaper_generation: "壁纸生成",
  ui_generation: "UI生成",
  ui_split: "UI切分",
  parallel_tasks: "并行处理",
  color_extraction: "颜色提取",
  convert_to_jpg: "格式转换",
  create_archive: "打包发布",
} as const;

// 添加重试计数器
const retryCount = ref(0);
const maxRetries = 3; // 最大重试次数

// 判断当前是否可以安全加载壁纸
const canSafelyLoadWallpaper = computed(() => {
  if (!generationStatus.value) return false;

  // 检查是否已经进入UI生成阶段或更后的阶段
  const status = generationStatus.value;
  return (
    status.current_step &&
    [
      "ui_generation",
      "ui_split",
      "parallel_tasks",
      "color_extraction",
      "convert_to_jpg",
      "create_archive",
    ].includes(status.current_step)
  );
});

// 判断壁纸生成是否已完成
const isWallpaperGenCompleted = computed(() => {
  if (!generationStatus.value || !generationStatus.value.steps) return false;

  const wallpaperStep = generationStatus.value.steps.find(
    (step) => step.name === "wallpaper_generation"
  );

  return wallpaperStep?.status === "completed";
});

// 添加测试模式标志
const isTestMode = ref(false);
const testTaskId = ref("mainLine_0de883cb71a24913_20250310111846");

// 添加建议标签数据
const suggestionTags = [
  "帮我生成一个中国风的西湖风光的主题",
  "生成一个未来科技感的赛博朋克主题",
  "创建一个清新自然的森林主题"
];

// 添加应用建议的方法
const applySuggestion = (suggestion: string) => {
  themeDescription.value = suggestion;
  // 延迟触发生成，确保文本已更新
  setTimeout(() => {
    startGeneration();
  }, 100);
};

// 开始生成主题
const startGeneration = async () => {
  try {
    stopStatusPolling(); // 确保之前的轮询已停止

    let taskId;

    if (isTestMode.value) {
      // 测试模式：使用预设的任务ID
      taskId = testTaskId.value;
      console.log("测试模式已启用，使用本地文件模拟主题生成流程");
      // 直接修改 currentTaskId
      currentTaskId.value = taskId;
      // 模拟生成进度
      await simulateGeneration(taskId);
    } else {
      // 真实模式：调用后端API
      taskId = await ThemeService.startGeneration(
        themeDescription.value,
        referenceStyles[selectedStyle.value].style
      );
      currentTaskId.value = taskId;
      await startStatusPolling(taskId);
    }
  } catch (error) {
    console.error("生成失败:", error);
    // 显示错误提示
  }
};

// 模拟生成进程
const simulateGeneration = async (taskId: string) => {
  // 初始化生成状态
  generationStatus.value = {
    status: "running",
    progress: 0,
    completedSteps: 0,
    totalSteps: 5,
    resources: [],
    current_step: "preparing",
    steps: [
      { name: "preparing", status: "running", progress: 0 },
      { name: "wallpaper_generation", status: "pending", progress: 0 },
      { name: "ui_generation", status: "pending", progress: 0 },
      { name: "parallel_tasks", status: "pending", progress: 0 },
      { name: "finalizing", status: "pending", progress: 0 },
    ],
  };

  // 更新总步骤数
  totalSteps.value = 5;

  // 模拟准备阶段
  await new Promise((resolve) => setTimeout(resolve, 2000));
  generationStatus.value.steps[0].status = "completed";
  generationStatus.value.steps[0].progress = 100;
  generationStatus.value.steps[1].status = "running";
  generationStatus.value.current_step = "wallpaper_generation";
  generationStatus.value.completedSteps = 1;
  generationStatus.value.progress = 20;
  completedSteps.value = 1;
  generationProgress.value = 20;

  // 模拟壁纸生成阶段
  await new Promise((resolve) => setTimeout(resolve, 3000));
  generationStatus.value.steps[1].status = "completed";
  generationStatus.value.steps[1].progress = 100;
  generationStatus.value.steps[2].status = "running";
  generationStatus.value.current_step = "ui_generation";
  generationStatus.value.completedSteps = 2;
  generationStatus.value.progress = 40;
  completedSteps.value = 2;
  generationProgress.value = 40;

  // 加载壁纸预览
  await loadTestWallpaperPreviews(taskId);

  // 模拟UI生成阶段
  await new Promise((resolve) => setTimeout(resolve, 3000));
  generationStatus.value.steps[2].status = "completed";
  generationStatus.value.steps[2].progress = 100;
  generationStatus.value.steps[3].status = "running";
  generationStatus.value.current_step = "parallel_tasks";
  generationStatus.value.completedSteps = 3;
  generationStatus.value.progress = 60;
  completedSteps.value = 3;
  generationProgress.value = 60;

  // 加载图标预览
  await loadTestIconPreviews(taskId);

  // 模拟并行任务阶段
  await new Promise((resolve) => setTimeout(resolve, 3000));
  generationStatus.value.steps[3].status = "completed";
  generationStatus.value.steps[3].progress = 100;
  generationStatus.value.steps[4].status = "running";
  generationStatus.value.current_step = "finalizing";
  generationStatus.value.completedSteps = 4;
  generationStatus.value.progress = 80;
  completedSteps.value = 4;
  generationProgress.value = 80;

  // 模拟最后阶段
  await new Promise((resolve) => setTimeout(resolve, 2000));
  generationStatus.value.steps[4].status = "completed";
  generationStatus.value.steps[4].progress = 100;
  generationStatus.value.completedSteps = 5;
  generationStatus.value.progress = 100;
  generationStatus.value.status = "completed";
  completedSteps.value = 5;
  generationProgress.value = 100;

  // 添加全部预览资源
  loadTestPreviewImages(taskId);
};

// 加载测试壁纸预览
const loadTestWallpaperPreviews = async (taskId: string) => {
  loadingPreviews.value = true;

  try {
    // 保留测试模式下的本地文件路径
    const basePath = `/test_theme/${taskId}/releases/wallpaper`;

    // 模拟API返回的壁纸数据
    wallpaperPreviews.value = [
      {
        file_name: "wallpaper.jpg",
        url: `${basePath}/wallpaper.jpg`,
        thumbnail: `${basePath}/wallpaper.jpg`,
      },
    ];

    // 添加到生成的资源列表
    generatedResources.value.push({
      type: "wallpaper",
      url: `${basePath}/wallpaper.jpg`,
      thumbnail: `${basePath}/wallpaper.jpg`,
    });
  } catch (error) {
    console.error("加载测试壁纸预览失败:", error);
    loadError.value = true;
  } finally {
    loadingPreviews.value = false;
  }
};

// 加载测试图标预览
const loadTestIconPreviews = async (taskId: string) => {
  loadingIcons.value = true;

  try {
    // 保留测试模式下的本地文件路径
    const basePath = `/test_theme/${taskId}/releases/icon`;

    // 获取图标文件列表 - 与ThemePreview.vue保持一致
    const iconFiles = [
      // 原有图标
      "com_tinnove_mediacenter.png",
      "com_wt_scene.png",
      "com_tinnove_apa.png",
      "com_wt_gamecenter.png",
      "com_incall_apps_personalcenter.png",
      "com_tinnove_aispace.png",
      "com_wt_vehiclecenter.png",
      "com_autopai_car_dialer.png",
      "com_tinnove_wecarnavi.png",
      "com_wtcl_filemanager.png",
      "com_wt_phonelink.png",
      // 添加文件夹中的其他图标
      "com_incall_dvr.png",
      "com_tinnove_chrome.png",
      "com_incall_app_drivershealth.png",
      "com_tinnove_carshow.png",
      "com_tinnove_link_client.png",
      "com_wt_maintenance.png",
      "com_autopai_smart_sound_effect.png",
      "com_tinnove_scenemode.png",
      "com_wt_airconditioner.png",
      "com_tinnove_cloudcamera.png",
      "com_changan_appmarket.png",
      "com_wt_carcamera.png",
      "com_tinnove_customer.png",
      "com_tinnove_gamezone.png",
      "com_wt_funbox.png",
      "com_autopai_album.png",
      "com_wtcl_electronicdirections.png",
      // 添加icon_数字.png格式的图标
      "icon_4.png",
      "icon_5.png",
      "icon_16.png",
      "icon_17.png",
      "icon_20.png",
      "icon_23.png",
      "icon_29.png",
    ];

    // 模拟API返回的图标数据
    iconPreviews.value = iconFiles.map((fileName) => ({
      file_name: fileName,
      url: `${basePath}/${fileName}`,
      thumbnail: `${basePath}/${fileName}`,
    }));

    // 添加到生成的资源列表
    iconFiles.forEach((fileName) => {
      generatedResources.value.push({
        type: "icon",
        url: `${basePath}/${fileName}`,
        thumbnail: `${basePath}/${fileName}`,
      });
    });
  } catch (error) {
    console.error("加载测试图标预览失败:", error);
    iconLoadError.value = true;
  } finally {
    loadingIcons.value = false;
  }
};

// 加载所有测试预览图片
const loadTestPreviewImages = (taskId: string) => {
  // 确保壁纸和图标预览已加载
  if (wallpaperPreviews.value.length === 0) {
    loadTestWallpaperPreviews(taskId);
  }

  if (iconPreviews.value.length === 0) {
    loadTestIconPreviews(taskId);
  }
};

// 检查进度
const checkProgress = async () => {
  if (currentTaskId.value) {
    showProgressModal.value = true;

    if (isTestMode.value) {
      // 测试模式：如果还没有状态，启动模拟
      if (!generationStatus.value) {
        await simulateGeneration(currentTaskId.value);
      }

      // 如果没有壁纸预览，尝试加载
      if (wallpaperPreviews.value.length === 0) {
        setTimeout(() => {
          loadTestWallpaperPreviews(currentTaskId.value!);
        }, 1000);
      }
    } else {
      // 真实模式：开始轮询状态
      await startStatusPolling(currentTaskId.value);

      // 如果没有壁纸预览，尝试加载
      if (wallpaperPreviews.value.length === 0) {
        setTimeout(() => {
          loadWallpaperPreviews(currentTaskId.value!);
        }, 1000);
      }
    }
  }
};

// 添加记录最后图标刷新时间的变量
let lastIconRefreshTime = 0;
// 添加记录上次进度值的变量
let lastProgressValue = 0;

// 启动状态轮询
const startStatusPolling = async (taskId: string) => {
  if (statusPollInterval) return; // 如果已经在轮询，则不重复启动

  const updateStatus = async () => {
    try {
      const status = await ThemeService.getGenerationStatus(taskId);
      generationStatus.value = status;
      generationProgress.value = status.progress;
      completedSteps.value = status.completedSteps;
      totalSteps.value = status.totalSteps;

      if (status.resources) {
        generatedResources.value = status.resources.map((r) => ({
          ...r,
          thumbnail: `${r.url}?width=100&quality=80`,
        }));
      }

      // 记录当前步骤
      console.log("当前步骤:", status.current_step);
      console.log("当前状态:", status.status);
      console.log("资源:", status.resources);

      // 判断壁纸生成是否已完成
      const wallpaperStep = status.steps?.find(
        (step) => step.name === "wallpaper_generation"
      );
      const nextStepStarted = status.steps?.some(
        (step) =>
          ["ui_generation", "ui_split"].includes(step.name) &&
          ["running", "completed"].includes(step.status)
      );

      // 只有当壁纸步骤明确标记为已完成，且下一个步骤已开始或完成时才加载壁纸
      const wallpaperGenFullyCompleted =
        wallpaperStep?.status === "completed" && nextStepStarted;

      if (
        wallpaperGenFullyCompleted &&
        wallpaperPreviews.value.length === 0 &&
        !loadingPreviews.value
      ) {
        console.log(
          "壁纸生成已完全完成，UI生成阶段已开始，现在加载壁纸预览是安全的"
        );
        // 添加较长延迟，确保文件系统操作和后端处理完全完成
        setTimeout(() => {
          loadWallpaperPreviews(taskId);
        }, 5000); // 延长等待时间以减少资源消耗
      }

      // 当处于并行任务阶段，检测进度变化并刷新图标
      const isParallelTaskRunning =
        status.current_step === "parallel_tasks" &&
        status.steps?.find((step) => step.name === "parallel_tasks")?.status ===
          "running";

      // 获取并行任务的当前进度
      const parallelTaskStep = status.steps?.find(
        (step) => step.name === "parallel_tasks"
      );
      const currentProgress = parallelTaskStep?.progress || 0;

      if (isParallelTaskRunning) {
        // 检测进度是否有显著变化，或者很长时间没刷新过
        const progressChanged =
          Math.abs(currentProgress - lastProgressValue) >= 1; // 进度变化超过1%就触发
        const timeThreshold = Date.now() - lastIconRefreshTime > 10000; // 至少10秒刷新一次

        // 首次进入并行任务阶段
        if (
          iconPreviews.value.length === 0 &&
          !loadingIcons.value &&
          !iconLoadError.value
        ) {
          console.log("并行任务阶段开始，首次加载图标预览");
          lastProgressValue = currentProgress;
          loadIconPreviews(taskId);
        }
        // 当进度有显著变化，或者很长时间没刷新过时，自动刷新图标
        else if ((progressChanged || timeThreshold) && !loadingIcons.value) {
          console.log(
            `检测到进度变化或超过刷新间隔，自动刷新图标 (${lastProgressValue.toFixed(
              1
            )}% -> ${currentProgress.toFixed(1)}%)`
          );
          lastProgressValue = currentProgress;
          lastIconRefreshTime = Date.now();
          loadIconPreviews(taskId, true); // 传入true表示这是更新操作
        }
      }

      // 当生成完成时，确保加载所有预览图片
      if (status.status === "completed" || status.status === "success") {
        console.log("主题生成已完全完成，加载所有预览");
        // 添加延迟确保所有文件已准备就绪
        setTimeout(() => {
          loadPreviewImages(taskId);
        }, 5000); // 延长等待时间
        stopStatusPolling();
      } else if (status.status === "failed") {
        stopStatusPolling();
      }
    } catch (error) {
      console.error("获取状态失败:", error);
      stopStatusPolling();
    }
  };

  await updateStatus(); // 立即执行一次
  statusPollInterval = window.setInterval(updateStatus, 5000); // 从2秒改为5秒，减少请求频率
};

// 仅加载壁纸预览 - 添加重试机制和限制
const loadWallpaperPreviews = async (taskId: string) => {
  if (!taskId) return;
  if (loadingPreviews.value) return; // 防止重复请求

  // 检查重试次数
  if (retryCount.value >= maxRetries) {
    console.log(
      `已达到最大重试次数(${maxRetries})，停止自动重试，等待用户手动触发`
    );
    loadError.value = true;
    return;
  }

  loadingPreviews.value = true;
  loadError.value = false;

  try {
    console.log(
      `开始加载壁纸预览, taskId: ${taskId}, 重试次数: ${retryCount.value}`
    );

    // 检查当前是否已进入UI生成阶段或后续阶段
    const status = generationStatus.value;
    const isUIStageOrLater =
      status &&
      status.current_step &&
      [
        "ui_generation",
        "ui_split",
        "parallel_tasks",
        "color_extraction",
        "convert_to_jpg",
        "create_archive",
      ].includes(status.current_step);

    if (!isUIStageOrLater) {
      console.warn("当前尚未进入UI生成阶段，壁纸可能尚未准备好，延迟请求");
      setTimeout(() => {
        if (!loadingPreviews.value) {
          loadWallpaperPreviews(taskId);
        }
      }, 8000); // 延长等待时间
      loadingPreviews.value = false;
      return;
    }

    try {
      // 使用ThemeService获取壁纸预览
      const wallpaperResponse = await ThemeService.getPreview(
        taskId,
        "wallpaper"
      );

      // 处理预览数据，确保URL格式正确
      const processedResponse = ThemeService.processPreviewData(
        taskId,
        "wallpaper",
        wallpaperResponse
      );

      if (processedResponse.previews && processedResponse.previews.length > 0) {
        wallpaperPreviews.value = processedResponse.previews;
        console.log(
          "壁纸预览加载成功:",
          wallpaperPreviews.value.length,
          "张壁纸"
        );
        retryCount.value = 0; // 成功后重置重试计数
      } else {
        throw new Error("预览结果为空");
      }
    } catch (apiError) {
      console.warn("API请求失败:", apiError);
      throw apiError; // 继续向上抛出错误，由外部catch处理
    }
  } catch (error) {
    console.error("加载壁纸预览失败:", error);
    loadError.value = true;
    retryCount.value++; // 增加重试计数

    // 服务器连接被拒绝，可能需要更长时间等待
    if (retryCount.value < maxRetries) {
      setTimeout(() => {
        if (wallpaperPreviews.value.length === 0 && !loadingPreviews.value) {
          console.log(`服务器连接失败，第${retryCount.value}次重试`);
          loadWallpaperPreviews(taskId);
        }
      }, 10000); // 更长的等待时间避免频繁刷新
    }
  } finally {
    loadingPreviews.value = false;
  }
};

// 停止轮询
const stopStatusPolling = () => {
  if (statusPollInterval) {
    clearInterval(statusPollInterval);
    statusPollInterval = null;
  }
};

// 取消生成
const cancelGeneration = async () => {
  if (!currentTaskId.value) return;

  // 保存当前状态以便恢复
  let previousStatus = "running";
  if (generationStatus.value && generationStatus.value.status) {
    previousStatus = generationStatus.value.status;
  }

  try {
    // 显示取消中的状态
    if (generationStatus.value) {
      generationStatus.value.status = "terminating";
    }

    if (isTestMode.value) {
      // 测试模式：直接模拟取消流程
      console.log("测试模式：模拟取消任务");

      // 延迟一下以模拟取消过程
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // 更新UI状态为已终止
      if (generationStatus.value) {
        generationStatus.value.status = "terminated";
      }
    } else {
      // 真实模式：调用终止任务接口
      const response = await ThemeService.terminateGeneration(
        currentTaskId.value
      );
      console.log("任务终止响应:", response);

      // 更新UI状态为已终止
      if (generationStatus.value) {
        generationStatus.value.status = "terminated";
      }
    }

    // 停止状态轮询
    stopStatusPolling();

    // 显示取消成功的提示
    alert("任务已成功取消");

    // 清空当前任务ID，允许用户重新生成
    setTimeout(() => {
      generationStatus.value = null;
      currentTaskId.value = null;
    }, 2000);
  } catch (error) {
    console.error("取消任务失败:", error);

    // 恢复之前的状态
    if (generationStatus.value) {
      generationStatus.value.status = previousStatus;
    }

    // 显示错误提示
    alert("取消任务失败，请稍后重试");
  }
};

// 跳转到预览页面
const goToPreview = () => {
  if (currentTaskId.value) {
    if (isTestMode.value) {
      // 测试模式：传递测试模式标志
      router.push({
        path: `/theme-preview/${currentTaskId.value}`,
        query: { test_mode: "true" },
      });
    } else {
      // 正常模式
      router.push(`/theme-preview/${currentTaskId.value}`);
    }
  }
};

// 加载预览图片 - 添加更多错误处理
const loadPreviewImages = async (taskId: string) => {
  if (!taskId) return;

  loadingPreviews.value = true;
  loadError.value = false;
  loadingIcons.value = true;
  iconLoadError.value = false;

  try {
    // 加载壁纸预览
    try {
      const wallpaperResponse = await ThemeService.getPreview(
        taskId,
        "wallpaper"
      );
      const processedWallpaperResponse = ThemeService.processPreviewData(
        taskId,
        "wallpaper",
        wallpaperResponse
      );

      if (
        processedWallpaperResponse.previews &&
        processedWallpaperResponse.previews.length > 0
      ) {
        wallpaperPreviews.value = processedWallpaperResponse.previews;
      } else {
        console.warn("加载完整壁纸预览结果为空");
      }
    } catch (wallpaperError) {
      console.error("加载壁纸预览失败:", wallpaperError);
    }

    // 加载图标预览
    try {
      const iconResponse = await ThemeService.getPreview(taskId, "icon");
      const processedIconResponse = ThemeService.processPreviewData(
        taskId,
        "icon",
        iconResponse
      );

      if (
        processedIconResponse.previews &&
        processedIconResponse.previews.length > 0
      ) {
        iconPreviews.value = processedIconResponse.previews;
        lastIconRefreshTime = Date.now();
      } else {
        console.warn("加载完整图标预览结果为空");
      }
    } catch (iconError) {
      console.error("加载图标预览失败:", iconError);
    }
  } catch (error) {
    console.error("加载预览图片失败:", error);
    loadError.value = true;

    // 添加重试按钮提示
    if (
      wallpaperPreviews.value.length === 0 &&
      iconPreviews.value.length === 0
    ) {
      setTimeout(() => {
        retryLoadPreviews();
      }, 5000);
    }
  } finally {
    loadingPreviews.value = false;
    loadingIcons.value = false;
  }
};

// 处理图片加载完成
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement;
  if (img) {
    img.classList.remove("opacity-0");
    img.classList.add("opacity-100");
  }
};

// 处理图片加载错误
const handleImageError = (index: number) => {
  console.error(`图片加载失败: ${index}`);
  loadError.value = true;
};

// 重试加载预览 - 重置重试计数
const retryLoadPreviews = async () => {
  if (!currentTaskId.value) return;

  loadError.value = false;
  retryCount.value = 0; // 手动重试时重置计数
  await loadWallpaperPreviews(currentTaskId.value);
};

// 添加强制加载壁纸的方法
const forceLoadWallpapers = () => {
  if (currentTaskId.value) {
    console.log("手动强制加载壁纸预览");
    if (loadingPreviews.value) return; // 防止重复请求

    // 检查是否处于可以安全加载壁纸的阶段
    if (!canSafelyLoadWallpaper.value && !isWallpaperGenCompleted.value) {
      console.warn("当前阶段不安全，壁纸可能尚未生成完成");
      loadError.value = true;
      return;
    }

    // 重置重试计数
    retryCount.value = 0;
    loadWallpaperPreviews(currentTaskId.value);
  }
};

// 组件卸载时清理
onUnmounted(() => {
  stopStatusPolling();
});

// 添加图片查看器状态
const showImageViewer = ref(false);
const viewerImages = ref<string[]>([]);
const currentImageIndex = ref(0);

// 打开图片查看器
const openImageViewer = (images: string[], index: number) => {
  viewerImages.value = images;
  currentImageIndex.value = index;
  showImageViewer.value = true;
};

// 查看所有壁纸
const openAllWallpapers = () => {
  openImageViewer(
    wallpaperPreviews.value.map((wp) => wp.url),
    0
  );
};

// 查看所有图标
const openAllIcons = () => {
  openImageViewer(
    iconPreviews.value.map((ic) => ic.url),
    0
  );
};

// 重置生成状态，允许用户重新开始
const resetGeneration = () => {
  // 停止任何可能正在运行的轮询
  stopStatusPolling();

  // 清除状态
  generationStatus.value = null;
  currentTaskId.value = null;
  generationProgress.value = 0;
  completedSteps.value = 0;
  totalSteps.value = 0;
  generatedResources.value = [];
  wallpaperPreviews.value = [];
  iconPreviews.value = [];

  // 清除错误状态
  loadError.value = false;
  retryCount.value = 0;
  loadingPreviews.value = false;

  console.log("已重置生成状态，用户可以开始新的生成任务");
};

// 新增方法：仅加载图标预览
const loadIconPreviews = async (taskId: string, isUpdate = false) => {
  if (!taskId) return;
  if (loadingIcons.value) return; // 防止重复请求

  loadingIcons.value = true;
  if (!isUpdate) {
    // 只有在首次加载时才重置错误状态
    iconLoadError.value = false;
  }

  try {
    console.log(`${isUpdate ? "更新" : "开始加载"}图标预览, taskId: ${taskId}`);

    // 获取图标预览
    const iconResponse = await ThemeService.getPreview(taskId, "icon");
    const processedIconResponse = ThemeService.processPreviewData(
      taskId,
      "icon",
      iconResponse
    );

    if (
      processedIconResponse.previews &&
      processedIconResponse.previews.length > 0
    ) {
      // 如果是更新操作，合并新的图标，而不是替换
      if (isUpdate && iconPreviews.value.length > 0) {
        // 获取所有现有图标的文件名，用于检查是否有新图标
        const existingFileNames = new Set(
          iconPreviews.value.map((icon) => icon.file_name)
        );

        // 找出新图标（不在现有列表中的图标）
        const newIcons = processedIconResponse.previews.filter(
          (icon) => !existingFileNames.has(icon.file_name)
        );

        if (newIcons.length > 0) {
          console.log(`发现${newIcons.length}个新图标，添加到列表`);
          // 按照id排序，确保图标按顺序显示
          const combinedIcons = [...iconPreviews.value, ...newIcons].sort(
            (a, b) => (a.id || 0) - (b.id || 0)
          );
          iconPreviews.value = combinedIcons;
          // 强制更新UI
          iconPreviews.value = [...iconPreviews.value];
        } else {
          console.log("没有发现新图标");
        }
      } else {
        // 首次加载，直接设置（确保按id排序）
        iconPreviews.value = processedIconResponse.previews.sort(
          (a, b) => (a.id || 0) - (b.id || 0)
        );
      }

      console.log(`当前共有${iconPreviews.value.length}个图标预览`);
      lastIconRefreshTime = Date.now();
    } else {
      console.warn("图标预览结果为空");
      if (!isUpdate) {
        // 只有在首次加载时才设置空数组
        iconPreviews.value = [];
      }
    }
  } catch (error) {
    console.error("加载图标预览失败:", error);
    if (!isUpdate) {
      // 只有在首次加载出错时才设置错误状态
      iconLoadError.value = true;
    }
  } finally {
    loadingIcons.value = false;
  }
};

// 添加图片查看器状态
const showAllIcons = ref(false);
</script>

<style scoped>
/* 滑块样式 */
input[type="range"] {
  -webkit-appearance: none;
  background: transparent;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #7c3aed;
  margin-top: -12px;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #7c3aed;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* 背景渐变 */
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
    radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
    radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* 暗色背景 */
.bg-dark-800\/50 {
  background-color: rgba(30, 41, 59, 0.5);
}

/* 玻璃拟态按钮悬停效果 */
.glass-card.bg-gradient-to-r:hover {
  box-shadow: 0 10px 25px rgba(124, 58, 237, 0.3);
}

/* 增强毛玻璃效果 */
.glass-panel {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 玻璃预览卡片 */
.glass-preview-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.glass-preview-card:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass-preview-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass-preview-card:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

.preview-img-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.wallpaper-card .preview-img-container {
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 */
}

.icon-card .preview-img-container {
  height: 0;
  padding-bottom: 100%; /* 1:1 比例 */
}

.preview-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.wallpaper-card .preview-img {
  object-fit: cover;
}

.icon-card .preview-img {
  object-fit: contain;
  padding: 0.5rem;
}

.glass-preview-card:hover .preview-img {
  transform: scale(1.05);
}

.preview-card-info {
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
}

.preview-filename {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 精美按钮样式 */
.fancy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.fancy-button:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
}

.fancy-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
}

.fancy-button:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fancy-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* 参考样式卡片增强 */
.glass-preview-card.h-40 {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(25, 35, 55, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
}

.glass-preview-card.h-40:hover {
  transform: translateY(-8px) scale(1.02);
  background: rgba(30, 41, 59, 0.6);
  box-shadow: 0 15px 30px rgba(124, 58, 237, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-preview-card.h-40::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: skewX(-25deg);
  z-index: 2;
  transition: all 0.6s ease;
}

.glass-preview-card.h-40:hover::before {
  left: 150%;
  transition: all 0.8s ease;
}

.glass-preview-card.h-40 .preview-img {
  transition: transform 0.8s ease;
}

.glass-preview-card.h-40:hover .preview-img {
  transform: scale(1.08);
}

.ring-offset-dark-800 {
  --tw-ring-offset-color: rgba(17, 24, 39, 0.8);
}
.checkmark {
  position: absolute;
  top: -12px; /* 调整位置以适应更大的尺寸 */
  right: -12px; /* 调整位置以适应更大的尺寸 */
  width: 54px; /* 原尺寸36px，改大一半为54px */
  height: 54px; /* 原尺寸36px，改大一半为54px */
  z-index: 20;
  overflow: visible;
}

.checkmark::after {
  content: "";
  position: absolute;
  width: 36px; /* 原尺寸24px，改大一半为36px */
  height: 36px; /* 原尺寸24px，改大一半为36px */
  top: 9px; /* 调整位置以适应更大的尺寸 */
  left: 9px; /* 调整位置以适应更大的尺寸 */
  background: theme('colors.dark-400'); /* 改为科技感的高级蓝色 */
  border-radius: 50%;
  box-shadow: 0 0 18px rgba(49, 130, 206, 0.4); /* 调整阴影以适应更大的尺寸和颜色 */
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='white' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
  background-size: 24px; /* 调整背景图片大小以适应更大的尺寸 */
  background-position: center;
  background-repeat: no-repeat;
  animation: pulse-dot 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}
</style> 