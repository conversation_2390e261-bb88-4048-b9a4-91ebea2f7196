// 壁纸管理Store - 动态壁纸和色彩提取
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useThemeStore } from './theme'

export const useWallpaperStore = defineStore('wallpaper', () => {
  // === 状态 ===
  const currentWallpaper = ref(null)
  const wallpaperType = ref('image') // 'image', 'video', 'gradient', 'pattern'
  const isLoading = ref(false)
  const error = ref(null)
  
  // 预设壁纸列表
  const presetWallpapers = ref([
    {
      id: 'default-1',
      name: '默认渐变',
      type: 'gradient',
      url: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      thumbnail: '/wallpapers/thumbnails/gradient-1.jpg',
      dominantColor: '#667eea',
      brightness: 0.6
    },
    {
      id: 'nature-1',
      name: '自然风光',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      dominantColor: '#4a7c59',
      brightness: 0.4
    },
    {
      id: 'city-1',
      name: '城市夜景',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=1920&h=1080&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=400&h=300&fit=crop',
      dominantColor: '#1a1a2e',
      brightness: 0.2
    },
    {
      id: 'abstract-1',
      name: '抽象艺术',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1557672172-298e090bd0f1?w=1920&h=1080&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1557672172-298e090bd0f1?w=400&h=300&fit=crop',
      dominantColor: '#ff6b6b',
      brightness: 0.7
    }
  ])

  // 用户自定义壁纸
  const customWallpapers = ref([])

  // === 计算属性 ===
  const allWallpapers = computed(() => [
    ...presetWallpapers.value,
    ...customWallpapers.value
  ])

  const currentWallpaperData = computed(() => {
    if (!currentWallpaper.value) return null
    return allWallpapers.value.find(w => w.id === currentWallpaper.value)
  })

  // === 方法 ===
  const setWallpaper = async (wallpaperId) => {
    isLoading.value = true
    error.value = null
    
    try {
      const wallpaper = allWallpapers.value.find(w => w.id === wallpaperId)
      if (!wallpaper) {
        throw new Error('Wallpaper not found')
      }

      currentWallpaper.value = wallpaperId
      
      // 应用壁纸到DOM
      await applyWallpaperToDOM(wallpaper)
      
      // 更新主题颜色
      const themeStore = useThemeStore()
      themeStore.setWallpaperColors(
        wallpaper.brightness,
        wallpaper.dominantColor,
        getContrastColor(wallpaper.dominantColor)
      )
      
      // 保存到本地存储
      saveWallpaperPreference(wallpaperId)
      
    } catch (err) {
      error.value = err.message
      console.error('Failed to set wallpaper:', err)
    } finally {
      isLoading.value = false
    }
  }

  const applyWallpaperToDOM = async (wallpaper) => {
    const body = document.body
    
    // 清除之前的壁纸
    body.style.backgroundImage = ''
    body.style.background = ''
    
    switch (wallpaper.type) {
      case 'image':
        body.style.backgroundImage = `url(${wallpaper.url})`
        body.style.backgroundSize = 'cover'
        body.style.backgroundPosition = 'center'
        body.style.backgroundRepeat = 'no-repeat'
        body.style.backgroundAttachment = 'fixed'
        break
        
      case 'gradient':
        body.style.background = wallpaper.url
        break
        
      case 'video':
        // 视频壁纸需要特殊处理
        await applyVideoWallpaper(wallpaper.url)
        break
        
      case 'pattern':
        body.style.backgroundImage = `url(${wallpaper.url})`
        body.style.backgroundSize = 'repeat'
        body.style.backgroundPosition = 'center'
        break
    }
  }

  const applyVideoWallpaper = async (videoUrl) => {
    // 创建视频背景元素
    let videoElement = document.getElementById('wallpaper-video')
    
    if (!videoElement) {
      videoElement = document.createElement('video')
      videoElement.id = 'wallpaper-video'
      videoElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        object-fit: cover;
        z-index: -1;
        pointer-events: none;
      `
      videoElement.autoplay = true
      videoElement.loop = true
      videoElement.muted = true
      document.body.appendChild(videoElement)
    }
    
    videoElement.src = videoUrl
  }

  const extractColorsFromImage = async (imageUrl) => {
    return new Promise((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        
        try {
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          const colors = analyzeImageColors(imageData)
          resolve(colors)
        } catch (error) {
          console.warn('Failed to extract colors:', error)
          resolve({
            dominantColor: '#ffffff',
            brightness: 0.5,
            contrastColor: '#000000'
          })
        }
      }
      
      img.onerror = () => {
        resolve({
          dominantColor: '#ffffff',
          brightness: 0.5,
          contrastColor: '#000000'
        })
      }
      
      img.src = imageUrl
    })
  }

  const analyzeImageColors = (imageData) => {
    const data = imageData.data
    const colorCounts = {}
    let totalBrightness = 0
    let pixelCount = 0
    
    // 采样分析（每10个像素采样一次以提高性能）
    for (let i = 0; i < data.length; i += 40) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      
      // 计算亮度
      const brightness = (r * 0.299 + g * 0.587 + b * 0.114) / 255
      totalBrightness += brightness
      pixelCount++
      
      // 量化颜色以减少计算
      const quantizedColor = `${Math.floor(r / 32) * 32},${Math.floor(g / 32) * 32},${Math.floor(b / 32) * 32}`
      colorCounts[quantizedColor] = (colorCounts[quantizedColor] || 0) + 1
    }
    
    // 找到主色调
    const dominantColorKey = Object.keys(colorCounts).reduce((a, b) => 
      colorCounts[a] > colorCounts[b] ? a : b
    )
    
    const [r, g, b] = dominantColorKey.split(',').map(Number)
    const dominantColor = `rgb(${r}, ${g}, ${b})`
    const averageBrightness = totalBrightness / pixelCount
    const contrastColor = getContrastColor(dominantColor)
    
    return {
      dominantColor,
      brightness: averageBrightness,
      contrastColor
    }
  }

  const getContrastColor = (color) => {
    // 简单的对比色计算
    const rgb = color.match(/\d+/g)
    if (!rgb) return '#000000'
    
    const [r, g, b] = rgb.map(Number)
    const brightness = (r * 0.299 + g * 0.587 + b * 0.114) / 255
    
    return brightness > 0.5 ? '#000000' : '#ffffff'
  }

  const addCustomWallpaper = async (file) => {
    isLoading.value = true
    error.value = null
    
    try {
      // 创建预览URL
      const url = URL.createObjectURL(file)
      
      // 提取颜色信息
      const colors = await extractColorsFromImage(url)
      
      const customWallpaper = {
        id: `custom-${Date.now()}`,
        name: file.name,
        type: 'image',
        url: url,
        thumbnail: url,
        ...colors,
        isCustom: true
      }
      
      customWallpapers.value.push(customWallpaper)
      saveCustomWallpapers()
      
      return customWallpaper.id
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const removeCustomWallpaper = (wallpaperId) => {
    const index = customWallpapers.value.findIndex(w => w.id === wallpaperId)
    if (index > -1) {
      const wallpaper = customWallpapers.value[index]
      if (wallpaper.url.startsWith('blob:')) {
        URL.revokeObjectURL(wallpaper.url)
      }
      customWallpapers.value.splice(index, 1)
      saveCustomWallpapers()
    }
  }

  const saveWallpaperPreference = (wallpaperId) => {
    try {
      localStorage.setItem('ai-hmi-current-wallpaper', wallpaperId)
    } catch (error) {
      console.warn('Failed to save wallpaper preference:', error)
    }
  }

  const saveCustomWallpapers = () => {
    try {
      // 只保存非blob URL的壁纸信息
      const persistentWallpapers = customWallpapers.value.filter(w => !w.url.startsWith('blob:'))
      localStorage.setItem('ai-hmi-custom-wallpapers', JSON.stringify(persistentWallpapers))
    } catch (error) {
      console.warn('Failed to save custom wallpapers:', error)
    }
  }

  const loadWallpaperPreferences = () => {
    try {
      // 加载当前壁纸
      const currentId = localStorage.getItem('ai-hmi-current-wallpaper')
      if (currentId) {
        setWallpaper(currentId)
      } else {
        // 设置默认壁纸
        setWallpaper('default-1')
      }
      
      // 加载自定义壁纸
      const saved = localStorage.getItem('ai-hmi-custom-wallpapers')
      if (saved) {
        customWallpapers.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('Failed to load wallpaper preferences:', error)
      setWallpaper('default-1')
    }
  }

  const initializeWallpaper = () => {
    loadWallpaperPreferences()
  }

  return {
    // 状态
    currentWallpaper,
    wallpaperType,
    isLoading,
    error,
    presetWallpapers,
    customWallpapers,
    
    // 计算属性
    allWallpapers,
    currentWallpaperData,
    
    // 方法
    setWallpaper,
    applyWallpaperToDOM,
    extractColorsFromImage,
    addCustomWallpaper,
    removeCustomWallpaper,
    saveWallpaperPreference,
    loadWallpaperPreferences,
    initializeWallpaper
  }
})
