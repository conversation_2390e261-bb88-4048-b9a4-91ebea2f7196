<template>
  <div class="tech-bg min-h-screen py-4 relative">
    <!-- 页面背景 - 增强背景效果 -->
    <div class="fixed top-0 left-0 w-full h-full z-0">
      <div class="w-full h-full bg-dark-900 opacity-90"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-tech-pattern opacity-10"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-radial from-purple-900/20 via-dark-900/5 to-dark-900/10"></div>
    </div>

    <div class="container relative z-10 mx-auto px-4">
      <!-- 顶部导航 - 美化标题区 -->
      <div class="glass-card py-4 px-8 flex items-center mb-6 border-b border-purple-600/20">
        <button class="text-gray-300 hover:text-purple-400 transition-colors mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <div>
          <h1 class="text-2xl font-bold text-white flex items-center">
            <span class="gradient-text">星云幻甲</span>
            <span class="text-gray-400 text-lg ml-2">|</span>
            <span class="text-lg ml-2 text-gray-300">车衣设计助手</span>
          </h1>
        </div>
        <!-- 添加3D查看按钮 -->
        <div class="ml-auto flex items-center gap-4">
          <button 
            @click="openCarShowcase" 
            class="px-5 py-2.5 rounded-full transition-all duration-300 relative overflow-hidden group"
          >
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-600/80 to-blue-600/80 opacity-80 group-hover:opacity-100 backdrop-blur-md z-0 transition-opacity"></span>
            <span class="absolute inset-0 w-full h-full border border-white/20 rounded-full blur z-0"></span>
            
            <!-- 粒子容器 -->
            <div class="car3d-particles absolute inset-0 w-full h-full pointer-events-none z-0"></div>
            
            <!-- 按钮文字 -->
            <span class="relative z-10 flex items-center gap-2 text-white font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 16v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2"></path>
                <rect x="8" y="2" width="10" height="16" rx="2"></rect>
              </svg>
              3D超维展示
            </span>
          </button>
          
          <button 
            @click="showConfigDialog = true" 
            class="w-10 h-10 rounded-full flex items-center justify-center bg-gray-800/60 hover:bg-gray-700/80 text-gray-300 hover:text-white transition-colors border border-gray-700"
            title="配置3D展示地址"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 主内容区域 - 三栏布局 -->
      <div class="flex flex-col md:flex-row gap-6 min-h-[calc(100vh-140px)]">
        <!-- 左侧控制面板 (1/4) -->
        <div class="w-full md:w-1/4">
          <CarArmorPanel ref="carArmorPanelRef" @update:car-settings="updateCarSettings" @apply="applyCarArmor" @reset="resetCarArmor" />
        </div>

        <!-- 中间3D模型展示区域 (1/2) - 美化展示区 -->
        <div class="w-full md:w-1/2 h-[600px] md:h-auto">
          <div class="glass-card h-full p-3 glow-border-subtle">
            <ThreeCarModel ref="threeCarModelRef" :model-path="modelPath" :car-color="carSettings.color" :material-type="carSettings.material" :rotation-speed="carSettings.rotationSpeed" :enable-particles="carSettings.enableParticles" :particle-count="carSettings.particleCount" :initial-scale="0.05" :car-parts-selector="['body', 'hood', 'exterior', 'door', 'chassis', 'bumper', 'fender', 'Panel']" @model-loaded="onModelLoaded" @loading-progress="onLoadingProgress" @error="onModelError" />
          </div>
        </div>

        <!-- 右侧信息面板 (1/4) -->
        <div class="w-full md:w-1/4">
          <CarArmorInfo :current-armor="currentArmor" @select-recommended="selectRecommendedArmor" />
        </div>
      </div>
    </div>

    <!-- 消息通知 - 增强通知样式 -->
    <div v-if="showNotification" class="fixed bottom-4 right-4 z-50 animate-fade-in-up">
      <div class="glass-card py-3 px-5 flex items-center text-white max-w-sm border-l-4" :class="notificationType === 'success' ? 'border-green-500' : 'border-red-500'">
        <svg v-if="notificationType === 'success'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <svg v-else-if="notificationType === 'error'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ notificationMessage }}</span>
      </div>
    </div>
    
    <!-- 配置对话框 -->
    <JumpDialog v-model:visible="showConfigDialog" @saved="onConfigSaved" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { useJumpConfig } from "../config/jumpConfig";

const router = useRouter();

// 异步加载组件
const ThreeCarModel = defineAsyncComponent(
  () => import("../components/ThreeCarModel.vue")
);
const CarArmorPanel = defineAsyncComponent(
  () => import("../components/CarArmorPanel.vue")
);
const CarArmorInfo = defineAsyncComponent(
  () => import("../components/CarArmorInfo.vue")
);
const JumpDialog = defineAsyncComponent(
  () => import("../components/JumpDialog.vue")
);

// 定义类型
interface CarSettings {
  name: string;
  color: string;
  material: string;
  rotationSpeed: number;
  enableParticles: boolean;
  particleCount: number;
}

interface Armor extends CarSettings {
  appliedAt?: Date;
}

// 引用
const threeCarModelRef = ref(null);
const carArmorPanelRef = ref<any>(null);

// 模型路径
const modelPath = ref("/3D/fbx/qiyuan.fbx");

// 车辆设置 - 将初始旋转速度设置为0
const carSettings = ref<CarSettings>({
  name: "星云幻甲 - 璀璨银河",
  color: "#8b5cf6", // 默认紫色
  material: "glossy",
  rotationSpeed: 0, // 确保默认不旋转
  enableParticles: true,
  particleCount: 1500,
});

// 当前应用的车衣
const currentArmor = ref<Armor | null>(null);

// 通知消息
const showNotification = ref(false);
const notificationMessage = ref("");
const notificationType = ref("success");

// 配置对话框可见性
const showConfigDialog = ref(false);

// 粒子效果动画参数
let particles: HTMLElement[] = [];
let animationFrameId: number | null = null;

// 模型加载事件
const onModelLoaded = (data: { success: boolean }) => {
  if (data.success) {
    showNotify("车辆模型加载成功", "success");
  }
};

// 加载进度事件
const onLoadingProgress = (percent: number) => {
  // 可以在UI中显示加载进度
  console.log(`模型加载进度: ${percent}%`);
};

// 模型加载错误事件
const onModelError = (error: { message: string }) => {
  showNotify(`模型加载失败: ${error.message}`, "error");
};

// 更新车辆设置
const updateCarSettings = (settings: CarSettings) => {
  carSettings.value = { ...settings };
};

// 应用车衣
const applyCarArmor = (settings: CarSettings) => {
  // 设置当前应用的车衣信息
  currentArmor.value = {
    ...settings,
    appliedAt: new Date(),
  };

  showNotify(`成功应用车衣: ${settings.name}`, "success");
};

// 重置车衣
const resetCarArmor = (settings: CarSettings) => {
  carSettings.value = { ...settings };
  showNotify("已重置为默认设置", "success");
};

// 选择推荐车衣
const selectRecommendedArmor = (armor: CarSettings) => {
  // 更新控制面板
  if (carArmorPanelRef.value && typeof carArmorPanelRef.value.resetChanges === 'function') {
    carArmorPanelRef.value.resetChanges();
  }

  // 更新设置
  carSettings.value = { ...armor };

  // 自动应用
  applyCarArmor(armor);
};

// 显示通知
const showNotify = (message: string, type: 'success' | 'error' = "success") => {
  notificationMessage.value = message;
  notificationType.value = type;
  showNotification.value = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    showNotification.value = false;
  }, 3000);
};

// 打开3D汽车展示
const openCarShowcase = () => {
  try {
    const url = useJumpConfig.getCar3dUrl();
    window.location.href = url; // 修改为在同一标签页中跳转
    showNotify("正在打开3D超维展示模块", "success");
  } catch (error) {
    showNotify("打开3D展示模块失败", "error");
    console.error("打开3D展示失败:", error);
  }
};

// 配置保存成功
const onConfigSaved = () => {
  showNotify("3D展示地址配置已保存", "success");
};

// 创建粒子效果
const createParticles = () => {
  const container = document.querySelector('.car3d-particles');
  if (!container) return;
  
  particles = [];
  
  // 创建30个粒子
  for (let i = 0; i < 30; i++) {
    const particle = document.createElement('div');
    
    // 随机尺寸
    const size = Math.random() * 4 + 2;
    
    // 随机位置
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    
    // 随机颜色
    const hue = Math.random() * 60 + 220; // 蓝紫色系
    
    // 设置样式
    Object.assign(particle.style, {
      position: 'absolute',
      width: `${size}px`,
      height: `${size}px`,
      borderRadius: '50%',
      backgroundColor: `hsla(${hue}, 100%, 70%, 0.8)`,
      boxShadow: `0 0 ${size * 2}px hsla(${hue}, 100%, 70%, 0.6)`,
      left: `${x}%`,
      top: `${y}%`,
      opacity: '0',
      pointerEvents: 'none',
    });
    
    container.appendChild(particle);
    particles.push(particle);
  }
};

// 粒子动画
const animateParticles = () => {
  particles.forEach(particle => {
    const x = parseFloat(particle.style.left);
    const y = parseFloat(particle.style.top);
    
    // 随机移动距离
    const dx = (Math.random() - 0.5) * 0.5;
    const dy = (Math.random() - 0.5) * 0.5;
    
    // 为每个粒子设置随机不透明度
    const opacity = Math.random() * 0.5 + 0.2;
    
    // 应用新位置和不透明度
    particle.style.left = `${(x + dx + 100) % 100}%`;
    particle.style.top = `${(y + dy + 100) % 100}%`;
    particle.style.opacity = opacity.toString();
  });
  
  animationFrameId = requestAnimationFrame(animateParticles);
};

// 页面加载时
onMounted(() => {
  // 预设一个初始车衣
  currentArmor.value = {
    ...carSettings.value,
    appliedAt: new Date(),
  };
  
  // 创建粒子效果
  createParticles();
  
  // 启动粒子动画
  animationFrameId = requestAnimationFrame(animateParticles);
});

// 清理资源
onBeforeUnmount(() => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
  }
  
  // 清除粒子
  particles.forEach(particle => {
    if (particle.parentNode) {
      particle.parentNode.removeChild(particle);
    }
  });
  particles = [];
});
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  overflow-x: hidden;
  min-height: 100vh;
}

.glass-card {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.07);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

.glow-border-subtle {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.2),
    inset 0 0 10px rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.gradient-text {
  background: linear-gradient(to right, #8b5cf6, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.bg-gradient-radial {
  background-image: radial-gradient(var(--tw-gradient-stops));
}

.bg-tech-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238b5cf6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.bg-dark-900 {
  background-color: #0f172a;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@media (max-width: 768px) {
  .min-h-\[calc\(100vh-140px\)\] {
    min-height: auto;
  }
}

/* 粒子容器样式 */
.car3d-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}
</style>
