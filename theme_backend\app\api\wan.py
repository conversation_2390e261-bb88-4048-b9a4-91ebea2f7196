from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional
import json
import traceback
import os
import random
import httpx
import aiofiles
from pathlib import Path
import asyncio
from datetime import datetime
from ..core.mysql_util import connect_PBDB,update_data,insert_data_and_get_id
from ..core.minio_util import async_put_file
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    logger
)
from ..core.config import settings

router = APIRouter()
server_name="wan_server"
class TextToVideoRequest(BaseModel):
    """文本生成视频请求模型"""
    prompt: str
    negative_prompt: Optional[str] = Field(
        default="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
        description="负向提示词，用于控制不希望出现的内容"
    )
    width: Optional[int] = Field(default=832, description="视频宽度")
    height: Optional[int] = Field(default=480, description="视频高度")
    num_frames: Optional[int] = Field(default=81, description="视频帧数")
    seed: Optional[int] = Field(default=None, description="随机种子，不提供则随机生成")
    task_id: str = Field(..., description="任务ID，用于文件保存和标识")
    taskId: Optional[str] = Field(default=None, description="视频文件名标识，如不提供则使用task_id")
    
def build_text_to_video_workflow(request: TextToVideoRequest) -> dict:
    """构建文生视频工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("wan_t2v.json")
        
        # 设置随机种子（如果未提供）
        if request.seed is None:
            request.seed = random.randint(1, 9999999999999999)
        
        # 设置文件标识（如果未提供）
        if request.taskId is None:
            request.taskId = request.task_id
            
        # 设置提示词（节点16）
        if "16" not in workflow:
            logger.error("工作流中找不到节点16")
            raise HTTPException(status_code=500, detail="Node 16 not found in workflow")
            
        workflow["16"]["inputs"]["positive_prompt"] = request.prompt
        workflow["16"]["inputs"]["negative_prompt"] = request.negative_prompt
        logger.info(f"设置正向提示词: {request.prompt}")
        logger.info(f"设置负向提示词: {request.negative_prompt}")
        
        # 设置采样参数（节点27）
        if "27" not in workflow:
            logger.error("工作流中找不到节点27")
            raise HTTPException(status_code=500, detail="Node 27 not found in workflow")
            
        workflow["27"]["inputs"]["seed"] = request.seed
        logger.info(f"设置随机种子: {request.seed}")
        
        # 设置视频参数（节点37）
        if "37" not in workflow:
            logger.error("工作流中找不到节点37")
            raise HTTPException(status_code=500, detail="Node 37 not found in workflow")
            
        workflow["37"]["inputs"]["width"] = request.width
        workflow["37"]["inputs"]["height"] = request.height
        workflow["37"]["inputs"]["num_frames"] = request.num_frames
        logger.info(f"设置视频分辨率: {request.width}x{request.height}, 帧数: {request.num_frames}")
        
        # 设置保存路径和前缀（节点30）
        if "30" not in workflow:
            logger.error("工作流中找不到节点30")
            raise HTTPException(status_code=500, detail="Node 30 not found in workflow")
        
        file_prefix = f"Wan/WanVideo2_1_T2V_{request.taskId}"
        workflow["30"]["inputs"]["filename_prefix"] = file_prefix
        logger.info(f"设置文件前缀: {file_prefix}")
        
        return workflow, "30"  # 30是视频输出节点
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/text-to-video", summary="文本生成视频", description="根据文本描述生成一段视频")
async def generate_video(request: TextToVideoRequest):
    connection = None
    public_url = ''
    """从文本生成视频的端点"""
    try:
        connection = connect_PBDB()
        logger.info(f"\n========== 开始生成视频 ==========")
        logger.info(f"提示词: {request.prompt}")
        logger.info(f"任务ID: {request.task_id}")

        if not request.prompt or not request.task_id:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        logId = insert_data_and_get_id(connection,'theme_task_log',
                                       {'task_id':request.task_id,
                                        'query':request.prompt,
                                        'task_type':'video',
                                        'task_status':'running',
                                        'create_time':datetime.now(),
                                        'update_time':datetime.now()}
                                       )
        # 构建工作流
        workflow, output_node_id = build_text_to_video_workflow(request)
        logger.info(f"当前工作流配置已准备完成")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待视频生成（增加超时时间到20分钟）
        subfolder = "Wan"  # 根据节点30中的设置
        
        # 专门为视频输出实现定制检查
        prompt_id = data["prompt_id"]
        video_url = None
        retries = 0
        max_retries = 600  # 20分钟超时
        video_filename = None
        
        while retries < max_retries:
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{settings.API_URLS['wan_server']}/history/{prompt_id}")
                    response.raise_for_status()
                    history = response.json()
                    
                    if prompt_id in history:
                        prompt_data = history[prompt_id]
                        if "status" in prompt_data and prompt_data["status"].get("completed", False):
                            logger.info(f"视频生成任务已完成 (prompt_id: {prompt_id})")
                            
                            # 视频节点的输出
                            if "outputs" in prompt_data and output_node_id in prompt_data["outputs"]:
                                output_data = prompt_data["outputs"][output_node_id]
                                logger.debug(f"节点输出数据: {json.dumps(output_data, indent=2)}")
                                
                                # 查找视频文件名
                                # 尝试多种可能的键
                                if "videos" in output_data and len(output_data["videos"]) > 0:
                                    # 第一种方式：直接从视频列表中获取
                                    video_info = output_data["videos"][0]
                                    video_filename = video_info.get("filename")
                                    subfolder = video_info.get("subfolder", "Wan")
                                elif "input" in output_data and "filename" in output_data["input"]:
                                    # 第二种方式：从输入信息中获取
                                    video_filename = output_data["input"]["filename"]
                                    subfolder = output_data["input"].get("subfolder", "Wan")
                                elif "file" in output_data:
                                    # 第三种方式：可能有直接的文件路径
                                    video_filename = output_data["file"]
                                    subfolder = "Wan"
                                
                                # 如果找到了文件名
                                if video_filename:
                                    # 构建视频URL
                                    # 格式参考: http://27.159.93.61:8193/api/view?filename=WanVideo2_1_T2V_00006.mp4&subfolder=Wan&type=output&format=video%2Fh264-mp4&frame_rate=16
                                    video_url = f"{settings.API_URLS['wan_server']}/view?filename={video_filename}&subfolder={subfolder}&type=output&format=video%2Fh264-mp4&frame_rate=16&t={int(datetime.now().timestamp())}"
                                    logger.info(f"找到生成的视频URL: {video_url}")
                                    break
                                
                                # 如果上面的方法都找不到文件名，尝试直接构建
                                if not video_filename:
                                    expected_filename = f"WanVideo2_1_T2V_{request.taskId}_00001.mp4"
                                    video_url = f"{settings.API_URLS['wan_server']}/view?filename={expected_filename}&subfolder=Wan&type=output&format=video%2Fh264-mp4&frame_rate=16&t={int(datetime.now().timestamp())}"
                                    logger.info(f"使用预期文件名构建URL: {video_url}")
                                    video_filename = expected_filename
                                    break
                            
                            # 如果无法找到视频数据
                            if not video_url:
                                logger.error("视频节点输出数据中找不到视频文件")
                                logger.debug(f"节点输出数据: {json.dumps(prompt_data.get('outputs', {}), indent=2)}")
                        
                        elif "status" in prompt_data and "error" in prompt_data["status"]:
                            logger.error(f"视频生成失败，错误信息: {prompt_data['status']['error']}")
                            raise HTTPException(status_code=500, detail=f"Video generation failed: {prompt_data['status']['error']}")
            
            except Exception as e:
                if isinstance(e, HTTPException):
                    raise
                logger.error(f"检查视频状态出错: {str(e)}")
            
            retries += 1
            await asyncio.sleep(2)  # 每2秒检查一次
        
        if not video_url:
            logger.error("视频生成失败或超时")
            raise HTTPException(status_code=500, detail="Video generation failed or timed out")
            
        logger.info(f"生成的视频URL: {video_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{request.task_id}/video"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存视频
        if not video_filename:
            video_filename = f"WanVideo2_1_T2V_{request.taskId}.mp4"
        save_filename = f"{request.taskId}.mp4"
        video_path = save_dir / save_filename
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(video_url)
                response.raise_for_status()
                async with aiofiles.open(video_path, "wb") as f:
                    await f.write(response.content)
                logger.info(f"视频已保存到: {video_path}")
            except Exception as e:
                logger.error(f"保存视频失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save video: {str(e)}")

        # 构建公开访问URL
        public_url = f"/ui_source/mainLine_{request.task_id}/video/{save_filename}"
        logger.info(f"公开访问URL: {public_url}")

        logger.info(f"========== 视频生成完成 ==========\n")
        logger.info(f"上传视频: {public_url}")
        with video_path.open('rb') as video_stream:
            async_put_file({
                'bucketName': 'changan-video',
                'objectName': request.task_id + "/" + video_path.name,
                'fileType': 'mp4',
                'fileStream': video_stream
            })
        logger.info(f"完成上传视频")

        return {
            "prompt_id": data["prompt_id"],
            "video_url": public_url,
            "original_video_url": video_url,  # 添加原始视频URL，方便调试
            "seed": request.seed,
            "width": request.width,
            "height": request.height,
            "num_frames": request.num_frames,
            "taskId": request.taskId
        }

    except HTTPException:
        raise
    except asyncio.TimeoutError:
        logger.error("视频生成超时")
        raise HTTPException(status_code=504, detail="Video generation timed out")
    except Exception as e:
        logger.error(f"处理文生视频请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        update_data(connection,'theme_task_log',
                    {'task_status': 'success' if public_url and public_url != '' else 'failed',
                     'result_urls': public_url,
                     'update_time':datetime.now()},"task_id='{}'".format(request.task_id))
        if connection:
            connection.close()