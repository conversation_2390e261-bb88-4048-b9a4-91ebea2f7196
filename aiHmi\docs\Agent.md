# AI-HMI 场景生成智能体设计文档

## 1. 概述 (Overview)
本智能体 (Agent) 的核心任务是接收用户定义的自然语言场景描述,并基于此自动完成整个 HMI (人机交互界面) 的设计。这包括选择合适的UI组件、应用视觉样式、生成文本内容、构建屏幕布局,并为背景壁纸生成AI绘画提示词 (Prompt)。

最终,Agent会输出一个结构化的JSON对象,该对象完整描述了整个UI界面,可直接用于驱动前端进行渲染。

## 2. 核心工作流 (Core Workflow)
智能体的工作流程被设计为一个串联的管道模型,确保每一步的输出都成为下一步的有效输入。

1.  **场景解析 (Scene Parsing):**
    *   **输入:** 用户的自然语言描述 (例如: "下雨天开车回家,听着悲伤的音乐")。
    *   **处理:** 利用自然语言理解(NLU)技术,提取场景中的核心要素,并将其转换为标准化的**标签(Tags)**。
    *   **输出:** 结构化的场景标签对象 (例如: `{ "weather": "rainy", "activity": "driving", "mood": "sad", "media": "music" }`)。

2.  **资源选择 (Resource Selection):**
    *   **输入:** 场景标签对象。
    *   **处理:** 并行执行两个匹配任务:
        *   **组件选择:** 根据标签从**组件知识库**中选择最匹配的UI组件 (如 `MusicPlayer`, `WeatherWidget`)。
        *   **样式选择:** 根据标签从**样式知识库**中选择最匹配的视觉主题 (如 `theme-dark-blue`)。
    *   **输出:** 一个包含已选组件列表和样式名称的对象。

3.  **内容与提示词生成 (Content & Prompt Generation):**
    *   **输入:** 场景标签对象和已选组件列表。
    *   **处理:** 并行执行两个生成任务:
        *   **壁纸提示词生成:** 基于场景标签,使用**提示词工程模板**组合生成详细、丰富的AI绘画提示词。
        *   **组件内容生成:** 为每个已选组件,调用大语言模型(LLM)并结合**内容模板**,生成符合场景氛围的文本。
    *   **输出:** 壁纸提示词字符串和组件内容对象。

4.  **布局生成 (Layout Generation):**
    *   **输入:** 已选组件列表(包含其优先级或尺寸信息)。
    *   **处理:** 调用**布局规则引擎**,根据预设的规则(如格栅系统、优先级排序)为每个组件分配屏幕位置。
    *   **输出:** 定义了所有组件位置的布局对象。

5.  **最终聚合输出 (Final Output Aggregation):**
    *   **输入:** 以上所有步骤的输出。
    *   **处理:** 将场景标签、样式、壁纸提示词、布局和内容聚合成一个单一、完整的JSON对象。
    *   **输出:** 最终的UI描述JSON。

## 3. Agent所需资源与能力
为了完成上述工作流,Agent需要依赖以下知识库和能力:

### 3.1. 场景元素知识库 (Scene Element Knowledge Base)
*   **用途:** 用于场景解析,将自然语言映射为标准化标签。
*   **格式:** 一个键值对词典或分类词汇表。
*   **示例:**
    ```json
    {
      "weather": ["下雨", "晴天", "雪", "sunny", "rainy"],
      "activity": ["开车", "工作", "会议", "driving", "working"],
      "mood": ["开心", "悲伤", "放松", "happy", "sad", "relax"]
    }
    ```

### 3.2. 组件知识库 (Component Knowledge Base)
*   **用途:** 根据标签选择UI组件。
*   **格式:** JSON数组,每个组件对象包含名称、触发标签和可选的布局属性(如优先级、建议尺寸)。
*   **示例:**
    ```json
    [
      { "name": "MusicPlayer", "tags": ["music", "radio"], "priority": 1 },
      { "name": "WeatherWidget", "tags": ["weather", "rainy", "sunny"], "priority": 3 },
      { "name": "NavigationCard", "tags": ["driving", "map"], "priority": 1 }
    ]
    ```

### 3.3. 样式知识库 (Style Knowledge Base)
*   **用途:** 根据标签选择视觉主题。
*   **格式:** JSON数组,每个样式对象包含名称和触发标签。
*   **示例:**
    ```json
    [
      { "name": "theme-dark-blue", "tags": ["rainy", "sad", "night"] },
      { "name": "theme-light-orange", "tags": ["sunny", "happy", "morning"] }
    ]
    ```

### 3.4. 壁纸提示词工程 (Wallpaper Prompt Engineering)
*   **用途:** 结构化地生成高质量的AI绘画提示词。
*   **格式:** 一个包含多个占位符的模板字符串。
*   **模板:** `(主体), (环境), (细节), (情绪/氛围), (艺术风格), (画质)`
*   **示例:**
    *   **输入标签:** `rainy`, `driving`, `city`, `sad`
    *   **输出Prompt:** "A photorealistic wallpaper of a car's dashboard, looking through a rain-streaked windshield at blurry city night lights, creating a melancholic and lonely atmosphere, cinematic lighting, 8k."

### 3.5. 布局规则引擎 (Layout Rule Engine)
*   **用途:** 动态计算和分配组件的屏幕位置。
*   **格式:** 一系列条件规则。
*   **示例规则:**
    *   `IF MusicPlayer exists THEN position = 'bottom-bar'`
    *   `IF NavigationCard exists THEN position = 'main-view'`
    *   `IF count(widgets) > 2 THEN arrange_widgets_in_sidebar()`

## 4. 输入与输出格式示例

### 4.1. 输入 (Input)
*   **格式:** `string`
*   **示例:** `"下班开车回家,外面在下雨,想听点轻松的音乐"`

### 4.2. 输出 (Output)
*   **格式:** `JSON`
*   **示例:**
    ```json
    {
      "scene_analysis": {
        "source_text": "下班开车回家,外面在下雨,想听点轻松的音乐",
        "tags": {
          "activity": "driving",
          "event": "off-work",
          "destination": "home",
          "weather": "rainy",
          "media": "music",
          "mood": "relax"
        }
      },
      "design_system": {
        "style_theme": "theme-dark-blue"
      },
      "background": {
        "prompt": "A relaxing wallpaper, car interior view, rain drops on the window, driving through the city street at dusk, soft focus, lo-fi aesthetic, calming mood, digital art."
      },
      "layout": {
        "grid_template": "...",
        "components": [
          {
            "component_name": "MusicPlayer",
            "grid_area": "footer",
            "content": {
              "title": "城市漫游歌单",
              "artist": "Chillhop Radio",
              "message": "愿音乐洗去你一天的疲惫"
            }
          },
          {
            "component_name": "NavigationCard",
            "grid_area": "main",
            "content": {
              "destination": "家",
              "eta_minutes": 15,
              "distance_km": 5.2
            }
          },
          {
            "component_name": "WeatherWidget",
            "grid_area": "sidebar",
            "content": {
              "status": "小雨",
              "temperature_celsius": 22
            }
          }
        ]
      }
    }
    ```
