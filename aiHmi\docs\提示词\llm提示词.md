// 从 webhook 获取用户场景描述
const input = $input.all()[0].json;
const userSceneDescription = input.scene_description || input.description || '早高峰通勤，需要送孩子上学然后去公司';

// 知识库数据 - 核心场景库（基于场景文档）
const sceneElementKB = [
  {
    "id": "morning_family_commute",
    "name": "早高峰家庭通勤",
    "description": "送孩子上学后独自前往公司的分阶段通勤场景",
    "phases": [
      {
        "phase": "family_mode",
        "name": "家庭出行模式",
        "trigger": "用户和孩子进入车辆",
        "components": ["dynamic-island", "kid-education-card", "pedia-card", "vpa-avatar-widget"],
        "background": "温馨家庭氛围"
      },
      {
        "phase": "focus_mode",
        "name": "专注通勤模式",
        "trigger": "孩子离车，独自前往公司",
        "components": ["dynamic-island", "music-control-card", "todo-card", "order-status-card", "vpa-avatar-widget"],
        "background": "放松的音乐动态壁纸"
      }
    ],
    "elements": ["灵动岛导航", "儿童教育内容", "百科问答", "音乐控制", "待办事项"]
  },
  {
    "id": "evening_commute",
    "name": "下班通勤",
    "description": "下班回家路上的放松和便民服务场景",
    "components": ["dynamic-island", "vpa-interaction-panel", "music-control-card", "smart-home-card"],
    "background": "傍晚城市街景壁纸",
    "elements": ["主动服务对话", "智能家居控制", "购物建议", "放松音乐"]
  },
  {
    "id": "waiting_entertainment",
    "name": "车内等待/摸鱼",
    "description": "驻车状态下的娱乐和休息场景",
    "trigger": "车辆挂入P档超过1分钟",
    "components": ["video-player-card", "news-digest-card", "ambient-sound-card", "vpa-avatar-widget"],
    "background": "宁静湖畔壁纸",
    "elements": ["视频播放", "新闻摘要", "环境音", "VPA待命"]
  },
  {
    "id": "rainy_night_drive",
    "name": "雨夜归途",
    "description": "深夜雨天回家的极简安全驾驶场景",
    "components": ["dynamic-island", "music-control-card", "vpa-avatar-widget"],
    "background": "模糊城市夜景+动态雨滴特效",
    "elements": ["专注导航", "氛围音乐", "最小干扰"]
  },
  {
    "id": "family_weekend_trip",
    "name": "周末家庭出游",
    "description": "家庭出游的多人服务和娱乐场景",
    "components": ["dynamic-island", "rear-seat-control-card", "facility-finder-card", "trip-reminder-card"],
    "background": "导航地图路线高亮",
    "elements": ["后座娱乐控制", "设施查找", "行程提醒", "家庭服务"]
  }
];

// 风格库
const styleKB = [
  {
    "id": "natural",
    "name": "自然通勤",
    "properties": {
      "theme-color-primary": "#7D8A6E",
      "theme-color-background": "#F0F2EB",
      "font-family": "'Noto Sans', sans-serif",
      "border-radius": "12px"
    }
  },
  {
    "id": "cyberpunk",
    "name": "赛博朋克",
    "properties": {
      "theme-color-primary": "#EA00D9",
      "theme-color-secondary": "#00F0FF",
      "theme-color-background": "#0A021A",
      "theme-color-text": "#F0F0F0",
      "font-family": "'Orbitron', sans-serif"
    }
  },
  {
    "id": "glassmorphism",
    "name": "玻璃拟物",
    "properties": {
      "background-blur": "15px",
      "border": "1px solid rgba(255, 255, 255, 0.25)",
      "background-color": "rgba(255, 255, 255, 0.1)",
      "border-radius": "16px"
    }
  },
  {
    "id": "neumorphism",
    "name": "新拟物",
    "properties": {
      "background-color": "#E0E5EC",
      "box-shadow": "9px 9px 16px #A3B1C6, -9px -9px 16px #FFFFFF",
      "border-radius": "20px"
    }
  },
  {
    "id": "kawaii",
    "name": "可爱卡通",
    "properties": {
      "theme-color-primary": "#FFC0CB",
      "theme-color-background": "#FFF8E1",
      "font-family": "'Comic Sans MS', cursive, sans-serif",
      "border-radius": "50px"
    }
  }
];

// 组件库（基于场景文档的实际需求）
const componentKB = [
  {"id": "dynamic-island", "name": "灵动岛", "description": "顶部状态栏，显示导航信息和关键状态", "size": "16x1"},
  {"id": "vpa-avatar-widget", "name": "VPA头像小部件", "description": "VPA的悬浮头像，用于实时语音反馈", "size": "2x2"},
  {"id": "vpa-interaction-panel", "name": "VPA交互面板", "description": "VPA主动服务对话框，显示对话和建议", "size": "8x3"},

  // 家庭通勤专用组件
  {"id": "kid-education-card", "name": "儿童教育卡片", "description": "播放教育视频和儿童内容", "size": "8x9"},
  {"id": "pedia-card", "name": "百科问答卡片", "description": "回答孩子的各种问题", "size": "8x4"},

  // 通勤和娱乐组件
  {"id": "music-control-card", "name": "音乐控制卡片", "description": "音乐播放控制和歌曲信息", "size": "8x9"},
  {"id": "todo-card", "name": "今日待办卡片", "description": "显示日程安排和待办事项", "size": "8x4"},
  {"id": "order-status-card", "name": "订单状态卡片", "description": "显示外卖、购物等订单状态", "size": "4x2"},

  // 智能家居和服务
  {"id": "smart-home-card", "name": "智能家居卡片", "description": "控制家中的智能设备", "size": "8x4"},

  // 等待和娱乐组件
  {"id": "video-player-card", "name": "视频播放器", "description": "全屏视频播放功能", "size": "16x5"},
  {"id": "news-digest-card", "name": "新闻摘要卡片", "description": "显示新闻摘要和资讯", "size": "4x2"},
  {"id": "ambient-sound-card", "name": "环境音卡片", "description": "播放环境音和白噪音", "size": "4x2"},

  // 家庭出游组件
  {"id": "rear-seat-control-card", "name": "后座娱乐控制", "description": "控制后排娱乐系统", "size": "4x2"},
  {"id": "facility-finder-card", "name": "查找设施卡片", "description": "查找附近的洗手间、餐厅等", "size": "4x2"},
  {"id": "trip-reminder-card", "name": "行程提醒卡片", "description": "旅行相关的提醒和建议", "size": "4x2"}
];

// 布局库（基于场景文档的ASCII原型图）
const layoutKB = [
  {
    "id": "hmi-standard-layout",
    "name": "HMI标准布局",
    "description": "标准的车载HMI布局，包含灵动岛、主内容区和VPA区域",
    "structure": {
      "dynamic-island": {"position": "top", "width": "100%", "height": "60px"},
      "background-canvas": {"position": "full", "zIndex": 0},
      "main-content": {"position": "center", "width": "80%", "height": "70%"},
      "vpa-area": {"position": "bottom-right", "width": "120px", "height": "120px"}
    }
  },
  {
    "id": "two-column-layout",
    "name": "双栏布局",
    "description": "左右分栏布局，适合同时显示多个功能卡片",
    "structure": {
      "dynamic-island": {"position": "top", "width": "100%"},
      "left-column": {"position": "left", "width": "50%"},
      "right-column": {"position": "right", "width": "50%"},
      "vpa-area": {"position": "bottom-right"}
    }
  },
  {
    "id": "entertainment-layout",
    "name": "娱乐布局",
    "description": "以大屏幕内容为主的娱乐布局",
    "structure": {
      "main-video": {"position": "center-top", "width": "90%", "height": "60%"},
      "control-bar": {"position": "bottom", "width": "100%", "height": "30%"},
      "vpa-area": {"position": "bottom-left"}
    }
  },
  {
    "id": "minimal-layout",
    "name": "极简布局",
    "description": "最小化干扰的简洁布局，适合专注驾驶",
    "structure": {
      "dynamic-island": {"position": "top", "width": "100%"},
      "essential-info": {"position": "bottom", "width": "60%", "height": "20%"},
      "vpa-area": {"position": "bottom-left"}
    }
  }
];

// 构建完整的提示词
const prompt = `**角色:** 你是一位专业的AI-HMI界面设计师和场景分析专家，精通车载人机交互设计。

**核心任务:**
1. **场景智能识别**: 深度分析用户场景描述，识别关键要素（时间、人员、目的地、情绪状态等）
2. **分阶段场景处理**: 对于复杂场景（如家庭通勤），识别并处理不同阶段的UI切换需求
3. **精确组件匹配**: 根据场景需求选择最合适的组件组合，确保功能完整性
4. **高保真HTML生成**: 生成符合场景文档ASCII原型图要求的完整HTML界面

**场景文档参考标准:**
- 必须包含灵动岛(dynamic-island)作为顶部状态栏
- VPA头像小部件(vpa-avatar-widget)必须位于界面右下角
- 组件尺寸严格按照场景文档中的规格（如8x9, 4x2等）
- 背景层要体现场景氛围（如"放松的音乐动态壁纸"、"宁静湖畔壁纸"等）

**知识库:**

**核心场景库:**
${JSON.stringify(sceneElementKB, null, 2)}

**视觉风格库:**
${JSON.stringify(styleKB, null, 2)}

**组件库:**
${JSON.stringify(componentKB, null, 2)}

**布局库:**
${JSON.stringify(layoutKB, null, 2)}

**用户场景描述:** "${userSceneDescription}"

**输出要求:**
请严格按照以下格式输出：

## 场景分析
- **场景识别**: [识别的主场景ID和子场景/阶段]
- **关键要素**: [时间、人员、环境、情绪等关键信息]
- **设计策略**: [界面设计的核心策略和考虑因素]

## 布局设计方案
- **选择的布局**: [布局ID和选择理由]
- **组件配置**: [选择的组件列表及其功能说明]
- **视觉风格**: [选择的风格ID和视觉特征]
- **交互逻辑**: [关键的交互流程说明]

## 布局JSON结构
\`\`\`json
{
  "sceneId": "场景ID",
  "layoutId": "布局ID",
  "styleId": "风格ID",
  "backgroundTheme": "背景主题描述",
  "components": [
    {
      "id": "组件ID",
      "name": "组件名称",
      "position": {"x": 0, "y": 0, "width": "尺寸", "height": "尺寸"},
      "content": "具体内容",
      "style": "特殊样式"
    }
  ]
}
\`\`\`

## 完整HTML代码
\`\`\`html
[生成完整的HTML代码，必须包含：]
[1. 完整的CSS样式，体现选择的风格特征]
[2. 灵动岛组件，显示导航或状态信息]
[3. 所有选择的功能组件，内容要生动贴切]
[4. VPA头像组件，位于右下角]
[5. 背景层样式，体现场景氛围]
[6. 响应式布局，适配车载屏幕]
\`\`\`

**关键要求:**
1. **严格遵循场景文档**: 生成的界面必须与场景文档中的ASCII原型图高度一致
2. **组件内容真实化**: 不要使用"占位内容"，要生成符合场景的具体内容
3. **视觉层次清晰**: 重要信息突出显示，次要信息适当弱化
4. **交互友好**: 考虑驾驶安全，避免过多视觉干扰
5. **技术规范**: HTML代码要完整可运行，CSS样式要精确实现设计效果`;

return [{
  json: {
    prompt: prompt,
    userSceneDescription: userSceneDescription,
    timestamp: new Date().toISOString()
  }
}];