"""
提示词增强测试脚本
"""
import sys
import os
import asyncio
import logging
import json

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入需要的组件
from theme_backend.agent.promptAgent import PromptEnhancerAgent
from theme_backend.agent.event_handler import PromptEnhancerEventHandler

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 模拟WebSocket管理器
class MockWebSocketManager:
    """模拟WebSocket管理器，用于测试"""
    
    async def send_message(self, session_id, message):
        """记录发送的消息"""
        print("\n[WebSocket消息] 会话ID:", session_id)
        print(f"[WebSocket消息] 类型: {message.get('event_type')}")
        
        if "data" in message and "content" in message["data"]:
            content = message["data"]["content"]
            is_final = message["data"].get("is_final", False)
            content_preview = content[:100] + "..." if len(content) > 100 else content
            print(f"[WebSocket消息] 内容: {content_preview}")
            print(f"[WebSocket消息] 是最终消息: {is_final}")
            
            # 保存最终结果
            if is_final and message.get("event_type") == "content":
                with open("enhanced_result.txt", "w", encoding="utf-8") as f:
                    f.write(content)
                print(f"[结果] 已保存到 enhanced_result.txt")
        
        return True

async def test_enhance_prompt():
    """测试提示词增强功能"""
    print("\n===== 开始测试提示词增强 =====\n")
    
    # 创建提示词
    prompt = "一个站在山顶的猫，背景是日落"
    session_id = "test-session-" + str(hash(prompt))[:8]
    
    print(f"[测试] 提示词: {prompt}")
    print(f"[测试] 会话ID: {session_id}")
    
    # 创建模拟WebSocket管理器
    ws_manager = MockWebSocketManager()
    
    # 创建提示词增强代理
    try:
        print("[测试] 创建提示词增强代理...")
        agent = PromptEnhancerAgent(websocket_manager=ws_manager)
        print("[测试] 提示词增强代理创建成功")
        
        # 执行提示词增强
        print(f"[测试] 开始增强提示词...")
        result = await agent.enhance_prompt(session_id, prompt)
        
        print(f"\n===== 增强结果 =====\n")
        print(result)
        
    except Exception as e:
        print(f"[测试错误] {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    print("开始测试提示词增强功能")
    asyncio.run(test_enhance_prompt()) 