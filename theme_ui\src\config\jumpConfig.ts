/**
 * 跳转配置管理
 * 用于存储和管理跳转到3D汽车展示模块的配置
 */

// 默认配置
const defaultConfig = {
  car3dUrl: 'http://localhost:5173'
};

/**
 * 跳转配置管理类
 */
export class JumpConfigManager {
  private static instance: JumpConfigManager;
  private config: typeof defaultConfig;

  private constructor() {
    this.config = { ...defaultConfig };
    this.loadFromStorage();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): JumpConfigManager {
    if (!JumpConfigManager.instance) {
      JumpConfigManager.instance = new JumpConfigManager();
    }
    return JumpConfigManager.instance;
  }

  /**
   * 获取3D汽车展示模块URL
   */
  public getCar3dUrl(): string {
    return this.config.car3dUrl;
  }

  /**
   * 设置3D汽车展示模块URL
   */
  public setCar3dUrl(url: string): void {
    this.config.car3dUrl = url;
    this.saveToStorage();
  }

  /**
   * 保存配置到localStorage
   */
  private saveToStorage(): void {
    try {
      localStorage.setItem('theme-ui-jump-config', JSON.stringify(this.config));
    } catch (e) {
      console.error('保存跳转配置失败:', e);
    }
  }

  /**
   * 从localStorage加载配置
   */
  private loadFromStorage(): void {
    try {
      const storedConfig = localStorage.getItem('theme-ui-jump-config');
      if (storedConfig) {
        this.config = { ...this.config, ...JSON.parse(storedConfig) };
      }
    } catch (e) {
      console.error('加载跳转配置失败:', e);
    }
  }
}

/**
 * 导出简化API
 */
export const useJumpConfig = {
  getCar3dUrl: () => JumpConfigManager.getInstance().getCar3dUrl(),
  setCar3dUrl: (url: string) => JumpConfigManager.getInstance().setCar3dUrl(url)
}; 