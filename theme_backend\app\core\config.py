from pydantic_settings import BaseSettings
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # ComfyUI 配置
    API_URLS: dict[str, any] = {
        "theme_text_to_image_server": "http://************:18190/api",
        "car_texture_server": "http://************:18191/api",
        "theme_content_server": "http://************:18192/api",
        "doodle_server": "http://************:8193/api",  # 修复端口号：18193 -> 8193
        "theme_icon_refine_server": "http://************:18194/api",
        "image_to_text_server": "http://************:18195/api",
        "image_to_video_server": "http://************:18196/api",
        "image_to_image_server": "http://************:18197/api",
        "theme_ui_generation_server": "http://************:18198/api",
        "theme_ui_split_server": "http://************:18199/api",
        "voice_to_voice_server": "http://************:18200/api",
        "wan_server": "http://************:18201/api",
    }
    API_URL: str = os.getenv("COMFYUI_BASE_URL", "http://************:8193")
    #COMFYUI_BASE_URL: str = os.getenv("COMFYUI_BASE_URL", "http://************:8193")
    #COMFYUI_WS_URL: str = os.getenv("COMFYUI_WS_URL", "ws://************:8193/ws")
    COMFYUI_BASE_URL: str = os.getenv("COMFYUI_BASE_URL", "http://************:8193")
    COMFYUI_WS_URL: str = os.getenv("COMFYUI_WS_URL", "ws://************:8193/ws")
    # API 配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ComfyUI Backend API"
    
    MY_SQL_HOST: str = os.getenv("MY_SQL_HOST", "***********")
    MY_SQL_PORT: int = os.getenv("MY_SQL_PORT", 19090)
    MY_SQL_USER: str = os.getenv("MY_SQL_USER", "root")
    MY_SQL_PASSWORD: str = os.getenv("MY_SQL_PASSWORD", "Fsit#2024")
    MY_SQL_DB: str = os.getenv("MY_SQL_DB", "changan")

    # Pipeline 配置
    ICON_COUNT: int = 35  # 固定图标数量为35
    ICON_REFINE_WEIGHT: float = 40.0  # 图标细化任务权重
    MAX_PARALLEL_WORKERS: int = 1  # 最大并行工作线程数
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    OUTPUT_DIR: str = "outputs"
    
    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    class Config:
        case_sensitive = True

settings = Settings() 