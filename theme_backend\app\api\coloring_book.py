from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import uuid
import time
from typing import Optional
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    wait_for_multiple_images,
    logger
)

router = APIRouter(prefix="/coloring-book", tags=["魔法画册"])

# 服务器名称
SERVER_NAME = "doodle_server"

# 工作流节点ID
PROMPT_NODE_ID = "35"  # 提示词输入节点
SAVE_NODE_ID = "47"    # 保存图像节点

class StartRequest(BaseModel):
    prompt: str = Field(..., description="用于生成线稿图的文本提示词")
    count: int = Field(1, description="生成图片数量", ge=1, le=10)
    ratio: str = Field("3:4", description="图片比例，支持1:1, 3:4, 4:3, 16:9, 9:16")

class StartResponse(BaseModel):
    task_id: str = Field(..., description="本次生成任务的唯一ID")
    prompt_id: str = Field(..., description="ComfyUI提示ID")
    image_urls: list[str] = Field([], description="生成图片的URL列表")

def build_coloring_book_workflow(prompt: str, task_id: str, count: int = 1, ratio: str = "3:4") -> tuple[dict, str]:
    """构建涂色书工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("lineart.json")
        
        # 比例转换为尺寸
        ratio_to_size = {
            "1:1": (1024, 1024),
            "3:4": (768, 1024),
            "4:3": (1024, 768),
            "16:9": (1024, 576),
            "9:16": (576, 1024)
        }
        
        width, height = ratio_to_size.get(ratio, (768, 1024))
        logger.info(f"设置图片尺寸: {width}x{height} (比例: {ratio})")
        
        # 设置图片尺寸和批量数量（节点5是EmptyLatentImage）
        if "5" in workflow:
            workflow["5"]["inputs"]["width"] = width
            workflow["5"]["inputs"]["height"] = height
            workflow["5"]["inputs"]["batch_size"] = count
            logger.info(f"设置批量数量: {count}")
        
        # 设置提示词
        if PROMPT_NODE_ID not in workflow:
            logger.error(f"工作流中找不到节点{PROMPT_NODE_ID}")
            raise HTTPException(status_code=500, detail=f"Node {PROMPT_NODE_ID} not found in workflow")
            
        workflow[PROMPT_NODE_ID]["inputs"]["prompt"] = prompt
        logger.info(f"设置提示词: {prompt}")

        # 设置保存文件名
        if SAVE_NODE_ID not in workflow:
            logger.error(f"工作流中找不到节点{SAVE_NODE_ID}")
            raise HTTPException(status_code=500, detail=f"Node {SAVE_NODE_ID} not found in workflow")
        
        workflow[SAVE_NODE_ID]["inputs"]["filename_prefix"] = f"coloring_book_{task_id}"
        logger.info(f"设置文件名前缀: coloring_book_{task_id}")

        # 设置随机种子
        if "25" in workflow and "inputs" in workflow["25"]:
            workflow["25"]["inputs"]["noise_seed"] = int(time.time() * 1000000) % (2**32)
            
        return workflow, SAVE_NODE_ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/start", response_model=StartResponse)
async def start_generation_task(request: StartRequest):
    """启动涂色书生成任务"""
    try:
        task_id = str(uuid.uuid4())
        logger.info(f"========== 开始生成涂色书 ==========")
        logger.info(f"提示词: {request.prompt}")
        logger.info(f"任务ID: {task_id}")

        if not request.prompt:
            raise HTTPException(status_code=400, detail="提示词不能为空")

        # 构建工作流
        workflow, output_node_id = build_coloring_book_workflow(request.prompt, task_id, request.count, request.ratio)

        # 发送生成请求
        data = await send_prompt(SERVER_NAME, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        subfolder = ""  # 涂色书通常保存在根目录
        if request.count > 1:
            image_urls = await wait_for_multiple_images(SERVER_NAME, data["prompt_id"], output_node_id, subfolder)
            logger.info(f"生成的图片URL列表: {image_urls}")
        else:
            image_url = await wait_for_image(SERVER_NAME, data["prompt_id"], output_node_id, subfolder)
            image_urls = [image_url] if image_url else []
            logger.info(f"生成的图片URL: {image_url}")

        logger.info(f"========== 涂色书生成完成 ==========")

        return StartResponse(
            task_id=task_id,
            prompt_id=data["prompt_id"],
            image_urls=image_urls
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理涂色书生成请求时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态（简化版本，直接返回完成状态）"""
    try:
        # 简化版本：由于我们采用同步等待模式，任务创建成功就意味着已完成
        return {
            "status": "completed",
            "progress": 100,
            "message": "任务已完成，图片已生成"
        }
    except Exception as e:
        logger.error(f"查询任务状态时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))