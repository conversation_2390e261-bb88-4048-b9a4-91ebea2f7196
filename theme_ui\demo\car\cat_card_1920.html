<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能车机界面</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-900': 'rgba(15, 23, 42, 0.9)',
                        'dark-800': 'rgba(15, 23, 42, 0.8)',
                        'dark-700': 'rgba(30, 41, 59, 0.7)',
                        'dark-600': 'rgba(30, 41, 59, 0.6)',
                        'dark-500': 'rgba(30, 41, 59, 0.5)',
                        'dark-400': 'rgba(30, 41, 59, 0.4)',
                    },
                }
            }
        }
    </script>
    <style>
        body {
            overflow: hidden;
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, sans-serif;
        }
        
        .bg-stars {
            background-image: url('https://images.unsplash.com/photo-1539721972319-f0e80a00d424?q=80&w=2940&auto=format&fit=crop');
            background-size: cover;
            background-position: center;
        }
        
        .glass {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        
        /* 防止滚动条出现 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        /* 图标比例5:3 */
        .icon-5x3 {
            aspect-ratio: 5/3;
        }
    </style>
</head>

<body class="h-screen w-screen text-white bg-black bg-stars">
    <!-- 主容器 -->
    <div class="relative h-full w-full flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="flex justify-between items-center px-6 py-1">
            <!-- 左侧网络信号 -->
            <div class="flex items-center space-x-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.072 0m-9.9-2.828a9 9 0 0112.728 0" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01" />
                </svg>
            </div>
            
            <!-- 中间留空 -->
            <div></div>
            
            <!-- 右侧时间 -->
            <div class="text-xl font-medium">11:36</div>
        </div>
        
     
        <!-- 主要内容区域 - 三个卡片 -->
        <div class="flex-1 px-4 flex space-x-4 mb-10 h-44">
            <!-- 左侧卡片：电影推荐 -->
            <div class="glass rounded-xl w-1/3 overflow-hidden transition-transform duration-300 hover:scale-[1.01]">
                <div class="h-full relative">
                    <!-- 电影海报 -->
                    <img src="https://images.unsplash.com/photo-1665419411877-defbd98b2d8b?q=80&w=2574&auto=format&fit=crop" alt="沙丘2" class="absolute inset-0 w-full h-full object-cover opacity-80">
                    
                    <!-- 电影文字信息 -->
                    <div class="absolute inset-0 flex flex-col justify-between p-6">
                        <div>
                            <h2 class="text-2xl font-bold mb-1 drop-shadow-lg">沙丘2</h2>
                            <p class="text-white/80 text-xs mb-1 drop-shadow-md">导演: 丹尼斯·维伦纽瓦</p>
                        </div>
                        
                        <button class="bg-white/20 hover:bg-white/30 backdrop-blur-md text-white py-1.5 px-3 rounded-lg w-28 text-sm transition-all duration-300 transform hover:scale-105">
                            立即播放
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 中间卡片：操作指引 -->
            <div class="glass rounded-xl w-1/3 transition-transform duration-300 hover:scale-[1.01]">
                <div class="h-full flex flex-col p-4">
                    <div class="flex-1">
                        <h2 class="text-xl font-semibold mb-1">操作指引</h2>
                        <p class="text-white/70 text-xs">快速了解你的车机</p>
                        
                        <!-- 车辆图片 -->
                        <div class="mt-2 flex justify-center h-24">
                            <img src="https://images.unsplash.com/photo-1603383928972-2116518cd3f3?q=80&w=2574&auto=format&fit=crop" alt="车辆" class="object-contain h-full">
                        </div>
                    </div>
                    
                    <button class="bg-white/20 hover:bg-white/30 backdrop-blur-md text-white py-1.5 px-3 rounded-lg w-full mt-2 text-sm transition-all duration-300 transform hover:scale-[1.02]">
                        查看详情
                    </button>
                </div>
            </div>
            
            <!-- 右侧卡片：搜索功能 -->
            <div class="glass rounded-xl w-1/3 transition-transform duration-300 hover:scale-[1.01]">
                <div class="h-full flex flex-col p-4">
                    <!-- 搜索栏 -->
                    <div class="relative mb-3">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <input type="search" class="w-full py-2 pl-10 pr-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30" placeholder="搜索目的地">
                        
                        <div class="absolute inset-y-0 right-3 flex items-center">
                            <button class="text-white/70 hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 功能图标 -->
                    <div class="grid grid-cols-4 gap-3">
                        <div class="flex flex-col items-center">
                            <div class="icon-5x3 w-14 h-8 bg-white/10 rounded-lg mb-1 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                </svg>
                            </div>
                            <span class="text-xs text-center">筛选</span>
                        </div>
                        
                        <div class="flex flex-col items-center">
                            <div class="icon-5x3 w-14 h-8 bg-white/10 rounded-lg mb-1 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <span class="text-xs text-center">历史</span>
                        </div>
                        
                        <div class="flex flex-col items-center">
                            <div class="icon-5x3 w-14 h-8 bg-white/10 rounded-lg mb-1 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <span class="text-xs text-center">位置</span>
                        </div>
                        
                        <div class="flex flex-col items-center">
                            <div class="icon-5x3 w-14 h-8 bg-white/10 rounded-lg mb-1 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <span class="text-xs text-center">购物</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 应用图标区域 -->
        <div class="px-4 mb-10">
            <div class="grid grid-cols-6 gap-6">
                <!-- AI空间 -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-blue-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">AI空间</span>
                </div>
                
                <!-- KTV -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-purple-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">KTV</span>
                </div>
                
                <!-- 场景魔方 -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-orange-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">场景魔方</span>
                </div>
                
                <!-- 驾车手册 -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-amber-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">驾车手册</span>
                </div>
                
                <!-- 情景模式 -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-blue-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">情景模式</span>
                </div>
                
                <!-- 游戏空间 -->
                <div class="flex flex-col items-center">
                    <div class="icon-5x3 w-20 h-12 bg-red-500/30 rounded-lg mb-1 flex items-center justify-center transition-transform duration-300 hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M11 17.25V16.75C11 16.3358 11.3358 16 11.75 16H12.25C12.6642 16 13 16.3358 13 16.75V17.25C13 17.6642 12.6642 18 12.25 18H11.75C11.3358 18 11 17.6642 11 17.25ZM7.25 12C7.25 12.4142 7.58579 12.75 8 12.75C8.41421 12.75 8.75 12.4142 8.75 12C8.75 11.5858 8.41421 11.25 8 11.25C7.58579 11.25 7.25 11.5858 7.25 12ZM15.25 12C15.25 12.4142 15.5858 12.75 16 12.75C16.4142 12.75 16.75 12.4142 16.75 12C16.75 11.5858 16.4142 11.25 16 11.25C15.5858 11.25 15.25 11.5858 15.25 12ZM5 6.25C5 5.55964 5.55964 5 6.25 5H17.75C18.4404 5 19 5.55964 19 6.25V17.75C19 18.4404 18.4404 19 17.75 19H6.25C5.55964 19 5 18.4404 5 17.75V6.25Z" />
                        </svg>
                    </div>
                    <span class="text-xs text-center">游戏空间</span>
                </div>
            </div>
        </div>
        
        <!-- 底部工具栏 -->
        <div class="fixed bottom-0 left-0 right-0 bg-black h-14 flex justify-between items-center px-6">
            <!-- 左侧工具图标 -->
            <div class="flex items-center space-x-8">
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                </button>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                </button>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                    </svg>
                </button>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                </button>
            </div>
            
            <!-- 中间音乐控制 -->
            <div class="flex items-center space-x-6">
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
            
            <!-- 右侧温度控制 -->
            <div class="flex items-center space-x-8">
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <div class="text-lg font-medium">28.5</div>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                
                <div class="text-sm font-medium">AUTO</div>
                
                <button class="p-2 text-white hover:text-white/80 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14.121 14.121L19 19m-7-7l7-7m-7 7l-2.879 2.879M12 12L9.121 9.121m0 5.758a3 3 0 10-4.243-4.243 3 3 0 004.243 4.243zm0-5.758a3 3 0 10-4.243 4.243 3 3 0 004.243-4.243z" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 交互脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导航标签切换
            const tabs = document.querySelectorAll('.flex.space-x-10 > div');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的活跃状态
                    tabs.forEach(t => {
                        t.classList.add('opacity-60');
                        const indicator = t.querySelector('.h-0.5');
                        indicator.classList.remove('bg-white');
                        indicator.classList.add('bg-transparent');
                    });
                    
                    // 添加当前标签的活跃状态
                    this.classList.remove('opacity-60');
                    const indicator = this.querySelector('.h-0.5');
                    indicator.classList.remove('bg-transparent');
                    indicator.classList.add('bg-white');
                });
            });
            
            // 卡片点击效果
            const cards = document.querySelectorAll('.glass');
            
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.classList.add('scale-[0.98]');
                    setTimeout(() => {
                        this.classList.remove('scale-[0.98]');
                    }, 200);
                });
            });
            
            // 图标点击效果
            const icons = document.querySelectorAll('.icon-5x3');
            
            icons.forEach(icon => {
                icon.addEventListener('click', function() {
                    this.classList.add('scale-95');
                    setTimeout(() => {
                        this.classList.remove('scale-95');
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
