# MCP服务集成指南

本文档详细介绍了在AutoGen框架中实现Model Context Protocol (MCP)服务集成的不同方法，包括各种连接方式的比较、优缺点、适用场景以及代码示例。

## MCP简介

Model Context Protocol (MCP)是一种标准化的协议，用于AI应用与各种上下文和工具服务之间的通信。它允许语言模型通过定义良好的接口访问外部数据和功能，实现更复杂的智能应用场景。

## MCP服务集成方式对比

AutoGen框架支持多种方式与MCP服务集成，每种方式有其特定的适用场景和优缺点：

| 集成方式               | 描述                                  | 优点                   | 缺点                           | 适用场景              |
| ---------------------- | ------------------------------------- | ---------------------- | ------------------------------ | --------------------- |
| SseServerParams        | 基于SSE(Server-Sent Events)的HTTP连接 | 简单易用，适合远程服务 | 依赖HTTP服务质量               | 连接远程MCP服务       |
| StdioServerParams      | 基于标准输入输出的进程通信            | 本地启动，低延迟       | 仅适用于本地服务               | 本地快速启动MCP服务   |
| HttpServerToolProvider | 直接基于HTTP API的工具提供方式        | 不需要额外的适配层     | 工具格式转换复杂               | 简单的HTTP工具API集成 |
| 直接HTTP调用           | 直接使用HTTP客户端调用MCP API         | 实现简单，无依赖       | 无会话管理，需自行处理格式转换 | 简单场景或特殊需求    |

## 推荐的集成方式

### 1. 使用SseServerParams (推荐用于远程MCP服务)

这是连接远程MCP服务的标准方式，支持事件流和会话管理。

```python
async def init_mcp_tools(self):
    """初始化MCP工具 - 使用SseServerParams方式"""
    try:
        from autogen_ext.tools.mcp import SseServerParams, mcp_server_tools
        
        # 创建服务参数
        server_params = SseServerParams(
            url=self.mcp_url,  # MCP服务URL，如"http://localhost:19220"
            headers={"Authorization": "Bearer your-token"}  # 可选的认证头
        )
        
        # 获取工具列表
        tools = await mcp_server_tools(server_params)
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
```

### 2. 使用StdioServerParams (推荐用于本地MCP服务)

这是启动并连接本地MCP服务的推荐方式，通过进程间通信实现低延迟交互。

```python
async def init_mcp_tools(self):
    """初始化MCP工具 - 使用StdioServerParams方式"""
    try:
        from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
        
        # 定义MCP服务启动命令和参数
        server_params = StdioServerParams(
            command="python",  # 或其他命令用于启动MCP服务
            args=["path/to/mcp_server.py", "--port", "19220"]
        )
        
        # 获取工具列表
        tools = await mcp_server_tools(server_params)
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
```

### 3. 使用HttpServerToolProvider (适用于已知工具结构)

适用于直接与HTTP工具API交互且已知工具结构的场景。

```python
async def init_mcp_tools(self):
    """初始化MCP工具 - 使用HttpServerToolProvider方式"""
    try:
        from autogen.tool_factory import HttpServerToolProvider
        
        # 创建HTTP工具提供者
        provider = HttpServerToolProvider(
            server_url=self.mcp_url,
            list_tools_url="/api/v1/tools",  # 工具列表端点
            call_tool_url="/api/v1/call_tool"  # 工具调用端点
        )
        
        # 获取工具列表
        tools = await provider.list_tools()
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
```

### 4. 使用直接HTTP调用 (适用于特殊需求)

最基础的实现方式，适用于特殊需求或不依赖AutoGen适配层的情况。

```python
async def init_mcp_tools(self):
    """初始化MCP工具 - 使用直接HTTP调用"""
    try:
        import aiohttp
        from autogen.agentchat.tool import Tool
        
        # 直接HTTP请求获取工具列表
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.mcp_url}/api/v1/tools") as response:
                if response.status != 200:
                    raise Exception(f"MCP服务返回非200状态码: {response.status}")
                data = await response.json()
                raw_tools = data.get("tools", [])
        
        # 转换为AutoGen Tool对象
        tools = []
        for raw_tool in raw_tools:
            # 转换参数格式
            tool_params = []
            for param in raw_tool.get("parameters", []):
                # 这里需要根据实际格式进行处理
                tool_params.append({
                    "name": param.get("name"),
                    "description": param.get("description"),
                    "type": param.get("type"),
                    "required": param.get("required", False)
                })
            
            # 创建Tool对象
            tool = Tool(
                name=raw_tool.get("name"),
                description=raw_tool.get("description"),
                parameters=tool_params,
                callback=self._create_tool_callback(raw_tool.get("name"))
            )
            tools.append(tool)
            
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
    
def _create_tool_callback(self, tool_name):
    """创建工具回调函数"""
    async def tool_callback(**kwargs):
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.mcp_url}/api/v1/call_tool",
                json={"tool_name": tool_name, "parameters": kwargs}
            ) as response:
                if response.status != 200:
                    raise Exception(f"工具调用失败: {response.status}")
                result = await response.json()
                return result.get("result")
    
    return tool_callback
```

## 双协议集成方案

在我们的系统中，采用了一种特殊的"双协议"集成策略，同时支持两种不同的协议来满足不同组件的需求：

### 双协议架构设计

1. **Agent与MCP服务之间使用SSE协议**
   - AutoGen提供了对SSE协议的标准支持
   - 适合服务器到Agent的单向数据流传输
   - 便于工具执行结果的异步获取

2. **前端与MCP服务之间使用WebSocket协议**
   - 支持双向实时通信
   - 更适合交互式界面的实时反馈
   - 优化了用户体验和响应性能

### 双协议实现要点

#### MCP服务器配置

MCP服务器需要同时支持SSE和WebSocket两种协议：

```python
def setup_routes(self):
    """设置路由"""
    # ... 其他路由 ...
    
    # WebSocket支持的路由 - 用于前端连接
    self.app.router.add_get("/api/v1/connect", self.handle_websocket_connect)
    
    # SSE支持路由 - 用于AutoGen连接
    self.app.router.add_get("/api/v1/sse", self.handle_sse_connect)
```

#### SSE处理函数实现

```python
async def handle_sse_connect(self, request: web.Request) -> web.StreamResponse:
    """处理SSE连接请求，适用于AutoGen通过SseServerParams连接"""
    # 创建SSE响应
    response = web.StreamResponse()
    response.headers['Content-Type'] = 'text/event-stream'
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['Connection'] = 'keep-alive'
    await response.prepare(request)
    
    # 获取请求参数
    session_id = request.query.get('session_id', f"sse-{time.time()}")
    
    # 发送连接成功事件
    await response.write(f"event: connected\ndata: {json.dumps({'session_id': session_id})}\n\n".encode())
    
    try:
        # 订阅事件并处理
        # ... 事件处理逻辑 ...
    except Exception as e:
        logger.error(f"SSE处理错误: {str(e)}")
    finally:
        return response
```

#### Agent连接配置

在提示词增强Agent中，连接到MCP服务的SSE端点：

```python
async def init_mcp_tools(self):
    """初始化MCP工具"""
    try:
        # 确保MCP URL以/api/v1/sse结尾
        sse_url = self.mcp_url
        if not sse_url.endswith('/api/v1/sse'):
            if sse_url.endswith('/'):
                sse_url += 'api/v1/sse'
            else:
                sse_url += '/api/v1/sse'
        
        # 创建SSE服务参数
        server_params = SseServerParams(
            url=sse_url,
            headers={"Content-Type": "application/json"}
        )
        
        # 获取MCP工具
        tools = await mcp_server_tools(server_params)
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
```

#### 前端WebSocket实现

前端通过WebSocket连接到MCP服务：

```javascript
// 创建WebSocket连接
const wsUrl = `${mcpWsUrl}/api/v1/connect`;
const ws = new WebSocket(wsUrl);

// 连接打开时，发送创建会话命令
ws.onopen = () => {
  ws.send(JSON.stringify({
    cmd: "call_tool",
    tool_name: "create_session",
    params: {
      session_id: sessionId,
      session_name: `prompt-enhancer-${Date.now()}`,
      meta_data: { /* ... */ }
    }
  }));
};

// 处理接收的消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // 处理不同类型的事件
};
```

### 双协议方案的优势

1. **协议隔离**：每个组件使用最适合其需求的协议
2. **无缝集成**：与AutoGen框架标准集成方式兼容
3. **用户体验优化**：实时反馈增强用户体验
4. **架构清晰**：职责分离，便于维护和扩展
5. **性能优化**：针对不同场景选择最优连接方式

## 在我们的项目中的实现方式

在当前的提示词增强智能体中，我们选择了使用`SseServerParams`方式与MCP服务集成，这是因为：

1. 它提供了标准化的接口，与AutoGen框架无缝集成
2. 支持远程MCP服务连接，增强了系统灵活性
3. 自动处理会话管理和工具格式转换
4. 提供了更好的错误处理和重试机制

实现代码如下：

```python
async def init_mcp_tools(self):
    """初始化MCP工具"""
    try:
        logger.info(f"连接到MCP服务: {self.mcp_url}")
        
        # 使用SseServerParams直接连接HTTP服务
        from autogen_ext.tools.mcp import SseServerParams, mcp_server_tools
        server_params = SseServerParams(url=self.mcp_url)
        tools = await mcp_server_tools(server_params)
        
        self.mcp_tools = tools
        logger.info(f"已连接到MCP服务并初始化工具，共获取 {len(tools)} 个工具")
        return tools
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        raise
```

## 前端与MCP服务的交互

前端界面需要通过WebSocket与MCP服务建立连接，以接收实时的思考过程和生成内容。以下是前端实现示例：

```javascript
// 连接到MCP WebSocket
function connectToMcpWebSocket(sessionId) {
  // 关闭之前的连接
  if (this.ws) {
    this.ws.close();
  }
  
  // 创建新的WebSocket连接
  const wsUrl = `ws://localhost:19220/api/v1/connect`;
  this.ws = new WebSocket(wsUrl);
  
  this.ws.onopen = () => {
    console.log('已连接到MCP WebSocket');
    
    // 连接后发送创建会话命令
    this.ws.send(
      JSON.stringify({
        cmd: "call_tool",
        tool_name: "create_session",
        params: {
          session_id: sessionId,
          session_name: `prompt-enhancer-${Date.now()}`,
          meta_data: {
            agent_name: "PromptEnhancer",
            agent_type: "assistant",
          },
        },
      })
    );
  };
  
  // 处理接收到的消息
  this.ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    // 处理不同类型的事件...
  };
}
```

## 常见问题与解决方案

### 1. "Unsupported tool type: <class 'dict'>"错误

**问题**：直接HTTP获取的工具是原始字典格式，而AutoGen期望Tool对象。

**解决方案**：使用AutoGen提供的`SseServerParams`或`StdioServerParams`，它们会自动处理格式转换。如需手动处理，参考上述"直接HTTP调用"示例中的格式转换部分。

### 2. SSE连接失败

**问题**：SSE连接失败，返回错误"Expected response header Content-Type to contain 'text/event-stream'"。

**解决方案**：
- 确认MCP服务配置了正确的SSE端点，响应头必须包含`Content-Type: text/event-stream`
- 使用正确的SSE端点URL，通常为`/api/v1/sse`
- 检查服务器SSE实现是否符合SSE协议标准

### 3. MCP服务连接失败

**问题**：无法连接到MCP服务。

**解决方案**：
- 确认MCP服务已启动并在正确的端口运行
- 检查URL格式是否正确（如http://localhost:19220）
- 查看服务日志以获取详细错误信息
- 尝试使用健康检查端点验证服务状态：`/health`、`/healthz`或`/`

### 4. 工具列表为空

**问题**：成功连接到MCP服务，但获取不到工具列表。

**解决方案**：
- 检查MCP服务是否正确注册了工具
- 验证权限和认证设置
- 查看MCP服务日志以获取更多信息

## 结论

选择适当的MCP服务集成方式应基于以下考虑因素：
1. 服务部署位置（本地或远程）
2. 性能要求（低延迟或高吞吐量）
3. 功能需求（会话管理、工具发现等）
4. 系统复杂度（简单直接或功能完备）

在大多数情况下，推荐使用AutoGen提供的标准适配器（`SseServerParams`或`StdioServerParams`）来确保最佳兼容性和功能支持。

对于高级应用场景，如我们的系统，可以考虑实现双协议策略，针对不同组件使用最适合的通信协议。 