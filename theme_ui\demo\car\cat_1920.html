<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能车机界面 - 1920分辨率</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'dark-900': 'rgba(15, 23, 42, 0.9)',
            'dark-800': 'rgba(15, 23, 42, 0.8)',
            'dark-700': 'rgba(30, 41, 59, 0.7)',
            'dark-600': 'rgba(30, 41, 59, 0.6)',
            'dark-500': 'rgba(30, 41, 59, 0.5)',
            'dark-400': 'rgba(30, 41, 59, 0.4)',
            'glasswhite': 'rgba(255, 255, 255, 0.1)',
          },
          boxShadow: {
            'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
          }
        }
      }
    }
  </script>
  <style>
    body {
      overflow: hidden;
      background-color: #000;
      font-family: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
    }
    
    .bg-noise {
      background-image: url('https://images.unsplash.com/photo-1534274867514-d5b47ef89ed7?q=80&w=2940&auto=format&fit=crop');
      background-size: cover;
      background-position: center;
    }
    
    .glass-card {
      background: rgba(30, 41, 59, 0.4);
      backdrop-filter: blur(12px) saturate(180%);
      -webkit-backdrop-filter: blur(12px) saturate(180%);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    /* 防止滚动条出现 */
    ::-webkit-scrollbar {
      display: none;
    }
  </style>
</head>

<body class="h-screen w-screen text-white bg-noise">
  <!-- 主容器 -->
  <div class="relative h-full w-full p-4 flex flex-col">
    
    <!-- 顶部状态栏 -->
    <div class="flex justify-between items-center mb-5">
      <!-- 左侧速度显示 -->
      <div class="flex items-end">
        <div class="text-7xl font-bold">120</div>
        <div class="flex flex-col ml-2 mb-1.5">
          <span class="text-sm text-gray-300">km/h</span>
          <div class="flex items-center mt-1">
            <span class="bg-green-500 px-2 text-xs font-medium rounded-sm">READY</span>
            <div class="flex items-center ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </div>
          </div>
          <div class="flex items-center mt-1">
            <div class="h-1.5 w-20 bg-gray-700 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-green-500 to-green-400 w-full"></div>
            </div>
            <span class="ml-1 text-xs text-gray-300">100%</span>
          </div>
          <div class="text-xs text-gray-400 mt-1">
            <span>480 km</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧状态图标 -->
      <div class="flex items-center space-x-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
        </svg>
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19l-7-7 7-7m8 14l7-7-7-7" />
          </svg>
          <span class="text-xs ml-1">4G</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.072 0m-9.9-2.828a9 9 0 0112.728 0" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01" />
        </svg>
        <div class="text-xl font-medium">11:36</div>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="glass-card rounded-xl mb-5 py-3 px-4">
      <div class="flex justify-center">
        <div class="flex space-x-10">
          <div class="flex flex-col items-center opacity-100">
            <span class="text-white text-sm font-medium">应用中心</span>
            <div class="h-1 w-full bg-white rounded-full mt-2"></div>
          </div>
          <div class="flex flex-col items-center opacity-60">
            <span class="text-white text-sm">小程序</span>
            <div class="h-1 w-full bg-transparent rounded-full mt-2"></div>
          </div>
          <div class="flex flex-col items-center opacity-60">
            <span class="text-white text-sm">HUAWEI Hicar</span>
            <div class="h-1 w-full bg-transparent rounded-full mt-2"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 应用图标网格 - 使用一体化毛玻璃背景 -->
    <div class="glass-card rounded-xl p-6 mb-5">
      <div class="grid grid-cols-5 gap-6">
        <!-- 第一行应用图标 -->
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-blue-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <span class="text-sm text-center">AI空间</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-green-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
            </svg>
          </div>
          <span class="text-sm text-center">消息</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-blue-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <span class="text-sm text-center">导航</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-gray-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </div>
          <span class="text-sm text-center">主页</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-blue-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
            </svg>
          </div>
          <span class="text-sm text-center">设置</span>
        </div>
        
        <!-- 第二行应用图标 -->
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-purple-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <span class="text-sm text-center">媒体中心</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-blue-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <span class="text-sm text-center">相册</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-teal-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-teal-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <span class="text-sm text-center">数据分析</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-orange-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-orange-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <span class="text-sm text-center">支付</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-red-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-red-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <span class="text-sm text-center">应用商店</span>
        </div>
        
        <!-- 第三行应用图标 -->
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-indigo-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-indigo-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          </div>
          <span class="text-sm text-center">夜间模式</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-pink-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-pink-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <span class="text-sm text-center">健康</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-green-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
            </svg>
          </div>
          <span class="text-sm text-center">微信</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-teal-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-teal-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
          <span class="text-sm text-center">天气</span>
        </div>
        
        <div class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg">
          <div class="bg-purple-600/40 w-16 h-10 rounded-lg flex items-center justify-center mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
            </svg>
          </div>
          <span class="text-sm text-center">游戏</span>
        </div>
      </div>
    </div>
    
    <!-- 底部工具栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-black h-16 flex justify-between items-center px-5">
      <div class="flex items-center space-x-8">
        <!-- 左侧工具图标 -->
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
          </svg>
        </button>
      </div>
      
      <div class="flex items-center space-x-8">
        <!-- 中间工具图标 -->
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
        </button>
      </div>
      
      <div class="flex items-center space-x-6">
        <!-- 右侧控制按钮 -->
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <div class="flex flex-col items-center">
          <div class="text-lg font-medium text-white">28.5</div>
        </div>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7" />
          </svg>
        </button>
        
        <div class="text-sm font-medium text-white mr-1">AUTO</div>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        </button>
        
        <button class="p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 104 0 2 2 0 012-2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h.5A2.5 2.5 0 0020 5.5v-1.65" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- 添加交互脚本 -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 应用选项卡切换
      const tabs = document.querySelectorAll('.flex.space-x-10 .flex.flex-col.items-center');
      
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有标签的活跃状态
          tabs.forEach(t => {
            t.classList.remove('opacity-100');
            t.classList.add('opacity-60');
            t.querySelector('.h-1').classList.remove('bg-white');
            t.querySelector('.h-1').classList.add('bg-transparent');
          });
          
          // 添加当前标签的活跃状态
          this.classList.remove('opacity-60');
          this.classList.add('opacity-100');
          this.querySelector('.h-1').classList.remove('bg-transparent');
          this.querySelector('.h-1').classList.add('bg-white');
        });
      });
      
      // 应用图标点击效果
      const appCards = document.querySelectorAll('.glass-card.rounded-lg');
      
      appCards.forEach(card => {
        card.addEventListener('click', function() {
          this.classList.add('scale-95');
          setTimeout(() => {
            this.classList.remove('scale-95');
          }, 200);
        });
      });
    });
  </script>
</body>
</html>
