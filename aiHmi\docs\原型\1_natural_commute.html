<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自然通勤 (Natural Commute)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'system-ui', sans-serif;
            background-color: #f0f2f5; /* Fallback color */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            overflow: hidden; /* 禁止滚动 */
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        /* 动态氛围层 - 自然风格 */
        .ambiance-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        .floating-leaves {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(76, 175, 80, 0.6);
            border-radius: 50% 0;
            animation: float 8s infinite ease-in-out;
        }
        .floating-leaves:nth-child(1) { left: 10%; animation-delay: 0s; }
        .floating-leaves:nth-child(2) { left: 30%; animation-delay: 2s; }
        .floating-leaves:nth-child(3) { left: 50%; animation-delay: 4s; }
        .floating-leaves:nth-child(4) { left: 70%; animation-delay: 6s; }
        .floating-leaves:nth-child(5) { left: 90%; animation-delay: 1s; }
        @keyframes float {
            0%, 100% { transform: translateY(-10px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            50% { transform: translateY(50vh) rotate(180deg); opacity: 0.8; }
            90% { opacity: 1; }
        }
        .card {
            background-color: rgba(255, 255, 255, 0.6); /* 玻璃拟态基础 */
            backdrop-filter: blur(15px) saturate(150%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px; /* 有机圆角 */
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.2);
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 2rem; /* 卡片间距 */
            width: 100%;
            height: 100%;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        .icon-style {
            color: #3d4b64;
        }
        
        /* 返回按钮样式 */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 1rem 1.5rem;
            color: #3d4b64;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
        
        .back-button i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80');">

    <!-- 返回按钮 -->
    <a href="过渡/complete_transition_demo.html" class="back-button" data-transition="sliding_panel">
        <i class="fas fa-arrow-left"></i>
        返回演示
    </a>

    <!-- 动态氛围层 -->
    <div class="ambiance-layer">
        <div class="floating-leaves"></div>
        <div class="floating-leaves"></div>
        <div class="floating-leaves"></div>
        <div class="floating-leaves"></div>
        <div class="floating-leaves"></div>
    </div>

    <div class="grid-container">

        <!-- Dynamic Island (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-arrow-up-right-from-square fa-lg icon-style"></i>
                <div>
                    <p class="text-lg font-bold text-gray-800">前方500米右转</p>
                    <p class="text-sm text-gray-600">预计8:45到达</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-gray-800">8:15</p>
                <p class="text-sm text-gray-600">AM</p>
            </div>
        </div>

        <!-- Weather Card (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cloud-sun fa-lg text-sky-500"></i>
                <div>
                    <p class="text-lg font-bold text-gray-800">多云转晴</p>
                    <p class="text-sm text-gray-600">空气质量：良好</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-gray-800">22°C</p>
                <p class="text-sm text-gray-600">体感 24°C</p>
            </div>
        </div>

        <!-- VPA Avatar (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-4">
            <img src="https://media.giphy.com/media/3oKIPnAiaMCws8nOsE/giphy.gif" alt="VPA Avatar" class="w-32 h-32 rounded-full border-4 border-white object-cover shadow-lg">
            <p class="mt-4 text-xl font-semibold text-gray-800">小绿</p>
            <p class="text-gray-600">早上好，主人</p>
        </div>

        <!-- Music Card (4x2) -->
        <div class="card col-span-4 row-span-2 flex flex-col p-6">
            <div class="flex items-center mb-4">
                <img src="https://source.unsplash.com/100x100/?album,art" alt="Album Art" class="w-24 h-24 rounded-lg shadow-md">
                <div class="ml-6">
                    <h3 class="text-3xl font-bold text-gray-800">Forest Lullaby</h3>
                    <p class="text-xl text-gray-600">Nature's Harmony</p>
                </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mt-auto mb-4">
                <div class="bg-green-600 h-2.5 rounded-full" style="width: 45%"></div>
            </div>
            <div class="flex items-center justify-around text-4xl icon-style">
                <i class="fas fa-backward-step cursor-pointer hover:text-green-600"></i>
                <i class="fas fa-pause-circle cursor-pointer hover:text-green-600"></i>
                <i class="fas fa-forward-step cursor-pointer hover:text-green-600"></i>
            </div>
        </div>

        <!-- Weather Card (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-6">
            <i class="fas fa-cloud-sun fa-6x text-sky-500"></i>
            <p class="text-6xl font-bold text-gray-800 mt-4">22°C</p>
            <p class="text-xl text-gray-600">多云转晴</p>
        </div>

        <!-- Traffic Card (8x1) -->
        <div class="card col-span-8 row-span-1 flex items-center justify-between px-8">
             <div class="flex items-center space-x-4">
                <i class="fas fa-car-side fa-2x icon-style"></i>
                <div>
                    <p class="text-2xl font-bold text-gray-800">通勤路况</p>
                    <p class="text-lg text-gray-600">科技大道 <span class="text-orange-500 font-semibold">中度拥堵</span>，预计延误5分钟</p>
                </div>
            </div>
            <button class="bg-green-500 text-white font-bold py-3 px-6 rounded-full hover:bg-green-600 transition-colors">
                切换路线
            </button>
        </div>

    </div>

    <!-- 引入过渡管理器 -->
    <script src="过渡/enhanced_transition_manager.js"></script>
    
    <script>
        // 页面加载完成后初始化过渡管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已有全局过渡管理器
            if (!window.enhancedTransitionManager) {
                window.enhancedTransitionManager = new EnhancedTransitionManager({
                    enableDebug: false,
                    enableSnapshots: true,
                    snapshotQuality: 0.8,
                    transitionDuration: 1.2
                });
            }
        });
    </script>

</body>
</html>