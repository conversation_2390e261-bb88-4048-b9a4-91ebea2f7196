import asyncio
import os
import sys
from concurrent.futures import ThreadPoolExecutor
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path
from .api import text_to_image, ui_generation, ui_split, icon_refine, pipeline, content, wan, task_api, kolors, image_to_text, voice_to_voice, magic_camera, image_to_video, car_texture, doodle, coloring_book
from .api.util import router as util_router
from .core.redis_client import RedisClient
from .api.common import logger
from .api.enhance import router as enhance_router
from .core.webscoket import router as ws_router

# 只在非Windows系统上使用uvloop
if sys.platform != 'win32':
    import uvloop
    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# 创建线程池
thread_pool = ThreadPoolExecutor(
    max_workers=min(32, int(os.getenv('PYTHON_THREADPOOL_SIZE', 20))),
    thread_name_prefix='AsyncWorker'
)

# 设置默认线程池大小
import concurrent.futures
concurrent.futures.ThreadPoolExecutor._max_workers = min(32, int(os.getenv('PYTHON_THREADPOOL_SIZE', 20)))

app = FastAPI(
    title="ComfyUI API",
    description="ComfyUI Backend API for various image generation and processing tasks",
    version="1.0.0",
    openapi_url="/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
public_dir = Path("public")
ui_source_dir = public_dir / "ui_source"
ui_source_dir.mkdir(parents=True, exist_ok=True)

# 挂载静态文件目录
app.mount("/ui_source", StaticFiles(directory=str(ui_source_dir)), name="ui_source")

# 包含各个路由模块
app.include_router(text_to_image.router, prefix="/api/v1", tags=["text-to-image"])
app.include_router(ui_generation.router, prefix="/api/v1", tags=["ui-generation"])
app.include_router(ui_split.router, prefix="/api/v1", tags=["ui-split"])
app.include_router(icon_refine.router, prefix="/api/v1", tags=["icon-refine"])
app.include_router(pipeline.router, prefix="/api/v1", tags=["pipeline"])
app.include_router(util_router, prefix="/api")
app.include_router(content.router, prefix="/api/content", tags=["content"])
app.include_router(wan.router, prefix="/api/v1/wan", tags=["wan-video"])
app.include_router(kolors.router, prefix="/api/v1/kolors", tags=["kolors-text-to-image"])
app.include_router(image_to_text.router, prefix="/api/v1", tags=["image-to-text"])
app.include_router(voice_to_voice.router, prefix="/api/v1", tags=["voice-to-voice"])
app.include_router(magic_camera.router, prefix="/api/v1", tags=["magic-camera"])
app.include_router(image_to_video.router, prefix="/api/v1", tags=["image-to-video"])
app.include_router(task_api.router, prefix="/api/v1")
app.include_router(enhance_router)
app.include_router(ws_router)
app.include_router(car_texture.router, prefix="/api/v1", tags=["car-texture"])
app.include_router(doodle.router, prefix="/api", tags=["doodle"])
app.include_router(coloring_book.router, prefix="/api/v1", tags=["coloring-book"])

@app.on_event("startup")
async def startup_event():
    # 设置事件循环的线程池
    loop = asyncio.get_running_loop()
    loop.set_default_executor(thread_pool)
    
    # 测试Redis连接
    if RedisClient.test_connection():
        logger.info("Redis连接成功，状态管理已启用")
    else:
        logger.error("Redis连接失败！请检查Redis配置和连接信息")

@app.on_event("shutdown")
async def shutdown_event():
    thread_pool.shutdown(wait=True)

@app.get("/")
async def root():
    return {"message": "Welcome to ComfyUI API"} 