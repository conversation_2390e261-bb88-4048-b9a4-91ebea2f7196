#!/usr/bin/env python
"""
提示词增强API服务启动脚本
"""
import os
import sys
import logging
import asyncio
import argparse
import uvicorn
import httpx

# 添加父级目录到模块搜索路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from prompt_enhancer_agent import PromptEnhancerAgent
from api import app

async def test_mcp_connection(mcp_url: str):
    """测试MCP连接并获取工具列表"""
    try:
        logger.info("🔍 开始测试MCP连接...")
        
        # 首先检查MCP服务是否在运行
        health_check_url = mcp_url
        if health_check_url.endswith('/'):
            health_check_url += 'health'
        else:
            health_check_url += '/health'
            
        logger.info(f"🔍 检查MCP服务健康状态: {health_check_url}")
        
        # 发送健康检查请求
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                health_response = await client.get(health_check_url)
                if health_response.status_code != 200:
                    logger.warning(f"⚠️ MCP服务健康检查失败，状态码: {health_response.status_code}")
                    logger.warning(f"⚠️ 响应内容: {health_response.text}")
                else:
                    logger.info(f"✅ MCP服务健康检查成功: {health_response.json()}")
            except Exception as e:
                logger.warning(f"⚠️ MCP服务健康检查异常 (这可能是正常的): {str(e)}")
        
        # 创建增强器并尝试获取工具
        logger.info(f"🔄 创建提示词增强智能体并尝试获取工具...")
        enhancer = PromptEnhancerAgent(mcp_url=mcp_url)
        tools = await enhancer.init_mcp_tools()
        
        if tools:
            logger.info(f"✅ MCP连接测试成功！获取到 {len(tools)} 个工具")
            
            # 记录每个获取到的工具
            for i, tool in enumerate(tools):
                if isinstance(tool, dict):
                    logger.info(f"🛠️ 工具 {i+1}: {tool.get('name', '未命名')} - {tool.get('description', '无描述')}")
                else:
                    logger.info(f"🛠️ 工具 {i+1}: {getattr(tool, 'name', '未命名')} - {getattr(tool, 'description', '无描述')}")
            
            return True
        else:
            logger.warning("⚠️ MCP连接测试完成，但未获取到工具")
            return False
    except Exception as e:
        logger.error(f"❌ MCP连接测试失败: {str(e)}")
        logger.exception("详细异常信息:")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="提示词增强服务")
    parser.add_argument("--host", default="0.0.0.0", help="监听主机")
    parser.add_argument("--port", type=int, default=8100, help="监听端口")
    parser.add_argument("--mcp-url", default="http://127.0.0.1:19220", help="MCP服务URL")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别")
    args = parser.parse_args()
    
    # 设置日志级别
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建增强器实例并测试MCP连接
    logger.info(f"🚀 创建提示词增强智能体，MCP服务: {args.mcp_url}")
    app.enhancer_agent = PromptEnhancerAgent(mcp_url=args.mcp_url)
    
    # 运行MCP连接测试
    logger.info(f"📝 初始化API并测试MCP连接...")
    asyncio.run(test_mcp_connection(args.mcp_url))
    
    # 启动API服务
    logger.info(f"🌐 启动提示词增强API服务: http://{args.host}:{args.port}")
    uvicorn.run(app, host=args.host, port=args.port)

if __name__ == "__main__":
    main() 