import logging
import json
import asyncio
import time
from typing import Dict, List, Optional, Set, Any, Callable
import uuid
import base64
import secrets
from dataclasses import asdict

import websockets
from websockets.server import WebSocketServerProtocol
from websockets.exceptions import ConnectionClosed

from .stream_manager import StreamManager
from .stream_types import StreamEvent, StreamEventType

logger = logging.getLogger(__name__)

class WebSocketServer:
    """WebSocket服务器，负责与客户端建立WebSocket连接并传输消息"""
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        """
        初始化WebSocket服务器
        
        Args:
            host: 服务器主机名
            port: 服务器端口
        """
        self.host = host
        self.port = port
        self.stream_manager = StreamManager()
        self.active_connections: Dict[str, Set[WebSocketServerProtocol]] = {}
        self.server = None
        self.connection_info: Dict[str, Dict[str, Any]] = {}  # 连接信息
        
    async def start(self):
        """启动WebSocket服务器"""
        try:
            self.server = await websockets.serve(
                self.ws_handler,
                self.host,
                self.port,
                ping_interval=30,  # 心跳间隔（秒）
                ping_timeout=10    # 心跳超时（秒）
            )
            
            logger.info(f"WebSocket服务器已启动: ws://{self.host}:{self.port}")
            
            # 保持服务器运行
            await self.server.wait_closed()
        except Exception as e:
            logger.error(f"启动WebSocket服务器失败: {str(e)}")
            
    async def stop(self):
        """停止WebSocket服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("WebSocket服务器已停止")
    
    async def authenticate_connection(self, websocket: WebSocketServerProtocol) -> Optional[Dict[str, Any]]:
        """
        验证WebSocket连接
        
        Args:
            websocket: WebSocket连接
            
        Returns:
            Optional[Dict[str, Any]]: 连接信息，验证失败返回None
        """
        try:
            # 获取URL参数
            path = websocket.path
            params = {}
            
            # 解析URL查询参数
            if "?" in path:
                query_string = path.split("?", 1)[1]
                pairs = query_string.split("&")
                for pair in pairs:
                    if "=" in pair:
                        key, value = pair.split("=", 1)
                        params[key] = value
            
            # 检查必须参数
            if "userId" not in params:
                logger.warning("WebSocket连接缺少userId参数")
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "缺少userId参数"
                }))
                return None
                
            user_id = params["userId"]
            session_id = params.get("sessionId")
            
            # 如果没有会话ID，创建新会话
            if not session_id:
                session_id = self.stream_manager.create_session(
                    user_id=user_id,
                    metadata={
                        "connection_time": time.time(),
                        "ip": websocket.remote_address[0] if websocket.remote_address else "unknown"
                    }
                )
                
                if not session_id:
                    logger.error(f"为用户创建会话失败: {user_id}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "创建会话失败"
                    }))
                    return None
                
                logger.info(f"为用户创建新会话: {user_id} -> {session_id}")
            else:
                # 验证会话是否存在
                session_info = self.stream_manager.mongo_client.get_session(session_id)
                if not session_info:
                    logger.warning(f"无效的会话ID: {session_id}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "无效的会话ID"
                    }))
                    return None
                    
                # 检查会话所属用户
                if session_info["userId"] != user_id:
                    logger.warning(f"用户ID与会话不匹配: {user_id} != {session_info['userId']}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "用户ID与会话不匹配"
                    }))
                    return None
                
                logger.info(f"用户连接到现有会话: {user_id} -> {session_id}")
            
            # 创建连接信息
            connection_info = {
                "userId": user_id,
                "sessionId": session_id,
                "connectionTime": time.time(),
                "lastActiveTime": time.time(),
                "remoteAddress": websocket.remote_address[0] if websocket.remote_address else "unknown"
            }
            
            # 发送连接成功消息
            await websocket.send(json.dumps({
                "type": "connected",
                "sessionId": session_id,
                "userId": user_id,
                "timestamp": time.time()
            }))
            
            return connection_info
        
        except Exception as e:
            logger.error(f"验证WebSocket连接失败: {str(e)}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": "连接验证失败"
            }))
            return None
    
    async def ws_handler(self, websocket: WebSocketServerProtocol, path: str):
        """WebSocket连接处理器"""
        connection_id = str(uuid.uuid4())
        
        try:
            # 验证连接
            connection_info = await self.authenticate_connection(websocket)
            if not connection_info:
                return
                
            session_id = connection_info["sessionId"]
            user_id = connection_info["userId"]
            
            # 保存连接信息
            self.connection_info[connection_id] = connection_info
            
            # 添加到活跃连接
            if session_id not in self.active_connections:
                self.active_connections[session_id] = set()
            self.active_connections[session_id].add(websocket)
            
            # 发送欢迎消息
            await websocket.send(json.dumps({
                "type": "welcome",
                "message": "连接成功，开始接收消息",
                "sessionId": session_id,
                "timestamp": time.time()
            }))
            
            # 订阅Redis消息
            self.stream_manager.subscribe(
                session_id=session_id,
                callback=lambda event: asyncio.create_task(self.broadcast_event(session_id, event))
            )
            
            # 发送会话已恢复状态
            self.stream_manager.publish_status(
                session_id=session_id,
                agent_id="system",
                status="session_resumed",
                message="会话已恢复",
                details={"userId": user_id}
            )
            
            # 消息处理循环
            async for message in websocket:
                try:
                    # 更新最后活动时间
                    self.connection_info[connection_id]["lastActiveTime"] = time.time()
                    
                    # 解析消息
                    data = json.loads(message)
                    message_type = data.get("type")
                    
                    # 根据消息类型处理
                    if message_type == "ping":
                        # 心跳请求
                        await websocket.send(json.dumps({
                            "type": "pong",
                            "timestamp": time.time()
                        }))
                    elif message_type == "get_history":
                        # 获取历史消息
                        limit = data.get("limit", 50)
                        history = self.stream_manager.get_session_messages(session_id, limit)
                        await websocket.send(json.dumps({
                            "type": "history",
                            "messages": history,
                            "timestamp": time.time()
                        }))
                    elif message_type == "close_session":
                        # 关闭会话
                        self.stream_manager.close_session(session_id)
                        await websocket.send(json.dumps({
                            "type": "session_closed",
                            "timestamp": time.time()
                        }))
                        break
                    else:
                        # 未知消息类型
                        logger.warning(f"未知的消息类型: {message_type}")
                        await websocket.send(json.dumps({
                            "type": "error",
                            "message": f"未知的消息类型: {message_type}",
                            "timestamp": time.time()
                        }))
                
                except json.JSONDecodeError:
                    logger.warning(f"无效的JSON格式: {message}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "无效的JSON格式",
                        "timestamp": time.time()
                    }))
                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {str(e)}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "处理消息失败",
                        "timestamp": time.time()
                    }))
        
        except ConnectionClosed:
            logger.info(f"WebSocket连接已关闭: {connection_id}")
        except Exception as e:
            logger.error(f"WebSocket处理器异常: {str(e)}")
        finally:
            # 清理连接
            await self.cleanup_connection(connection_id, websocket)
    
    async def cleanup_connection(self, connection_id: str, websocket: WebSocketServerProtocol):
        """清理连接"""
        try:
            # 获取会话ID
            if connection_id in self.connection_info:
                session_id = self.connection_info[connection_id]["sessionId"]
                
                # 从活跃连接中移除
                if session_id in self.active_connections:
                    self.active_connections[session_id].discard(websocket)
                    
                    # 如果没有活跃连接，取消订阅
                    if not self.active_connections[session_id]:
                        self.stream_manager.unsubscribe(session_id)
                        del self.active_connections[session_id]
                
                # 移除连接信息
                del self.connection_info[connection_id]
                
                logger.info(f"连接已清理: {connection_id}")
        except Exception as e:
            logger.error(f"清理连接失败: {str(e)}")
    
    async def broadcast_event(self, session_id: str, event: StreamEvent):
        """广播事件到会话的所有连接"""
        if session_id not in self.active_connections:
            return
            
        # 将事件转换为JSON
        event_json = event.to_json()
        
        # 待移除的连接
        to_remove = set()
        
        # 广播到所有连接
        for websocket in self.active_connections[session_id]:
            try:
                await websocket.send(event_json)
            except Exception as e:
                logger.error(f"发送事件到WebSocket失败: {str(e)}")
                to_remove.add(websocket)
        
        # 移除失效的连接
        for websocket in to_remove:
            self.active_connections[session_id].discard(websocket)
            
        # 如果没有活跃连接，取消订阅
        if not self.active_connections[session_id]:
            self.stream_manager.unsubscribe(session_id)
            del self.active_connections[session_id]

# 辅助函数
def run_websocket_server(host: str = "localhost", port: int = 8765):
    """启动WebSocket服务器（阻塞）"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    server = WebSocketServer(host, port)
    
    # 获取事件循环
    loop = asyncio.get_event_loop()
    
    try:
        loop.run_until_complete(server.start())
    except KeyboardInterrupt:
        loop.run_until_complete(server.stop())
    finally:
        loop.close()

if __name__ == "__main__":
    # 当作为脚本运行时，启动WebSocket服务器
    run_websocket_server() 