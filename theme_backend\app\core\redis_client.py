import redis
import json
from typing import Any, Dict, Optional, Union
import logging

# 配置Redis连接
REDIS_HOST = "***********"
REDIS_PORT = 5182
REDIS_PASSWORD = "kLKe3NFM4RZMgXhA"
REDIS_EXPIRY_TIME = 60 * 60 * 24  # 24小时过期（秒）

logger = logging.getLogger(__name__)

# 创建Redis连接池
redis_pool = redis.ConnectionPool(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    decode_responses=True,  # 自动解码响应
    socket_timeout=5,       # 连接超时时间
    socket_connect_timeout=5,
    retry_on_timeout=True   # 超时时自动重试
)

class RedisClient:
    """Redis客户端封装，提供状态信息存储和获取功能"""
    
    @staticmethod
    def get_redis():
        """获取Redis连接"""
        return redis.Redis(connection_pool=redis_pool)
    
    @staticmethod
    def set_task_info(task_id: str, task_info: Dict[str, Any]) -> bool:
        """
        存储任务信息到Redis
        
        Args:
            task_id: 任务ID
            task_info: 任务信息字典
            
        Returns:
            bool: 是否设置成功
        """
        try:
            r = RedisClient.get_redis()
            # 将任务信息转换为JSON字符串
            task_info_json = json.dumps(task_info)
            # 设置到Redis并添加过期时间
            result = r.setex(f"task:{task_id}", REDIS_EXPIRY_TIME, task_info_json)
            logger.info(f"任务信息已存储到Redis: {task_id}")
            return result
        except Exception as e:
            logger.error(f"存储任务信息到Redis失败: {str(e)}")
            return False
    
    @staticmethod
    def get_task_info(task_id: str) -> Optional[Dict[str, Any]]:
        """
        从Redis获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务信息字典，不存在则返回None
        """
        try:
            r = RedisClient.get_redis()
            task_info_json = r.get(f"task:{task_id}")
            if task_info_json:
                return json.loads(task_info_json)
            return None
        except Exception as e:
            logger.error(f"从Redis获取任务信息失败: {str(e)}")
            return None
    
    @staticmethod
    def update_task_info(task_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新Redis中的任务信息
        
        Args:
            task_id: 任务ID
            updates: 需要更新的字段
            
        Returns:
            bool: 是否更新成功
        """
        try:
            r = RedisClient.get_redis()
            # 先获取现有数据
            key = f"task:{task_id}"
            task_info_json = r.get(key)
            
            if not task_info_json:
                logger.warning(f"任务信息不存在，无法更新: {task_id}")
                return False
                
            # 更新任务信息
            task_info = json.loads(task_info_json)
            task_info.update(updates)
            
            # 重新保存并刷新过期时间
            result = r.setex(key, REDIS_EXPIRY_TIME, json.dumps(task_info))
            logger.info(f"任务信息已更新: {task_id}")
            return result
        except Exception as e:
            logger.error(f"更新任务信息失败: {str(e)}")
            return False
    
    @staticmethod
    def delete_task_info(task_id: str) -> bool:
        """
        删除Redis中的任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            r = RedisClient.get_redis()
            result = r.delete(f"task:{task_id}")
            return result > 0
        except Exception as e:
            logger.error(f"删除任务信息失败: {str(e)}")
            return False
            
    @staticmethod
    def test_connection() -> bool:
        """
        测试Redis连接
        
        Returns:
            bool: 是否能够连接到Redis
        """
        try:
            r = RedisClient.get_redis()
            r.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接测试失败: {str(e)}")
            return False 