import logging
import json
import time
import uuid
from typing import Dict, List, Optional, Union, Any, Callable
import threading
import asyncio
from queue import Queue

import redis_client
from redis_client import RedisClient
import mongo_client
from mongo_client import MongoClient
import stream_types
from stream_types import (
    StreamEvent, StreamEventType,
    create_token_event, create_thinking_step_event,
    create_code_block_event, create_tool_call_event,
    create_completion_event, create_error_event,
    create_status_event
)

logger = logging.getLogger(__name__)

class StreamManager:
    """流式输出管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(StreamManager, cls).__new__(cls)
            cls._instance._init()
        return cls._instance
    
    def _init(self):
        """初始化"""
        self.redis_client = RedisClient()
        self.mongo_client = MongoClient()
        self.active_sessions = {}  # 活跃会话
        self._subscribe_threads = {}  # 订阅线程
        
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4())
    
    def create_session(self, user_id: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            metadata: 会话元数据
            
        Returns:
            str: 会话ID
        """
        session_id = self.generate_session_id()
        logger.info(f"🔄 StreamManager.create_session 开始: session_id={session_id}, user_id={user_id}")
        logger.info(f"�� 元数据: {metadata}")
        
        # 创建MongoDB会话记录
        logger.info(f"👉 尝试创建MongoDB会话记录")
        try:
            result = self.mongo_client.create_session(session_id, user_id, metadata)
            logger.info(f"🔙 MongoDB创建结果: {result}")
            
            if not result:
                logger.warning(f"⚠️ MongoDB创建会话失败，但将继续处理: {session_id}")
                # 即使MongoDB失败，我们也继续处理
        except Exception as e:
            logger.error(f"❌ MongoDB创建会话异常: {str(e)}")
            logger.exception("MongoDB详细异常:")
            # 即使MongoDB失败，我们也继续处理
        
        # 初始化Redis会话状态
        logger.info(f"👉 尝试初始化Redis会话状态")
        try:
            initial_state = {
                "status": "created",
                "userId": user_id,
                "createTime": time.time(),
                "lastActiveTime": time.time(),
                "metadata": metadata or {}
            }
            redis_result = self.redis_client.set_task_info(f"session:{session_id}", initial_state)
            logger.info(f"🔙 Redis设置结果: {redis_result}")
        except Exception as e:
            logger.error(f"❌ Redis设置会话状态异常: {str(e)}")
            logger.exception("Redis详细异常:")
            # 即使Redis失败，我们也继续处理
        
        # 记录活跃会话
        self.active_sessions[session_id] = {
            "userId": user_id,
            "createTime": time.time(),
            "lastActiveTime": time.time()
        }
        
        logger.info(f"✅ 会话创建成功: {session_id}")
        return session_id
    
    def close_session(self, session_id: str) -> bool:
        """
        关闭会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否关闭成功
        """
        # 更新MongoDB会话状态
        result = self.mongo_client.update_session(session_id, {"status": "closed"})
        
        # 从活跃会话中移除
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            
        # 发布会话关闭事件
        self.publish_event(create_status_event(
            agent_id="system",
            session_id=session_id,
            status="closed",
            message="会话已关闭"
        ))
        
        logger.info(f"会话已关闭: {session_id}")
        return result
    
    def publish_event(self, event: StreamEvent) -> bool:
        """
        发布事件
        
        Args:
            event: 流式事件
            
        Returns:
            bool: 是否发布成功
        """
        try:
            # 获取Redis连接
            r = self.redis_client.get_redis()
            
            # 根据事件类型确定通道
            session_id = event.session_id
            agent_id = event.agent_id
            
            if event.type == StreamEventType.ERROR:
                channel = f"system:error:{session_id}"
            elif event.type == StreamEventType.STATUS:
                channel = f"system:status:{session_id}"
            elif event.type == StreamEventType.TOOL_CALL:
                channel = f"agent:tool_call:{agent_id}:{session_id}"
            else:
                channel = f"agent:thinking:{agent_id}:{session_id}"
                
            # 将事件转换为JSON
            event_json = event.to_json()
            
            # 发布到Redis通道
            result = r.publish(channel, event_json)
            
            # 更新会话最后活动时间
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["lastActiveTime"] = time.time()
                
            # 持久化事件到MongoDB
            # 只持久化重要事件，token事件太多会造成性能问题
            if event.type not in [StreamEventType.TOKEN]:
                self.mongo_client.add_message(
                    session_id=session_id,
                    agent_id=agent_id,
                    message_type=event.type,
                    content=event.to_dict()["payload"]
                )
                
            logger.debug(f"事件已发布: {channel}")
            return True
        except Exception as e:
            logger.error(f"发布事件失败: {str(e)}")
            return False
    
    def subscribe(self, session_id: str, callback: Callable[[StreamEvent], None]) -> bool:
        """
        订阅会话事件
        
        Args:
            session_id: 会话ID
            callback: 回调函数
            
        Returns:
            bool: 是否订阅成功
        """
        try:
            # 如果已有该会话的订阅线程，先取消
            if session_id in self._subscribe_threads:
                self.unsubscribe(session_id)
                
            # 启动订阅线程
            thread = threading.Thread(
                target=self._subscription_worker,
                args=(session_id, callback),
                daemon=True
            )
            thread.start()
            
            self._subscribe_threads[session_id] = {
                "thread": thread,
                "stop_event": threading.Event()
            }
            
            logger.info(f"已订阅会话事件: {session_id}")
            return True
        except Exception as e:
            logger.error(f"订阅会话事件失败: {str(e)}")
            return False
    
    def _subscription_worker(self, session_id: str, callback: Callable[[StreamEvent], None]):
        """订阅工作线程"""
        try:
            # 获取Redis连接
            r = self.redis_client.get_redis()
            
            # 订阅通道
            pubsub = r.pubsub()
            
            # 订阅所有相关通道
            channels = [
                f"system:status:{session_id}",
                f"system:error:{session_id}"
            ]
            
            # 查询会话关联的所有智能体
            session_info = self.mongo_client.get_session(session_id)
            if session_info and "agents" in session_info:
                for agent_id in session_info["agents"]:
                    channels.append(f"agent:thinking:{agent_id}:{session_id}")
                    channels.append(f"agent:tool_call:{agent_id}:{session_id}")
            else:
                # 如果没有特定智能体，订阅所有通配符
                channels.append(f"agent:thinking:*:{session_id}")
                channels.append(f"agent:tool_call:*:{session_id}")
            
            # 执行订阅
            pubsub.subscribe(*channels)
            
            # 事件循环
            stop_event = self._subscribe_threads[session_id]["stop_event"]
            for message in pubsub.listen():
                # 检查是否应该停止
                if stop_event.is_set():
                    break
                    
                # 只处理消息类型为message的数据
                if message["type"] == "message":
                    try:
                        # 解析事件
                        event = StreamEvent.from_json(message["data"])
                        # 调用回调
                        callback(event)
                    except Exception as e:
                        logger.error(f"处理订阅消息失败: {str(e)}")
                        
            # 取消订阅并关闭
            pubsub.unsubscribe()
            pubsub.close()
            
        except Exception as e:
            logger.error(f"订阅工作线程异常: {str(e)}")
            
    def unsubscribe(self, session_id: str) -> bool:
        """
        取消订阅
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否取消成功
        """
        if session_id not in self._subscribe_threads:
            return False
            
        try:
            # 设置停止标志
            self._subscribe_threads[session_id]["stop_event"].set()
            
            # 等待线程结束
            self._subscribe_threads[session_id]["thread"].join(timeout=2.0)
            
            # 移除记录
            del self._subscribe_threads[session_id]
            
            logger.info(f"已取消订阅: {session_id}")
            return True
        except Exception as e:
            logger.error(f"取消订阅失败: {str(e)}")
            return False
    
    def save_context(self, session_id: str, agent_id: str, name: str, 
                     value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        保存上下文数据
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            name: 上下文名称
            value: 上下文数据
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否保存成功
        """
        return self.mongo_client.save_context(session_id, agent_id, name, value, ttl)
    
    def get_context(self, session_id: str, agent_id: str, name: str) -> Optional[Dict[str, Any]]:
        """
        获取上下文数据
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            name: 上下文名称
            
        Returns:
            Optional[Dict[str, Any]]: 上下文数据
        """
        return self.mongo_client.get_context(session_id, agent_id, name)

    def get_session_messages(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取会话消息历史
        
        Args:
            session_id: 会话ID
            limit: 消息数量限制
            
        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        return self.mongo_client.get_messages(session_id, limit)
    
    async def stream_tokens(self, session_id: str, agent_id: str, tokens: List[str]) -> bool:
        """
        流式输出token
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            tokens: token列表
            
        Returns:
            bool: 是否成功
        """
        try:
            for token in tokens:
                # 创建token事件
                event = create_token_event(agent_id, session_id, token)
                
                # 发布事件
                self.publish_event(event)
                
                # 适当延迟，模拟流式效果
                await asyncio.sleep(0.01)
                
            return True
        except Exception as e:
            logger.error(f"流式输出token失败: {str(e)}")
            return False
    
    async def stream_thinking(self, session_id: str, agent_id: str, 
                             steps: List[Dict[str, Union[str, float]]]) -> bool:
        """
        流式输出思考步骤
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            steps: 思考步骤列表，每步包含icon、text和可选的progress
            
        Returns:
            bool: 是否成功
        """
        try:
            for i, step in enumerate(steps):
                # 创建思考步骤事件
                event = create_thinking_step_event(
                    agent_id=agent_id,
                    session_id=session_id,
                    icon=step.get("icon", "🤔"),
                    text=step.get("text", ""),
                    progress=step.get("progress", (i + 1) / len(steps))
                )
                
                # 发布事件
                self.publish_event(event)
                
                # 适当延迟
                await asyncio.sleep(0.5)
                
            return True
        except Exception as e:
            logger.error(f"流式输出思考步骤失败: {str(e)}")
            return False
    
    def publish_code_block(self, session_id: str, agent_id: str, 
                         language: str, code: str) -> bool:
        """
        发布代码块
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            language: 代码语言
            code: 代码内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建代码块事件
            event = create_code_block_event(
                agent_id=agent_id,
                session_id=session_id,
                language=language,
                code=code
            )
            
            # 发布事件
            return self.publish_event(event)
        except Exception as e:
            logger.error(f"发布代码块失败: {str(e)}")
            return False
    
    def publish_tool_call(self, session_id: str, agent_id: str, tool_name: str,
                       tool_input: Dict[str, Any], tool_output: Optional[Dict[str, Any]] = None) -> bool:
        """
        发布工具调用
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            tool_name: 工具名称
            tool_input: 工具输入
            tool_output: 工具输出（可选）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建工具调用事件
            event = create_tool_call_event(
                agent_id=agent_id,
                session_id=session_id,
                tool_name=tool_name,
                tool_input=tool_input,
                tool_output=tool_output
            )
            
            # 发布事件
            return self.publish_event(event)
        except Exception as e:
            logger.error(f"发布工具调用失败: {str(e)}")
            return False
    
    def publish_completion(self, session_id: str, agent_id: str, text: str) -> bool:
        """
        发布完成事件
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            text: 完整内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建完成事件
            event = create_completion_event(
                agent_id=agent_id,
                session_id=session_id,
                text=text
            )
            
            # 发布事件
            return self.publish_event(event)
        except Exception as e:
            logger.error(f"发布完成事件失败: {str(e)}")
            return False
    
    def publish_error(self, session_id: str, agent_id: str, 
                   error_type: str, message: str, 
                   details: Optional[Dict[str, Any]] = None) -> bool:
        """
        发布错误事件
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            error_type: 错误类型
            message: 错误消息
            details: 错误详情
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建错误事件
            event = create_error_event(
                agent_id=agent_id,
                session_id=session_id,
                error_type=error_type,
                message=message,
                details=details
            )
            
            # 发布事件
            return self.publish_event(event)
        except Exception as e:
            logger.error(f"发布错误事件失败: {str(e)}")
            return False
    
    def publish_status(self, session_id: str, agent_id: str,
                    status: str, message: Optional[str] = None,
                    details: Optional[Dict[str, Any]] = None) -> bool:
        """
        发布状态事件
        
        Args:
            session_id: 会话ID
            agent_id: 智能体ID
            status: 状态
            message: 状态消息
            details: 状态详情
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建状态事件
            event = create_status_event(
                agent_id=agent_id,
                session_id=session_id,
                status=status,
                message=message,
                details=details
            )
            
            # 发布事件
            return self.publish_event(event)
        except Exception as e:
            logger.error(f"发布状态事件失败: {str(e)}")
            return False 