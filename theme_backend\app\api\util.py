from fastapi import APIRouter, HTTPException
from PIL import Image
import shutil
from pathlib import Path
import traceback
from .common import logger
import json
from typing import Dict, Any
import zipfile
import os
from fastapi.responses import FileResponse
from ..core.redis_client import RedisClient
import time
import httpx
from .pipeline import TaskStatus, pipeline_manager
from ..core.minio_util import async_put_file
from ..core.config import settings

router = APIRouter()

# 使用common.py中定义的API_URL
COMFYUI_API_URL = settings.API_URL

def convert_png_to_jpg(png_path: Path, jpg_path: Path) -> None:
    """将PNG图片转换为JPG格式"""
    try:
        with Image.open(png_path) as img:
            # 如果图片有透明通道，先将其转换为RGB
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                bg = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                bg.paste(img, mask=img.split()[3] if img.mode == 'RGBA' else None)
                bg.save(jpg_path, 'JPEG', quality=95)
            else:
                img.convert('RGB').save(jpg_path, 'JPEG', quality=95)
    except Exception as e:
        logger.error(f"转换图片失败 {png_path}: {str(e)}")
        raise

# 将转换函数改为普通异步函数，以便内部调用
async def convert_task_images_to_jpg(task_id: str):
    """将指定任务ID的壁纸PNG图片转换为JPG格式"""
    try:
        # 基础路径
        base_path = Path("public/ui_source") / f"mainLine_{task_id}/releases/wallpaper"
        if not base_path.exists():
            raise HTTPException(status_code=404, detail=f"Wallpaper directory not found: {base_path}")

        # 创建备份目录
        backup_path = base_path.parent / "wallpaper_backup"
        logger.info(f"创建备份目录: {backup_path}")
        
        try:
            # 如果已存在备份目录，先删除
            if backup_path.exists():
                shutil.rmtree(backup_path)
            # 创建备份
            shutil.copytree(base_path, backup_path)
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create backup: {str(e)}")

        # 转换计数器
        converted_count = 0
        failed_count = 0

        # 遍历所有PNG文件
        for png_file in base_path.rglob("*.png"):
            try:
                # 构建对应的JPG路径（保持相同的目录结构）
                jpg_file = png_file.with_suffix('.jpg')
                
                # 转换图片
                convert_png_to_jpg(png_file, jpg_file)
                
                # 删除原PNG文件
                png_file.unlink()
                
                converted_count += 1
                logger.info(f"成功转换: {png_file} -> {jpg_file}")
                
            except Exception as e:
                failed_count += 1
                logger.error(f"转换失败 {png_file}: {str(e)}")
                continue

        # 如果所有转换都成功，删除备份
        if failed_count == 0:
            try:
                shutil.rmtree(backup_path)
                logger.info("转换成功，已删除备份")
            except Exception as e:
                logger.error(f"删除备份失败: {str(e)}")
                # 不抛出异常，因为转换已经成功
        else:
            # 如果有失败的转换，恢复备份
            try:
                shutil.rmtree(base_path)
                shutil.move(backup_path, base_path)
                logger.info("检测到转换失败，已恢复备份")
            except Exception as e:
                logger.error(f"恢复备份失败: {str(e)}")
                raise HTTPException(
                    status_code=500, 
                    detail="Failed to restore backup after conversion errors"
                )

        return {
            "status": "success" if failed_count == 0 else "partial_success",
            "converted_count": converted_count,
            "failed_count": failed_count,
            "message": (
                "All wallpapers converted successfully" 
                if failed_count == 0 
                else f"Converted {converted_count} wallpapers, {failed_count} failed"
            )
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"转换过程出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 保留原有的路由端点，但改为调用上面的函数
@router.post("/convert-to-jpg/{task_id}")
async def convert_images_endpoint(task_id: str):
    """API端点：将壁纸PNG转换为JPG"""
    return await convert_task_images_to_jpg(task_id)

# 添加新的取色函数
async def extract_colors(task_id: str) -> Dict[str, Any]:
    """从内容图片中提取指定坐标的颜色"""
    try:
        # 构建图片路径
        image_path = Path(f"public/ui_source/mainLine_{task_id}/content/content.png")
        if not image_path.exists():
            raise HTTPException(status_code=404, detail=f"Content image not found: {image_path}")

        # 打开图片
        with Image.open(image_path) as img:
            # 确保图片是RGB模式
            img = img.convert('RGB')
            
            # 定义取色坐标
            color_positions = {
                'text_color': (200, 103),
                'ai_chuangke_card_content_color': {
                    'car_bg1': (140, 190),
                    'car_bg2': (671, 487)
                },
                'search': {
                    'search_bg': (501, 274)
                },
                'button': {
                    'ai_global_focus_color': {
                        'button': (866, 793),
                        'button_bg': (824, 793)
                    },
                    'normal': {
                        'button': (822, 967),
                        'button_bg': (862, 967)
                    }
                },
                'ai_dock_bg_color': {
                    'ai_dock_content_color': (280, 1352),
                    'ai_global_focus_color': (3000, 1356),
                    'dock_view_bg_color': [(708, 1354), (2448, 1354), (2811, 1352)]
                },
                'media_center': {
                    'activate': {
                        'bg': (3318, 232),
                        'ai_global_focus_color': (3131, 235)
                    },
                    'normal': {
                        'icon': (2900, 1186)
                    }
                },
                'media_control': {
                    'bg': (2843, 1186),
                    'icon': (2892, 1184)
                }
            }

            # 提取颜色函数
            def get_color(pos) -> str:
                try:
                    r, g, b = img.getpixel((pos[0], pos[1]))
                    return f"#{r:02x}{g:02x}{b:02x}"
                except IndexError:
                    logger.warning(f"坐标超出图片范围: {pos}")
                    return "#000000"

            # 递归处理颜色提取
            def process_colors(config):
                if isinstance(config, tuple):
                    return get_color(config)
                elif isinstance(config, list):
                    return [get_color(pos) for pos in config]
                elif isinstance(config, dict):
                    return {k: process_colors(v) for k, v in config.items()}
                return config

            # 提取所有颜色
            colors = process_colors(color_positions)

            # 保存结果
            output_dir = Path(f"public/ui_source/mainLine_{task_id}/releases")
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / "colors.json"

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(colors, f, ensure_ascii=False, indent=2)

            logger.info(f"颜色提取完成，已保存到: {output_path}")
            
            return colors

    except Exception as e:
        logger.error(f"颜色提取失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加新的路由端点
@router.post("/extract-colors/{task_id}")
async def extract_colors_endpoint(task_id: str):
    """提取指定任务的内容图片颜色"""
    try:
        colors = await extract_colors(task_id)
        return {
            "status": "success",
            "task_id": task_id,
            "colors": colors
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理颜色提取请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加压缩函数
async def create_release_archive(task_id: str) -> str:
    """将releases目录打包成zip文件"""
    try:
        # 构建源目录和目标zip文件路径
        source_dir = Path(f"public/ui_source/mainLine_{task_id}/releases")
        if not source_dir.exists():
            raise HTTPException(status_code=404, detail=f"Releases directory not found: {source_dir}")
            
        archive_dir = Path("public/archives")
        archive_dir.mkdir(parents=True, exist_ok=True)
        zip_path = archive_dir / f"mainLine_{task_id}_release.zip"
        
        # 创建zip文件
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 遍历目录下所有文件
            for root, _, files in os.walk(source_dir):
                for file in files:
                    file_path = Path(root) / file
                    # 计算文件在zip中的相对路径
                    arc_path = file_path.relative_to(source_dir)
                    zipf.write(file_path, arc_path)

        logger.info(f"成功创建发布包: {zip_path}")

        logger.info(f"上传发布包: {zip_path}")
        # 上传所有releases目录下的文件
        for foldername, subfolders, filenames in os.walk(source_dir):
            for filename in filenames:
                file_path = Path(foldername) / filename
                relative_path = file_path.relative_to(source_dir)
                with file_path.open('rb') as fileStream:
                    async_put_file({
                        'bucketName': 'changan-theme',
                        'objectName': task_id + "/" + filename,
                        'fileType': file_path.suffix.lstrip('.'),
                        'fileStream': fileStream
                    })

        # 上传生成的zip文件
        with zip_path.open('rb') as zip_stream:
            objectName = task_id + "/" + zip_path.name
            async_put_file({
                'bucketName': 'changan-theme',
                'objectName': objectName,
                'fileType': 'zip',
                'fileStream': zip_stream
            })
        # 删除 public/ui_source/mainLine_{task_id} 路径的文件
        logger.info(f"完成上传发布包")
        return str(zip_path)
        
    except Exception as e:
        logger.error(f"创建发布包失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加压缩接口
@router.post("/create-archive/{task_id}")
async def create_archive_endpoint(task_id: str):
    """创建发布包的API端点"""
    try:
        zip_path = await create_release_archive(task_id)
        return {
            "status": "success",
            "task_id": task_id,
            "archive_path": zip_path
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理压缩请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加下载接口
@router.get("/download-archive/{task_id}")
async def download_archive(task_id: str):
    """下载发布包的API端点"""
    try:
        zip_path = Path(f"public/archives/mainLine_{task_id}_release.zip")
        if not zip_path.exists():
            raise HTTPException(status_code=404, detail="Archive not found")
            
        return FileResponse(
            zip_path,
            media_type="application/zip",
            filename=f"mainLine_{task_id}_release.zip"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理下载请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加图标重命名函数
async def rename_task_icons(task_id: str) -> Dict[str, Any]:
    """根据映射字典重命名任务的图标文件"""
    try:
        from ..core.DictUtil import get_icon_mapping
        
        # 获取图标映射字典
        icon_mapping = get_icon_mapping()
        
        # 构建图标目录路径
        icon_dir = Path("public/ui_source") / f"mainLine_{task_id}/releases/icon"
        if not icon_dir.exists():
            raise HTTPException(status_code=404, detail=f"Icon directory not found: {icon_dir}")

        renamed_count = 0
        skipped_count = 0
        total_icons = len(list(icon_dir.glob("*.png")))
        
        # 遍历目录中的所有PNG文件
        for file in icon_dir.glob("*.png"):
            try:
                # 提取数字部分（例如从 "icon_1.png" 提取 "1"）
                icon_num = int(''.join(filter(str.isdigit, file.stem)))
                old_name = f"icon_{icon_num}"
                
                # 如果在映射字典中存在对应关系则重命名,否则跳过
                if old_name in icon_mapping:
                    new_name = f"{icon_mapping[old_name]}.png"
                    new_path = file.parent / new_name
                    
                    # 如果目标文件已存在，先删除
                    if new_path.exists():
                        os.remove(new_path)
                        
                    # 重命名文件
                    os.rename(file, new_path)
                    renamed_count += 1
                    logger.info(f"重命名图标: {file.name} -> {new_name}")
                else:
                    skipped_count += 1
                    logger.info(f"跳过图标 {old_name}: 在映射字典中未找到对应关系")
            except Exception as e:
                logger.error(f"重命名图标 {file.name} 时出错: {str(e)}")
        
        return {
            "status": "success",
            "renamed_count": renamed_count,
            "skipped_count": skipped_count,
            "total_icons": total_icons,
            "message": f"Successfully renamed {renamed_count} icons, skipped {skipped_count} icons out of {total_icons} total"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图标重命名过程出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

# 添加重命名接口
@router.post("/rename-icons/{task_id}")
async def rename_icons_endpoint(task_id: str):
    """重命名图标的API端点"""
    return await rename_task_icons(task_id)

@router.get("/health")
async def health_check():
    """健康检查接口，验证服务和Redis状态"""
    redis_status = "ok" if RedisClient.test_connection() else "failed"
    
    return {
        "service": "ok",
        "timestamp": time.time(),
        "dependencies": {
            "redis": redis_status
        }
    }

@router.post("/terminate-task/{task_id}")
async def terminate_task(task_id: str):
    """终止指定任务ID的处理
    
    1. 清除ComfyUI队列
    2. 中断当前正在执行的ComfyUI任务
    3. 取消正在执行的异步任务
    4. 终止与任务相关的所有子进程
    5. 立即更新pipeline任务状态为已终止
    """
    try:
        logger.info(f"开始终止任务: {task_id}")
        
        # 检查任务是否存在
        if not pipeline_manager.task_exists(task_id):
            raise HTTPException(status_code=404, detail="Task not found")
            
        # 获取当前任务状态
        task_info = pipeline_manager.get_task_info(task_id)
        current_status = task_info.get("status")
        
        # 如果任务已经完成或失败，则无需终止
        if current_status in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, 
                              TaskStatus.SUCCESS.value, TaskStatus.TERMINATED.value]:
            return {
                "status": "warning",
                "task_id": task_id,
                "message": f"Task already in final state: {current_status}"
            }
        
        # 立即更新任务状态为已终止 - 提前设置状态可以让正在运行的任务检查到终止标志
        pipeline_manager.set_task_status(task_id, TaskStatus.TERMINATED, "Task manually terminated")
        logger.info(f"已将任务 {task_id} 状态设置为终止")
        
        termination_success = True
        errors = []
        
        # 1. 清除ComfyUI队列
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                logger.info("正在清除ComfyUI队列...")
                queue_response = await client.post(
                    f"{COMFYUI_API_URL}/queue",
                    json={"clear": True}
                )
                queue_response.raise_for_status()
                logger.info(f"清除队列结果: {queue_response.status_code} - {queue_response.text}")
            except Exception as e:
                error_msg = f"清除队列失败: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                termination_success = False
                # 继续尝试中断当前任务
        
        # 2. 中断当前ComfyUI任务
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                logger.info("正在中断当前ComfyUI任务...")
                interrupt_response = await client.post(f"{COMFYUI_API_URL}/interrupt")
                interrupt_response.raise_for_status()
                logger.info(f"中断任务结果: {interrupt_response.status_code} - {interrupt_response.text}")
            except Exception as e:
                error_msg = f"中断当前任务失败: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                termination_success = False
                # 即使API调用失败，我们仍然更新状态
        
        # 3. 强制取消异步任务
        try:
            was_cancelled = pipeline_manager.cancel_running_task(task_id)
            if was_cancelled:
                logger.info(f"成功取消任务 {task_id} 的主异步任务")
            else:
                logger.warning(f"未找到任务 {task_id} 的主异步任务或取消失败")
                
            # 查找与任务ID关联的子任务并取消
            cancelled_tasks = await pipeline_manager.cancel_all_related_tasks(task_id)
            logger.info(f"已取消 {cancelled_tasks} 个相关子任务")
        except Exception as e:
            error_msg = f"取消异步任务失败: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            termination_success = False
        
        # 4. 尝试终止所有可能的子进程
        try:
            # 查找并终止关联的进程 (使用Python的multiprocessing模块)
            from ..core.process_manager import terminate_task_processes
            terminated_count = await terminate_task_processes(task_id)
            logger.info(f"已终止 {terminated_count} 个与任务 {task_id} 相关的进程")
        except Exception as e:
            error_msg = f"终止子进程失败: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            termination_success = False
        
        # 确保redis中的状态被正确设置为TERMINATED
        pipeline_manager.set_task_status(task_id, TaskStatus.TERMINATED, 
                                        "Task manually terminated" + (f" with errors: {'; '.join(errors)}" if errors else ""))
        
        # 返回终止结果
        if termination_success:
            logger.info(f"任务 {task_id} 已成功终止")
            return {
                "status": "success",
                "task_id": task_id,
                "message": "Task terminated successfully"
            }
        else:
            # 即使有错误，我们也认为任务已被终止，但返回警告和错误信息
            logger.warning(f"任务 {task_id} 终止过程中有错误，但任务已被标记为终止状态")
            return {
                "status": "partial_success",
                "task_id": task_id,
                "message": "Task marked as terminated with some errors",
                "errors": errors
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"终止任务过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks-status")
async def get_tasks_status():
    """获取ComfyUI队列状态和当前运行任务状态"""
    try:
        comfyui_status = {"queue": [], "executing": None, "error": None}
        
        # 获取ComfyUI队列状态
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{COMFYUI_API_URL}/queue")
                if response.status_code == 200:
                    comfyui_status["queue"] = response.json().get("queue_running", [])
        except Exception as e:
            comfyui_status["error"] = f"无法获取ComfyUI队列信息: {str(e)}"
            logger.error(comfyui_status["error"])
        
        # 获取正在执行的任务
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{COMFYUI_API_URL}/progress")
                if response.status_code == 200:
                    comfyui_status["executing"] = response.json()
        except Exception as e:
            if not comfyui_status["error"]:
                comfyui_status["error"] = f"无法获取ComfyUI进度信息: {str(e)}"
                logger.error(comfyui_status["error"])
        
        # 获取我们自己的任务状态 (仅获取运行中和等待中的任务)
        running_tasks = {}
        try:
            # 仅示例，实际上需要一个获取所有运行中任务的方法
            running_task_ids = list(pipeline_manager.running_tasks.keys())
            for task_id in running_task_ids:
                task_info = pipeline_manager.get_task_info(task_id)
                if task_info and task_info.get("status") in [
                    TaskStatus.PENDING.value, 
                    TaskStatus.RUNNING.value
                ]:
                    running_tasks[task_id] = {
                        "status": task_info.get("status"),
                        "progress": task_info.get("progress"),
                        "current_step": task_info.get("current_step")
                    }
        except Exception as e:
            logger.error(f"获取运行中的任务状态失败: {str(e)}")
        
        return {
            "comfyui": comfyui_status,
            "pipeline_tasks": running_tasks,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

async def get_style_from_prompt(prompt: str) -> str:
    """根据用户输入的主题提示词获取对应的风格
    
    Args:
        prompt: 用户输入的主题提示词
        
    Returns:
        str: 返回风格标识符，可能是 'jianzhi'、'bing' 或 'dunhuang'
    """
    try:
        # 定义风格映射
        style_mapping = {
            "中国风": "jianzhi",
            "冰晶风": "bing",
            "敦煌风": "dunhuang"
        }
        
        # 构建API请求
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://************:8007/v1/chat/completions",
                headers={
                    "Content-Type": "application/json"
                },
                json={
                    "model": "Qwen2.5-32B-Instruct-AWQ",
                    "messages": [
                        {
                            "role": "system",
                            "content": "我有三个风格的图标,分别为冰晶风,中国风,敦煌风,基于用户输入的主题提示词内容告诉我需要什么风格,不要回复其他东西。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                },
                timeout=10.0
            )
            
            if response.status_code == 200:
                result = response.json()
                style_text = result["choices"][0]["message"]["content"].strip()
                
                # 检查返回的风格是否在映射中
                if style_text in style_mapping:
                    return style_mapping[style_text]
                
        # 如果API调用失败或返回无效结果，返回默认值
        return "jianzhi"
        
    except Exception as e:
        logger.error(f"获取风格失败: {str(e)}")
        logger.error(traceback.format_exc())
        return "jianzhi"  # 发生异常时返回默认值
