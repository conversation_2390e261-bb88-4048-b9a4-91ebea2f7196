<template>
  <div class="min-h-screen py-8 relative">
    <!-- 使用星空背景 -->
    <div class="fullscreen-background">
      <StarCanvasBackground 
        :particleDensity="60" 
        primaryColor="rgba(99, 102, 241, 0.5)" 
        secondaryColor="rgba(59, 130, 246, 0.5)" 
        particleColor="rgba(255, 255, 255, 0.6)" 
        :minSize="0.1" 
        :maxSize="0.7" 
        :speed="0.2" 
      />
    </div>

    <div class="container mx-auto px-4 relative z-10">
      <!-- 添加顶部导航栏 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">海报创作</h1>
      </div>

      <!-- 输入区域 -->
      <div class="max-w-2xl mx-auto mb-8">
        <div class="glass-card p-6 rounded-lg">
          <div class="flex gap-4">
            <input 
              v-model="prompt" 
              type="text" 
              placeholder="输入一句话，让AI为你生成独特的视觉艺术..." 
              class="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary"
              @keyup.enter="generatePoster"
            />
            <button 
              @click="generatePoster"
              class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
            >
              <span>生成海报</span>
              <svg v-if="isGenerating" class="animate-spin h-5 w-5" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"/>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="max-w-6xl mx-auto">
        <div class="glass-card p-6 rounded-lg">
          <div class="flex gap-6">
            <!-- 代码预览区域 -->
            <div class="w-1/2 bg-gray-900/80 rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-purple-200 font-medium">代码预览</h3>
                <div class="flex gap-2">
                  <button 
                    v-if="generatedHtml"
                    @click="copyCode"
                    class="px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600 transition-colors flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                    复制代码
                  </button>
                  <button 
                    v-if="generatedHtml"
                    @click="runCode"
                    class="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600 transition-colors"
                  >
                    运行代码
                  </button>
                </div>
              </div>
              <pre class="bg-gray-900 rounded p-4 overflow-x-auto h-[calc(100vh-100px)]"><code class="text-gray-300">{{ generatedHtml || '// 生成的代码将显示在这里' }}</code></pre>
            </div>

            <!-- 运行预览区域 -->
            <div class="w-1/2 aspect-[3/4] relative overflow-hidden rounded-lg bg-gray-900/50">
              <div v-if="!generatedHtml" class="absolute inset-0 flex items-center justify-center">
                <div class="text-center text-gray-400">
                  <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                  </svg>
                  <p>预览区域</p>
                  <p class="text-sm mt-2">输入描述后点击生成按钮</p>
                </div>
              </div>
              <iframe 
                v-else
                ref="previewFrame"
                class="w-full h-full"
                sandbox="allow-scripts allow-same-origin"
                :srcdoc="sanitizedHtml"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import StarCanvasBackground from '../components/StarCanvasBackground.vue';
import { themeApi } from '../api/themeApi';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
// import DOMPurify from 'dompurify';

const prompt = ref('');
const generatedHtml = ref('');
const isGenerating = ref(false);
const taskId = ref('');
const previewFrame = ref<HTMLIFrameElement | null>(null);

// 计算属性：安全的HTML内容
const sanitizedHtml = computed(() => {
  return '';
  // if (!generatedHtml.value) return '';
  // return DOMPurify.sanitize(generatedHtml.value, {
  //   ALLOWED_TAGS: ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'img', 'a', 'button', 'input', 'textarea', 'select', 'option', 'form', 'label', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot', 'style', 'link'],
  //   ALLOWED_ATTR: ['class', 'id', 'style', 'src', 'href', 'alt', 'title', 'target', 'rel', 'type', 'name', 'value', 'placeholder', 'required', 'disabled', 'readonly', 'checked', 'selected'],
  //   ALLOWED_STYLES: ['color', 'background-color', 'font-size', 'font-weight', 'text-align', 'margin', 'padding', 'border', 'border-radius', 'display', 'flex', 'flex-direction', 'justify-content', 'align-items'],
  // });
});

// 运行代码
const runCode = () => {
  if (previewFrame.value) {
    previewFrame.value.srcdoc = generatedHtml.value;
    // previewFrame.value.srcdoc = sanitizedHtml.value;
  }
};

// 生成任务ID
const generateTaskId = (): string => {
  const timestamp = dayjs().format('YYYYMMDDHHmmss');
  const uuid = uuidv4().replace(/-/g, '').substr(0, 16);
  return `${uuid}_${timestamp}`;
};

const generatePoster = async () => {
  if (!prompt.value.trim()) return;
  
  isGenerating.value = true;
  try {
    taskId.value = generateTaskId();
    
    // 调用API生成海报
    const response = await themeApi.textToHtml({
      "model": "Qwen2.5-32B-Instruct-AWQ",
      "messages": [
        {
          "role": "system",
          "content": `你是一个擅长拟态风格的海报设计大师。拟态风格（Neumorphism）海报以极简美学为核心，通过柔和的双层阴影（明暗对比±10%亮度）塑造立体感，典型配色为低饱和中性色。设计采用无边框布局，元素间距≥20px，通过微凸起按钮（外阴影5px）和凹陷式信息框（内阴影3px）形成触觉质感。字体选用无衬线体，严格遵循2.5rem:1.5rem:1rem的黄金字号比例，文本色与背景明度差控制在30%以内。所有元素保持左上光源统一，阴影模糊度随元素尺寸递增（3-10px）。动效采用200-300ms缓动过渡，悬停时轻微位移（1px）和阴影收缩模拟物理反馈。该风格强调材质统一性，禁用高对比色和直角，适合科技、高端品牌等需要细腻质感的场景，暗黑模式需同步调整阴影参数`
        },  
        {
          "role": "user",
          "content": `请根据用户要求生成一个美观的海报HTML代码。\n\n用户要求如下：${prompt.value.trim()}\n\n要求：\n1. 生成完整的HTML代码，包含必要的CSS样式\n2. 使用现代设计风格，确保美观\n3. 代码需要可以直接运行\n4. 响应式设计，适配不同屏幕尺寸`
        }
      ],
      "max_tokens": 2048,
      "temperature": 0.7
    });

    // 解析返回的HTML代码
    if (response.choices && response.choices[0].message && response.choices[0].message.content) {
      const content = response.choices[0].message.content;
      // 提取HTML代码部分
      const htmlMatch = content.match(/```html\n([\s\S]*?)\n```/);
      if (htmlMatch) {
        generatedHtml.value = htmlMatch[1];
      } else {
        generatedHtml.value = content;
      }
      runCode();
    }
  } catch (error) {
    console.error('生成海报失败:', error);
    alert('生成海报失败，请重试');
  } finally {
    isGenerating.value = false;
  }
};

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedHtml.value);
    alert('代码已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    alert('复制失败，请手动复制');
  }
};
</script>

<style scoped>
.glass-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.sandbox-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.sandbox-container :deep(*) {
  max-width: 100%;
  height: auto;
}
</style> 