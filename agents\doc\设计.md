# 文生视频智能体系统设计文档

## 1. 系统架构
采用分层架构设计，分为以下四个层级：

1. **交互层（Presentation Layer）**
   - WebSocket实时通信
   - SSE（Server-Sent Events）状态推送
   - 基于sj.html的流式交互界面

2. **智能体层（Agent Layer）**
   ```mermaid
   graph TD
   A[用户输入] --> B(PromptEnhancer)
   B --> C{需要内容提取?}
   C -->|是| H[ContentExtractorAgent]
   C -->|否| D{是否需要多模态?}
   D -->|是| E[IllustratorAgent]
   D -->|否| F[VideoGeneratorAgent]
   E --> G[QualityCheckerAgent]
   F --> G
   H --> I{内容来源?}
   I -->|AI生成内容| J[从AutoGen等提取]
   I -->|视频内容| K[从视频内容提取]
   J --> L[结构化输出]
   K --> L
   L --> G
   G --> M[输出结果]
   ```

3. **服务层（Service Layer）**
   - ComfyUI视频生成服务
   - DeepSeek-API提示词增强
   - Redis状态管理

4. **存储层（Storage Layer）**
   - 任务状态存储（Redis）
   - 生成结果存储（MinIO）
   - 会话历史存储（MongoDB）

## 2. 核心智能体角色

### 2.1 PromptEnhancerAgent（提示词增强智能体）
- **职责**：
  - 接收原始用户输入
  - 生成多维度提示词（场景/风格/镜头语言）
  - 流式输出思考过程
  
- **交互特性**：
  ```tsx
  // 前端展示示例
  <ThinkingProcessView>
    <ReasoningStep icon="🎨" text="正在分析艺术风格..."/>
    <ReasoningStep icon="🎥" text="规划镜头运动..."/>
  </ThinkingProcessView>
  ```

- **决策能力**：
  - 提示词场景分析
  - 风格与情感抽取
  - 多维度关键词扩展
  - 镜头语言编排

- **工具调用模式**：
  - 情感分析工具：解析用户输入的情感基调
  - 风格词典工具：匹配合适的艺术风格描述符
  - 镜头运动规划工具：设计时间维度的镜头变化

### 2.2 VideoGeneratorAgent（视频生成智能体）
- **职责**：
  - 调用ComfyUI工作流API
  - 实时反馈生成进度
  - 异常处理与重试机制

- **交互协议**：
  ```python
  class VideoGenRequest(BaseModel):
      enhanced_prompt: str 
      resolution: Tuple[int, int] = (832, 480)
      num_frames: int = 81
      seed: Optional[int] = None

  class VideoGenResponse(BaseModel):
      progress: float
      preview_url: Optional[str]
      metrics: Dict[str, Any]
  ```

- **决策能力**：
  - 参数自动优化
  - 资源使用监控
  - 进度预估与报告
  - 中间结果缓存

- **工具调用模式**：
  - ComfyUI执行引擎：核心视频生成工具
  - 参数优化器：根据提示词自动调整技术参数
  - 资源监控器：实时监控GPU使用情况
  - 分块渲染引擎：大型视频分段处理

### 2.3 IllustratorAgent（概念图绘制智能体）
- **职责**：
  - 生成概念参考图
  - 关键帧草图设计
  - 风格参考板创建

- **决策能力**：
  - 视觉元素抽取与合成
  - 构图平衡与审美评估
  - 风格一致性保障
  - 多样化方案生成

- **工具调用模式**：
  - 图像生成器：快速生成参考图
  - 风格迁移工具：应用特定风格
  - 构图优化器：改进画面布局
  - 参考板生成器：创建多元素风格板

### 2.4 QualityCheckerAgent（质量检查智能体）
- **职责**：
  - 评估生成视频质量
  - 检测技术缺陷
  - 提供改进建议
  - 版本对比与选择

- **决策能力**：
  - 视频质量多维评估
  - 时间连贯性分析
  - 与原始需求匹配度评估
  - 风格一致性判断

- **工具调用模式**：
  - 视频分析器：帧间连贯性检测
  - 质量评估矩阵：多维度评分
  - 缺陷检测器：发现画面瑕疵
  - 版本对比器：并排比较不同版本

### 2.5 OrchestratorAgent（编排智能体）
- **职责**：
  - 智能体合作流程编排
  - 全局任务进度监控
  - 异常情况决策与调整
  - 资源分配与优先级管理

- **决策能力**：
  - 智能体角色动态分配
  - 任务拆解与并行处理
  - 关键节点决策
  - 全局优化决策

- **工具调用模式**：
  - 流程控制器：任务流转与阻塞管理
  - 资源调度器：计算资源分配
  - 状态监控器：全局进度与状态追踪
  - 决策树执行器：基于条件自动决策

### 2.6 UserProxyAgent（用户代理智能体）
- **职责**：
  - 用户意图理解与澄清
  - 需求转化为系统参数
  - 交互历史管理
  - 个性化偏好学习

- **决策能力**：
  - 多轮交互意图维护
  - 用户反馈解析
  - 隐式需求推断
  - 交互体验优化

- **工具调用模式**：
  - 意图解析器：提取用户真实需求
  - 偏好学习器：记忆用户历史选择
  - 反馈收集器：结构化收集评价信息
  - 对话状态管理器：维护对话上下文

### 2.7 ContentExtractorAgent（内容提取智能体）
- **职责**：
  - 分析用户输入的内容需求
  - 从AI生成内容中提取关键信息
  - 对内容进行分类和结构化整理
  - 生成符合用户期望的内容摘要
  
- **交互特性**：
  ```tsx
  // 前端展示示例
  <ContentExtractionView>
    <ExtractionStep icon="🔍" text="正在分析用户需求..."/>
    <ExtractionStep icon="📊" text="对内容进行分类..."/>
    <ExtractionStep icon="📝" text="提取关键信息..."/>
    <ExtractionStep icon="🔄" text="优化内容结构..."/>
  </ContentExtractionView>
  ```

- **决策能力**：
  - 用户意图理解与内容需求分析
  - 主题相关性判断
  - 关键信息优先级排序
  - 内容结构优化与重组
  - 多来源信息整合
  
- **工具调用模式**：
  - 意图分析工具：解析用户的具体内容需求
  - 关键信息提取器：从长文本中提取核心信息点
  - 内容分类器：按主题、领域、重要性等多维度对内容分类
  - 文本摘要工具：生成不同粒度和长度的内容摘要
  - 知识图谱构建工具：建立内容间的关联关系

## 3. 工具系统设计

### 3.1 工具分类矩阵
```mermaid
graph LR
A[工具系统] --> B(核心工具)
A --> C(扩展工具)
A --> D(辅助工具)

B --> B1[提示词解析器]
B --> B2[视频生成引擎]
B --> B3[质量评估器]
B --> B4[内容提取器]

C --> C1[风格迁移工具]
C --> C2[镜头语言生成器]
C --> C3[运动模式库]
C --> C4[知识图谱构建器]

D --> D1[种子管理器]
D --> D2[版本对比器]
D --> D3[异常恢复器]
D --> D4[内容相关性评分器]
```

### 3.2 工具能力描述规范
每个工具需声明以下元信息：
- **能力指纹**：JSON Schema描述输入/输出格式
- **适用阶段**：预处理/生成中/后处理
- **资源需求**：GPU内存/计算时长
- **版本兼容**：支持的API版本范围
- **效果指标**：历史成功率/平均提升率

### 3.3 工具注册中心
- 动态注册机制：新工具上线自动发现
- 健康检查：定期心跳检测与自动降级
- 版本沙箱：支持多版本共存与A/B测试

### 3.4 内容提取工具集

#### 3.4.1 基础提取工具
| 工具名称             | 描述             | 适用智能体       | 功能特点                               |
| -------------------- | ---------------- | ---------------- | -------------------------------------- |
| `intent-analyzer`    | 用户意图分析工具 | ContentExtractor | 解析用户的具体内容需求，识别关键查询点 |
| `key-info-extractor` | 关键信息提取工具 | ContentExtractor | 从大型文本语料中提取核心观点和要点     |
| `content-classifier` | 内容分类工具     | ContentExtractor | 按主题、领域、重要性等多维度对内容分类 |
| `summary-generator`  | 摘要生成工具     | ContentExtractor | 生成不同粒度和长度的内容摘要           |
| `knowledge-mapper`   | 知识图谱工具     | ContentExtractor | 构建内容之间的关联关系网络             |

#### 3.4.2 专业内容处理工具
| 工具名称                  | 描述              | 适用场景         | 功能特点                         |
| ------------------------- | ----------------- | ---------------- | -------------------------------- |
| `agent-content-extractor` | AutoGen内容提取器 | 多智能体输出处理 | 从多智能体对话中提取有价值信息   |
| `video-content-analyzer`  | 视频内容分析器    | 视频脚本提取     | 从视频内容提取关键场景和文本描述 |
| `cross-modal-linker`      | 跨模态内容关联器  | 多模态内容整合   | 链接不同模态内容之间的语义关系   |
| `structured-formatter`    | 结构化输出格式器  | 内容重组与展示   | 将提取的内容重新组织为结构化格式 |

## 4. 智能体调用机制

### 4.1 决策工作流
```mermaid
graph TD
A[任务解析] --> B{是否需要工具协作?}
B -->|是| C[工具发现]
C --> D[能力匹配]
D --> E[成本评估]
E --> F[执行编排]
F --> G[并行执行]
G --> H[结果聚合]
B -->|否| I[直接处理]
```

### 4.2 动态绑定策略
- **上下文感知绑定**：根据任务类型自动关联工具集
- **渐进式加载**：按需加载工具依赖项
- **故障转移**：主工具失效时自动切换备用方案

### 4.3 执行沙箱环境
- 资源隔离：独立内存空间与计算资源分配
- 输入消毒：防止恶意参数注入
- 超时熔断：设置最大执行时长阈值

### 4.4 自主决策模型
#### 4.4.1 决策因子权重
| 因子类别   | 权重 | 说明                 |
| ---------- | ---- | -------------------- |
| 任务匹配度 | 30%  | 工具与当前任务相关性 |
| 历史成功率 | 25%  | 过往执行成功记录     |
| 资源消耗   | 20%  | CPU/内存/显存占用    |
| 时效性     | 15%  | 预估执行时间         |
| 成本       | 10%  | API调用费用等        |

#### 4.4.2 决策过程优化
- **多臂老虎机模型**：平衡探索与利用
- **联邦学习**：跨智能体共享决策经验
- **实时反馈调整**：根据执行结果动态更新权重

#### 4.4.3 异常处理策略
- 阶梯式重试：3次渐进间隔重试
- 影响面分析：评估失败对整体流程的影响
- 自动修复建议：生成备选方案组合

## 5. LLM流式输出交互机制

### 5.1 流式输出架构
```mermaid
graph TD
    A[LLM思考过程] --> B[流式输出处理器]
    B --> C[Redis Pub/Sub]
    C --> D[WebSocket服务]
    D --> E[WebSocket客户端]
    E --> F[前端渲染组件]
    F --> G1[思考步骤可视化]
    F --> G2[代码块高亮]
    F --> G3[进度指示器]
    H[MongoDB] --- I[会话上下文存储]
    I --> B
    I --> D
```

### 5.2 WebSocket+Redis架构模式

#### 5.2.1 核心组件职责
- **LLM服务**：生成思考过程和结果的大语言模型
- **流式处理器**：解析LLM输出并格式化为结构化消息
- **Redis Pub/Sub**：消息中间件，负责分发和广播消息
- **WebSocket服务**：维护客户端连接和消息推送
- **MongoDB**：存储会话上下文和历史记录

#### 5.2.2 数据流向
1. **生成阶段**：LLM生成思考过程流式输出到处理器
2. **处理阶段**：处理器解析结构化消息并发布到Redis频道
3. **分发阶段**：Redis将消息广播给所有订阅的WebSocket服务
4. **推送阶段**：WebSocket服务推送消息到客户端
5. **渲染阶段**：前端组件接收并渲染消息

#### 5.2.3 上下文管理
- **短期会话状态**：Redis Hash结构存储（TTL设置）
- **会话历史记录**：MongoDB持久化存储
- **客户端状态同步**：WebSocket双向通信

### 5.3 流式数据协议

```typescript
interface TokenStreamEvent {
  type: 'token' | 'thinking_step' | 'code_block' | 'tool_call' | 'completion';
  agentId: string;
  sessionId: string;
  timestamp: number;
  payload: {
    text?: string;
    toolName?: string;
    reasoningStep?: {
      icon: string;
      text: string;
      progress?: number;
    };
    codeBlock?: {
      language: string;
      code: string;
    };
  };
}
```

### 5.4 Redis消息通道设计

#### 5.4.1 通道结构
```
// 智能体思考过程通道
agent:thinking:{agent_id}:{session_id}

// 工具调用通道
agent:tool_call:{agent_id}:{session_id}

// 系统状态通道
system:status:{session_id}

// 错误通道
system:error:{session_id}
```

#### 5.4.2 消息序列化
- 使用JSON序列化消息结构
- 大型内容（如生成的图像）采用引用方式，避免Redis性能问题
- 支持压缩算法减少带宽占用

#### 5.4.3 消息保留策略
- 临时消息：TTL设为5分钟
- 重要状态消息：TTL设为30分钟
- 完整会话持久化到MongoDB

### 5.5 MongoDB上下文存储

#### 5.5.1 数据集合设计
```javascript
// 会话集合
sessions {
  _id: ObjectId,
  sessionId: String,
  userId: String,
  startTime: ISODate,
  lastActiveTime: ISODate,
  agents: [String],
  status: String,
  metadata: Object
}

// 消息集合
messages {
  _id: ObjectId,
  sessionId: String,
  agentId: String, 
  type: String,
  content: Object,
  timestamp: ISODate,
  parentMessageId: String,
  metadata: Object
}

// 上下文集合
contexts {
  _id: ObjectId,
  sessionId: String,
  agentId: String,
  name: String,
  value: Object,
  lastUpdated: ISODate,
  ttl: Number
}
```

#### 5.5.2 索引设计
- 会话ID和时间戳的复合索引
- 智能体ID和消息类型的索引
- TTL索引用于上下文自动过期

#### 5.5.3 查询模式
- 实时查询：最近消息获取
- 聚合查询：会话统计和分析
- 全文检索：上下文内容搜索

### 5.6 WebSocket服务实现

#### 5.6.1 连接管理
- 基于会话ID的连接池管理
- 心跳机制保持连接活跃
- 自动重连策略与状态恢复
- 多设备同步支持

#### 5.6.2 消息处理
- 消息分类与优先级排序
- 批量发送与流量控制
- 消息确认与重传机制
- 异步消息队列处理

#### 5.6.3 安全措施
- 基于JWT的连接认证
- 消息签名验证
- 速率限制防止滥用
- 连接超时自动关闭

### 5.7 前端流式渲染组件

#### 5.7.1 核心渲染组件
- **ThinkingStreamRenderer**：显示LLM思考步骤
- **CodeBlockStream**：实时代码块构建与高亮
- **ToolCallVisualizer**：工具调用过程可视化
- **ProgressiveExplanation**：渐进式解释渲染

#### 5.7.2 WebSocket客户端实现
```typescript
class AgentStreamClient {
  private socket: WebSocket;
  private reconnectAttempts: number = 0;
  private eventListeners: Map<string, Function[]> = new Map();
  
  constructor(sessionId: string, userId: string) {
    this.connect(sessionId, userId);
  }
  
  private connect(sessionId: string, userId: string) {
    const wsUrl = `${WS_BASE_URL}/stream?sessionId=${sessionId}&userId=${userId}`;
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onopen = () => this.handleOpen();
    this.socket.onmessage = (event) => this.handleMessage(event);
    this.socket.onerror = (error) => this.handleError(error);
    this.socket.onclose = () => this.handleClose();
  }
  
  public on(eventType: string, callback: Function) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(callback);
  }
  
  private handleMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      const listeners = this.eventListeners.get(data.type) || [];
      listeners.forEach(callback => callback(data));
      
      // 全局事件监听器
      const allListeners = this.eventListeners.get('*') || [];
      allListeners.forEach(callback => callback(data));
    } catch (e) {
      console.error('Failed to parse message:', e);
    }
  }
  
  // 其他方法: handleOpen, handleError, handleClose, reconnect等
}
```

#### 5.7.3 交互特性
- 思考步骤可展开/折叠
- 代码块实时高亮与复制
- 工具调用过程可视化动画
- 进度百分比实时更新
- 网络状态指示与自动重连

### 5.8 多设备同步机制

#### 5.8.1 同步策略
- WebSocket消息广播到同一用户的所有设备
- MongoDB存储的会话上下文作为真实数据源
- 连接恢复时基于时间戳的增量同步

#### 5.8.2 冲突处理
- 基于时间戳的冲突检测
- 后发优先的冲突解决策略
- 用户手动合并选项

### 5.9 扩展性设计

#### 5.9.1 水平扩展
- WebSocket服务多实例部署
- Redis集群支持高可用
- MongoDB分片集群存储

#### 5.9.2 性能优化
- 消息批处理减少网络开销
- 选择性订阅减少不必要的消息处理
- 增量更新减少数据传输量

#### 5.9.3 灾备能力
- Redis持久化确保消息不丢失
- WebSocket断线重连自动恢复
- MongoDB副本集保障数据安全

该设计可实现：
1. 高性能的流式输出传输
2. 可靠的会话上下文管理
3. 多设备同步与状态一致性
4. 水平扩展支持大规模并发

## 6. 基于MCP的能力组件设计

### 6.1 MCP服务架构
```mermaid
graph TD
    A[Agent System] ---> B[MCP服务器集群]
    B --- C[提示词增强MCP]
    B --- D[视频生成MCP]
    B --- E[质量评估MCP]
    B --- F[资源访问MCP]
    G[客户端] ---> B
```

### 6.2 核心MCP组件

#### 6.2.1 MCP服务器组件
- **VideoCraftServer**: 视频创作核心服务器
  - 暴露视频生成工具集
  - 提供视频分析资源
  - 定义常用视频创作提示模板
  
- **PromptEnhancementServer**: 提示词增强服务器
  - 提供语义分析工具
  - 暴露风格库资源
  - 定义提示词优化模板

- **QualityAssessmentServer**: 质量评估服务器
  - 提供视频质量分析工具
  - 暴露评估标准资源
  - 定义质量改进提示模板

#### 6.2.2 MCP工具组件
| 工具名称              | 描述                 | 输入模式 | 服务器                  |
| --------------------- | -------------------- | -------- | ----------------------- |
| `enhance-prompt`      | 提示词多维度增强工具 | JSON参数 | PromptEnhancementServer |
| `generate-video`      | 视频生成执行工具     | JSON参数 | VideoCraftServer        |
| `analyze-quality`     | 视频质量评估工具     | 文件引用 | QualityAssessmentServer |
| `refine-style`        | 视频风格优化工具     | JSON参数 | VideoCraftServer        |
| `generate-storyboard` | 分镜头脚本生成工具   | 文本参数 | PromptEnhancementServer |

#### 6.2.3 MCP资源组件
| 资源类型               | 描述             | 访问模式  | 服务器                  |
| ---------------------- | ---------------- | --------- | ----------------------- |
| `style-library`        | 视频艺术风格库   | 只读/搜索 | PromptEnhancementServer |
| `camera-movement`      | 镜头语言库       | 只读/搜索 | PromptEnhancementServer |
| `quality-standards`    | 视频质量评价标准 | 只读      | QualityAssessmentServer |
| `example-videos`       | 示例视频集合     | 只读/流式 | VideoCraftServer        |
| `generation-templates` | 生成参数模板     | 只读/写入 | VideoCraftServer        |

#### 6.2.4 MCP提示模板
| 提示名称                   | 描述             | 参数               | 服务器                  |
| -------------------------- | ---------------- | ------------------ | ----------------------- |
| `create-cinematic-video`   | 电影风格视频创作 | 场景、风格、时长   | VideoCraftServer        |
| `optimize-for-quality`     | 视频质量优化     | 视频引用、目标品质 | QualityAssessmentServer |
| `transform-text-to-visual` | 文本到视觉转换   | 文本描述、风格偏好 | PromptEnhancementServer |
| `analyze-visual-coherence` | 视觉连贯性分析   | 视频引用           | QualityAssessmentServer |

#### 6.2.5 新增MCP服务器组件
- **ContentExtractionServer**: 内容提取服务器
  - 提供内容分析工具集
  - 暴露知识图谱资源
  - 定义内容结构化模板

#### 6.2.6 新增MCP工具组件
| 工具名称                | 描述             | 输入模式      | 服务器                  |
| ----------------------- | ---------------- | ------------- | ----------------------- |
| `extract-key-info`      | 关键信息提取工具 | 文本/JSON参数 | ContentExtractionServer |
| `classify-content`      | 内容分类工具     | 文本/JSON参数 | ContentExtractionServer |
| `generate-summary`      | 摘要生成工具     | 文本/参数     | ContentExtractionServer |
| `build-knowledge-graph` | 知识图谱构建工具 | JSON参数      | ContentExtractionServer |

### 6.3 MCP集成架构

#### 6.3.1 MCP客户端集成
```typescript
// 客户端MCP集成示例
class MCPClientManager {
  // MCP服务器连接池
  private servers: Map<string, MCPServer>;
  
  // 工具调用代理
  async callTool(serverName: string, toolName: string, params: any): Promise<any> {
    const server = this.servers.get(serverName);
    return await server.tools.call(toolName, params);
  }
  
  // 资源获取代理
  async getResource(serverName: string, resourceName: string): Promise<any> {
    const server = this.servers.get(serverName);
    return await server.resources.get(resourceName);
  }
  
  // 提示模板执行
  async executePrompt(serverName: string, promptName: string, args: any): Promise<any> {
    const server = this.servers.get(serverName);
    return await server.prompts.execute(promptName, args);
  }
}
```

#### 6.3.2 流式输出支持
MCP流式输出特性通过如下机制实现：
- 长时间运行操作的实时进度更新
- 基于WebSocket的双向通信通道
- 支持代理层次结构的状态传播

#### 6.3.3 MCP工具服务器实现
```python
# 示例MCP服务器定义
from mcp_server import Server, Capability

server = Server(
    name="video-craft-server",
    version="1.0.0",
    capabilities={
        Capability.TOOLS: {},
        Capability.RESOURCES: {},
        Capability.PROMPTS: {}
    }
)

# 注册工具
@server.tool(
    name="generate-video",
    description="根据提示词生成视频",
    input_schema={
        "type": "object",
        "properties": {
            "prompt": {"type": "string"},
            "width": {"type": "integer"},
            "height": {"type": "integer"},
            "num_frames": {"type": "integer"},
            "seed": {"type": "integer"}
        },
        "required": ["prompt"]
    }
)
async def generate_video(params):
    # 实现视频生成逻辑
    # 支持流式进度更新
    return {"video_url": "...", "seed": 12345}
```

### 6.4 MCP与智能体交互模式

#### 6.4.1 自动化工具发现
智能体可通过MCP的工具发现机制自动获取可用工具列表，基于任务需求动态选择合适工具。

#### 6.4.2 层次化决策系统
```mermaid
graph TD
    A[编排智能体] --> B{决策分发}
    B --> C[提示词增强]
    B --> D[视频生成]
    B --> E[质量评估]
    C --> F{工具选择}
    F -->|MCP工具| G[enhance-prompt]
    F -->|MCP工具| H[generate-storyboard]
    D --> I{工具选择}
    I -->|MCP工具| J[generate-video]
    I -->|MCP工具| K[refine-style]
```

#### 6.4.3 错误恢复机制
- MCP工具调用失败自动重试
- 服务降级策略实现
- 备用工具自动切换

### 6.5 MCP部署策略

#### 6.5.1 服务器部署模式
- 独立容器化MCP服务器
- Kubernetes编排管理
- 自动扩缩容支持

#### 6.5.2 安全考量
- 工具调用权限控制
- 资源访问限制策略
- 用户意图确认机制

#### 6.5.3 监控与可观测性
- MCP工具调用统计
- 资源使用监控
- 性能瓶颈识别

通过MCP组件构建，系统能够实现：
1. 标准化工具接口，降低集成复杂度
2. 灵活扩展新能力，无需修改核心架构
3. 多模态交互支持，适应未来AI发展
4. 层次化代理系统，实现复杂工作流自动化

## 7. 创意内容智能体扩展

随着系统不断演进，我们可以扩展更多创意内容生成能力，如古诗生成、八字运势分析以及更多文生图功能。以下是对应的智能体角色与工具设计：

### 7.1 扩展智能体角色

#### 7.1.1 PoetryGeneratorAgent（诗歌生成智能体）
- **职责**：
  - 基于用户输入生成古诗、词、赋等
  - 提供多种风格和体裁选择
  - 解析诗歌内涵和意境

- **决策能力**：
  - 韵律与格律分析
  - 文化背景与典故融入
  - 意境与情感表达
  - 多种诗歌体裁生成

- **工具调用模式**：
  - 古诗词数据库：检索相关诗句与典故
  - 韵律检查器：确保诗词符合格律要求
  - 风格模拟器：模拟特定诗人风格
  - 意象库：提供传统诗歌常用意象

#### 7.1.2 FortuneAnalysisAgent（运势分析智能体）
- **职责**：
  - 根据用户输入的八字信息分析运势
  - 提供多维度的运势解读
  - 生成图文并茂的运势报告

- **决策能力**：
  - 五行属性分析
  - 八字组合解读
  - 运势周期判断
  - 吉凶方位推算

- **工具调用模式**：
  - 八字计算引擎：计算五行属性
  - 运势模型：预测不同维度运势
  - 命盘生成器：生成可视化八字命盘
  - 解读模板库：提供专业解读文案

#### 7.1.3 CreativeIllustrationAgent（创意插画智能体）
- **职责**：
  - 根据文本描述生成风格化插画
  - 结合诗歌/运势内容创建配图
  - 提供多种艺术风格选项

- **决策能力**：
  - 文本主题提取
  - 视觉元素编排
  - 艺术风格匹配
  - 文化元素整合

- **工具调用模式**：
  - 风格化图像生成器：创建特定风格插画
  - 构图优化器：改善画面布局与焦点
  - 文化元素库：融入传统文化符号
  - 色彩协调器：保证色彩方案协调

### 7.2 扩展工具集

#### 7.2.1 文创内容工具
| 工具名称             | 描述           | 适用智能体           | 功能特点                                       |
| -------------------- | -------------- | -------------------- | ---------------------------------------------- |
| `poetry-dataset`     | 古典诗词数据库 | PoetryGenerator      | 包含数万首古典诗词，支持按主题、意象、作者检索 |
| `rhythm-analyzer`    | 韵律分析工具   | PoetryGenerator      | 检查诗词格律，提供平仄建议，确保符合传统格式   |
| `cultural-reference` | 文化典故库     | PoetryGenerator      | 提供中国传统文化典故，支持典故融入创作         |
| `bazi-calculator`    | 八字计算工具   | FortuneAnalysis      | 根据生辰八字计算五行属性，分析命盘结构         |
| `fate-chart`         | 命盘可视化工具 | FortuneAnalysis      | 生成专业八字命盘图，展示五行关系               |
| `fortune-forecast`   | 运势预测模型   | FortuneAnalysis      | 基于传统命理预测事业、健康、感情等运势         |
| `style-transfer`     | 风格迁移工具   | CreativeIllustration | 将特定艺术风格应用到生成图像上                 |
| `cultural-element`   | 文化元素库     | CreativeIllustration | 包含传统文化视觉元素，如花鸟、山水等           |

#### 7.2.2 多模态融合工具
| 工具名称             | 描述         | 适用场景   | 功能特点                         |
| -------------------- | ------------ | ---------- | -------------------------------- |
| `poetry-to-image`    | 诗歌配图生成 | 诗歌创作   | 分析诗歌意境，生成匹配的插画     |
| `fortune-visualizer` | 运势可视化   | 运势分析   | 将八字运势以图形方式直观呈现     |
| `poetry-calligraphy` | 诗词书法生成 | 诗歌创作   | 将生成的诗词转为书法作品         |
| `content-layout`     | 内容排版工具 | 多模态输出 | 智能排版文字与图像，生成美观报告 |

### 7.3 扩展交互流程

```mermaid
graph TD
    A[用户输入] --> B{内容类型判断}
    B -->|诗歌需求| C[PoetryGeneratorAgent]
    B -->|运势需求| D[FortuneAnalysisAgent]
    B -->|插画需求| E[CreativeIllustrationAgent]
    B -->|视频需求| F[原有视频生成流程]
    
    C --> G[生成诗歌内容]
    G --> H{需要配图?}
    H -->|是| I[CreativeIllustrationAgent]
    H -->|否| J[输出纯文本]
    
    D --> K[分析八字运势]
    K --> L[生成运势报告]
    L --> M{需要可视化?}
    M -->|是| N[运势命盘生成]
    N --> O[CreativeIllustrationAgent]
    M -->|否| P[输出文本报告]
    
    I --> Q[多模态结果整合]
    O --> Q
    J --> R[最终输出]
    P --> R
    Q --> R
```

### 7.4 MCP扩展组件

#### 7.4.1 新增MCP服务器组件
- **CreativeContentServer**: 创意内容服务器
  - 提供诗歌生成工具集
  - 提供运势分析工具集
  - 暴露文化资源库

- **VisualArtServer**: 视觉艺术服务器
  - 提供风格化图像生成
  - 暴露书法与国画资源
  - 提供排版与布局工具

#### 7.4.2 新增MCP工具组件
| 工具名称                | 描述             | 输入模式 | 服务器                |
| ----------------------- | ---------------- | -------- | --------------------- |
| `generate-poetry`       | 古诗词生成工具   | 文本参数 | CreativeContentServer |
| `analyze-fortune`       | 八字运势分析工具 | JSON参数 | CreativeContentServer |
| `generate-illustration` | 风格化插画生成   | JSON参数 | VisualArtServer       |
| `create-calligraphy`    | 书法作品生成     | 文本参数 | VisualArtServer       |

#### 7.4.3 新增MCP资源组件
| 资源类型              | 描述               | 访问模式  | 服务器                |
| --------------------- | ------------------ | --------- | --------------------- |
| `poetry-database`     | 古典诗词数据库     | 只读/搜索 | CreativeContentServer |
| `cultural-symbols`    | 中国传统文化符号库 | 只读/搜索 | CreativeContentServer |
| `calligraphy-styles`  | 书法风格库         | 只读      | VisualArtServer       |
| `painting-techniques` | 国画技法资源       | 只读/流式 | VisualArtServer       |

### 7.5 示例用户场景

#### 7.5.1 古诗创作场景
用户输入: "帮我写一首描写西湖春景的七言绝句，并配上水墨画风格的插图"

处理流程:
1. **内容类型判断**: 识别为诗歌创作请求
2. **PoetryGeneratorAgent激活**: 
   - 分析主题(西湖)和情感(春景)
   - 检索相关典故和意象(断桥、柳树、桃花等)
   - 生成符合七言绝句格律的诗歌
3. **CreativeIllustrationAgent激活**:
   - 分析诗歌内容提取关键意象
   - 应用水墨画风格生成西湖春景图
4. **内容整合**:
   - 排版诗歌与插图
   - 可选择添加书法风格的标题

输出:
- 格律工整的七言绝句
- 与诗歌内容匹配的水墨风格西湖春景图

#### 7.5.2 八字运势分析场景
用户输入: "我是1985年农历三月初五巳时出生的，帮我分析一下今年的事业运势"

处理流程:
1. **内容类型判断**: 识别为八字运势分析请求
2. **FortuneAnalysisAgent激活**:
   - 将农历日期转换为天干地支
   - 计算八字五行属性
   - 分析大运流年与本命八字关系
   - 生成专业的事业运势分析
3. **CreativeIllustrationAgent激活**:
   - 生成八字命盘可视化图
   - 创建五行关系图表
4. **内容整合**:
   - 将文字分析与图表整合
   - 排版为专业运势报告

输出:
- 详细的事业运势分析文本
- 八字命盘可视化图
- 五行关系与流年吉凶指数图表

通过这些扩展，系统能够提供更丰富的创意内容生成服务，满足用户在文学创作、文化体验和个性化内容方面的需求，同时保持与现有视频生成功能的无缝集成。

## 8. ComfyUI工具封装设计

为了更好地利用ComfyUI强大的视频生成能力，我们需要将其功能封装为标准化的工具组件，供智能体系统调用。以下是ComfyUI工具封装的设计方案：

### 8.1 ComfyUI交互架构

```mermaid
graph TD
    A[智能体系统] -->|工具调用| B[ComfyUI适配器]
    B -->|工作流管理| C[工作流库]
    B -->|API请求| D[ComfyUI服务器]
    D -->|状态更新| B
    B -->|结果回传| A
    E[工作流编辑器] -->|创建/修改| C
    F[参数优化器] -->|动态调整| B
```

### 8.2 核心组件设计

#### 8.2.1 ComfyUIAdapter（ComfyUI适配器）
- **职责**：
  - 标准化工具接口
  - 工作流参数注入
  - 任务状态跟踪
  - 结果处理与转换

- **接口设计**：
```python
class ComfyUIAdapter:
    async def execute_workflow(
        self, 
        workflow_name: str, 
        params: Dict[str, Any],
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """执行指定的工作流"""
        
    async def get_workflow_status(
        self, 
        prompt_id: str
    ) -> Dict[str, Any]:
        """获取工作流执行状态"""
        
    async def list_available_workflows(self) -> List[Dict[str, Any]]:
        """列出可用的工作流"""
        
    async def get_workflow_schema(
        self, 
        workflow_name: str
    ) -> Dict[str, Any]:
        """获取工作流参数模式"""
```

#### 8.2.2 WorkflowManager（工作流管理器）
- **职责**：
  - 工作流存储与检索
  - 工作流版本管理
  - 参数验证与默认值
  - 节点映射与依赖分析

- **实现策略**：
```python
class WorkflowManager:
    def __init__(self, workflow_dir: str):
        self.workflow_dir = workflow_dir
        self.workflow_cache = {}
        self.schema_cache = {}
        
    def load_workflow(self, name: str) -> Dict[str, Any]:
        """加载并解析工作流"""
        
    def extract_parameters(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """提取工作流中的可配置参数"""
        
    def inject_parameters(
        self, 
        workflow: Dict[str, Any], 
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """将参数注入工作流"""
        
    def analyze_dependencies(self, workflow: Dict[str, Any]) -> Dict[str, List[str]]:
        """分析节点依赖关系"""
```

#### 8.2.3 ResultProcessor（结果处理器）
- **职责**：
  - 结果数据提取
  - 格式转换
  - 元数据解析
  - 错误处理

- **处理策略**：
```python
class ResultProcessor:
    async def extract_images(
        self, 
        prompt_id: str, 
        node_id: str, 
        api_url: str
    ) -> List[str]:
        """提取生成的图像"""
        
    async def extract_video(
        self, 
        prompt_id: str, 
        node_id: str, 
        api_url: str
    ) -> str:
        """提取生成的视频"""
        
    async def process_error(
        self, 
        prompt_id: str, 
        api_url: str
    ) -> Dict[str, Any]:
        """处理执行错误"""
        
    async def extract_metadata(
        self, 
        prompt_id: str, 
        node_id: str, 
        api_url: str
    ) -> Dict[str, Any]:
        """提取结果元数据"""
```

### 8.3 工具接口设计

#### 8.3.1 基本工具类型
| 工具类型            | 描述           | 输入模式      | 输出模式       |
| ------------------- | -------------- | ------------- | -------------- |
| `T2V-Standard`      | 标准文生视频   | 文本+参数     | 视频URL+元数据 |
| `T2V-HighQuality`   | 高质量文生视频 | 文本+高级参数 | 视频URL+元数据 |
| `T2I-Realistic`     | 写实风格文生图 | 文本+风格参数 | 图像URL+元数据 |
| `T2I-Artistic`      | 艺术风格文生图 | 文本+风格参数 | 图像URL+元数据 |
| `I2I-StyleTransfer` | 图像风格迁移   | 图像+风格参数 | 图像URL+元数据 |
| `I2V-Animation`     | 图像转视频动画 | 图像+动画参数 | 视频URL+元数据 |

#### 8.3.2 参数模板设计
```json
{
  "T2V-Standard": {
    "prompt": {
      "type": "string",
      "description": "详细的场景与动作描述",
      "required": true
    },
    "negative_prompt": {
      "type": "string",
      "description": "不希望出现的内容",
      "default": "模糊, 低质量, 扭曲"
    },
    "width": {
      "type": "integer",
      "description": "视频宽度",
      "default": 832,
      "enum": [512, 768, 832, 1024]
    },
    "height": {
      "type": "integer",
      "description": "视频高度",
      "default": 480,
      "enum": [480, 576, 768]
    },
    "num_frames": {
      "type": "integer",
      "description": "视频帧数",
      "default": 81,
      "minimum": 16,
      "maximum": 200
    },
    "fps": {
      "type": "integer",
      "description": "每秒帧数",
      "default": 8,
      "enum": [8, 12, 16, 24, 30]
    },
    "seed": {
      "type": "integer",
      "description": "随机种子",
      "default": -1
    }
  }
}
```

### 8.4 交互流程设计

#### 8.4.1 异步执行流程
```mermaid
sequenceDiagram
    参与者 Agent as 智能体
    参与者 Adapter as ComfyUI适配器
    参与者 ComfyUI as ComfyUI服务器
    参与者 Redis as Redis状态存储
    参与者 WSServer as WebSocket服务器
    
    Agent->>Adapter: 请求执行工作流
    Adapter->>Adapter: 加载并注入参数
    Adapter->>ComfyUI: 发送工作流请求
    ComfyUI-->>Adapter: 返回prompt_id
    Adapter->>Redis: 记录任务状态
    Adapter-->>Agent: 返回任务ID
    
    loop 状态轮询
        ComfyUI->>Redis: 更新进度
        Redis->>WSServer: 发布状态更新
        WSServer->>Agent: 推送进度消息
    end
    
    ComfyUI->>Redis: 标记任务完成
    Redis->>WSServer: 发布完成消息
    WSServer->>Agent: 推送完成消息
    Agent->>Adapter: 获取结果
    Adapter->>ComfyUI: 请求结果数据
    ComfyUI-->>Adapter: 返回结果数据
    Adapter-->>Agent: 处理并返回标准化结果
```

#### 8.4.2 错误处理策略
- **网络错误**：自动重试3次，指数退避
- **参数错误**：提供详细的验证信息
- **服务器错误**：降级处理，使用备用方案
- **超时处理**：可配置的超时时间与部分结果返回

### 8.5 开发者工具与体验

#### 8.5.1 工作流编辑器
- 提供可视化界面编辑工作流
- 支持参数配置与测试
- 预设模板库与分享功能
- 版本控制与比较

#### 8.5.2 调试控制台
- 实时监控执行状态
- 中间结果可视化
- 性能分析与瓶颈检测
- 日志聚合与搜索

#### 8.5.3 示例代码
```python
# 智能体使用ComfyUI示例
async def generate_video(prompt: str) -> Dict[str, Any]:
    adapter = ComfyUIAdapter()
    
    # 执行工作流
    task = await adapter.execute_workflow(
        workflow_name="wan_t2v", 
        params={
            "prompt": prompt,
            "width": 832,
            "height": 480,
            "num_frames": 81
        },
        callback=lambda status: print(f"进度: {status['progress'] * 100:.2f}%")
    )
    
    # 等待执行完成
    result = await task.wait()
    
    # 返回结果
    return {
        "video_url": result["video_url"],
        "seed": result["seed"],
        "parameters": result["parameters"]
    }
```

### 8.6 部署与扩展策略

#### 8.6.1 多实例集群
- ComfyUI服务集群自动负载均衡
- 任务分发基于GPU可用性
- 水平扩展支持

#### 8.6.2 缓存层设计
- 重复请求结果缓存
- 参数相似度检测
- 局部修改增量更新

#### 8.6.3 监控与告警
- 资源利用率监控
- 异常任务检测
- 服务健康检查
- 性能指标仪表盘

通过ComfyUI工具封装，智能体系统能够：
1. 灵活调用各种视频生成工作流
2. 实时获取生成进度
3. 动态优化生成参数
4. 获取标准化的结果格式

## 9. 内容提取场景示例

用户输入: "从AutoGen的文档中提取有关多智能体协作模式的关键信息，重点关注设计模式和实现方法"

处理流程:
1. **内容类型判断**: 识别为内容提取请求，涉及AutoGen和多智能体系统
2. **ContentExtractorAgent激活**:
   - 分析用户意图：提取关于"多智能体协作模式"的信息
   - 关注重点：设计模式和实现方法
   - 内容来源：AutoGen文档
3. **信息提取过程**:
   - 使用`agent-content-extractor`工具分析AutoGen文档
   - 应用`key-info-extractor`提取关键信息点
   - 使用`content-classifier`对提取内容按"设计模式"和"实现方法"分类
   - 应用`knowledge-mapper`构建概念间关系
4. **内容整合**:
   - 使用`structured-formatter`将内容重组为结构化格式
   - 生成多层次内容摘要
   - 添加概念关系图

输出:
- 结构化的AutoGen多智能体协作模式信息
- 设计模式与实现方法的归类整理
- 核心概念间的关系图谱
- 不同深度的内容摘要选项

通过添加ContentExtractorAgent，系统能够智能从各种AI生成内容中提取关键信息，满足用户对特定内容的提取和分类需求，大大提高内容利用效率。
