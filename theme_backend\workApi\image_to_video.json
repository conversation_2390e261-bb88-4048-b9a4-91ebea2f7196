{"80": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "teacache", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["204", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "198": {"inputs": {"model": "Wan2_1-I2V-14B-480P_fp8_e4m3fn.safetensors", "base_precision": "bf16", "quantization": "fp8_e4m3fn", "load_device": "offload_device", "attention_mode": "sageattn", "lora": ["228", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "199": {"inputs": {"model_name": "umt5-xxl-enc-bf16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "disabled"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "Load WanVideo T5 TextEncoder"}}, "202": {"inputs": {"model_name": "Wan2_1_VAE_bf16.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "204": {"inputs": {"enable_vae_tiling": true, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "vae": ["202", 0], "samples": ["205", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "205": {"inputs": {"steps": 25, "cfg": 6, "shift": 5, "seed": 6433141672591, "force_offload": true, "scheduler": "dpm++", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": false, "rope_function": "default", "model": ["198", 0], "text_embeds": ["213", 0], "image_embeds": ["216", 0], "feta_args": ["223", 0], "teacache_args": ["222", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "213": {"inputs": {"positive_prompt": "dabaichui，someone holds someone's head with both hands and twists someone's waist and hips left and right ，making dabaichui motion", "negative_prompt": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", "force_offload": true, "t5": ["199", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "提示词"}}, "215": {"inputs": {"model_name": "open-clip-xlm-roberta-large-vit-huge-14_fp16.safetensors", "precision": "fp16", "load_device": "offload_device"}, "class_type": "LoadWanVideoClipTextEncoder", "_meta": {"title": "Load WanVideo Clip Encoder"}}, "216": {"inputs": {"generation_width": 512, "generation_height": 512, "num_frames": 81, "force_offload": true, "noise_aug_strength": 0, "latent_strength": 1, "clip_embed_strength": 1, "adjust_resolution": true, "clip_vision": ["215", 0], "image": ["220", 0], "vae": ["202", 0]}, "class_type": "WanVideoImageClipEncode", "_meta": {"title": "WanVideo ImageClip Encode (Deprecated)"}}, "220": {"inputs": {"image": "4ace058dfd666c3cc4ed9a41f353116.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "222": {"inputs": {"rel_l1_thresh": 0.04, "start_step": 6, "end_step": 25, "cache_device": "offload_device", "use_coefficients": true}, "class_type": "WanVideoTeaCache", "_meta": {"title": "WanVideo TeaCache"}}, "223": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}, "228": {"inputs": {"lora": "wan/dbc.safetensors", "strength": 1, "low_mem_load": false}, "class_type": "WanVideoLoraSelect", "_meta": {"title": "WanVideo Lora Select"}}}