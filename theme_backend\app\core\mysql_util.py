import mysql.connector
from mysql.connector import Error
import json

properties = {
    "mysql_conf":{
        "host": "***********",
        "user": "root",
        "password": "Fsit#2024",
        "database": "changan",
        "port": 3306
    }
}

mysqlConf = properties['mysql_conf']
isPrimary = {
    'data': True
}    #默认优先主
if 'mysql_conf_1' in properties and properties['mysql_conf_1']:
    mysqlConf1 = properties['mysql_conf_1']
else: 
    mysqlConf1 = None

#连接默认主数据库
def connect_defaultDB():
    return mysql.connector.connect(**mysqlConf)

# 连接主备默认数据库
def connect_PBDB():
  if not mysqlConf1:
      return connect_defaultDB()
  connection = None
  try:
    connection =  connect_current_pbdb()
    if connection.is_connected():
        return connection
  except Error as e:
    print(e)
  isPrimary['data'] = not isPrimary['data']
  return connect_current_pbdb()


def connect_current_pbdb():
  if isPrimary['data']:
      return mysql.connector.connect(**mysqlConf)
  else:
      return mysql.connector.connect(**mysqlConf1)





# 连接到MySQL数据库
def connect_to_database(host, user, password, database):
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )
    return connection

# 增加数据
def insert_data(connection, table, data):
    cursor = connection.cursor()
    columns = ', '.join(data.keys())
    values = ', '.join(['%s'] * len(data))
    sql = f"INSERT INTO {table} ({columns}) VALUES ({values})"
    cursor.execute(sql, list(data.values()))
    connection.commit()
    cursor.close()

def insert_data_id_not_exists(connection, table, data, id,autocommit=True):
    cursor = connection.cursor()
    columns = ', '.join(data.keys())
    values = ', '.join(['%s'] * len(data))
    sql = f"INSERT INTO {table} ({columns})  (select {values} from dual where not EXISTS (select 1 from {table} where {id} = %s) ) "
    # print(sql)
    queryList = list(data.values())
    queryList.append(data[id])
    cursor.execute(sql,queryList)
    if autocommit:
        connection.commit()
    last_inserted_id = cursor.lastrowid
    cursor.close()
    return last_inserted_id

def insert_data_and_get_id(connection, table, data):
    cursor = connection.cursor()
    columns = ', '.join(data.keys())
    values = ', '.join(['%s'] * len(data))
    sql = f"INSERT INTO {table} ({columns}) VALUES ({values})"
    cursor.execute(sql, tuple(data.values()))
    connection.commit()
    last_inserted_id = cursor.lastrowid
    cursor.close()
    return last_inserted_id

# 修改数据
def update_data(connection, table, data, condition, autocommit = True):
    cursor = connection.cursor()
    updates = ', '.join([f"{key} = %s" for key in data.keys()])
    sql = f"UPDATE {table} SET {updates} WHERE {condition}"
    cursor.execute(sql, list(data.values()))
    update_count = cursor.rowcount
    if autocommit:
        connection.commit()
    cursor.close()
    return update_count

def execute_query(connection, query):
    cursor = connection.cursor(dictionary=True)
    try:
        cursor.execute(query)
        result = cursor.fetchall()
        return result
    except mysql.connector.Error as e:
        print(f"查询错误: {e}")
        return None, None
    finally:
        cursor.close()

def delete(connection,query, autocommit = True):
    cursor = connection.cursor()
    try:
         cursor.execute(query)
         if autocommit:
            connection.commit()
    except mysql.connector.Error as e:
        print(f"删除失败: {e}")
        return None
    finally:
        cursor.close()

def get_page_str(page:int,size:int):
    query = ''
    offset = '' 
    if not page or page <= 0 :
        page = 0 
    
    
    if  size and size > 0:
        if page > 0:
            offset = f'offset {size*(page - 1)}'    
        query = f' limit {size} {offset}'
    return query