export const API_CONFIG = {
    // 从环境变量读取，默认使用开发环境地址
    //BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000',
    //生产环境
    BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://***********:9077',

    API_PREFIX: '/api/v1',

    // API端点
    ENDPOINTS: {
        PIPELINE: '/pipeline',
        PIPELINE_STATUS: '/pipeline/status',
        TERMINATE_TASK: '/terminate-task',
        PREVIEW: '/preview',
        PREVIEW_IMAGE: '/preview-image'
    },

    // 获取完整的API URL
    getApiUrl(endpoint: string): string {
        return `${this.BASE_URL}${this.API_PREFIX}${endpoint}`;
    },

    // 获取预览API URL
    getPreviewUrl(taskId: string, type: string): string {
        return this.getApiUrl(`${this.ENDPOINTS.PREVIEW}/${taskId}/${type}`);
    },

    // 获取预览图片URL
    getPreviewImageUrl(taskId: string, type: string, fileName: string): string {
        return this.getApiUrl(`${this.ENDPOINTS.PREVIEW_IMAGE}/${taskId}/${type}/${fileName}`);
    }
}; 