## ComfyUI 涂鸦与提示词生成图像 Web 界面设计文档 (修订版 V1.4)

**版本:** 1.4
**日期:** 2025年4月20日

### 1. 引言

**1.1. 目标**
本文档旨在设计一个前端 HTML 页面，允许用户在画布上进行涂鸦，并**输入与涂鸦内容相关的提示词**。点击生成后，前端将涂鸦图像和用户输入的提示词发送到后端。后端启动一个**异步任务**（使用 `theme_backend/workApi/tuya.json` ComfyUI 工作流），**生成任务 ID**，并将该任务 ID 与用户提示词**存储在 Redis 中（建议有效期 24 小时）**。后端返回任务 ID 和用户提供的提示词给前端。前端可通过任务 ID **轮询**任务状态，最终获取并展示生成的图像。用户可以在前端**查看并修改**当前任务的提示词，修改后可重新触发生成。

**1.2. 背景**
基于 ComfyUI (`theme_backend/workApi/tuya.json` 工作流) 强大的图像生成能力，我们希望创建一个简化的 Web 界面，让用户可以通过简单的涂鸦结合**用户输入的提示词**来生成图像。此方案采用异步任务机制，更适合可能耗时较长的 AI 生成过程。

**1.3. 范围**
*   设计 HTML 页面布局和核心元素（使用 ASCII 艺术图示例）。
*   定义用户交互流程：绘图 -> **输入提示词** -> 启动任务 -> 轮询状态 -> 显示结果 -> 修改提示词 -> 重新生成。
*   明确前后端数据交互格式（涂鸦图像、任务 ID、提示词）和 API 调用方式。
*   技术栈：前端基于 `theme_ui` 项目（如 Vue/React, TypeScript, TailwindCSS），后端需新增 API 接口并集成 Redis 和 ComfyUI。
*   **不包括：** 后端 ComfyUI API 的具体实现、Redis 配置。

### 2. 用户界面 (UI) 设计

**2.1. 页面布局**
页面采用左右分栏布局（小屏幕下可考虑上下布局）。

**ASCII 艺术界面布局示例:**

```
+--------------------------------------+--------------------------------------+
| 左侧: 绘图与输入区                   | 右侧: 任务与结果区                   |
|                                      |                                      |
| +----------------------------------+ | 任务ID: [_________________________]   |
| |      绘图说明区域                | | (`task-id-display`, 只读)          |
| |    (`drawing-instruction`)       | |                                      |
| |(e.g., "请绘制并输入提示词...") | | 使用的提示词 (可编辑):               |
| +----------------------------------+ | (`used-prompt-display`, 任务创建后显示)|
|                                      | | +----------------------------------+ |
| +----------------------------------+ | | | [________________________________] | |
| |        提示词输入区域            | | | (`current-prompt-display`, 可编辑)| |
| | (`user-prompt-input`)          | | +----------------------------------+ |
| |  <textarea>                      | |                                      |
| +----------------------------------+ | 结果图像:                            |
|                                      | | +----------------------------------+ |
| +----------------------------------+ | | |                                  | |
| |          绘图画布区域            | | |      (<img> `result-image`)      | |
| |         (Canvas Element)         | | |    (加载时显示 Spinner          | |
| |         `drawing-canvas`         | | |     `loading-indicator`)       | |
| |                                  | | |                                  | |
| +----------------------------------+ | +----------------------------------+ |
|                                      |                                      |
| 工具栏 (颜色, 大小, 工具)            | 状态消息:                            |
| `drawing-toolbar`                    | +----------------------------------+ |
|                                      | | [请绘制并输入提示词...]            | |
| 控制按钮:                            | | (`status-message`)               | |
| +-------------+  +-------------+     | +----------------------------------+ |
| | 清除画布  |  | 生成图像  |     |                                      |
| |`clear-button`| `generate-button`|                                      |
| +-------------+  +-------------+     |                                      |
| (生成后可能变为"更新提示词并重生成") |                                      |
+--------------------------------------+--------------------------------------+
```

**2.2. 左侧区域 - 绘图与输入区 (`drawing-input-area`)**
*   **绘图说明 (`drawing-instruction`):** `<p>` 或 `<div>`，显示操作指引。
*   **提示词输入 (`user-prompt-input`):** `<textarea>`，允许用户输入描述涂鸦内容的提示词。
*   **绘图工具栏 (`drawing-toolbar`):** 包含颜色选择器、笔刷大小选择器、画笔/橡皮切换按钮。
    *   **颜色选择器 (`color-picker`)**: 允许用户选择画笔颜色。
    *   **笔刷大小滑块/选择器 (`brush-size-selector`)**: 允许用户调整画笔粗细。
    *   **画笔/橡皮切换按钮 (`tool-toggle`)**: 切换画笔/橡皮擦模式。
*   **画布 (`drawing-canvas`):** HTML5 `<canvas>` 元素，供用户实时绘制。
*   **控制按钮:**
    *   **清除画布 (`clear-button`):** 清空画布内容。建议增加二次确认。
    *   **生成图像 (`generate-button`):** 首次点击时，将画布内容和用户输入的提示词发送到后端启动任务。任务进行中或完成后，此按钮的功能可能变为"更新提示词并重新生成"。

**2.3. 右侧区域 - 任务与结果区 (`result-area`)**
*   **任务ID显示 (`task-id-display`):** 只读区域，显示后端返回的任务 ID。
*   **使用的提示词 (`used-prompt-display`):** `<textarea>` 或带编辑功能的文本区域，**仅在任务创建后显示**后端返回或用户修改后的提示词。用户可在此编辑并触发重新生成。
*   **结果图像 (`result-image`):** `<img>` 元素，显示后端返回的生成图像。
*   **加载指示器 (`loading-indicator`):** 在等待 API 响应时显示。
*   **状态/信息 (`status-message`):** `<p>` 或 `<div>` 元素，显示操作指引、任务状态、错误信息等。

**2.4. 视觉风格 (CSS)**
*   遵循 `theme_ui` 项目的整体设计规范和 `tailwind.config.js` 配置。
*   界面简洁直观，清晰区分左右区域。
*   按钮、输入框、状态提示等元素样式友好且一致。
*   考虑响应式设计。

### 3. 核心功能与交互流程 (异步任务流程)

**3.1. 页面加载**
*   浏览器加载应用（HTML, CSS, JS/TS）。
*   初始化画布绘图功能。
*   显示初始的**绘图说明**。
*   界面处于空闲状态 (`idle`)。

**3.2. 用户操作**
*   用户在左侧**画布**上进行涂鸦。
*   用户在左侧**提示词输入框**输入描述。

**3.3. 清除画布**
*   用户点击"清除画布"按钮。
*   画布内容被清空。提示词输入框**不应**被清空。任务 ID、结果图像等状态**通常应被重置**。

**3.4. 启动生成任务**
1.  用户完成绘制并**输入提示词**后，点击"生成图像"按钮 (`generate-button`)。
2.  **前端触发操作:**
    *   将画布内容转换为图像 Blob 数据。（**更新: 使用 Canvas.toBlob() 而非 toDataURL()**）
    *   **获取用户输入的提示词 (`user_prompt`)。**
    *   **验证提示词是否为空。**
    *   禁用"生成图像"和"清除画布"按钮，**禁用提示词输入框**。
    *   显示加载指示器，更新状态消息为"正在创建任务..."。
    *   **创建 FormData 对象，添加涂鸦图像文件、任务 ID 和提示词。**（**更新: 使用 FormData 而非 JSON**）
    *   发送 `POST` 请求到后端 API 端点：`/api/v1/doodle/start`，设置内容类型为 `multipart/form-data`。
3.  **后端处理 (异步):**
    *   接收请求，验证数据。
    *   **从 FormData 中获取上传的图像文件和提示词。**（**更新: 直接获取上传的文件，而非从 Base64 解码**）
    *   生成唯一的 `task_id`（如果前端未提供）。
    *   **获取用户提供的提示词 (`user_prompt`)。**
    *   **将 {task_id: user_prompt} 键值对存入 Redis，设置 TTL (如 24 小时)。**
    *   **直接将上传的图像文件传递给 ComfyUI，无需保存临时文件。**（**更新: 直接使用 upload_image 函数处理文件流**）
    *   **异步触发 `theme_backend/workApi/tuya.json` ComfyUI 工作流**。 **注意：必须能覆盖工作流中预设的提示词节点输入。**
    *   **立即**向前端返回响应。
4.  **处理启动响应 (前端):**
    *   接收后端响应：成功时为 { "task_id": "...", "prompt": "..." }，失败时为 { "error": "..." }。
    *   若成功：
        *   保存 `task_id` 到前端状态。
        *   将后端返回的 `prompt`（即用户输入的提示词）显示在**右侧**的 `used-prompt-display` 区域，并允许编辑。
        *   将 `task_id` 显示在 `task-id-display` 区域。
        *   更新状态消息为"任务已创建 (ID: ...)，正在生成图像..."。
        *   **开始轮询**任务状态 API：`GET /api/v1/doodle/status/{task_id}`。
        *   按钮 `generate-button` 文本可变为"更新提示词并重新生成"。
    *   若失败：
        *   显示错误信息到 `status-message`。
        *   隐藏加载指示器。
        *   恢复按钮和提示词输入框状态。
5.  **轮询与结果处理 (前端):**
    *   使用 `setInterval` 或类似机制，定期调用状态 API `GET /api/v1/doodle/status/{task_id}`。
    *   处理状态 API 响应：
        *   { "status": "pending" | "running", "progress": ... }: 更新状态消息，保持加载指示器。
        *   { "status": "completed", "result_image_url": "..." | "result_image_base64": "..." }:
            *   停止轮询。
            *   隐藏加载指示器。
            *   将结果图像加载到 `result-image` (`src` 属性)。
            *   更新状态消息为"生成成功"。
            *   恢复按钮状态（允许清除或重新生成），**使右侧提示词编辑框可编辑**。
        *   { "status": "failed", "error": "..." }:
            *   停止轮询。
            *   隐藏加载指示器。
            *   在 `status-message` 显示错误信息。
            *   恢复按钮状态，**使右侧提示词编辑框可编辑**。
    *   轮询期间可以提供取消任务的按钮（需后端支持 `terminate` API）。

**3.5. 修改提示词并重新生成**
1.  用户在**右侧**的 `used-prompt-display` 区域修改了提示词。
2.  用户点击"更新提示词并重新生成"按钮（即修改后的 `generate-button`）。
3.  **前端触发操作:**
    *   获取当前 `task_id` 和**右侧编辑框中**修改后的提示词 (`new_prompt`)。
    *   禁用按钮和右侧提示词编辑框。
    *   显示加载指示器，更新状态消息为"正在更新提示词并重新生成..."。
    *   发送 `PUT` 请求到后端 API 端点：`/api/v1/doodle/prompt/{task_id}`。请求体包含 `{ "prompt": "new_prompt" }`。
4.  **后端处理:**
    *   接收请求，验证 `task_id` 和 `new_prompt`。
    *   **更新 Redis 中该 `task_id` 对应的提示词值为 `new_prompt`。** （考虑是否重置 TTL）。
    *   使用**相同的**临时图像文件路径（需确保图像文件仍然存在或可访问）和 `new_prompt` **重新异步触发 `tuya.json` 工作流**。（可能需要机制来处理或覆盖仍在运行的旧工作流实例）。
    *   向前端返回确认信息。
5.  **前端处理:**
    *   接收后端确认响应。若成功：
        *   更新状态消息为"提示词已更新，正在重新生成..."。
        *   **重新开始轮询** `GET /api/v1/doodle/status/{task_id}` 以获取新的结果。
    *   若失败：
        *   显示错误信息。
        *   隐藏加载指示器。
        *   恢复按钮状态和右侧提示词编辑框状态。

### 4. 技术实现细节

**4.1. HTML 结构**
*   参照上面的 ASCII 艺术图，使用 `theme_ui` 项目的技术栈（如 Vue/React 组件）构建 HTML 结构。
*   为各交互元素分配合理的 ID 或 class，便于 CSS 选择和 JS/TS 操作。

**4.2. CSS (`tailwind.config.js` & 组件样式)**
*   利用 TailwindCSS 实现布局、样式和响应式设计。
*   确保加载指示器、按钮禁用状态等有清晰的视觉反馈。
*   样式与 `theme_ui` 整体风格保持一致。

**4.3. TypeScript/JavaScript (前端逻辑)**
*   **画布处理 (增强与 UX):**
    *   **初始化 Canvas:** 获取 `<canvas>` 元素的 2D 上下文。
    *   **状态变量:** 维护绘图状态 (`isDrawing`, `currentColor`, `currentBrushSize`, `currentTool`, `lastX`, `lastY`)。
    *   **光标样式**: 根据 `currentTool` 和 `currentBrushSize` 动态修改画布元素的 CSS `cursor` 属性。
    *   **事件监听:** 绑定鼠标/触摸事件到画布元素。
    *   **绘制函数 `draw(x, y)`:** 实现线条绘制逻辑。
    *   **工具栏交互:** 更新状态变量并高亮选中工具。
    *   **清除画布:** 添加确认对话框。
    *   **获取图像数据:** 
        * **更新: 使用 `canvas.toBlob()` 异步获取图像 Blob 对象**，而不是 `canvas.toDataURL()`
        * 图像格式为 'image/png'
        * **在回调函数内创建 FormData 并添加文件和其他表单字段**
    *   **检查画布空白状态:** 添加函数检测画布是否为空（或几乎为空），防止用户提交空画布。
*   **状态管理:** 使用 Vuex/Pinia/Redux 或组件内部状态管理，**新增管理用户输入的提示词 (`userPrompt`) 和右侧显示的当前提示词 (`currentPrompt`)**。
*   **API 调用:** 封装 `fetch` 或 `axios` 调用，处理 `/api/v1/doodle/start`, `/api/v1/doodle/status/{task_id}`, `/api/v1/doodle/prompt/{task_id}` 等接口。
    * **对于 `/start` 接口，使用 FormData 格式，设置 'Content-Type': 'multipart/form-data'**
*   **轮询逻辑:** 实现健壮的轮询机制。
*   **DOM 更新:** 根据状态变化动态更新界面元素（按钮/输入框禁用/启用、加载指示器、状态消息、提示词内容、结果图像）。

**4.4. 前后端数据格式约定 (API 接口)**

*   **启动任务:**
    *   请求: `POST /api/v1/doodle/start`
    *   **格式: `multipart/form-data`**
    *   **表单字段:**
        *   **`sketch_image`: 图像文件 (PNG 格式)**
        *   **`task_id`: 字符串，可选 (如由前端生成)**
        *   **`prompt`: 用户输入的提示词**
        *   **`initial_context`: 可选的初始上下文**
    *   响应 (成功): `{ "task_id": "...", "enhanced_prompt": "..." }`
    *   响应 (失败): `{ "detail": "错误消息" }`
*   **查询任务状态:**
    *   请求: `GET /api/v1/doodle/status/{task_id}`
    *   响应 (进行中): `{ "status": "running", "progress": ... }`
    *   响应 (完成): `{ "status": "completed", "result_image_url": "..." }` (或 `result_image_base64`)
    *   响应 (失败): `{ "status": "failed", "error": "..." }`
*   **更新提示词并重新生成:**
    *   请求: `PUT /api/v1/doodle/prompt/{task_id}`
    *   格式: `application/json`
    *   Body: `{ "prompt": "用户在右侧编辑框修改后的新提示词" }`
    *   响应 (成功): `{ "message": "提示词已更新，重新生成已启动" }`
    *   响应 (失败): `{ "detail": "错误消息" }`

**4.5. 后端要求**
*   实现上述三个 API 端点。
    * **更新 `start_doodle_task` 函数以使用 FastAPI 的 File 和 Form 参数**
*   集成 Redis 用于存储 `task_id` -> `prompt` 映射，并处理 TTL。
*   **处理图像上传:** 
    * **接收 `sketch_image` 文件**
    * **直接使用 `upload_image` 函数将文件流上传到 ComfyUI 服务器**
    * **不再需要 Base64 解码和临时文件处理**
*   **处理提示词:** 接收用户提供的 `prompt`，并存储到 Redis。
*   **调用 ComfyUI 工作流:**
    *   加载工作流模板 `tuya.json`。
    *   动态修改工作流节点参数：
        *   **节点 44 (LoadImage):** 使用上传的图像文件名。
        *   **节点 41 (String - 正面提示词):** 使用从 Redis 读取的（或请求中传递的）用户提示词。
        *   **节点 45 (SaveImage):** 使用包含 `task_id` 的唯一前缀。
    *   发送修改后的工作流到 ComfyUI `/prompt` API。
*   **管理异步任务的状态:** 使用任务队列或后台机制处理 ComfyUI 调用和状态查询。
*   （可选）提供终止 ComfyUI 任务的机制。

### 5. ComfyUI 工作流 (`tuya.json`) 集成要点

*   后端服务加载 `tuya.json` 并动态修改节点值。
*   **图像输入 (节点 44):** 使用前端上传的涂鸦图像。
*   **提示词输入 (节点 41):** **使用用户提供的提示词（从 Redis 读取）**。
*   **图像输出 (节点 45):** 使用包含 `task_id` 的文件名前缀。

### 6. 未来可能的增强

*   **撤销/重做:** 实现画布状态栈。
*   **负面提示词输入框**。
*   **参数调整界面:** 允许用户控制后端 AI 模型的某些参数。
*   **任务历史记录/画廊**。
*   **WebSocket** 替代轮询。
*   **文件格式多样化**: 除了PNG外，考虑支持JPEG等其他常见图像格式。

---
此文档描述了基于用户输入提示词和异步任务的涂鸦生成图像方案，已更新为使用文件上传方式而非Base64编码方式处理图像。请参考此文档进行前后端开发。