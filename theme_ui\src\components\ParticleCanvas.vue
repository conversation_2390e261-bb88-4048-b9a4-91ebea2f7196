<template>
  <div class="particle-canvas-container" :style="{ width: width, height: height }">
    <canvas ref="canvasRef"></canvas>
  </div>
</template>

<script lang="ts">
// 提供一个常规脚本导出以便其他组件导入
import { defineComponent } from "vue";

export default defineComponent({
  name: "ParticleCanvas",
});
</script>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import ParticleEngine, {
  Particle,
  ParticleOptions,
} from "../utils/ParticleEngine";

// 定义组件属性
const props = defineProps({
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "10rem",
  },
  primaryColor: {
    type: String,
    default: "rgba(99, 102, 241, 0.7)",
  },
  secondaryColor: {
    type: String,
    default: "rgba(59, 130, 246, 0.7)",
  },
  particleColor: {
    type: String,
    default: "rgba(255, 255, 255, 0.6)",
  },
  particleDensity: {
    type: Number,
    default: 50,
  },
  minSize: {
    type: Number,
    default: 0.2,
  },
  maxSize: {
    type: Number,
    default: 1.0,
  },
  speed: {
    type: Number,
    default: 0.3,
  },
});

// 定义事件
const emit = defineEmits(["initialized", "destroyed"]);

// 组件状态
const canvasRef = ref<HTMLCanvasElement | null>(null);
let ctx: CanvasRenderingContext2D | null = null;
let animationFrameId: number | null = null;
let lastTimestamp = 0;
let particles: Particle[] = [];
let particleEngine: ParticleEngine | null = null;
let canvasWidth = 0;
let canvasHeight = 0;

// 初始化Canvas
const initCanvas = () => {
  if (!canvasRef.value) return;

  const canvas = canvasRef.value;
  ctx = canvas.getContext("2d");

  if (!ctx) return;

  // 设置Canvas尺寸
  const container = canvas.parentElement;
  if (container) {
    canvasWidth = container.clientWidth;
    canvasHeight = container.clientHeight;
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;
  }

  // 初始化粒子引擎
  const containerDiv = document.createElement("div");
  particleEngine = new ParticleEngine(containerDiv, {
    width: canvasWidth,
    height: canvasHeight,
    primaryColor: props.primaryColor,
    secondaryColor: props.secondaryColor,
    particleColor: props.particleColor,
    particleDensity: props.particleDensity,
    minSize: props.minSize,
    maxSize: props.maxSize,
    speed: props.speed,
  });

  particleEngine.initParticles();
  particles = particleEngine.getParticles();

  // 启动渲染循环
  startRenderLoop();

  // 通知初始化完成
  emit("initialized");
};

// 渲染帧
const renderFrame = (timestamp: number) => {
  if (!ctx || !particleEngine) return;

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 更新粒子状态
  if (!lastTimestamp) lastTimestamp = timestamp;
  const _deltaTime = timestamp - lastTimestamp;
  lastTimestamp = timestamp;

  // 让粒子引擎处理更新逻辑
  particleEngine.updateParticle(timestamp);
  particles = particleEngine.getParticles();

  // 绘制粒子
  particles.forEach((particle) => {
    if (!ctx) return;

    // 计算实际坐标
    const x = (particle.x / 100) * canvasWidth;
    const y = (particle.y / 100) * canvasHeight;

    // 设置粒子样式
    ctx.globalAlpha = particle.opacity;
    ctx.fillStyle = particle.color;

    // 绘制圆形粒子
    ctx.beginPath();
    const radius = (particle.size * (particle.isSpecialStar ? 3.5 : 2.5)) / 2;
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();

    // 添加发光效果
    if (particle.isSpecialStar) {
      // 特殊星星有更强的光晕
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius * 4);
      gradient.addColorStop(0, particle.color);

      // 修复颜色处理方式，创建正确的透明度颜色
      let transparentColor;
      if (particle.color.startsWith("rgba")) {
        // 如果已经是RGBA格式，解析并修改透明度
        const matches = particle.color.match(
          /rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/
        );
        if (matches && matches.length === 5) {
          const [_, r, g, b] = matches;
          transparentColor = `rgba(${r}, ${g}, ${b}, 0.3)`;
        } else {
          transparentColor = "rgba(255, 255, 255, 0.3)";
        }
      } else if (particle.color.startsWith("rgb")) {
        // 处理RGB格式
        const matches = particle.color.match(
          /rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/
        );
        if (matches && matches.length === 4) {
          const [_, r, g, b] = matches;
          transparentColor = `rgba(${r}, ${g}, ${b}, 0.3)`;
        } else {
          transparentColor = "rgba(255, 255, 255, 0.3)";
        }
      } else {
        // 其他颜色格式使用半透明白色
        transparentColor = "rgba(255, 255, 255, 0.3)";
      }

      gradient.addColorStop(0.5, transparentColor);
      gradient.addColorStop(1, "transparent");

      ctx.globalAlpha = particle.opacity * 0.7;
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(x, y, radius * 4, 0, Math.PI * 2);
      ctx.fill();
    } else {
      // 普通星星有较弱的光晕
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius * 2);
      gradient.addColorStop(0, particle.color);

      // 修复颜色处理方式，创建正确的透明度颜色
      let transparentColor;
      if (particle.color.startsWith("rgba")) {
        // 如果已经是RGBA格式，解析并修改透明度
        const matches = particle.color.match(
          /rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/
        );
        if (matches && matches.length === 5) {
          const [_, r, g, b] = matches;
          transparentColor = `rgba(${r}, ${g}, ${b}, 0.1)`;
        } else {
          transparentColor = "rgba(255, 255, 255, 0.1)";
        }
      } else if (particle.color.startsWith("rgb")) {
        // 处理RGB格式
        const matches = particle.color.match(
          /rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/
        );
        if (matches && matches.length === 4) {
          const [_, r, g, b] = matches;
          transparentColor = `rgba(${r}, ${g}, ${b}, 0.1)`;
        } else {
          transparentColor = "rgba(255, 255, 255, 0.1)";
        }
      } else {
        // 其他颜色格式使用半透明白色
        transparentColor = "rgba(255, 255, 255, 0.1)";
      }

      gradient.addColorStop(0.7, transparentColor);
      gradient.addColorStop(1, "transparent");

      ctx.globalAlpha = particle.opacity * 0.5;
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(x, y, radius * 2, 0, Math.PI * 2);
      ctx.fill();
    }
  });

  // 继续渲染循环
  animationFrameId = requestAnimationFrame(renderFrame);
};

// 启动渲染循环
const startRenderLoop = () => {
  if (animationFrameId) cancelAnimationFrame(animationFrameId);
  animationFrameId = requestAnimationFrame(renderFrame);
};

// 处理窗口大小变化
const handleResize = () => {
  if (!canvasRef.value || !ctx || !particleEngine) return;

  const container = canvasRef.value.parentElement;
  if (container) {
    canvasWidth = container.clientWidth;
    canvasHeight = container.clientHeight;
    canvasRef.value.width = canvasWidth;
    canvasRef.value.height = canvasHeight;

    // 更新粒子引擎设置
    particleEngine.updateOptions({
      width: canvasWidth,
      height: canvasHeight,
    });

    // 重新初始化粒子
    particleEngine.initParticles();
    particles = particleEngine.getParticles();
  }
};

// 监听属性变化
watch(
  [
    () => props.primaryColor,
    () => props.secondaryColor,
    () => props.particleColor,
    () => props.particleDensity,
    () => props.minSize,
    () => props.maxSize,
    () => props.speed,
  ],
  () => {
    if (!particleEngine) return;

    // 更新粒子引擎选项
    particleEngine.updateOptions({
      primaryColor: props.primaryColor,
      secondaryColor: props.secondaryColor,
      particleColor: props.particleColor,
      particleDensity: props.particleDensity,
      minSize: props.minSize,
      maxSize: props.maxSize,
      speed: props.speed,
    });

    // 重新初始化粒子
    particleEngine.initParticles();
    particles = particleEngine.getParticles();
  }
);

// 生命周期钩子
onMounted(() => {
  initCanvas();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }

  window.removeEventListener("resize", handleResize);

  if (particleEngine) {
    particleEngine.destroy();
    particleEngine = null;
  }

  emit("destroyed");
});

// 暴露公共方法
defineExpose({
  // 重新初始化
  reinitialize: () => {
    if (particleEngine) {
      particleEngine.initParticles();
      particles = particleEngine.getParticles();
    }
  },
  // 暂停动画
  pause: () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  },
  // 恢复动画
  resume: () => {
    if (!animationFrameId) {
      startRenderLoop();
    }
  },
});
</script>

<style scoped>
.particle-canvas-container {
  position: relative;
  overflow: hidden;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
}
</style> 