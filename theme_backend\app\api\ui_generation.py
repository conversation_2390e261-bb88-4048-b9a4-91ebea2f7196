from fastapi import APIRouter, UploadFile, HTTPException
import json
import os
from datetime import datetime
import uuid
import aiofiles
import httpx
import traceback
from pathlib import Path
import logging
from .common import (
    wait_for_image,
    upload_image,
    load_workflow,
    send_prompt,
    logger
)

router = APIRouter()
server_name="theme_ui_generation_server"
def build_ui_generation_workflow(wallpaper_filename: str, task_id: str) -> dict:
    """构建UI生成工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("icon_api.json")
        
        # 修改壁纸输入节点
        if "69" not in workflow:
            logger.error("工作流中找不到节点69")
            raise HTTPException(status_code=500, detail="Node 69 not found in workflow")
            
        workflow["69"]["inputs"]["image"] = wallpaper_filename
        logger.info(f"设置输入图片: {wallpaper_filename}")

        # 修改保存路径
        if "42" not in workflow:
            logger.error("工作流中找不到节点42")
            raise HTTPException(status_code=500, detail="Node 42 not found in workflow")
            
        save_prefix = f"changan/mainLine_{task_id}/styleIcon/styleIcon"
        workflow["42"]["inputs"]["filename_prefix"] = save_prefix
        logger.info(f"设置保存路径前缀: {save_prefix}")
            
        return workflow, "42"  # 42是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

def build_ui_path_workflow(task_id: str) -> dict:
    """构建UI路径工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("icon_api path.json")
        
        # 修改输入节点88的路径
        if "88" not in workflow:
            logger.error("工作流中找不到节点88")
            raise HTTPException(status_code=500, detail="Node 88 not found in workflow")
            
        workflow["88"]["inputs"]["file_path"] = "/ssd2/changan/theme/main_line/source_icon"
        logger.info(f"设置源图标路径: {workflow['88']['inputs']['file_path']}")

        # 修改输入节点89的路径
        if "89" not in workflow:
            logger.error("工作流中找不到节点89")
            raise HTTPException(status_code=500, detail="Node 89 not found in workflow")
            
        workflow["89"]["inputs"]["file_path"] = "/ssd2/changan/theme/main_line/source_mask"
        logger.info(f"设置源遮罩路径: {workflow['89']['inputs']['file_path']}")

        # 修改输入节点90的路径
        if "90" not in workflow:
            logger.error("工作流中找不到节点90")
            raise HTTPException(status_code=500, detail="Node 90 not found in workflow")
            
        workflow["90"]["inputs"]["file_path"] = f"/ssd2/ComfyUI/output/changan/mainLine_{task_id}/releases/wallpaper"
        logger.info(f"设置壁纸路径: {workflow['90']['inputs']['file_path']}")

        # 修改保存路径
        if "42" not in workflow:
            logger.error("工作流中找不到节点42")
            raise HTTPException(status_code=500, detail="Node 42 not found in workflow")
            
        save_prefix = f"changan/mainLine_{task_id}/UI/styleIcon"
        workflow["42"]["inputs"]["filename_prefix"] = save_prefix
        logger.info(f"设置保存路径前缀: {save_prefix}")
            
        return workflow, "42"  # 42是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/ui-generation")
async def generate_ui(wallpaper_image: UploadFile, task_id: str):
    try:
        logger.info("\n========== 开始处理UI生成请求 ==========")
        logger.info(f"壁纸文件名: {wallpaper_image.filename}")
        logger.info(f"任务ID: {task_id}")

        if not wallpaper_image:
            raise HTTPException(status_code=400, detail="请上传壁纸图片")
        if not task_id:
            raise HTTPException(status_code=400, detail="请提供任务ID")

        # 上传壁纸图片
        wallpaper_uploaded_filename = await upload_image(server_name, wallpaper_image.file, "壁纸")

        # 构建工作流
        workflow, output_node_id = build_ui_generation_workflow(wallpaper_uploaded_filename, task_id)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 构建子文件夹路径
        subfolder = f"changan/mainLine_{task_id}/styleIcon"
        
        # 等待图片生成
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{task_id}/styleIcon"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_name = "styleIcon.png"
        image_path = save_dir / image_name
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                
                # 构建公开访问URL
                public_url = f"/ui_source/mainLine_{task_id}/styleIcon/{image_name}"
                logger.info(f"保存图片: {public_url}")
            except Exception as e:
                logger.error(f"保存图片失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")

        logger.info("========== UI生成请求处理完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "task_id": task_id,
            "image_url": public_url
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理UI生成请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ui-path-generation")
async def generate_ui_path(task_id: str):
    """使用路径工作流生成UI"""
    try:
        logger.info("\n========== 开始处理UI路径生成请求 ==========")
        logger.info(f"任务ID: {task_id}")

        if not task_id:
            raise HTTPException(status_code=400, detail="请提供任务ID")

        # 构建工作流
        workflow, output_node_id = build_ui_path_workflow(task_id)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 构建子文件夹路径
        subfolder = f"changan/mainLine_{task_id}/UI"
        
        # 等待图片生成
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{task_id}/UI"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_name = "styleIcon.png"
        image_path = save_dir / image_name
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                
                # 构建公开访问URL
                public_url = f"/ui_source/mainLine_{task_id}/UI/{image_name}"
                logger.info(f"保存图片: {public_url}")
            except Exception as e:
                logger.error(f"保存图片失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")

        logger.info("========== UI路径生成请求处理完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "task_id": task_id,
            "image_url": public_url
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理UI路径生成请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e)) 