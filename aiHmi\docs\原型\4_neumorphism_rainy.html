<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新拟态雨夜 (Neumorphism Rainy Night)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'system-ui', sans-serif;
            background-color: #2c3e50; /* 深蓝灰色背景 */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            overflow: hidden; /* 禁止滚动 */
            height: 100vh;
            width: 100vw;
            color: #bdc3c7;
            position: relative;
        }
        /* 动态氛围层 - 新拟态风格 */
        .ambiance-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        .rain-drops {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, rgba(100, 150, 200, 0.6), transparent);
            animation: rainFall 2s infinite linear;
        }
        .rain-drops:nth-child(1) { left: 10%; animation-delay: 0s; }
        .rain-drops:nth-child(2) { left: 25%; animation-delay: 0.3s; }
        .rain-drops:nth-child(3) { left: 40%; animation-delay: 0.6s; }
        .rain-drops:nth-child(4) { left: 55%; animation-delay: 0.9s; }
        .rain-drops:nth-child(5) { left: 70%; animation-delay: 1.2s; }
        .rain-drops:nth-child(6) { left: 85%; animation-delay: 1.5s; }
        .soft-glow {
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent);
            border-radius: 50%;
            animation: glowPulse 4s infinite ease-in-out;
        }
        .soft-glow:nth-child(7) { top: 20%; left: 20%; animation-delay: 0s; }
        .soft-glow:nth-child(8) { top: 60%; left: 70%; animation-delay: 2s; }
        @keyframes rainFall {
            0% { transform: translateY(-20px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(100vh); opacity: 0; }
        }
        @keyframes glowPulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.2); }
        }
        .card {
            border-radius: 30px;
            background: #2c3e50;
            box-shadow: 8px 8px 16px #22303f, -8px -8px 16px #364c61;
            transition: all 0.3s ease-in-out;
        }
        .card-inner {
            border-radius: 25px;
            background: #2c3e50;
            box-shadow: inset 6px 6px 12px #22303f, inset -6px -6px 12px #364c61;
        }
        .card-pressed {
            border-radius: 30px;
            background: #2c3e50;
            box-shadow: inset 8px 8px 16px #22303f, inset -8px -8px 16px #364c61;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 2rem; /* 卡片间距 */
            width: 100%;
            height: 100%;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        .icon-style {
            color: #8e9eab;
            transition: all 0.3s ease;
        }
        .icon-style:hover {
            color: #ecf0f1;
            text-shadow: 0 0 10px #ecf0f1;
        }
        
        /* 返回按钮样式 - 新拟态风格 */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            padding: 1rem 1.5rem;
            border-radius: 20px;
            background: #2c3e50;
            box-shadow: 8px 8px 16px #22303f, -8px -8px 16px #364c61;
            color: #bdc3c7;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .back-button:hover {
            box-shadow: inset 4px 4px 8px #22303f, inset -4px -4px 8px #364c61;
            color: #ecf0f1;
            text-shadow: 0 0 10px #ecf0f1;
        }
    </style>
</head>
<body style="background-image: url('https://images.unsplash.com/photo-1519692933481-e162a57d6721?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">

    <!-- 动态氛围层 -->
    <div class="ambiance-layer">
        <div class="rain-drops"></div>
        <div class="rain-drops"></div>
        <div class="rain-drops"></div>
        <div class="rain-drops"></div>
        <div class="rain-drops"></div>
        <div class="rain-drops"></div>
        <div class="soft-glow"></div>
        <div class="soft-glow"></div>
    </div>

    <!-- 返回按钮 -->
    <a href="complete_transition_demo.html" class="back-button" data-transition="curtain_open">
        <i class="fas fa-arrow-left"></i>
        返回演示
    </a>

    <div class="grid-container">

        <!-- Dynamic Island (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-route fa-lg icon-style"></i>
                <div>
                    <p class="text-lg font-bold text-gray-200">雨夜归途</p>
                    <p class="text-sm text-gray-400">剩余：15分钟</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-gray-200">22:10</p>
                <p class="text-sm text-gray-400">PM</p>
            </div>
        </div>

        <!-- Weather Card (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cloud-showers-heavy fa-lg icon-style"></i>
                <div>
                    <p class="text-lg font-bold text-gray-200">大雨</p>
                    <p class="text-sm text-gray-400">路面湿滑</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold text-gray-200">15°C</p>
                <p class="text-sm text-gray-400">体感 13°C</p>
            </div>
        </div>

        <!-- VPA Avatar (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-4">
            <div class="card-inner w-32 h-32 rounded-full flex items-center justify-center">
                <img src="https://media.giphy.com/media/3oKIPnAiaMCws8nOsE/giphy.gif" alt="VPA Avatar" class="w-28 h-28 rounded-full object-cover">
            </div>
            <p class="mt-4 text-xl font-semibold text-gray-200">小绿</p>
            <p class="text-gray-400">雨夜陪伴</p>
        </div>

        <!-- Map Card (4x2) -->
        <div class="card col-span-4 row-span-2 p-2">
            <div class="card-inner w-full h-full flex items-center justify-center bg-cover bg-center rounded-2xl" style="background-image: url('https://source.unsplash.com/800x400/?map,dark');">
                 <p class="text-2xl font-bold text-white bg-black/30 p-4 rounded-lg">前方拥堵，已规划新路线</p>
            </div>
        </div>

        <!-- Climate Control Card (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-6">
            <p class="text-lg text-gray-400">车内温度</p>
            <p class="text-7xl font-bold text-gray-200 my-2">24°</p>
            <div class="flex space-x-8 text-4xl mt-4">
                <div class="card-pressed p-4 rounded-full">
                    <i class="fas fa-snowflake icon-style"></i>
                </div>
                <div class="card p-4 rounded-full">
                    <i class="fas fa-fire-flame-curved icon-style"></i>
                </div>
            </div>
        </div>

        <!-- Quick Control Card (8x1) -->
        <div class="card col-span-8 row-span-1 flex items-center justify-around px-8 text-3xl">
            <div class="flex flex-col items-center cursor-pointer">
                <div class="card p-5 rounded-full hover:card-pressed">
                    <i class="fas fa-car-burst icon-style"></i>
                </div>
                <span class="text-base mt-2 text-gray-400">危险警报</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer">
                <div class="card p-5 rounded-full hover:card-pressed">
                    <i class="fas fa-wind-window icon-style"></i>
                </div>
                <span class="text-base mt-2 text-gray-400">车窗除雾</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer">
                <div class="card p-5 rounded-full hover:card-pressed">
                    <i class="fas fa-volume-high icon-style"></i>
                </div>
                <span class="text-base mt-2 text-gray-400">媒体音量</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer">
                <div class="card p-5 rounded-full hover:card-pressed">
                    <i class="fas fa-phone icon-style"></i>
                </div>
                <span class="text-base mt-2 text-gray-400">紧急呼叫</span>
            </div>
        </div>

    </div>

    <script src="enhanced_transition_manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化增强过渡管理器
            const transitionManager = new EnhancedTransitionManager({
                debug: false,
                enableSnapshots: true,
                transitionDuration: 1000
            });
        });
    </script>

</body>
</html>