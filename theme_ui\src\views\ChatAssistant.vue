<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">闲聊助手</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 对话区域 -->
        <div class="w-full h-full overflow-y-auto">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">对话交流</h2>

            <div class="flex flex-col gap-4 h-full">
              <!-- 对话历史 -->
              <div v-for="(message, index) in messages" :key="index" :class="{'self-end': message.user === 'user'}">
                <div class="bg-gray-700 text-white p-4 rounded-lg max-w-md">
                  <p>{{ message.text }}</p>
                </div>
              </div>

              <!-- 输入区域 -->
              <form @submit.prevent="sendMessage">
                <div class="mb-6">
                  <label for="user-input" class="block text-sm font-medium text-purple-200 mb-2">
                  </label>
                  <textarea id="user-input" v-model="userInput" rows="3" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="随便聊点什么吧..." required style="background-color: rgba(15, 23, 42, 0.7);"></textarea>
                </div>

                <div class="flex justify-end">
                  <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isSending">
                    <svg v-if="isSending" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ isSending ? '处理中...' : '发送' }}
                  </button>
                </div>
              </form>
            </div>
          </GlassPanel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);

interface Message {
  user: string; // 'user' 或 'assistant'
  text: string;
}

const messages = ref<Message[]>([]);
const userInput = ref<string>("");
const isSending = ref<boolean>(false);
const taskId = ref<string>("");

// 生成任务ID
const generateTaskId = (): string => {
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim()) {
    alert("请输入请求文本");
    return;
  }

  try {
    isSending.value = true;

    taskId.value = generateTaskId();

    // 添加用户消息到对话历史
    messages.value.push({ user: 'user', text: userInput.value });

    // 清空用户输入框
    userInput.value = "";

    // 调用API
    const response = {};//await themeApi.sendMessage(taskId.value, messages.value[messages.value.length - 1].text);

    // 添加助手回复到对话历史
    if (response.reply_text) {
      messages.value.push({ user: 'assistant', text: response.reply_text });
    }
  } catch (error) {
    console.error("发送对话请求失败", error);
    alert("发送对话请求失败，请重试");
  } finally {
    isSending.value = false;
  }
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}
</style>
