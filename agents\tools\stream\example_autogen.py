#!/usr/bin/env python
"""
AutoGen调用MCP服务示例
演示如何在AutoGen中使用流式输出MCP服务
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
import time

# 导入AutoGen相关
from autogen import UserProxyAgent, AssistantAgent, ConversableAgent, Agent
from autogen.agentchat.contrib.capabilities.agent_capabilities import CapabilityIO, Capability

# 导入MCP工具箱
from .autogen_adapter import MCPToolkit, create_streaming_functions, StreamingOutputManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)


class StreamingCapability(Capability):
    """流式输出能力"""
    
    def __init__(self, mcp_url: str):
        """
        初始化流式输出能力
        
        Args:
            mcp_url: MCP服务URL
        """
        self.mcp_toolkit = MCPToolkit(mcp_url)
        # 初始化会话
        self.session_id = None
    
    def reset(self):
        """重置能力状态"""
        if self.session_id:
            self.mcp_toolkit.close_current_session()
            self.session_id = None
    
    def initialize_session(self, session_name: str, meta_data: Optional[Dict[str, Any]] = None):
        """初始化会话"""
        try:
            self.session_id = self.mcp_toolkit.init_session(session_name, meta_data)
            logger.info(f"初始化会话: {self.session_id}")
            return True
        except Exception as e:
            logger.error(f"初始化会话失败: {str(e)}")
            return False
    
    def get_tools(self):
        """获取工具定义"""
        functions = create_streaming_functions(self.mcp_toolkit)
        
        tool_configs = [
            {
                "name": "thinking",
                "description": "流式输出思考步骤",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": "思考内容"
                        }
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "stream_output",
                "description": "流式输出文本内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": "输出内容"
                        },
                        "is_final": {
                            "type": "boolean",
                            "description": "是否为最后的内容块"
                        }
                    },
                    "required": ["content"]
                }
            },
            {
                "name": "show_code",
                "description": "显示代码块",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "description": "代码内容"
                        },
                        "language": {
                            "type": "string",
                            "description": "代码语言"
                        }
                    },
                    "required": ["code"]
                }
            },
            {
                "name": "save_data",
                "description": "保存数据到上下文",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "key": {
                            "type": "string",
                            "description": "上下文键"
                        },
                        "value": {
                            "description": "上下文值（任意类型）"
                        },
                        "ttl": {
                            "type": "integer",
                            "description": "过期时间（秒）"
                        }
                    },
                    "required": ["key", "value"]
                }
            },
            {
                "name": "load_data",
                "description": "从上下文加载数据",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "key": {
                            "type": "string",
                            "description": "上下文键"
                        }
                    },
                    "required": ["key"]
                }
            },
            {
                "name": "log_tool_call",
                "description": "记录工具调用",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "tool_name": {
                            "type": "string",
                            "description": "工具名称"
                        },
                        "tool_input": {
                            "type": "object",
                            "description": "工具输入参数"
                        },
                        "tool_output": {
                            "type": "object",
                            "description": "工具输出结果（可选）"
                        }
                    },
                    "required": ["tool_name", "tool_input"]
                }
            }
        ]
        
        return tool_configs, functions


def create_streaming_agent(mcp_url: str, name: str, system_message: str) -> AssistantAgent:
    """
    创建带有流式输出能力的助手代理
    
    Args:
        mcp_url: MCP服务URL
        name: 助手名称
        system_message: 系统消息
        
    Returns:
        AssistantAgent: 助手代理
    """
    # 创建流式输出能力
    streaming_capability = StreamingCapability(mcp_url)
    
    # 初始化会话
    session_name = f"autogen-{int(time.time())}"
    streaming_capability.initialize_session(session_name, {"agent_name": name})
    
    # 获取工具配置
    tool_configs, functions = streaming_capability.get_tools()
    
    # 创建助手代理
    assistant = AssistantAgent(
        name=name,
        system_message=system_message,
        llm_config={
            "config_list": [
                {
                    "model": "gpt-4-turbo",
                    "api_key": os.environ.get("OPENAI_API_KEY")
                }
            ],
            "tools": tool_configs,
            "tool_choice": "auto",
        }
    )
    
    # 注册工具函数
    assistant.register_for_llm(
        tool_name="thinking",
        tool_fn=functions["thinking"]
    )
    assistant.register_for_llm(
        tool_name="stream_output",
        tool_fn=functions["stream_output"]
    )
    assistant.register_for_llm(
        tool_name="show_code",
        tool_fn=functions["show_code"]
    )
    assistant.register_for_llm(
        tool_name="save_data",
        tool_fn=functions["save_data"]
    )
    assistant.register_for_llm(
        tool_name="load_data",
        tool_fn=functions["load_data"]
    )
    assistant.register_for_llm(
        tool_name="log_tool_call",
        tool_fn=functions["log_tool_call"]
    )
    
    return assistant


def main():
    """主函数"""
    # MCP服务URL
    mcp_url = os.environ.get("MCP_URL", "http://localhost:8000")
    
    # 创建用户代理
    user_proxy = UserProxyAgent(
        name="User",
        human_input_mode="ALWAYS"
    )
    
    # 创建助手代理
    system_message = """你是一个具有流式输出能力的AI助手。
你可以使用以下工具：
- thinking: 输出你的思考过程，这些内容用户将会看到，但以思考模式呈现
- stream_output: 流式输出内容，是向用户直接展示的内容
- show_code: 显示代码块，支持语法高亮
- save_data: 将数据保存到会话上下文中
- load_data: 从会话上下文中加载数据
- log_tool_call: 记录工具调用过程

对于复杂问题，请先使用thinking工具思考解决方案，再用stream_output输出结果。
代码应使用show_code工具展示，可以指定语言类型。
你可以保存和加载上下文数据，以便在对话过程中保持状态。

请展示所有这些能力，确保你的响应是流畅的。
"""
    
    assistant = create_streaming_agent(
        mcp_url=mcp_url,
        name="StreamingAssistant",
        system_message=system_message
    )
    
    # 启动对话
    user_proxy.initiate_chat(
        assistant,
        message="你好！请展示一下你的流式输出能力。首先思考一下你能做什么，然后给我计算斐波那契数列的Python代码，并解释它是如何工作的。"
    )
    
    # 对话继续
    while True:
        try:
            # 等待用户输入
            user_input = input("输入消息(输入'exit'退出): ")
            if user_input.lower() in ["exit", "quit", "q"]:
                break
                
            # 发送消息
            user_proxy.send(
                recipient=assistant,
                message=user_input
            )
        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"对话错误: {str(e)}")
            break
    
    logger.info("对话结束")


if __name__ == "__main__":
    main() 