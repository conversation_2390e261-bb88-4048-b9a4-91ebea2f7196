// 主题管理Store - 支持动态主题切换和壁纸整合
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // === 状态 ===
  const currentTheme = ref('glassmorphism') // 当前主题
  const isDarkMode = ref(false) // 深色模式
  const isHighContrast = ref(false) // 高对比度模式
  const reducedMotion = ref(false) // 减少动画
  const wallpaperBrightness = ref(0.5) // 壁纸亮度 (0-1)
  const wallpaperDominantColor = ref('#ffffff') // 壁纸主色调
  const wallpaperContrastColor = ref('#000000') // 壁纸对比色

  // 可用主题列表
  const availableThemes = ref([
    {
      id: 'glassmorphism',
      name: '玻璃拟态',
      description: '现代、轻盈、通透的毛玻璃效果',
      preview: '/themes/glassmorphism-preview.jpg'
    },
    {
      id: 'serenity',
      name: '宁静',
      description: '平静、放松、自然的氛围',
      preview: '/themes/serenity-preview.jpg'
    },
    {
      id: 'cyberpunk',
      name: '赛博朋克',
      description: '科技感、未来感、霓虹灯效',
      preview: '/themes/cyberpunk-preview.jpg'
    }
  ])

  // === 计算属性 ===
  const themeConfig = computed(() => {
    const themes = {
      glassmorphism: {
        primaryColor: '#007aff',
        secondaryColor: '#5856d6',
        accentColor: '#ff9500',
        transparency: {
          light: 0.2,
          medium: 0.6,
          heavy: 0.9
        },
        blur: {
          light: 8,
          medium: 12,
          heavy: 20
        }
      },
      serenity: {
        primaryColor: '#4a7c59',
        secondaryColor: '#7fb069',
        accentColor: '#d4a574',
        transparency: {
          light: 0.15,
          medium: 0.5,
          heavy: 0.85
        },
        blur: {
          light: 6,
          medium: 10,
          heavy: 16
        }
      },
      cyberpunk: {
        primaryColor: '#00ffff',
        secondaryColor: '#ff00ff',
        accentColor: '#ffff00',
        transparency: {
          light: 0.1,
          medium: 0.4,
          heavy: 0.8
        },
        blur: {
          light: 4,
          medium: 8,
          heavy: 12
        }
      }
    }

    return themes[currentTheme.value] || themes.glassmorphism
  })

  const adaptiveColors = computed(() => {
    // 根据壁纸亮度自动调整颜色
    const brightness = wallpaperBrightness.value
    const isLight = brightness > 0.5

    return {
      textPrimary: isLight
        ? `rgba(0, 0, 0, 0.9)`
        : `rgba(255, 255, 255, 0.9)`,
      textSecondary: isLight
        ? `rgba(0, 0, 0, 0.6)`
        : `rgba(255, 255, 255, 0.6)`,
      textTertiary: isLight
        ? `rgba(0, 0, 0, 0.4)`
        : `rgba(255, 255, 255, 0.4)`,
      backgroundPrimary: isLight
        ? `rgba(255, 255, 255, ${themeConfig.value.transparency.medium})`
        : `rgba(28, 28, 30, ${themeConfig.value.transparency.medium})`,
      borderColor: isLight
        ? `rgba(0, 0, 0, 0.1)`
        : `rgba(255, 255, 255, 0.1)`
    }
  })

  // === 方法 ===
  const setTheme = (themeId) => {
    if (availableThemes.value.find(t => t.id === themeId)) {
      currentTheme.value = themeId
      applyThemeToDOM()
    }
  }

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    applyThemeToDOM()
  }

  const setWallpaperColors = (brightness, dominantColor, contrastColor) => {
    wallpaperBrightness.value = Math.max(0, Math.min(1, brightness))
    wallpaperDominantColor.value = dominantColor
    wallpaperContrastColor.value = contrastColor
    applyThemeToDOM()
  }

  const applyThemeToDOM = () => {
    const root = document.documentElement
    const config = themeConfig.value
    const colors = adaptiveColors.value

    // 应用主题颜色
    root.style.setProperty('--color-primary', config.primaryColor)
    root.style.setProperty('--color-secondary', config.secondaryColor)
    root.style.setProperty('--color-accent', config.accentColor)

    // 应用透明度
    root.style.setProperty('--transparency-light', config.transparency.light)
    root.style.setProperty('--transparency-medium', config.transparency.medium)
    root.style.setProperty('--transparency-heavy', config.transparency.heavy)

    // 应用模糊效果
    root.style.setProperty('--blur-light', `${config.blur.light}px`)
    root.style.setProperty('--blur-medium', `${config.blur.medium}px`)
    root.style.setProperty('--blur-heavy', `${config.blur.heavy}px`)

    // 应用自适应颜色
    root.style.setProperty('--text-primary', colors.textPrimary)
    root.style.setProperty('--text-secondary', colors.textSecondary)
    root.style.setProperty('--text-tertiary', colors.textTertiary)
    root.style.setProperty('--bg-primary', colors.backgroundPrimary)
    root.style.setProperty('--border-color', colors.borderColor)

    // 应用壁纸相关变量
    root.style.setProperty('--wallpaper-brightness', wallpaperBrightness.value)
    root.style.setProperty('--wallpaper-dominant-color', wallpaperDominantColor.value)
    root.style.setProperty('--wallpaper-contrast-color', wallpaperContrastColor.value)
  }

  const initializeTheme = () => {
    applyThemeToDOM()
  }

  return {
    // 状态
    currentTheme,
    isDarkMode,
    isHighContrast,
    reducedMotion,
    wallpaperBrightness,
    wallpaperDominantColor,
    wallpaperContrastColor,
    availableThemes,

    // 计算属性
    themeConfig,
    adaptiveColors,

    // 方法
    setTheme,
    toggleDarkMode,
    setWallpaperColors,
    applyThemeToDOM,
    initializeTheme
  }
})

  // === 计算属性 ===
  const themeConfig = computed(() => {
    const themes = {
      glassmorphism: {
        primaryColor: '#007aff',
        secondaryColor: '#5856d6',
        accentColor: '#ff9500',
        transparency: {
          light: 0.2,
          medium: 0.6,
          heavy: 0.9
        },
        blur: {
          light: 8,
          medium: 12,
          heavy: 20
        },
        borderRadius: {
          small: 8,
          medium: 12,
          large: 16
        }
      },
      serenity: {
        primaryColor: '#4a7c59',
        secondaryColor: '#7fb069',
        accentColor: '#d4a574',
        transparency: {
          light: 0.15,
          medium: 0.5,
          heavy: 0.85
        },
        blur: {
          light: 6,
          medium: 10,
          heavy: 16
        },
        borderRadius: {
          small: 12,
          medium: 16,
          large: 24
        }
      },
      cyberpunk: {
        primaryColor: '#00ffff',
        secondaryColor: '#ff00ff',
        accentColor: '#ffff00',
        transparency: {
          light: 0.1,
          medium: 0.4,
          heavy: 0.8
        },
        blur: {
          light: 4,
          medium: 8,
          heavy: 12
        },
        borderRadius: {
          small: 2,
          medium: 4,
          large: 8
        }
      }
    }
    
    return themes[currentTheme.value] || themes.glassmorphism
  })

  const adaptiveColors = computed(() => {
    // 根据壁纸亮度自动调整颜色
    const brightness = wallpaperBrightness.value
    const isLight = brightness > 0.5
    
    return {
      textPrimary: isLight 
        ? `rgba(0, 0, 0, 0.9)` 
        : `rgba(255, 255, 255, 0.9)`,
      textSecondary: isLight 
        ? `rgba(0, 0, 0, 0.6)` 
        : `rgba(255, 255, 255, 0.6)`,
      textTertiary: isLight 
        ? `rgba(0, 0, 0, 0.4)` 
        : `rgba(255, 255, 255, 0.4)`,
      backgroundPrimary: isLight
        ? `rgba(255, 255, 255, ${themeConfig.value.transparency.medium})`
        : `rgba(28, 28, 30, ${themeConfig.value.transparency.medium})`,
      borderColor: isLight
        ? `rgba(0, 0, 0, 0.1)`
        : `rgba(255, 255, 255, 0.1)`
    }
  })

  // === 方法 ===
  const setTheme = (themeId) => {
    if (availableThemes.value.find(t => t.id === themeId)) {
      currentTheme.value = themeId
      applyThemeToDOM()
    }
  }

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    applyThemeToDOM()
  }

  const setWallpaperColors = (brightness, dominantColor, contrastColor) => {
    wallpaperBrightness.value = Math.max(0, Math.min(1, brightness))
    wallpaperDominantColor.value = dominantColor
    wallpaperContrastColor.value = contrastColor
    applyThemeToDOM()
  }

  const detectSystemPreferences = () => {
    // 检测系统偏好设置
    if (window.matchMedia) {
      // 深色模式检测
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      isDarkMode.value = darkModeQuery.matches
      darkModeQuery.addEventListener('change', (e) => {
        isDarkMode.value = e.matches
        applyThemeToDOM()
      })

      // 高对比度检测
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
      isHighContrast.value = highContrastQuery.matches
      highContrastQuery.addEventListener('change', (e) => {
        isHighContrast.value = e.matches
        applyThemeToDOM()
      })

      // 减少动画检测
      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
      reducedMotion.value = reducedMotionQuery.matches
      reducedMotionQuery.addEventListener('change', (e) => {
        reducedMotion.value = e.matches
        applyThemeToDOM()
      })
    }
  }

  const applyThemeToDOM = () => {
    const root = document.documentElement
    const config = themeConfig.value
    const colors = adaptiveColors.value

    // 应用主题颜色
    root.style.setProperty('--color-primary', config.primaryColor)
    root.style.setProperty('--color-secondary', config.secondaryColor)
    root.style.setProperty('--color-accent', config.accentColor)

    // 应用透明度
    root.style.setProperty('--transparency-light', config.transparency.light)
    root.style.setProperty('--transparency-medium', config.transparency.medium)
    root.style.setProperty('--transparency-heavy', config.transparency.heavy)

    // 应用模糊效果
    root.style.setProperty('--blur-light', `${config.blur.light}px`)
    root.style.setProperty('--blur-medium', `${config.blur.medium}px`)
    root.style.setProperty('--blur-heavy', `${config.blur.heavy}px`)

    // 应用圆角
    root.style.setProperty('--border-radius-small', `${config.borderRadius.small}px`)
    root.style.setProperty('--border-radius-medium', `${config.borderRadius.medium}px`)
    root.style.setProperty('--border-radius-large', `${config.borderRadius.large}px`)

    // 应用自适应颜色
    root.style.setProperty('--text-primary', colors.textPrimary)
    root.style.setProperty('--text-secondary', colors.textSecondary)
    root.style.setProperty('--text-tertiary', colors.textTertiary)
    root.style.setProperty('--bg-primary', colors.backgroundPrimary)
    root.style.setProperty('--border-color', colors.borderColor)

    // 应用壁纸相关变量
    root.style.setProperty('--wallpaper-brightness', wallpaperBrightness.value)
    root.style.setProperty('--wallpaper-dominant-color', wallpaperDominantColor.value)
    root.style.setProperty('--wallpaper-contrast-color', wallpaperContrastColor.value)

    // 应用可访问性设置
    if (isHighContrast.value) {
      root.style.setProperty('--transparency-medium', '0.8')
      root.style.setProperty('--transparency-heavy', '0.95')
    }

    if (reducedMotion.value) {
      root.style.setProperty('--transition-fast', '0s')
      root.style.setProperty('--transition-normal', '0s')
      root.style.setProperty('--transition-slow', '0s')
    }
  }

  const saveThemePreferences = () => {
    const preferences = {
      theme: currentTheme.value,
      darkMode: isDarkMode.value,
      highContrast: isHighContrast.value,
      reducedMotion: reducedMotion.value
    }
    localStorage.setItem('ai-hmi-theme-preferences', JSON.stringify(preferences))
  }

  const loadThemePreferences = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-theme-preferences')
      if (saved) {
        const preferences = JSON.parse(saved)
        currentTheme.value = preferences.theme || 'glassmorphism'
        isDarkMode.value = preferences.darkMode || false
        isHighContrast.value = preferences.highContrast || false
        reducedMotion.value = preferences.reducedMotion || false
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error)
    }
  }

  // === 监听器 ===
  watch([currentTheme, isDarkMode, isHighContrast, reducedMotion], () => {
    saveThemePreferences()
  })

  watch([wallpaperBrightness, wallpaperDominantColor, wallpaperContrastColor], () => {
    applyThemeToDOM()
  })

  // === 初始化 ===
  const initializeTheme = () => {
    loadThemePreferences()
    detectSystemPreferences()
    applyThemeToDOM()
  }

  return {
    // 状态
    currentTheme,
    isDarkMode,
    isHighContrast,
    reducedMotion,
    wallpaperBrightness,
    wallpaperDominantColor,
    wallpaperContrastColor,
    availableThemes,
    
    // 计算属性
    themeConfig,
    adaptiveColors,
    
    // 方法
    setTheme,
    toggleDarkMode,
    setWallpaperColors,
    detectSystemPreferences,
    applyThemeToDOM,
    saveThemePreferences,
    loadThemePreferences,
    initializeTheme
  }
})
