import logging
import re
import json
import time
import asyncio
from typing import Dict, List, Optional, Union, Any, Callable, AsyncGenerator
import uuid

from .stream_manager import StreamManager
from .stream_types import (
    StreamEvent, StreamEventType,
    create_token_event, create_thinking_step_event,
    create_code_block_event, create_tool_call_event,
    create_completion_event, create_error_event
)

logger = logging.getLogger(__name__)

class LLMStreamAdapter:
    """LLM流式输出适配器，用于对接LLM的流式输出并处理为结构化事件"""
    
    def __init__(self, agent_id: str = "default_agent"):
        """
        初始化LLM流式适配器
        
        Args:
            agent_id: 智能体ID
        """
        self.agent_id = agent_id
        self.stream_manager = StreamManager()
        
    async def process_llm_stream(
        self, 
        session_id: str,
        stream_generator: AsyncGenerator[str, None],
        extract_thinking_steps: bool = True,
        extract_code_blocks: bool = True,
        extract_tool_calls: bool = True
    ) -> str:
        """
        处理LLM流式输出
        
        Args:
            session_id: 会话ID
            stream_generator: LLM流式输出生成器
            extract_thinking_steps: 是否提取思考步骤
            extract_code_blocks: 是否提取代码块
            extract_tool_calls: 是否提取工具调用
            
        Returns:
            str: 完整的生成内容
        """
        accumulated_text = ""
        current_thinking_step = ""
        in_thinking_step = False
        current_code_block = ""
        in_code_block = False
        code_language = ""
        current_tool_call = ""
        in_tool_call = False
        
        # 正则表达式
        thinking_step_pattern = r'🤔|💭|🧠|🔍|✏️|🔄|⚙️|📝|🧩|🧪|🔬|🔎|🤖'
        code_block_start_pattern = r'```([a-zA-Z0-9]+)?'
        code_block_end_pattern = r'```'
        tool_call_start_pattern = r'<tool_call>|<tool>'
        tool_call_end_pattern = r'</tool_call>|</tool>'
        
        try:
            async for chunk in stream_generator:
                # 更新累积文本
                accumulated_text += chunk
                
                # 处理思考步骤
                if extract_thinking_steps:
                    if not in_thinking_step and re.search(thinking_step_pattern, chunk):
                        in_thinking_step = True
                        current_thinking_step = chunk
                    elif in_thinking_step:
                        current_thinking_step += chunk
                        if chunk.endswith('\n') or len(current_thinking_step) > 100:
                            # 发布思考步骤事件
                            icon = re.search(thinking_step_pattern, current_thinking_step)
                            icon = icon.group(0) if icon else "🤔"
                            text = current_thinking_step.replace(icon, "").strip()
                            
                            if text:  # 只有非空文本才发布
                                self.stream_manager.publish_event(create_thinking_step_event(
                                    agent_id=self.agent_id,
                                    session_id=session_id,
                                    icon=icon,
                                    text=text
                                ))
                            
                            in_thinking_step = False
                            current_thinking_step = ""
                
                # 处理代码块
                if extract_code_blocks:
                    if not in_code_block:
                        code_start_match = re.search(code_block_start_pattern, chunk)
                        if code_start_match:
                            in_code_block = True
                            code_language = code_start_match.group(1) or "text"
                            current_code_block = ""
                    elif in_code_block:
                        if re.search(code_block_end_pattern, chunk):
                            in_code_block = False
                            
                            # 发布代码块事件
                            self.stream_manager.publish_code_block(
                                session_id=session_id,
                                agent_id=self.agent_id,
                                language=code_language,
                                code=current_code_block.strip()
                            )
                            
                            current_code_block = ""
                            code_language = ""
                        else:
                            current_code_block += chunk
                
                # 处理工具调用
                if extract_tool_calls:
                    if not in_tool_call:
                        if re.search(tool_call_start_pattern, chunk):
                            in_tool_call = True
                            current_tool_call = chunk
                    elif in_tool_call:
                        current_tool_call += chunk
                        if re.search(tool_call_end_pattern, chunk):
                            in_tool_call = False
                            
                            # 尝试解析工具调用
                            try:
                                # 简单解析工具调用，实际使用中可能需要更复杂的解析逻辑
                                tool_call_content = re.sub(
                                    r'<tool_call>|</tool_call>|<tool>|</tool>', 
                                    '', 
                                    current_tool_call
                                ).strip()
                                
                                # 假设格式为：工具名称: 参数JSON
                                tool_parts = tool_call_content.split(':', 1)
                                if len(tool_parts) == 2:
                                    tool_name = tool_parts[0].strip()
                                    tool_input_str = tool_parts[1].strip()
                                    
                                    try:
                                        tool_input = json.loads(tool_input_str)
                                    except json.JSONDecodeError:
                                        # 如果不是JSON，尝试作为普通字符串
                                        tool_input = {"input": tool_input_str}
                                    
                                    # 发布工具调用事件
                                    self.stream_manager.publish_tool_call(
                                        session_id=session_id,
                                        agent_id=self.agent_id,
                                        tool_name=tool_name,
                                        tool_input=tool_input
                                    )
                            except Exception as e:
                                logger.error(f"解析工具调用失败: {str(e)}")
                            
                            current_tool_call = ""
                
                # 发布token事件（每个chunk）
                self.stream_manager.publish_event(create_token_event(
                    agent_id=self.agent_id,
                    session_id=session_id,
                    text=chunk
                ))
                
                # 适当延迟，避免过快发送
                await asyncio.sleep(0.01)
            
            # 完成后发布完成事件
            self.stream_manager.publish_completion(
                session_id=session_id,
                agent_id=self.agent_id,
                text=accumulated_text
            )
            
            return accumulated_text
            
        except Exception as e:
            logger.error(f"处理LLM流式输出失败: {str(e)}")
            
            # 发布错误事件
            self.stream_manager.publish_error(
                session_id=session_id,
                agent_id=self.agent_id,
                error_type="stream_processing_error",
                message=f"处理LLM流式输出失败: {str(e)}"
            )
            
            return accumulated_text
    
    async def stream_thinking_process(self, session_id: str, steps: List[Dict[str, str]]) -> None:
        """
        流式输出思考过程
        
        Args:
            session_id: 会话ID
            steps: 思考步骤列表，每步包含icon和text
        """
        await self.stream_manager.stream_thinking(
            session_id=session_id,
            agent_id=self.agent_id,
            steps=steps
        )
    
    def save_context(self, session_id: str, name: str, 
                   value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        保存上下文数据
        
        Args:
            session_id: 会话ID
            name: 上下文名称
            value: 上下文数据
            ttl: 过期时间（秒）
            
        Returns:
            bool: 是否保存成功
        """
        return self.stream_manager.save_context(
            session_id=session_id,
            agent_id=self.agent_id,
            name=name,
            value=value,
            ttl=ttl
        )
    
    def get_context(self, session_id: str, name: str) -> Optional[Dict[str, Any]]:
        """
        获取上下文数据
        
        Args:
            session_id: 会话ID
            name: 上下文名称
            
        Returns:
            Optional[Dict[str, Any]]: 上下文数据
        """
        return self.stream_manager.get_context(
            session_id=session_id,
            agent_id=self.agent_id,
            name=name
        )
    
    async def stream_from_openai(self, session_id: str, response) -> str:
        """
        处理OpenAI流式响应
        
        Args:
            session_id: 会话ID
            response: OpenAI流式响应对象
            
        Returns:
            str: 完整的生成内容
        """
        accumulated_text = ""
        
        try:
            async for chunk in response:
                if hasattr(chunk, 'choices') and chunk.choices:
                    # 获取增量内容
                    delta = chunk.choices[0].delta
                    if hasattr(delta, 'content') and delta.content:
                        content = delta.content
                        accumulated_text += content
                        
                        # 发布token事件
                        self.stream_manager.publish_event(create_token_event(
                            agent_id=self.agent_id,
                            session_id=session_id,
                            text=content
                        ))
                        
                        # 适当延迟，避免过快发送
                        await asyncio.sleep(0.01)
            
            # 完成后发布完成事件
            self.stream_manager.publish_completion(
                session_id=session_id,
                agent_id=self.agent_id,
                text=accumulated_text
            )
            
            return accumulated_text
            
        except Exception as e:
            logger.error(f"处理OpenAI流式响应失败: {str(e)}")
            
            # 发布错误事件
            self.stream_manager.publish_error(
                session_id=session_id,
                agent_id=self.agent_id,
                error_type="openai_stream_error",
                message=f"处理OpenAI流式响应失败: {str(e)}"
            )
            
            return accumulated_text
    
    async def stream_from_claude(self, session_id: str, response) -> str:
        """
        处理Claude流式响应
        
        Args:
            session_id: 会话ID
            response: Claude流式响应对象
            
        Returns:
            str: 完整的生成内容
        """
        accumulated_text = ""
        
        try:
            # Claude的流式响应结构根据实际情况调整
            async for chunk in response:
                if hasattr(chunk, 'delta') and hasattr(chunk.delta, 'text'):
                    content = chunk.delta.text
                    accumulated_text += content
                    
                    # 发布token事件
                    self.stream_manager.publish_event(create_token_event(
                        agent_id=self.agent_id,
                        session_id=session_id,
                        text=content
                    ))
                    
                    # 适当延迟，避免过快发送
                    await asyncio.sleep(0.01)
            
            # 完成后发布完成事件
            self.stream_manager.publish_completion(
                session_id=session_id,
                agent_id=self.agent_id,
                text=accumulated_text
            )
            
            return accumulated_text
            
        except Exception as e:
            logger.error(f"处理Claude流式响应失败: {str(e)}")
            
            # 发布错误事件
            self.stream_manager.publish_error(
                session_id=session_id,
                agent_id=self.agent_id,
                error_type="claude_stream_error",
                message=f"处理Claude流式响应失败: {str(e)}"
            )
            
            return accumulated_text

# 使用示例
async def example_usage():
    # 创建适配器
    adapter = LLMStreamAdapter(agent_id="example_agent")
    
    # 创建会话
    stream_manager = StreamManager()
    session_id = stream_manager.create_session(user_id="example_user")
    
    # 模拟LLM流式输出生成器
    async def mock_llm_stream():
        chunks = [
            "🤔 我正在分析你的问题\n",
            "让我思考一下如何实现这个功能。\n",
            "我们可以使用Python创建一个简单的例子：\n",
            "```python\n",
            "def hello_world():\n",
            "    print('Hello, world!')\n",
            "\n",
            "hello_world()\n",
            "```\n",
            "现在我需要调用一个外部工具：\n",
            "<tool_call>search_web: {\"query\": \"最新Python版本特性\"}</tool_call>\n",
            "希望这个例子对你有帮助！"
        ]
        
        for chunk in chunks:
            yield chunk
            await asyncio.sleep(0.5)  # 模拟流式输出的延迟
    
    # 处理流式输出
    full_text = await adapter.process_llm_stream(
        session_id=session_id,
        stream_generator=mock_llm_stream()
    )
    
    print(f"完整文本: {full_text}")
    
    # 保存上下文数据
    adapter.save_context(
        session_id=session_id,
        name="conversation_history",
        value={"messages": [{"role": "assistant", "content": full_text}]}
    )
    
    # 获取上下文数据
    context = adapter.get_context(session_id, "conversation_history")
    print(f"上下文数据: {context}")

if __name__ == "__main__":
    # 运行示例
    import asyncio
    asyncio.run(example_usage()) 