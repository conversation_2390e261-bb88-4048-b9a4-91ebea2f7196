/**
 * 粒子特效引擎
 * 用于生成和管理星空粒子效果
 * 性能优化版本
 */

export interface Particle {
    id: number;
    x: number;
    y: number;
    size: number;
    speed: number;
    opacity: number;
    color: string;
    moveX: number; // 水平方向移动
    rotationSpeed: number; // 旋转速度
    isSpecialStar: boolean;
    blinkSpeed: number;
    blinkPhase: number;
}

export interface ParticleOptions {
    width?: number;
    height?: number;
    primaryColor?: string;
    secondaryColor?: string;
    particleColor?: string;
    particleDensity?: number;
    minSize?: number;
    maxSize?: number;
    speed?: number;
}

export class ParticleEngine {
    private particles: Particle[] = [];
    private container: HTMLElement | null = null;
    private animationFrameId: number | null = null;
    private lastTimestamp: number = 0;
    private isRunning: boolean = false;
    // 性能优化：添加帧率控制
    private frameSkip: number = 0;
    private frameCount: number = 0;
    private colorCache: string[] = [];

    // 默认选项
    private options: Required<ParticleOptions> = {
        width: 0,
        height: 0,
        primaryColor: "rgba(99, 102, 241, 0.7)",
        secondaryColor: "rgba(59, 130, 246, 0.7)",
        particleColor: "rgba(255, 255, 255, 0.6)",
        particleDensity: 50,
        minSize: 0.2,
        maxSize: 1.0,
        speed: 0.3,
    };

    constructor(container: HTMLElement | null, options: ParticleOptions = {}) {
        this.container = container;
        this.updateOptions(options);
        this.initColorCache();
    }

    /**
     * 初始化颜色缓存，减少运行时计算
     */
    private initColorCache(): void {
        this.colorCache = [
            this.options.particleColor,
            "rgba(255, 255, 255, 0.7)",
            "rgba(173, 216, 230, 0.7)",
            "rgba(255, 223, 186, 0.6)",
            this.options.primaryColor,
            this.options.secondaryColor,
        ];
    }

    /**
     * 更新配置选项
     */
    updateOptions(options: ParticleOptions): void {
        this.options = { ...this.options, ...options };

        // 性能优化：根据粒子密度动态调整帧跳过
        if (this.options.particleDensity && this.options.particleDensity > 100) {
            this.frameSkip = 1; // 密度高时，每2帧更新一次
        } else {
            this.frameSkip = 0; // 密度低时，每帧都更新
        }

        // 更新颜色缓存
        this.initColorCache();

        // 如果容器已设置且有尺寸变化，重新初始化粒子
        if (this.container && (options.width !== undefined || options.height !== undefined ||
            options.particleDensity !== undefined)) {
            this.initParticles();
        }
    }

    /**
     * 设置容器元素
     */
    setContainer(container: HTMLElement): void {
        this.container = container;
        this.initParticles();
    }

    /**
     * 创建单个粒子
     */
    private createParticle(id: number): Particle {
        // 性能优化：直接使用缓存的颜色
        const colorIndex = Math.floor(Math.random() * this.colorCache.length);

        // 使用更广泛的区域分布粒子
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // 性能优化：减少比例计算
        const isSpecialStar = Math.random() < 0.15; // 减少特殊粒子比例

        // 简化随机生成粒子大小的逻辑
        const size = isSpecialStar
            ? this.options.minSize + Math.random() * (this.options.maxSize - this.options.minSize) * 1.5
            : this.options.minSize + Math.random() * (this.options.maxSize - this.options.minSize) * 0.8;

        // 明亮粒子有更高的不透明度，简化计算
        const opacity = isSpecialStar ? 0.5 + Math.random() * 0.4 : 0.1 + Math.random() * 0.3;

        // 简化闪烁动画参数
        const blinkSpeed = isSpecialStar ? 1 + Math.random() * 3 : 0;

        return {
            id,
            x,
            y,
            size,
            speed: 0.02 + Math.random() * this.options.speed,
            opacity,
            color: this.colorCache[colorIndex],
            moveX: (Math.random() - 0.5) * 0.02, // 减少水平移动量
            rotationSpeed: Math.random() * 0.2, // 减少旋转速度
            isSpecialStar,
            blinkSpeed,
            blinkPhase: Math.random() * Math.PI * 2,
        };
    }

    /**
     * 初始化粒子
     */
    initParticles(): void {
        if (!this.container) return;

        const containerWidth = this.options.width || this.container.offsetWidth;
        const containerHeight = this.options.height || this.container.offsetHeight;

        // 性能优化：限制最大粒子数
        const area = containerWidth * containerHeight;
        const density = Math.min(this.options.particleDensity, 100); // 安全上限
        const maxParticles = 400; // 限制最大粒子数
        const particleCount = Math.min(
            Math.floor((area / 1000) * density),
            maxParticles
        );

        // 重用现有粒子数组以减少垃圾回收
        if (this.particles.length > particleCount) {
            this.particles = this.particles.slice(0, particleCount);
        } else if (this.particles.length < particleCount) {
            const newParticles = Array.from(
                { length: particleCount - this.particles.length },
                (_, i) => this.createParticle(i + this.particles.length)
            );
            this.particles = [...this.particles, ...newParticles];
        } else {
            // 粒子数量不变，只更新属性
            this.particles.forEach((particle, i) => {
                const newParticle = this.createParticle(i);
                // 只更新关键属性，减少对象创建
                particle.x = newParticle.x;
                particle.y = newParticle.y;
                particle.size = newParticle.size;
                particle.speed = newParticle.speed;
                particle.opacity = newParticle.opacity;
                particle.color = newParticle.color;
                particle.moveX = newParticle.moveX;
                particle.isSpecialStar = newParticle.isSpecialStar;
                particle.blinkSpeed = newParticle.blinkSpeed;
                particle.blinkPhase = newParticle.blinkPhase;
            });
        }
    }

    /**
     * 更新粒子位置和状态
     */
    updateParticle(timestamp: number): void {
        // 帧跳过逻辑，减少更新频率
        if (this.frameSkip > 0) {
            this.frameCount = (this.frameCount + 1) % (this.frameSkip + 1);
            if (this.frameCount !== 0 && this.lastTimestamp !== 0) {
                // 跳过此帧的更新，只更新时间戳
                this.lastTimestamp = timestamp;
                return;
            }
        }

        if (!this.lastTimestamp) this.lastTimestamp = timestamp;

        // 性能优化：限制deltaTime上限，防止大幅度更新
        let deltaTime = timestamp - this.lastTimestamp;
        if (deltaTime > 100) deltaTime = 100; // 防止大间隔造成粒子跳跃

        this.lastTimestamp = timestamp;

        const time = timestamp / 1000; // 当前时间（秒）
        const timeScale = deltaTime / 30; // 减慢动画，提高流畅度

        // 性能优化：批量处理粒子更新
        for (let i = 0; i < this.particles.length; i++) {
            const particle = this.particles[i];

            // 向上移动粒子，更加缓慢
            particle.y -= particle.speed * timeScale;

            // 非常轻微的水平移动，减少随机数计算
            if (i % 3 === 0) { // 每三个粒子才计算随机偏移
                particle.x += particle.moveX * timeScale;
            }

            // 边界检查
            if (particle.x < -2) particle.x = 102;
            if (particle.x > 102) particle.x = -2;

            // 更新闪烁效果，只对特殊星星执行
            if (particle.isSpecialStar && particle.blinkSpeed > 0) {
                // 使用正弦函数计算闪烁，使用预计算的phase减少计算量
                particle.opacity = 0.2 + (Math.sin(
                    time * particle.blinkSpeed + particle.blinkPhase
                ) + 1) * 0.4;
            }

            // 如果粒子移出视野，将其重置到底部，随机位置
            if (particle.y < -2) {
                // 性能优化：简化重置逻辑
                particle.y = 105 + Math.random() * 5;
                particle.x = Math.random() * 100;
                particle.moveX = (Math.random() - 0.5) * 0.02;

                // 10%概率成为特殊星星
                particle.isSpecialStar = Math.random() < 0.15;

                // 根据特殊星星状态调整属性
                if (particle.isSpecialStar) {
                    particle.blinkSpeed = 1 + Math.random() * 3;
                    particle.size = this.options.minSize + Math.random() * (this.options.maxSize - this.options.minSize) * 1.5;
                    particle.opacity = 0.5 + Math.random() * 0.4;
                } else {
                    particle.blinkSpeed = 0;
                    particle.size = this.options.minSize + Math.random() * (this.options.maxSize - this.options.minSize) * 0.8;
                    particle.opacity = 0.1 + Math.random() * 0.3;
                }

                // 更新颜色，使用缓存
                particle.color = this.colorCache[Math.floor(Math.random() * this.colorCache.length)];
                particle.blinkPhase = Math.random() * Math.PI * 2;
            }
        }
    }

    /**
     * 获取粒子样式
     */
    getParticleStyle(particle: Particle): Record<string, string> {
        // 性能优化：简化阴影计算
        const glow = particle.isSpecialStar
            ? `0 0 ${particle.size * 3}px ${particle.color}`
            : `0 0 ${particle.size * 2}px ${particle.color}`;

        return {
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size * (particle.isSpecialStar ? 3 : 2)}px`,
            height: `${particle.size * (particle.isSpecialStar ? 3 : 2)}px`,
            opacity: `${particle.opacity}`,
            backgroundColor: particle.color,
            boxShadow: glow,
            transform: particle.rotationSpeed > 0.1 ? `rotate(${particle.id * particle.rotationSpeed}deg)` : '',
            filter: `blur(${particle.size * (particle.isSpecialStar ? 0.5 : 0.7)}px)`,
        };
    }

    /**
     * 创建带透明度的RGBA颜色
     * @param baseColor 基础颜色
     * @param alpha 透明度值
     */
    createRGBAColor(baseColor: string, alpha: number): string {
        if (baseColor.startsWith('rgba')) {
            // 如果已经是RGBA格式，解析并修改透明度
            const matches = baseColor.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/);
            if (matches && matches.length === 5) {
                const [_, r, g, b] = matches;
                return `rgba(${r}, ${g}, ${b}, ${alpha})`;
            }
        } else if (baseColor.startsWith('rgb')) {
            // 处理RGB格式
            const matches = baseColor.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
            if (matches && matches.length === 4) {
                const [_, r, g, b] = matches;
                return `rgba(${r}, ${g}, ${b}, ${alpha})`;
            }
        }

        // 默认返回半透明白色
        return `rgba(255, 255, 255, ${alpha})`;
    }

    /**
     * 获取当前粒子数组
     */
    getParticles(): Particle[] {
        return this.particles;
    }

    /**
     * 启动动画
     */
    start(): void {
        if (this.isRunning) return;

        this.isRunning = true;
        this.lastTimestamp = 0;
        this.animationFrameId = requestAnimationFrame(this.updateParticles);
    }

    /**
     * 停止动画
     */
    stop(): void {
        if (!this.isRunning) return;

        this.isRunning = false;
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * 更新粒子位置和状态（递归动画函数）
     */
    private updateParticles = (timestamp: number): void => {
        this.updateParticle(timestamp);

        if (this.isRunning) {
            this.animationFrameId = requestAnimationFrame(this.updateParticles);
        }
    };

    /**
     * 清理资源
     */
    destroy(): void {
        this.stop();
        this.particles = [];
        this.container = null;
    }
}

export default ParticleEngine; 