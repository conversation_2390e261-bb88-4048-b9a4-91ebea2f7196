@echo off
REM 启动提示词增强智能体API服务

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 激活虚拟环境
call venv\Scripts\activate.bat
.\venv\Scripts\Activate.ps1
REM 设置环境变量
set MCP_URL=http://127.0.0.1:19220

REM 启动API服务
python run_server.py --host 0.0.0.0 --port 8100 --mcp-url %MCP_URL%

REM 如果需要，取消激活虚拟环境
REM call venv\Scripts\deactivate.bat 

REM 创建虚拟环境
python -m venv venv

REM 激活虚拟环境
call venv\Scripts\activate.bat
.\venv\Scripts\Activate.ps1
