<template>
  <div class="relative pt-1">
    <div class="flex mb-2 items-center justify-between">
      <div>
        <span class="text-xs font-semibold inline-block text-purple-200">
          {{ Math.round(progress) }}%
        </span>
      </div>
      <div class="text-right">
        <span class="text-xs font-semibold inline-block text-purple-200">
          {{ completedSteps }} / {{ totalSteps }} 步骤
        </span>
      </div>
    </div>
    <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-dark-700">
      <div :style="{ width: `${Math.min(Math.round(progress), 100)}%` }" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  progress: number;
  completedSteps: number;
  totalSteps: number;
}>();
</script> 