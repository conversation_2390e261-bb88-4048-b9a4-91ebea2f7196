<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>视频展示</title>
  <style>
    /* 全局样式 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: #fff;
    }
    
    /* 技术风背景 */
    .tech-bg {
      background-color: #0f172a;
      background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
        radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
        radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-attachment: fixed;
      min-height: 100vh;
      padding: 2rem 0;
    }
    
    /* 容器 */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }
    
    /* 玻璃效果卡片 */
    .glass-card {
      background: rgba(30, 41, 59, 0.4);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
      border-radius: 0.75rem;
    }
    
    /* 顶部导航 */
    .header {
      padding: 1.25rem 2.5rem;
      display: flex;
      align-items: center;
      margin-bottom: 2rem;
    }
    
    .back-button {
      background: none;
      border: none;
      color: #cbd5e1;
      cursor: pointer;
      margin-right: 1.25rem;
      transition: color 0.3s;
    }
    
    .back-button:hover {
      color: #fff;
    }
    
    .header-title {
      font-size: 1.875rem;
      font-weight: bold;
      color: #fff;
    }
    
    /* 视频展示区域 */
    .video-section {
      padding: 1.5rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      background: rgba(30, 41, 59, 0.5);
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 0.75rem;
      margin-bottom: 2.5rem;
    }
    
    .video-container {
      width: 100%;
      background: rgba(15, 23, 42, 0.6);
      border-radius: 0.5rem;
      padding: 1rem;
      flex-grow: 1;
      overflow-y: auto;
    }
    
    /* 响应式网格 */
    .video-grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 1.5rem;
    }
    
    @media (min-width: 768px) {
      .video-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (min-width: 1024px) {
      .video-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
    
    /* 视频项目 */
    .video-item {
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;
      border-radius: 0.5rem;
    }
    
    .video-preview {
      width: 100%;
      height: auto;
      border-radius: 0.5rem;
      aspect-ratio: 4 / 3;
      object-fit: cover;
      pointer-events: none;
    }
    
    .video-overlay {
      position: absolute;
      inset: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    .video-item:hover .video-overlay {
      opacity: 1;
    }
    
    .play-icon {
      width: 4rem;
      height: 4rem;
      color: white;
    }
    
    .video-info {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.5rem;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    }
    
    .video-name {
      color: #fff;
      font-weight: 500;
      font-size: 0.875rem;
    }
    
    /* 模态框 */
    .modal {
      position: fixed;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.95);
      z-index: 50;
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .modal.open {
      display: flex;
    }
    
    .close-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      border: none;
      border-radius: 50%;
      width: 3rem;
      height: 3rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transition: background 0.3s;
    }
    
    .close-button:hover {
      background: rgba(0, 0, 0, 0.75);
    }
    
    .player-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .modal-video {
      max-width: 90%;
      max-height: 90%;
      width: auto;
      height: auto;
    }
  </style>
</head>
<body>
  <div class="tech-bg">
    <div class="container">
      <!-- 顶部导航 -->
      <div class="glass-card header">
        <button class="back-button" onclick="window.history.back()">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="header-title">效果演示</h1>
      </div>
      
      <!-- 视频展示区域 -->
      <div class="video-section">
        <div class="video-container">
          <div class="video-grid" id="videoGrid">
            <!-- 视频项目将由JavaScript动态生成 -->
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 视频播放模态框 -->
  <div class="modal" id="videoModal">
    <button class="close-button" id="closeModal">
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    
    <div class="player-container">
      <video id="videoPlayer" controls autoplay class="modal-video"></video>
    </div>
  </div>
  
  <script>
    // 视频数据
    const videos = [
      {
        url: "车衣大模型.mp4",
        name: "车衣大模型"
      },
      {
        url: "魔法相机.mp4",
        name: "魔法相机"
      },
      {
        url: "数字人.mp4",
        name: "数字人"
      },
      {
        url: "文生主题.mp4",
        name: "文生主题"
      },
      {
        url: "用车助手.mp4",
        name: "用车助手"
      }
    ];
    
    // 获取DOM元素
    const videoGrid = document.getElementById("videoGrid");
    const videoModal = document.getElementById("videoModal");
    const videoPlayer = document.getElementById("videoPlayer");
    const closeModal = document.getElementById("closeModal");
    
    // 创建视频卡片
    function createVideoCards() {
      videos.forEach(video => {
        const videoItem = document.createElement("div");
        videoItem.className = "video-item";
        videoItem.innerHTML = `
          <video class="video-preview" preload="metadata">
            <source src="${video.url}" type="video/mp4">
          </video>
          <div class="video-overlay">
            <svg class="play-icon" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="video-info">
            <span class="video-name">${video.name}</span>
          </div>
        `;
        
        // 点击事件打开模态框并播放视频
        videoItem.addEventListener("click", () => {
          openVideoModal(video.url);
        });
        
        videoGrid.appendChild(videoItem);
      });
    }
    
    // 打开视频模态框
    function openVideoModal(videoUrl) {
      videoPlayer.src = videoUrl;
      videoModal.classList.add("open");
    }
    
    // 关闭视频模态框
    function closeVideoModal() {
      videoModal.classList.remove("open");
      videoPlayer.pause();
      videoPlayer.src = "";
    }
    
    // 关闭按钮点击事件
    closeModal.addEventListener("click", closeVideoModal);
    
    // ESC键关闭模态框
    document.addEventListener("keydown", event => {
      if (event.key === "Escape" && videoModal.classList.contains("open")) {
        closeVideoModal();
      }
    });
    
    // 初始化页面
    createVideoCards();
    
    // 为预览视频添加静音和自动播放（预览效果）
    document.querySelectorAll(".video-preview").forEach(video => {
      video.muted = true;
      
      // 鼠标悬停时自动播放
      video.parentElement.addEventListener("mouseenter", () => {
        video.play();
      });
      
      // 鼠标离开时暂停
      video.parentElement.addEventListener("mouseleave", () => {
        video.pause();
        video.currentTime = 0;
      });
    });
  </script>
</body>
</html>