from fastapi import APIRouter

from .content import router as content_router
from .ui_split import router as ui_split_router
from .ui_generation import router as ui_generation_router
from .text_to_image import router as text_to_image_router
from .task_api import router as task_api_router
from .util import router as util_router
from .wan import router as wan_router
from .enhance import router as enhance_router
from .icon_refine import router as icon_refine_router 
from .image_to_text import router as image_to_text_router
from .kolors import router as kolors_router
from .voice_to_voice import router as voice_to_voice_router
from .magic_camera import router as magic_camera_router
from .image_to_video import router as image_to_video_router
from .pipeline import router as pipeline_router
from .car_texture import router as car_texture_router
from . import doodle

api_router = APIRouter()

api_router.include_router(content_router, tags=["content"])
api_router.include_router(ui_split_router, tags=["ui split"])
api_router.include_router(ui_generation_router, tags=["ui generation"])
api_router.include_router(text_to_image_router, tags=["text to image"])
api_router.include_router(task_api_router, tags=["task api"])
api_router.include_router(util_router, tags=["util"])
api_router.include_router(wan_router, tags=["wan"])
api_router.include_router(enhance_router, tags=["enhance"])
api_router.include_router(icon_refine_router, tags=["icon refine"])
api_router.include_router(image_to_text_router, tags=["image to text"])
api_router.include_router(kolors_router, tags=["kolors"])
api_router.include_router(voice_to_voice_router, tags=["voice to voice"])
api_router.include_router(magic_camera_router, tags=["magic camera"])
api_router.include_router(image_to_video_router, tags=["image to video"])
api_router.include_router(pipeline_router, tags=["pipeline"])
api_router.include_router(car_texture_router, tags=["car texture"])
api_router.include_router(doodle.router, prefix="/v1", tags=["doodle"])
