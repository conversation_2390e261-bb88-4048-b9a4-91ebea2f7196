version: '3.3'

services:
  changan-theme:
    build:
      context: .
      dockerfile: Dockerfile
    image: changan_theme:latest
    container_name: changan-theme-backend
    privileged: true
    ports:
      - "18632:18632"
    volumes:
      - ./public:/app/public
      - ./workApi:/app/workApi
      - ./app/temp:/app/temp
      - ./agent:/app/agent
    environment:
      - COMFYUI_BASE_URL=${COMFYUI_BASE_URL}
      - COMFYUI_WS_URL=${COMFYUI_WS_URL}
      - PYTHON_THREADPOOL_SIZE=20
      - PYTHONPATH=/app
    restart: unless-stopped
    networks:
      - changan-network
    extra_hosts:
      - "comfyui:************"
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

networks:
  changan-network:
    driver: bridge
