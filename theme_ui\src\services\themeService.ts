import { API_CONFIG } from '@/config/api';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

export interface ThemeGenerationStatus {
    status: 'pending' | 'running' | 'completed' | 'failed' | 'terminated';
    progress: number;
    completedSteps: number;
    totalSteps: number;
    resources: Array<{
        type: string;
        url: string;
        thumbnail: string;
    }>;
    current_step?: string;
    steps?: Array<{
        name: string;
        status: string;
        progress: number;
    }>;
}

export interface PreviewResponse {
    previews: Array<{
        id?: number;
        file_name: string;
        url: string;
        thumbnail?: string;
    }>;
}

export class ThemeService {
    private static generateTaskId(): string {
        // 生成16位UUID + 时间戳
        const timestamp = dayjs().format('YYYYMMDDHHmmss');
        const uuid = uuidv4().replace(/-/g, '').substr(0, 16);
        return `${uuid}_${timestamp}`;
    }

    private static async request(endpoint: string, options: RequestInit = {}) {
        const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint}`;
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });

        if (!response.ok) {
            throw new Error(`API请求失败: ${response.statusText}`);
        }

        return response.json();
    }

    static async startGeneration(prompt: string, style: string): Promise<string> {
        const taskId = this.generateTaskId();

        await this.request(API_CONFIG.ENDPOINTS.PIPELINE, {
            method: 'POST',
            body: JSON.stringify({
                prompt,
                task_id: taskId,
                style: style
            }),
        });

        return taskId;
    }

    static async getGenerationStatus(taskId: string): Promise<ThemeGenerationStatus> {
        return await this.request(`${API_CONFIG.ENDPOINTS.PIPELINE_STATUS}/${taskId}`);
    }

    static async terminateGeneration(taskId: string): Promise<any> {
        return await this.request(`${API_CONFIG.ENDPOINTS.TERMINATE_TASK}/${taskId}`, {
            method: 'POST',
        });
    }

    // 获取预览数据
    static async getPreview(taskId: string, type: string): Promise<PreviewResponse> {
        const url = API_CONFIG.getPreviewUrl(taskId, type);
        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-cache', // 确保每次都获取最新数据
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });

        if (!response.ok) {
            throw new Error(`预览API请求失败: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    // 处理预览数据，确保URL格式正确
    static processPreviewData(taskId: string, type: string, previews: PreviewResponse): PreviewResponse {
        if (!previews.previews || previews.previews.length === 0) {
            return previews;
        }

        // 修正URL格式
        const processedPreviews = previews.previews.map(preview => {
            if (!preview.url.startsWith('http')) {
                const previewImageUrl = API_CONFIG.getPreviewImageUrl(taskId, type, preview.file_name);
                return {
                    ...preview,
                    url: previewImageUrl,
                    thumbnail: previewImageUrl
                };
            }
            return preview;
        });

        return { previews: processedPreviews };
    }
} 