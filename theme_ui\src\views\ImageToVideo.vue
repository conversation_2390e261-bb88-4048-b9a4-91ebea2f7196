<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">图生视频</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧上传区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6 flex flex-col">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">上传图片</h2>

            <form @submit.prevent="createVideo">
              <div class="mb-6">
                <label for="file" class="block text-sm font-medium text-purple-200 mb-2">
                  选择图片（5MB以内） <span class="text-red-400">*</span>
                </label>
                <input type="file" id="file" @change="handleFileChange" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" accept="image/*" required />
                <p class="text-xs text-gray-400 mt-2">
                  上传一张图片，AI将为您生成特效视频。
                </p>
                <div v-if="previewImage" class="mt-4">
                  <img :src="previewImage" class="max-w-full h-auto rounded-lg" alt="预览图片" />
                </div>
              </div>

              <div class="mb-6">
                <label class="block text-sm font-medium text-purple-200 mb-2">
                  选择特效
                </label>
                <select v-model="effect" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                  <option v-for="option in effectOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
              </div>

              <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isProcessing">
                  <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isProcessing ? '处理中...' : '生成视频' }}
                </button>
              </div>
            </form>
          </GlassPanel>
        </div>

        <!-- 右侧新视频展示区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6 h-full flex flex-col">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">生成视频</h2>

            <!-- 视频结果展示 -->
            <div class="w-full bg-dark-800/60 rounded-lg p-4 flex-grow overflow-y-auto" :class="{'h-full': !isProcessing}">
              <template v-if="processedVideoUrl">
                <video :src="processedVideoUrl" controls class="w-full h-auto rounded-lg"></video>
              </template>
              <template v-else-if="isProcessing">
                <p class="text-lg font-medium text-purple-200 mb-2">正在生成视频...</p>
                <div class="pulsing-text"></div>
              </template>
              <template v-else>
                <p class="text-lg font-medium text-purple-200 mb-2">请在左侧上传图片并选择特效，然后点击"生成视频"按钮</p>
              </template>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end mt-4" v-if="processedVideoUrl">
              <a :href="processedVideoUrl" download target="_blank" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium mr-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a2 2 0 002 2h14a2 2 0 002-2v-1m-6-14h2a2 2 0 012 2v14a2 2 0 01-2 2v-1m-6-14a2 2 0 002 2v14a2 2 0 00-2 2v-1m2-4h.01M16 7v.01M4 7v.01M4 13v.01M8 13v.01M16 13v1M8 8v.01M12 8v.01M16 8v1M8 17v.01M12 17v.01M16 17v1" />
                </svg>
                下载视频
              </a>
            </div>

          </GlassPanel>
        </div>
      </div>
      <div class="mb-10">
        <p class="text-gray-300 text-lg mb-4"></p>
        <GlassPanel class="p-6 h-full flex flex-col">
          <h2 class="text-2xl font-bold text-purple-200 mb-6">视频样式</h2>
          <div class="w-full bg-dark-800/60 rounded-lg p-4 flex-grow overflow-y-auto">
            <div class="grid grid-cols-3 gap-6">
              <div v-for="(video, index) in referenceVideos" :key="index" class="relative cursor-pointer transition-all duration-300 overflow-hidden" :style="{ height: '400px' }">
                <video :src="video.url" :alt="video.name" class="w-auto h-full rounded-lg" controls></video>
                <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent">
                  <span class="text-white font-medium text-sm">{{ video.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </GlassPanel>
      </div>



    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);

const file = ref<File | null>(null);
const isProcessing = ref<boolean>(false);
const processedVideoUrl = ref<string>("");
const previewImage = ref<string>("");
const effectOptions = ref([
  { label: "人物飞翔", value: "man_takes_off" },
  { label: "人物大摆锤", value: "da_bai_chui" }
]);
const effect = ref<string>(effectOptions.value[0].value);

// 参考样式数据
const referenceVideos = [
  {
    url: "/videos/man_takes_off.mp4", // 放在public/images目录下的本地图片
    name: "人物飞翔",
    style: "man_takes_off",
  },
  {
    url: "/videos/da_bai_chui.mp4", // 放在public/images目录下的本地图片
    name: "大摆锤",
    style: "da_bai_chui",
  },
  {
    url: "/videos/man_takes_off_2.mp4", // 放在public/images目录下的本地图片
    name: "人物飞翔",
    style: "man_takes_off",
  },
  {
    url: "/videos/da_bai_chui_2.mp4", // 放在public/images目录下的本地图片
    name: "大摆锤",
    style: "da_bai_chui",
  },
];

// 生成任务ID
const generateTaskId = (): string => {
  // 生成16位UUID + 时间戳
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};

// 处理文件变化
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    file.value = target.files[0];
    previewImage.value = URL.createObjectURL(file.value); // 创建图片的临时URL以便预览
  }
};

// 生成视频
const createVideo = async () => {
  if (!file.value) {
    alert("请选择图片文件");
    return;
  }

  try {
    isProcessing.value = true;
    processedVideoUrl.value = "";

    // 生成任务ID
    const taskId = generateTaskId();

    // 创建FormData对象
    const formData = new FormData();
    formData.append("file", file.value);
    formData.append("task_id", taskId);
    formData.append("effect_type", effect.value);

    // 调用API
    const response = await themeApi.imageToVideo(formData);

    // 设置生成的视频URL
    if (response.video_url) {
      // 添加服务器地址前缀
      processedVideoUrl.value = response.video_url;
    }
  } catch (error) {
    console.error("生成视频失败", error);
    alert("生成视频失败，请重试");
  } finally {
    isProcessing.value = false;
  }
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

.pulsing-text::after {
  content: '';
  display: block;
  width: 100%;
  height: 3px;
  background-color: #7c3aed;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* 玻璃预览卡片 */
.glass-preview-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.glass-preview-card:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass-preview-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      to right,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass-preview-card:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

.preview-img-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.wallpaper-card .preview-img-container {
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 */
}

.icon-card .preview-img-container {
  height: 0;
  padding-bottom: 100%; /* 1:1 比例 */
}

.preview-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.wallpaper-card .preview-img {
  object-fit: cover;
}

.icon-card .preview-img {
  object-fit: contain;
  padding: 0.5rem;
}

.glass-preview-card:hover .preview-img {
  transform: scale(1.05);
}

.preview-card-info {
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
}

.preview-filename {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 精美按钮样式 */
.fancy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.fancy-button:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
}

.fancy-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      to right,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
}

.fancy-button:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fancy-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* 参考样式卡片增强 */
.glass-preview-card.h-40 {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(25, 35, 55, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
}

.glass-preview-card.h-40:hover {
  transform: translateY(-8px) scale(1.02);
  background: rgba(30, 41, 59, 0.6);
  box-shadow: 0 15px 30px rgba(124, 58, 237, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-preview-card.h-40::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      120deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
  );
  transform: skewX(-25deg);
  z-index: 2;
  transition: all 0.6s ease;
}

.glass-preview-card.h-40:hover::before {
  left: 150%;
  transition: all 0.8s ease;
}

.glass-preview-card.h-40 .preview-img {
  transition: transform 0.8s ease;
}

.glass-preview-card.h-40:hover .preview-img {
  transform: scale(1.08);
}
</style>
