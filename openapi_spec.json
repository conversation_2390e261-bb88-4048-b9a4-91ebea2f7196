{"openapi": "3.1.0", "info": {"title": "ComfyUI API", "description": "ComfyUI Backend API for various image generation and processing tasks", "version": "1.0.0"}, "paths": {"/api/v1/text-to-image": {"post": {"tags": ["text-to-image"], "summary": "Generate Image", "description": "从文本生成图片的端点", "operationId": "generate_image_api_v1_text_to_image_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__api__text_to_image__TextToImageRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ui-generation": {"post": {"tags": ["ui-generation"], "summary": "Generate Ui", "operationId": "generate_ui_api_v1_ui_generation_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_ui_api_v1_ui_generation_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ui-path-generation": {"post": {"tags": ["ui-generation"], "summary": "Generate Ui Path", "description": "使用路径工作流生成UI", "operationId": "generate_ui_path_api_v1_ui_path_generation_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ui-split": {"post": {"tags": ["ui-split"], "summary": "Split Ui", "operationId": "split_ui_api_v1_ui_split_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_split_ui_api_v1_ui_split_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/ui-split-path": {"post": {"tags": ["ui-split"], "summary": "Split Ui Path", "description": "使用路径工作流切分UI", "operationId": "split_ui_path_api_v1_ui_split_path_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/icon-refine": {"post": {"tags": ["icon-refine"], "summary": "Refine Icon", "operationId": "refine_icon_api_v1_icon_refine_post", "parameters": [{"name": "style", "in": "query", "required": true, "schema": {"type": "string", "title": "Style"}}, {"name": "index", "in": "query", "required": true, "schema": {"type": "integer", "title": "Index"}}, {"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline": {"post": {"tags": ["pipeline"], "summary": "Start Pipeline", "operationId": "start_pipeline_api_v1_pipeline_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PipelineRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/pipeline/status/{task_id}": {"get": {"tags": ["pipeline"], "summary": "Get Pipeline Status", "description": "获取任务进度", "operationId": "get_pipeline_status_api_v1_pipeline_status__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/preview/{task_id}/{category}": {"get": {"tags": ["pipeline"], "summary": "Get Previews", "description": "获取指定任务的壁纸或图标预览图片列表\n\nArgs:\n    task_id: 任务ID\n    category: 预览类别，wallpaper或icon\n    \nReturns:\n    预览图片信息列表", "operationId": "get_previews_api_v1_preview__task_id___category__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "description": "任务ID", "title": "Task Id"}, "description": "任务ID"}, {"name": "category", "in": "path", "required": true, "schema": {"type": "string", "description": "预览类别，支持wallpaper或icon", "title": "Category"}, "description": "预览类别，支持wallpaper或icon"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/preview-image/{task_id}/{category}/{file_name}": {"get": {"tags": ["pipeline"], "summary": "Get Preview Image", "description": "获取指定任务的单张预览图片\n\nArgs:\n    task_id: 任务ID\n    category: 预览类别，wallpaper或icon\n    file_name: 图片文件名\n    \nReturns:\n    图片文件", "operationId": "get_preview_image_api_v1_preview_image__task_id___category___file_name__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "description": "任务ID", "title": "Task Id"}, "description": "任务ID"}, {"name": "category", "in": "path", "required": true, "schema": {"type": "string", "description": "预览类别，支持wallpaper或icon", "title": "Category"}, "description": "预览类别，支持wallpaper或icon"}, {"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "description": "文件名", "title": "File Name"}, "description": "文件名"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/preview-minio/{task_id}/{category}": {"get": {"tags": ["pipeline"], "summary": "Get Previews Minio", "description": "获取指定任务的壁纸或图标预览图片列表\n\nArgs:\n    task_id: 任务ID\n    category: 预览类别，wallpaper或icon\n\nReturns:\n    预览图片信息列表", "operationId": "get_previews_minio_api_v1_preview_minio__task_id___category__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "description": "任务ID", "title": "Task Id"}, "description": "任务ID"}, {"name": "category", "in": "path", "required": true, "schema": {"type": "string", "description": "预览类别，支持wallpaper或icon", "title": "Category"}, "description": "预览类别，支持wallpaper或icon"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/preview-image-minio/{task_id}/{category}/{file_name}": {"get": {"tags": ["pipeline"], "summary": "Get Preview Image Minio", "operationId": "get_preview_image_minio_api_v1_preview_image_minio__task_id___category___file_name__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "description": "任务ID", "title": "Task Id"}, "description": "任务ID"}, {"name": "category", "in": "path", "required": true, "schema": {"type": "string", "description": "预览类别，支持wallpaper或icon", "title": "Category"}, "description": "预览类别，支持wallpaper或icon"}, {"name": "file_name", "in": "path", "required": true, "schema": {"type": "string", "description": "文件名", "title": "File Name"}, "description": "文件名"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/convert-to-jpg/{task_id}": {"post": {"summary": "Convert Images Endpoint", "description": "API端点：将壁纸PNG转换为JPG", "operationId": "convert_images_endpoint_api_convert_to_jpg__task_id__post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/extract-colors/{task_id}": {"post": {"summary": "Extract Colors Endpoint", "description": "提取指定任务的内容图片颜色", "operationId": "extract_colors_endpoint_api_extract_colors__task_id__post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/create-archive/{task_id}": {"post": {"summary": "Create Archive Endpoint", "description": "创建发布包的API端点", "operationId": "create_archive_endpoint_api_create_archive__task_id__post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/download-archive/{task_id}": {"get": {"summary": "Download Archive", "description": "下载发布包的API端点", "operationId": "download_archive_api_download_archive__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rename-icons/{task_id}": {"post": {"summary": "Rename Icons Endpoint", "description": "重命名图标的API端点", "operationId": "rename_icons_endpoint_api_rename_icons__task_id__post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/health": {"get": {"summary": "Health Check", "description": "健康检查接口，验证服务和Redis状态", "operationId": "health_check_api_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/terminate-task/{task_id}": {"post": {"summary": "Terminate Task", "description": "终止指定任务ID的处理\n\n1. 清除ComfyUI队列\n2. 中断当前正在执行的ComfyUI任务\n3. 取消正在执行的异步任务\n4. 终止与任务相关的所有子进程\n5. 立即更新pipeline任务状态为已终止", "operationId": "terminate_task_api_terminate_task__task_id__post", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tasks-status": {"get": {"summary": "Get Tasks Status", "description": "获取ComfyUI队列状态和当前运行任务状态", "operationId": "get_tasks_status_api_tasks_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/content/generate-content": {"post": {"tags": ["content"], "summary": "Generate Content", "description": "生成内容页面的端点", "operationId": "generate_content_api_content_generate_content_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/wan/text-to-video": {"post": {"tags": ["wan-video"], "summary": "文本生成视频", "description": "根据文本描述生成一段视频", "operationId": "generate_video_api_v1_wan_text_to_video_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TextToVideoRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/kolors/text-to-image": {"post": {"tags": ["kolors-text-to-image"], "summary": "文本生成壁纸", "description": "根据文本描述生成壁纸", "operationId": "text_to_image_api_v1_kolors_text_to_image_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__api__kolors__TextToImageRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/image-to-text": {"post": {"tags": ["image-to-text"], "summary": "文件提取文本", "description": "根据上传的文件提取文本", "operationId": "image_to_text_api_v1_image_to_text_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_image_to_text_api_v1_image_to_text_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/voice-to-voice": {"post": {"tags": ["voice-to-voice"], "summary": "语音转换", "description": "根据上传的文本和音频文件进行语音转换", "operationId": "voice_to_voice_api_v1_voice_to_voice_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_voice_to_voice_api_v1_voice_to_voice_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/magic-camera": {"post": {"tags": ["magic-camera"], "summary": "图片转换为图片", "description": "根据上传的图片生成新的图片", "operationId": "image_to_image_api_v1_magic_camera_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_image_to_image_api_v1_magic_camera_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/image-to-video": {"post": {"tags": ["image-to-video"], "summary": "图片转换为视频", "description": "根据上传的图片和特效文本生成新的视频", "operationId": "image_to_video_api_v1_image_to_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_image_to_video_api_v1_image_to_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/task/list": {"get": {"summary": "Get Task List", "operationId": "get_task_list_api_v1_task_list_get", "parameters": [{"name": "page", "in": "query", "required": true, "schema": {"type": "integer", "description": "页码", "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": true, "schema": {"type": "integer", "description": "每页显示的数量", "title": "Size"}, "description": "每页显示的数量"}, {"name": "taskType", "in": "query", "required": false, "schema": {"type": "string", "description": "任务类型", "default": "theme", "title": "Tasktype"}, "description": "任务类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/enhance/prompt": {"post": {"tags": ["enhance"], "summary": "Enhance Prompt", "description": "启动提示词增强任务", "operationId": "enhance_prompt_api_enhance_prompt_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancePromptRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancePromptResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/enhance/status/{session_id}": {"get": {"tags": ["enhance"], "summary": "Get Enhance Status", "description": "获取提示词增强任务状态", "operationId": "get_enhance_status_api_enhance_status__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhanceStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/enhance/cancel/{session_id}": {"post": {"tags": ["enhance"], "summary": "Cancel Enhance Task", "description": "取消提示词增强任务", "operationId": "cancel_enhance_task_api_enhance_cancel__session_id__post", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/ws/enhance/session": {"post": {"tags": ["websocket"], "summary": "Create Session", "description": "创建新的会话", "operationId": "create_session_api_ws_enhance_session_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/ws/enhance/session/{session_id}": {"delete": {"tags": ["websocket"], "summary": "Delete Session", "description": "删除会话", "operationId": "delete_session_api_ws_enhance_session__session_id__delete", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["websocket"], "summary": "Get Session", "description": "获取会话状态", "operationId": "get_session_api_ws_enhance_session__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/car-texture": {"post": {"tags": ["car-texture", "car texture"], "summary": "Generate Car Texture", "description": "SU7车衣生成接口\n\n根据用户输入的提示词，生成车辆纹理效果图。\n\nArgs:\n    request (CarTextureRequest): 包含prompt和task_id的请求体\n    \nReturns:\n    dict: 包含prompt_id和完整的图片URL", "operationId": "generate_car_texture_api_v1_car_texture_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarTextureRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/doodle/start": {"post": {"tags": ["doodle", "doodle"], "summary": "Start Doodle Task", "description": "启动涂鸦生成任务\n\n1. 接收前端上传的图像文件和表单数据\n2. 增强提示词（英文SD提示词）\n3. 上传图像文件到ComfyUI\n4. 存储提示词到Redis\n5. 异步启动ComfyUI工作流\n6. 返回任务ID和提示词", "operationId": "start_doodle_task_api_v1_doodle_start_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_start_doodle_task_api_v1_doodle_start_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DoodleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/doodle/status/{task_id}": {"get": {"tags": ["doodle", "doodle"], "summary": "Get Doodle Status", "description": "查询涂鸦任务状态\n\n1. 从Redis获取ComfyUI的prompt_id\n2. 调用ComfyUI /history API查询状态\n3. 解析状态，如果完成则获取结果URL\n4. 返回状态信息", "operationId": "get_doodle_status_api_v1_doodle_status__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__api__doodle__StatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/doodle/prompt/{task_id}": {"put": {"tags": ["doodle", "doodle"], "summary": "Update Prompt And Regenerate", "description": "更新提示词并重新生成\n\n1. 接收新的提示词\n2. 增强提示词（英文SD提示词）\n3. 更新Redis中的提示词\n4. 重新触发ComfyUI工作流\n5. 返回确认消息", "operationId": "update_prompt_and_regenerate_api_v1_doodle_prompt__task_id__put", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePromptRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/v1/coloring-book/start": {"post": {"tags": ["coloring-book", "魔法画册"], "summary": "Start Generation Task", "description": "启动一个新的线稿图生成任务。\n此接口会加载工作流，使用用户的提示词修改它，\n然后在后台将其入队等待生成。", "operationId": "start_generation_task_api_v1_v1_coloring_book_start_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/v1/coloring-book/status/{task_id}": {"get": {"tags": ["coloring-book", "魔法画册"], "summary": "Get Task Status", "description": "获取生成任务的状态。\n启动任务后，轮询此接口以获取结果。", "operationId": "get_task_status_api_v1_v1_coloring_book_status__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__api__coloring_book__StatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_generate_ui_api_v1_ui_generation_post": {"properties": {"wallpaper_image": {"type": "string", "format": "binary", "title": "Wallpaper Image"}}, "type": "object", "required": ["wallpaper_image"], "title": "Body_generate_ui_api_v1_ui_generation_post"}, "Body_image_to_image_api_v1_magic_camera_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["file", "task_id"], "title": "Body_image_to_image_api_v1_magic_camera_post"}, "Body_image_to_text_api_v1_image_to_text_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["file", "task_id"], "title": "Body_image_to_text_api_v1_image_to_text_post"}, "Body_image_to_video_api_v1_image_to_video_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "effect_type": {"type": "string", "title": "Effect Type"}, "task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["file", "effect_type", "task_id"], "title": "Body_image_to_video_api_v1_image_to_video_post"}, "Body_split_ui_api_v1_ui_split_post": {"properties": {"ui_image": {"type": "string", "format": "binary", "title": "Ui Image"}}, "type": "object", "required": ["ui_image"], "title": "Body_split_ui_api_v1_ui_split_post"}, "Body_start_doodle_task_api_v1_doodle_start_post": {"properties": {"sketch_image": {"type": "string", "format": "binary", "title": "Sketch Image"}, "task_id": {"type": "string", "title": "Task Id"}, "prompt": {"type": "string", "title": "Prompt"}, "initial_context": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Initial Context"}, "num_samples": {"type": "integer", "title": "N<PERSON>", "default": 1}}, "type": "object", "required": ["sketch_image", "task_id", "prompt"], "title": "Body_start_doodle_task_api_v1_doodle_start_post"}, "Body_voice_to_voice_api_v1_voice_to_voice_post": {"properties": {"text": {"type": "string", "title": "Text"}, "task_id": {"type": "string", "title": "Task Id"}, "audio": {"type": "string", "format": "binary", "title": "Audio"}, "audio_name": {"type": "string", "title": "Audio Name"}}, "type": "object", "required": ["text", "task_id"], "title": "Body_voice_to_voice_api_v1_voice_to_voice_post"}, "CarTextureRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["prompt", "task_id"], "title": "CarTextureRequest"}, "ContentRequest": {"properties": {"task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["task_id"], "title": "ContentRequest"}, "DoodleResponse": {"properties": {"task_id": {"type": "string", "title": "Task Id"}, "enhanced_prompt": {"type": "string", "title": "Enhanced Prompt"}}, "type": "object", "required": ["task_id", "enhanced_prompt"], "title": "DoodleResponse"}, "EnhancePromptRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "system_prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "System Prompt"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id", "default": "anonymous"}}, "type": "object", "required": ["prompt"], "title": "EnhancePromptRequest"}, "EnhancePromptResponse": {"properties": {"session_id": {"type": "string", "title": "Session Id"}, "status": {"type": "string", "title": "Status"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["session_id", "status", "message"], "title": "EnhancePromptResponse"}, "EnhanceStatus": {"properties": {"session_id": {"type": "string", "title": "Session Id"}, "status": {"type": "string", "title": "Status"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Time"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "is_running": {"type": "boolean", "title": "Is Running", "default": false}, "is_complete": {"type": "boolean", "title": "Is Complete", "default": false}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["session_id", "status"], "title": "EnhanceStatus"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MessageResponse": {"properties": {"message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["message"], "title": "MessageResponse"}, "PipelineRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "task_id": {"type": "string", "title": "Task Id"}, "style": {"type": "string", "title": "Style"}}, "type": "object", "required": ["prompt", "task_id", "style"], "title": "PipelineRequest"}, "StartRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "用于生成线稿图的文本提示词。"}}, "type": "object", "required": ["prompt"], "title": "StartRequest"}, "StartResponse": {"properties": {"task_id": {"type": "string", "title": "Task Id", "description": "本次生成任务的唯一ID。"}}, "type": "object", "required": ["task_id"], "title": "StartResponse"}, "TextToVideoRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "negative_prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Negative Prompt", "description": "负向提示词，用于控制不希望出现的内容", "default": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"}, "width": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "视频宽度", "default": 832}, "height": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Height", "description": "视频高度", "default": 480}, "num_frames": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "视频帧数", "default": 81}, "seed": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Seed", "description": "随机种子，不提供则随机生成"}, "task_id": {"type": "string", "title": "Task Id", "description": "任务ID，用于文件保存和标识"}, "taskId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "视频文件名标识，如不提供则使用task_id"}}, "type": "object", "required": ["prompt", "task_id"], "title": "TextToVideoRequest", "description": "文本生成视频请求模型"}, "UpdatePromptRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}}, "type": "object", "required": ["prompt"], "title": "UpdatePromptRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "app__api__coloring_book__StatusResponse": {"properties": {"status": {"type": "string", "title": "Status", "description": "任务当前状态 (例如: 'queued', 'running', 'completed', 'failed')"}, "progress": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress", "description": "生成进度，从 0.0 到 100.0"}, "result_image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result Image Url", "description": "最终生成图片的URL地址"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "任务失败时的错误信息"}}, "type": "object", "required": ["status"], "title": "StatusResponse"}, "app__api__doodle__StatusResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "progress": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress"}, "result_image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result Image Url"}, "result_image_base64": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result Image Base64"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["status"], "title": "StatusResponse"}, "app__api__kolors__TextToImageRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "task_id": {"type": "string", "title": "Task Id", "description": "任务ID，用于文件保存和标识"}}, "type": "object", "required": ["prompt", "task_id"], "title": "TextToImageRequest"}, "app__api__text_to_image__TextToImageRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}, "task_id": {"type": "string", "title": "Task Id"}}, "type": "object", "required": ["prompt", "task_id"], "title": "TextToImageRequest"}}}}