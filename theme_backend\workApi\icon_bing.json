{"5": {"inputs": {"width": 1280, "height": 680, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "42": {"inputs": {"text": "of glass,crystal clear,(pure white background:1.2),boli,Blisters,ice,Transparent water droplets,no human,best quality,UHD,8K,Composed of transparent water drops,gestural movement,surrealist fantasy style,organic fluid,light painting,nature theme,studio lighting,perfect lighting,outdoors,nature,golden ratio composition,realistic masterpiece,award winning photography,8k.,\n", "token_normalization": "length+mean", "weight_interpretation": "A1111", "clip": ["80", 0]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "43": {"inputs": {"text": "(worst quality, low quality:1.4),lowres, normal quality,text,Watermark,logo,nsfw,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["80", 0]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "45": {"inputs": {"ckpt_name": "sd1.5/dreamshaper_8.safetensors", "prompt": "[none]", "example": "[none]"}, "class_type": "CheckpointLoader|pysssss", "_meta": {"title": "Checkpoint Loader 🐍"}}, "47": {"inputs": {"add_noise": "enable", "noise_seed": 429404273128286, "steps": 20, "cfg": 8, "sampler_name": "dpmpp_2m_sde", "scheduler": "karras", "start_at_step": 0, "end_at_step": 10000, "return_with_leftover_noise": "disable", "model": ["82", 0], "positive": ["171", 0], "negative": ["171", 1], "latent_image": ["5", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "K采样器（高级）"}}, "48": {"inputs": {"samples": ["47", 0], "vae": ["49", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "49": {"inputs": {"vae_name": "vae-ft-mse-840000-ema-pruned.ckpt"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "50": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["183", 0], "negative": ["183", 1], "control_net": ["51", 0], "image": ["215", 0]}, "class_type": "ACN_AdvancedControlNetApply", "_meta": {"title": "Apply Advanced ControlNet 🛂🅐🅒🅝"}}, "51": {"inputs": {"control_net_name": "control_v11p_sd15_lineart.pth"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "80": {"inputs": {"stop_at_clip_layer": -2, "clip": ["82", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "设置CLIP最后一层"}}, "81": {"inputs": {"switch_1": "On", "lora_name_1": "1.5/abel万物冻结（冰）_0.1.safetensors", "model_weight_1": 0.8, "clip_weight_1": 1, "switch_2": "On", "lora_name_2": "1.5/彩色玻璃_彩色玻璃V1.safetensors", "model_weight_2": 0.25, "clip_weight_2": 1, "switch_3": "Off", "lora_name_3": "1.5/水效果——把图形变成水_v1.0.safetensors", "model_weight_3": 1, "clip_weight_3": 1}, "class_type": "CR LoRA Stack", "_meta": {"title": "💊 CR LoRA Stack"}}, "82": {"inputs": {"model": ["45", 0], "clip": ["45", 1], "lora_stack": ["81", 0]}, "class_type": "CR Apply LoRA Stack", "_meta": {"title": "💊 CR Apply LoRA Stack"}}, "161": {"inputs": {"strength": 0.75, "start_percent": 0, "end_percent": 1, "positive": ["50", 0], "negative": ["50", 1], "control_net": ["165", 0], "image": ["164", 0]}, "class_type": "ACN_AdvancedControlNetApply", "_meta": {"title": "Apply Advanced ControlNet 🛂🅐🅒🅝"}}, "164": {"inputs": {"pyrUp_iters": 1, "resolution": 512, "image": ["211", 0]}, "class_type": "TilePreprocessor", "_meta": {"title": "Tile"}}, "165": {"inputs": {"control_net_name": "control_v11f1e_sd15_tile.pth"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "170": {"inputs": {"upscale_method": "nearest-exact", "width": 1024, "height": 616, "crop": "disabled", "image": ["215", 0]}, "class_type": "ImageScale", "_meta": {"title": "缩放图像"}}, "171": {"inputs": {"strength": 1.5, "start_percent": 0, "end_percent": 1, "positive": ["161", 0], "negative": ["161", 1], "control_net": ["172", 0], "image": ["173", 0]}, "class_type": "ACN_AdvancedControlNetApply", "_meta": {"title": "Apply Advanced ControlNet 🛂🅐🅒🅝"}}, "172": {"inputs": {"control_net_name": "t2i/1.5/models/coadapter-color-sd15v1.pth"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "173": {"inputs": {"resolution": 512, "image": ["211", 0]}, "class_type": "ColorPreprocessor", "_meta": {"title": "Color Pallete"}}, "177": {"inputs": {"coarse": "disable", "resolution": 512, "image": ["170", 0]}, "class_type": "LineArtPreprocessor", "_meta": {"title": "Realistic Lineart"}}, "183": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["42", 0], "negative": ["43", 0], "control_net": ["184", 0], "image": ["189", 0]}, "class_type": "ACN_AdvancedControlNetApply", "_meta": {"title": "Apply Advanced ControlNet 🛂🅐🅒🅝"}}, "184": {"inputs": {"control_net_name": "control_v11f1p_sd15_depth.pth"}, "class_type": "ControlNetLoader", "_meta": {"title": "加载ControlNet模型"}}, "189": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 512, "image": ["215", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "199": {"inputs": {"image": "icon_mask_huabu2.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "201": {"inputs": {"channel": "red", "image": ["199", 0]}, "class_type": "ImageToMask", "_meta": {"title": "图像转换为遮罩"}}, "210": {"inputs": {"filename_prefix": "ice/applist", "images": ["216", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "211": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": 2, "watcher": "disable", "result": "", "prompt": "beautiful scenery nature glass bottle landscape,under water"}, "class_type": "LoadImagesFromPath", "_meta": {"title": "Load Images From Path ♾️Mixlab"}}, "213": {"inputs": {"image": "icon_mask_huabu2.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "215": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": 2, "watcher": "disable", "result": "", "prompt": "beautiful scenery nature glass bottle landscape,under water"}, "class_type": "LoadImagesFromPath", "_meta": {"title": "Load Images From Path ♾️Mixlab"}}, "216": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["48", 0], "mask": ["213", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "Cut By Mask"}}}