from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import json
import traceback
from pathlib import Path
import httpx
import aiofiles
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    logger
)

router = APIRouter()
server_name="theme_text_to_image_server"

class TextToImageRequest(BaseModel):
    prompt: str
    task_id: str

def build_release_path(task_id: str) -> str:
    """构建发布路径（相对路径，用于9节点）"""
    return f"changan/mainLine_{task_id}/releases/wallpaper/wallpaper"

def build_text_to_image_workflow(prompt: str, task_id: str) -> dict:
    """构建文生图工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("wallpaper_1920.json")
        
        # 设置提示词
        if "35" not in workflow:
            logger.error("工作流中找不到节点35")
            raise HTTPException(status_code=500, detail="Node 35 not found in workflow")
            
        workflow["35"]["inputs"]["prompt"] = prompt
        logger.info(f"设置提示词: {prompt}")

        # 设置保存路径
        if "9" not in workflow:
            logger.error("工作流中找不到节点9")
            raise HTTPException(status_code=500, detail="Node 9 not found in workflow")
        
        release_path = build_release_path(task_id)
        workflow["9"]["inputs"]["filename_prefix"] = release_path
        logger.info(f"设置保存路径: {release_path}")
            
        return workflow, "9"  # 9是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/text-to-image")
async def generate_image(request: TextToImageRequest):
    """从文本生成图片的端点"""
    try:
        logger.info(f"\n========== 开始生成图片 ==========")
        logger.info(f"提示词: {request.prompt}")
        logger.info(f"任务ID: {request.task_id}")

        if not request.prompt or not request.task_id:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 构建工作流
        workflow, output_node_id = build_text_to_image_workflow(
            request.prompt,
            request.task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        subfolder = f"changan/mainLine_{request.task_id}/releases/wallpaper"
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{request.task_id}/releases/wallpaper"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_path = save_dir / "wallpaper.png"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                logger.info(f"壁纸已保存到: {image_path}")
            except Exception as e:
                logger.error(f"保存壁纸失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save wallpaper: {str(e)}")

        # 构建公开访问URL
        public_url = f"/ui_source/mainLine_{request.task_id}/releases/wallpaper/wallpaper.png"
        logger.info(f"公开访问URL: {public_url}")

        logger.info(f"========== 图片生成完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "image_url": public_url
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文生图请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e)) 