// 布局管理Store - 8x4网格系统和组件布局
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLayoutStore = defineStore('layout', () => {
  // === 状态 ===
  const gridColumns = ref(8) // 网格列数
  const gridRows = ref(4) // 网格行数
  const gridGap = ref(16) // 网格间距
  
  // 桌面组件布局配置
  const desktopLayout = ref([
    {
      id: 'dynamic-island',
      component: 'DynamicIsland',
      position: { column: 1, row: 1, colspan: 8, rowspan: 1 },
      zIndex: 20,
      visible: true
    },
    {
      id: 'weather-card',
      component: 'WeatherCard',
      position: { column: 1, row: 2, colspan: 2, rowspan: 2 },
      zIndex: 10,
      visible: true
    },
    {
      id: 'navigation-card',
      component: 'NavigationCard',
      position: { column: 3, row: 2, colspan: 4, rowspan: 2 },
      zIndex: 10,
      visible: true
    },
    {
      id: 'media-card',
      component: 'MediaCard',
      position: { column: 7, row: 2, colspan: 2, rowspan: 1 },
      zIndex: 10,
      visible: true
    },
    {
      id: 'quick-actions',
      component: 'QuickActions',
      position: { column: 7, row: 3, colspan: 2, rowspan: 1 },
      zIndex: 10,
      visible: true
    },
    {
      id: 'vpa-avatar',
      component: 'VpaAvatar',
      position: { column: 1, row: 4, colspan: 2, rowspan: 1 },
      zIndex: 100,
      visible: true,
      special: 'vpa' // 特殊标记VPA组件
    }
  ])

  // 当前活动的对话框
  const activeDialog = ref(null)
  
  // 屏幕尺寸
  const screenSize = ref({
    width: window.innerWidth,
    height: window.innerHeight
  })

  // === 计算属性 ===
  const visibleComponents = computed(() => {
    return desktopLayout.value.filter(item => item.visible)
  })

  const gridCellSize = computed(() => {
    const availableWidth = screenSize.value.width - (gridGap.value * (gridColumns.value + 1))
    const availableHeight = screenSize.value.height - 44 - (gridGap.value * (gridRows.value + 1)) // 减去动态岛高度
    
    return {
      width: availableWidth / gridColumns.value,
      height: availableHeight / gridRows.value
    }
  })

  const componentStyles = computed(() => {
    const styles = {}
    const cellSize = gridCellSize.value
    
    visibleComponents.value.forEach(component => {
      const pos = component.position
      
      styles[component.id] = {
        gridColumn: `${pos.column} / span ${pos.colspan}`,
        gridRow: `${pos.row} / span ${pos.rowspan}`,
        zIndex: component.zIndex,
        width: `${cellSize.width * pos.colspan + gridGap.value * (pos.colspan - 1)}px`,
        height: `${cellSize.height * pos.rowspan + gridGap.value * (pos.rowspan - 1)}px`
      }
    })
    
    return styles
  })

  // === 方法 ===
  const updateScreenSize = () => {
    screenSize.value = {
      width: window.innerWidth,
      height: window.innerHeight
    }
    
    // 响应式调整网格
    if (screenSize.value.width < 768) {
      gridColumns.value = 4
      gridRows.value = 6
    } else if (screenSize.value.width < 1024) {
      gridColumns.value = 6
      gridRows.value = 5
    } else {
      gridColumns.value = 8
      gridRows.value = 4
    }
  }

  const moveComponent = (componentId, newPosition) => {
    const component = desktopLayout.value.find(c => c.id === componentId)
    if (component) {
      component.position = { ...component.position, ...newPosition }
    }
  }

  const resizeComponent = (componentId, newSize) => {
    const component = desktopLayout.value.find(c => c.id === componentId)
    if (component) {
      component.position.colspan = newSize.colspan || component.position.colspan
      component.position.rowspan = newSize.rowspan || component.position.rowspan
    }
  }

  const toggleComponentVisibility = (componentId) => {
    const component = desktopLayout.value.find(c => c.id === componentId)
    if (component) {
      component.visible = !component.visible
    }
  }

  const showDialog = (dialogConfig) => {
    activeDialog.value = {
      id: `dialog-${Date.now()}`,
      ...dialogConfig,
      zIndex: 40
    }
  }

  const hideDialog = () => {
    activeDialog.value = null
  }

  const addComponent = (componentConfig) => {
    const newComponent = {
      id: `component-${Date.now()}`,
      visible: true,
      zIndex: 10,
      ...componentConfig
    }
    desktopLayout.value.push(newComponent)
  }

  const removeComponent = (componentId) => {
    const index = desktopLayout.value.findIndex(c => c.id === componentId)
    if (index > -1) {
      desktopLayout.value.splice(index, 1)
    }
  }

  const getComponentById = (componentId) => {
    return desktopLayout.value.find(c => c.id === componentId)
  }

  const getComponentsByType = (componentType) => {
    return desktopLayout.value.filter(c => c.component === componentType)
  }

  // 检查位置是否被占用
  const isPositionOccupied = (position, excludeId = null) => {
    return desktopLayout.value.some(component => {
      if (component.id === excludeId || !component.visible) return false
      
      const comp = component.position
      const pos = position
      
      // 检查是否有重叠
      return !(
        pos.column >= comp.column + comp.colspan ||
        pos.column + pos.colspan <= comp.column ||
        pos.row >= comp.row + comp.rowspan ||
        pos.row + pos.rowspan <= comp.row
      )
    })
  }

  // 查找可用位置
  const findAvailablePosition = (size) => {
    for (let row = 1; row <= gridRows.value - size.rowspan + 1; row++) {
      for (let col = 1; col <= gridColumns.value - size.colspan + 1; col++) {
        const position = {
          column: col,
          row: row,
          colspan: size.colspan,
          rowspan: size.rowspan
        }
        
        if (!isPositionOccupied(position)) {
          return position
        }
      }
    }
    return null
  }

  // 保存布局到本地存储
  const saveLayout = () => {
    try {
      localStorage.setItem('ai-hmi-desktop-layout', JSON.stringify(desktopLayout.value))
    } catch (error) {
      console.warn('Failed to save layout:', error)
    }
  }

  // 从本地存储加载布局
  const loadLayout = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-desktop-layout')
      if (saved) {
        desktopLayout.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('Failed to load layout:', error)
    }
  }

  // 重置为默认布局
  const resetLayout = () => {
    desktopLayout.value = [
      {
        id: 'dynamic-island',
        component: 'DynamicIsland',
        position: { column: 1, row: 1, colspan: 8, rowspan: 1 },
        zIndex: 20,
        visible: true
      },
      {
        id: 'weather-card',
        component: 'WeatherCard',
        position: { column: 1, row: 2, colspan: 2, rowspan: 2 },
        zIndex: 10,
        visible: true
      },
      {
        id: 'navigation-card',
        component: 'NavigationCard',
        position: { column: 3, row: 2, colspan: 4, rowspan: 2 },
        zIndex: 10,
        visible: true
      },
      {
        id: 'media-card',
        component: 'MediaCard',
        position: { column: 7, row: 2, colspan: 2, rowspan: 1 },
        zIndex: 10,
        visible: true
      },
      {
        id: 'quick-actions',
        component: 'QuickActions',
        position: { column: 7, row: 3, colspan: 2, rowspan: 1 },
        zIndex: 10,
        visible: true
      },
      {
        id: 'vpa-avatar',
        component: 'VpaAvatar',
        position: { column: 1, row: 4, colspan: 2, rowspan: 1 },
        zIndex: 100,
        visible: true,
        special: 'vpa'
      }
    ]
  }

  // 初始化布局
  const initializeLayout = () => {
    updateScreenSize()
    loadLayout()
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateScreenSize)
  }

  return {
    // 状态
    gridColumns,
    gridRows,
    gridGap,
    desktopLayout,
    activeDialog,
    screenSize,
    
    // 计算属性
    visibleComponents,
    gridCellSize,
    componentStyles,
    
    // 方法
    updateScreenSize,
    moveComponent,
    resizeComponent,
    toggleComponentVisibility,
    showDialog,
    hideDialog,
    addComponent,
    removeComponent,
    getComponentById,
    getComponentsByType,
    isPositionOccupied,
    findAvailablePosition,
    saveLayout,
    loadLayout,
    resetLayout,
    initializeLayout
  }
})
