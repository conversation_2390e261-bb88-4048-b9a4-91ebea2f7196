<template>
  <div class="fullscreen-particle-background" :style="{ zIndex: zIndex }">
    <canvas ref="canvasRef" class="star-canvas"></canvas>
  </div>
</template>

<script lang="ts">
// 提供一个常规脚本导出以便其他组件导入
import { defineComponent } from "vue";

export default defineComponent({
  name: "StarCanvasBackground",
});
</script>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import ParticleEngine, { Particle } from "../utils/ParticleEngine";

// 定义组件属性
const props = defineProps({
  particleDensity: {
    type: Number,
    default: 80, // 降低默认密度提高性能
  },
  primaryColor: {
    type: String,
    default: "rgba(99, 102, 241, 0.5)",
  },
  secondaryColor: {
    type: String,
    default: "rgba(59, 130, 246, 0.5)",
  },
  particleColor: {
    type: String,
    default: "rgba(255, 255, 255, 0.6)",
  },
  minSize: {
    type: Number,
    default: 0.1, // 降低最小尺寸
  },
  maxSize: {
    type: Number,
    default: 0.7, // 降低最大尺寸
  },
  speed: {
    type: Number,
    default: 0.2, // 降低速度
  },
  zIndex: {
    type: Number,
    default: 0,
  },
  autoSuspend: {
    type: Boolean,
    default: true, // 是否在页面不可见时自动暂停动画
  },
});

// Canvas相关
const canvasRef = ref<HTMLCanvasElement | null>(null);
let ctx: CanvasRenderingContext2D | null = null;
let animationFrameId: number | null = null;
let lastTimestamp = 0;
let particles: Particle[] = [];
let particleEngine: ParticleEngine | null = null;
let canvasWidth = 0;
let canvasHeight = 0;
let isRunning = true;
let lastResizeTime = 0; // 用于防抖优化

// 初始化Canvas
const initCanvas = () => {
  if (!canvasRef.value) return;

  const canvas = canvasRef.value;
  ctx = canvas.getContext("2d", { alpha: true });

  if (!ctx) return;

  // 设置Canvas尺寸 - 使用window的尺寸
  const updateCanvasSize = () => {
    canvasWidth = window.innerWidth;
    canvasHeight = window.innerHeight;
    canvas.width = canvasWidth * (window.devicePixelRatio || 1);
    canvas.height = canvasHeight * (window.devicePixelRatio || 1);
    if (ctx) {
      ctx.scale(window.devicePixelRatio || 1, window.devicePixelRatio || 1);
    }
  };

  updateCanvasSize();

  // 初始化粒子引擎
  const containerDiv = document.createElement("div");
  containerDiv.style.width = `${canvasWidth}px`;
  containerDiv.style.height = `${canvasHeight}px`;

  particleEngine = new ParticleEngine(containerDiv, {
    width: canvasWidth,
    height: canvasHeight,
    primaryColor: props.primaryColor,
    secondaryColor: props.secondaryColor,
    particleColor: props.particleColor,
    particleDensity: props.particleDensity,
    minSize: props.minSize,
    maxSize: props.maxSize,
    speed: props.speed,
  });

  particleEngine.initParticles();
  particles = particleEngine.getParticles();

  // 启动渲染循环
  isRunning = true;
  startRenderLoop();
};

// 渲染帧
const renderFrame = (timestamp: number) => {
  if (!ctx || !particleEngine || !isRunning) return;

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 更新粒子状态 - 这里使用性能优化的时间控制
  if (!lastTimestamp) lastTimestamp = timestamp;
  const _deltaTime = timestamp - lastTimestamp;
  lastTimestamp = timestamp;

  // 让粒子引擎处理更新逻辑
  particleEngine.updateParticle(timestamp);
  particles = particleEngine.getParticles();

  // 绘制粒子 - 性能优化：合并相似颜色的绘制
  const colorGroups: Record<string, Particle[]> = {};

  particles.forEach((particle) => {
    if (!colorGroups[particle.color]) {
      colorGroups[particle.color] = [];
    }
    colorGroups[particle.color].push(particle);
  });

  // 按颜色分组绘制
  Object.entries(colorGroups).forEach(([color, particleGroup]) => {
    if (!ctx) return;

    // 为每组粒子设置统一样式
    ctx.fillStyle = color;

    // 批量绘制同颜色粒子
    particleGroup.forEach((particle) => {
      // 计算实际坐标
      const x = (particle.x / 100) * canvasWidth;
      const y = (particle.y / 100) * canvasHeight;

      if (!ctx) return;

      // 粒子不透明度
      ctx.globalAlpha = particle.opacity;

      // 绘制圆形粒子
      const radius = (particle.size * (particle.isSpecialStar ? 3 : 2)) / 2;

      // 性能优化: 对于小粒子使用矩形替代圆形
      if (radius < 1.5) {
        ctx.fillRect(x - radius, y - radius, radius * 2, radius * 2);
      } else {
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
      }

      // 只为特殊星星添加光晕效果
      if (particle.isSpecialStar && particle.size > 0.4 && ctx) {
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius * 3);
        gradient.addColorStop(0, color);

        // 修复颜色处理方式，创建正确的透明度颜色
        let transparentColor;
        if (color.startsWith("rgba")) {
          // 如果已经是RGBA格式，解析并修改透明度
          const matches = color.match(
            /rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/
          );
          if (matches && matches.length === 5) {
            const [_, r, g, b] = matches;
            transparentColor = `rgba(${r}, ${g}, ${b}, 0.2)`;
          } else {
            // 如果无法解析，使用默认透明度
            transparentColor = "rgba(255, 255, 255, 0.2)";
          }
        } else if (color.startsWith("rgb")) {
          // 处理RGB格式
          const matches = color.match(
            /rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/
          );
          if (matches && matches.length === 4) {
            const [_, r, g, b] = matches;
            transparentColor = `rgba(${r}, ${g}, ${b}, 0.2)`;
          } else {
            transparentColor = "rgba(255, 255, 255, 0.2)";
          }
        } else {
          // 其他颜色格式使用半透明白色
          transparentColor = "rgba(255, 255, 255, 0.2)";
        }

        gradient.addColorStop(0.7, transparentColor);
        gradient.addColorStop(1, "transparent");

        ctx.globalAlpha = particle.opacity * 0.5;
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius * 3, 0, Math.PI * 2);
        ctx.fill();
      }
    });
  });

  // 继续渲染循环
  if (isRunning) {
    animationFrameId = requestAnimationFrame(renderFrame);
  }
};

// 启动渲染循环
const startRenderLoop = () => {
  if (animationFrameId) cancelAnimationFrame(animationFrameId);
  isRunning = true;
  animationFrameId = requestAnimationFrame(renderFrame);
};

// 停止渲染循环
const stopRenderLoop = () => {
  isRunning = false;
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

// 处理窗口大小变化 - 使用防抖优化
const handleResize = () => {
  // 防抖: 300ms内不重复触发
  const now = Date.now();
  if (now - lastResizeTime < 300) {
    return;
  }
  lastResizeTime = now;

  if (!canvasRef.value || !ctx || !particleEngine) return;

  // 更新尺寸
  canvasWidth = window.innerWidth;
  canvasHeight = window.innerHeight;
  canvasRef.value.width = canvasWidth * (window.devicePixelRatio || 1);
  canvasRef.value.height = canvasHeight * (window.devicePixelRatio || 1);
  ctx.scale(window.devicePixelRatio || 1, window.devicePixelRatio || 1);

  // 更新粒子引擎设置
  particleEngine.updateOptions({
    width: canvasWidth,
    height: canvasHeight,
  });

  // 重新初始化粒子
  particleEngine.initParticles();
  particles = particleEngine.getParticles();
};

// 处理页面可见性变化 - 性能优化: 页面不可见时暂停动画
const handleVisibilityChange = () => {
  if (!props.autoSuspend) return;

  if (document.hidden) {
    stopRenderLoop();
  } else {
    startRenderLoop();
  }
};

// 处理滚动事件 - 优化: 滚动时减少更新频率
const handleScroll = () => {
  if (!props.autoSuspend) return;

  // 滚动时暂时降低更新频率
  if (isRunning) {
    stopRenderLoop();
    setTimeout(() => {
      if (!document.hidden) {
        startRenderLoop();
      }
    }, 200); // 滚动后200ms再恢复
  }
};

// 监听属性变化
watch(
  [
    () => props.primaryColor,
    () => props.secondaryColor,
    () => props.particleColor,
    () => props.particleDensity,
    () => props.minSize,
    () => props.maxSize,
    () => props.speed,
  ],
  () => {
    if (!particleEngine) return;

    particleEngine.updateOptions({
      primaryColor: props.primaryColor,
      secondaryColor: props.secondaryColor,
      particleColor: props.particleColor,
      particleDensity: props.particleDensity,
      minSize: props.minSize,
      maxSize: props.maxSize,
      speed: props.speed,
    });

    particleEngine.initParticles();
    particles = particleEngine.getParticles();
  }
);

// 生命周期钩子
onMounted(() => {
  initCanvas();

  // 事件监听 - 使用被动模式优化性能
  window.addEventListener("resize", handleResize, { passive: true });
  document.addEventListener("visibilitychange", handleVisibilityChange);
  window.addEventListener("scroll", handleScroll, { passive: true });

  // 低优先级事件使用IntersectionObserver更节能
  if ("IntersectionObserver" in window) {
    const observer = new IntersectionObserver(
      (entries) => {
        // 当组件进入/离开视口时调整动画状态
        const isVisible = entries[0].isIntersecting;
        if (props.autoSuspend) {
          if (isVisible && !isRunning) {
            startRenderLoop();
          } else if (!isVisible && isRunning) {
            stopRenderLoop();
          }
        }
      },
      { threshold: 0.1 }
    );

    if (canvasRef.value) {
      observer.observe(canvasRef.value);
    }
  }
});

onUnmounted(() => {
  // 清理所有资源
  stopRenderLoop();
  window.removeEventListener("resize", handleResize);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
  window.removeEventListener("scroll", handleScroll);

  if (particleEngine) {
    particleEngine.destroy();
    particleEngine = null;
  }
});

// 暴露公共方法
defineExpose({
  reinitialize: () => {
    if (particleEngine) {
      particleEngine.initParticles();
      particles = particleEngine.getParticles();
    }
  },
  pause: () => {
    stopRenderLoop();
  },
  resume: () => {
    if (!isRunning) {
      startRenderLoop();
    }
  },
});
</script>

<style scoped>
.fullscreen-particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  pointer-events: none;
  overflow: hidden;
}

.star-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
}
</style> 