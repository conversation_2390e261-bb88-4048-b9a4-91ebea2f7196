<template>
  <div class="theme-preview-container">
    <StarCanvasBackground />

    <div class="page-content">
      <!-- 美化顶部标题和导航 -->
      <div class="glass-card nav-bar mb-8">
        <button class="back-button mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="title-text">主题预览</h1>
        <p class="task-info" v-if="taskId">任务ID: {{ taskId }}</p>
      </div>

      <div class="preview-tabs">
        <button class="tab-button" :class="{ active: activeTab === 'wallpaper' }" @click="activeTab = 'wallpaper'">
          壁纸预览
        </button>
        <button class="tab-button" :class="{ active: activeTab === 'icon' }" @click="activeTab = 'icon'">
          图标预览
        </button>
        <button class="tab-button fancy-tab" @click="goToDashboard">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm5 9a1 1 0 10-2 0v3a1 1 0 102 0v-3zm3-3a1 1 0 10-2 0v6a1 1 0 102 0V10z" />
          </svg>
          车机仪表盘预览
        </button>
      </div>

      <div class="preview-section" v-if="activeTab === 'wallpaper'">
        <div v-if="loadingWallpapers" class="loading-container">
          <div class="loading-spinner"></div>
          <p>加载壁纸预览中...</p>
        </div>

        <div v-else-if="wallpaperPreviews.length === 0" class="empty-state">
          <p>暂无壁纸预览，请先生成或检查任务ID是否正确。</p>
        </div>

        <div v-else class="previews-grid wallpaper-grid">
          <!-- 添加玻璃反光效果的卡片 -->
          <div v-for="wallpaper in showAllWallpapers ? wallpaperPreviews : wallpaperPreviews.slice(0, 6)" :key="wallpaper.id" class="glass-preview-card wallpaper-card" @click="openImageViewer(wallpaperImageUrls, wallpaperPreviews.indexOf(wallpaper))">
            <div class="preview-img-container">
              <img :src="wallpaper.url" :alt="wallpaper.file_name" class="preview-img" />
            </div>
            <div class="preview-card-info">
              <span class="preview-filename">{{ wallpaper.file_name }}</span>
<!--              <span class="preview-dimensions">{{ wallpaper.width }} x {{ wallpaper.height }}</span>-->
            </div>
          </div>
        </div>

        <!-- 添加展开/折叠按钮 -->
        <div v-if="wallpaperPreviews.length > 6" class="text-center mt-6">
          <button @click="showAllWallpapers = !showAllWallpapers" class="expand-collapse-btn">
            <span v-if="!showAllWallpapers">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              展开全部 {{ wallpaperPreviews.length }} 张壁纸
            </span>
            <span v-else>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              折叠壁纸
            </span>
          </button>
        </div>
      </div>

      <div class="preview-section" v-if="activeTab === 'icon'">
        <div v-if="loadingIcons" class="loading-container">
          <div class="loading-spinner"></div>
          <p>加载图标预览中...</p>
        </div>

        <div v-else-if="iconPreviews.length === 0" class="empty-state">
          <p>暂无图标预览，请先生成或检查任务ID是否正确。</p>
        </div>

        <div v-else class="previews-grid icon-grid">
          <!-- 添加玻璃反光效果的卡片 -->
          <div v-for="icon in showAllIcons ? iconPreviews : iconPreviews.slice(0, 12)" :key="icon.id" class="glass-preview-card icon-card" @click="openImageViewer(iconImageUrls, iconPreviews.indexOf(icon))">
            <div class="preview-img-container">
              <img :src="icon.url" :alt="icon.file_name" class="preview-img" @error="handleIconError($event, icon)" loading="lazy" />
            </div>
            <div class="preview-card-info">
              <span class="preview-filename">{{ icon.file_name }}</span>
            </div>
          </div>
        </div>

        <!-- 添加展开/折叠按钮 -->
        <div v-if="iconPreviews.length > 12" class="text-center mt-6">
          <button @click="showAllIcons = !showAllIcons" class="expand-collapse-btn">
            <span v-if="!showAllIcons">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              展开全部 {{ iconPreviews.length }} 个图标
            </span>
            <span v-else>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              折叠图标
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- 图片查看器 -->
    <ImageViewer v-model="showImageViewer" :images="viewerImages" :initialIndex="currentImageIndex" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "ThemePreview",
});
</script>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { themeApi, PreviewImage } from "../api/themeApi";
import StarCanvasBackground from "../components/StarCanvasBackground.vue";
import ImagePreviewCard from "../components/ImagePreviewCard.vue";
import ImageViewer from "../components/ImageViewer.vue";

// 获取路由参数
const route = useRoute();
const router = useRouter();
const taskId = computed(() => route.params.taskId as string);

// 检测是否为测试模式
const isTestMode = computed(() => route.query.test_mode === "true");

// 选项卡状态
const activeTab = ref("wallpaper");

// 预览数据
const wallpaperPreviews = ref<PreviewImage[]>([]);
const iconPreviews = ref<PreviewImage[]>([]);
const loadingWallpapers = ref(false);
const loadingIcons = ref(false);

// 图片查看器状态
const showImageViewer = ref(false);
const viewerImages = ref<string[]>([]);
const currentImageIndex = ref(0);

// 计算图片URL列表
const wallpaperImageUrls = computed(() =>
  wallpaperPreviews.value.map((preview) => preview.url)
);
const iconImageUrls = computed(() =>
  iconPreviews.value.map((preview) => preview.url)
);

// 打开图片查看器
const openImageViewer = (images: string[], index: number) => {
  viewerImages.value = images;
  currentImageIndex.value = index;
  showImageViewer.value = true;
};

// 加载壁纸预览
const loadWallpaperPreviews = async () => {
  if (!taskId.value) return;

  loadingWallpapers.value = true;
  try {
    console.log("开始请求壁纸预览, taskId:", taskId.value);

    if (isTestMode.value) {
      // 测试模式：使用本地文件路径
      console.log("测试模式：使用本地测试文件");
      const basePath = `/test_theme/${taskId.value}/releases/wallpaper`;

      // 模拟API返回的壁纸数据
      wallpaperPreviews.value = [
        {
          id: 1,
          file_name: "wallpaper.jpg",
          url: `${basePath}/wallpaper.jpg`,
          width: 1920,
          height: 1080,
          type: "wallpaper" as "wallpaper",
        },
      ];

      console.log("测试模式壁纸预览加载成功:", wallpaperPreviews.value);
      return;
    }

    try {
      // 原有的正常API请求逻辑
      const response = await themeApi.getPreviewImagesMinio(
        taskId.value,
        "wallpaper"
      );
      console.log("API响应:", response);

      if (response.previews && response.previews.length > 0) {
        // 修复URL路径
        wallpaperPreviews.value = response.previews.map((preview) => {
          // 检查URL是否已经包含完整的http(s)://前缀
          if (!preview.url.startsWith("http")) {
            // 使用后端服务器地址
            const baseUrl = "http://localhost:8000"; // 后端服务器地址
            const previewImageUrl = `${baseUrl}/api/v1/preview-image/${
              taskId.value
            }/${preview.type || "wallpaper"}/${preview.file_name}`;

            console.log("修正后的壁纸URL路径 (后端直接地址):", previewImageUrl);

            return {
              ...preview,
              url: previewImageUrl,
            };
          }
          return preview;
        });

        console.log("壁纸预览加载成功(修正URL后):", wallpaperPreviews.value);
        return;
      } else {
        console.warn("API返回空结果，尝试直接访问文件");
      }
    } catch (apiError) {
      console.error("API请求失败:", apiError);
    }

    // 如果API请求失败，尝试直接访问文件
    console.log("尝试直接访问壁纸文件");
    const timestamp = taskId.value.split("_")[1] || "";
    const baseId = taskId.value.split("_")[0] || taskId.value;

    // 使用后端服务器地址
    const baseUrl = "http://localhost:8000"; // 后端服务器地址
    const directUrl = `${baseUrl}/api/v1/preview-image/${taskId.value}/wallpaper/wallpaper.png`;

    console.log("直接访问URL (后端直接地址):", directUrl);

    // 检查图片是否可以加载
    const img = new Image();
    let imageLoaded = false;

    await new Promise((resolve) => {
      img.onload = () => {
        console.log("直接访问文件成功");
        wallpaperPreviews.value = [
          {
            id: 1,
            file_name: "wallpaper.png",
            url: directUrl,
            width: Number(img.width) || 1920,
            height: Number(img.height) || 1080,
            type: "wallpaper" as "wallpaper",
          },
        ];
        imageLoaded = true;
        resolve(true);
      };
      img.onerror = () => {
        console.error("直接访问文件失败");
        resolve(false);
      };

      // 设置超时
      setTimeout(() => {
        if (!imageLoaded) {
          console.warn("图片加载超时");
          resolve(false);
        }
      }, 5000);

      img.src = directUrl;
    });

    if (!imageLoaded && wallpaperPreviews.value.length === 0) {
      // 如果还是失败，尝试其他格式
      const baseUrl = "http://localhost:8000"; // 后端服务器地址
      const alternativeFormats = [
        `${baseUrl}/api/v1/preview-image/${taskId.value}/wallpaper/wallpaper.jpg`,
        `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/releases/wallpaper/wallpaper.png`,
        `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/releases/wallpaper/wallpaper.jpg`,
        `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/wallpaper.png`,
        `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/wallpaper.jpg`,
      ];

      for (const format of alternativeFormats) {
        console.log("尝试替代路径:", format);
        const altResult = await checkImagePath(format);
        if (altResult) {
          wallpaperPreviews.value = [
            {
              id: 1,
              file_name: format.split("/").pop() || "wallpaper.png",
              url: format,
              width: 1920,
              height: 1080,
              type: "wallpaper" as "wallpaper",
            },
          ];
          break;
        }
      }
    }
  } catch (error) {
    console.error("加载壁纸预览失败:", error);
  } finally {
    loadingWallpapers.value = false;
  }
};

// 检查图片路径是否有效
const checkImagePath = (path: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);

    // 设置超时
    setTimeout(() => resolve(false), 3000);

    img.src = path;
  });
};

// 加载图标预览
const loadIconPreviews = async () => {
  if (!taskId.value) return;

  loadingIcons.value = true;
  try {
    console.log("开始请求图标预览, taskId:", taskId.value);

    if (isTestMode.value) {
      // 测试模式：使用本地文件路径
      console.log("测试模式：使用本地测试文件");
      const basePath = `/test_theme/${taskId.value}/releases/icon`;

      // 获取文件夹中的所有图标文件
      // 这里我们将使用实际文件夹中的所有图标文件
      const iconFiles = [
        // 原有图标
        "com_tinnove_mediacenter.png",
        "com_wt_scene.png",
        "com_tinnove_apa.png",
        "com_wt_gamecenter.png",
        "com_incall_apps_personalcenter.png",
        "com_tinnove_aispace.png",
        "com_wt_vehiclecenter.png",
        "com_autopai_car_dialer.png",
        "com_tinnove_wecarnavi.png",
        "com_wtcl_filemanager.png",
        "com_wt_phonelink.png",
        // 添加文件夹中的其他图标
        "com_incall_dvr.png",
        "com_tinnove_chrome.png",
        "com_incall_app_drivershealth.png",
        "com_tinnove_carshow.png",
        "com_tinnove_link_client.png",
        "com_wt_maintenance.png",
        "com_autopai_smart_sound_effect.png",
        "com_tinnove_scenemode.png",
        "com_wt_airconditioner.png",
        "com_tinnove_cloudcamera.png",
        "com_changan_appmarket.png",
        "com_wt_carcamera.png",
        "com_tinnove_customer.png",
        "com_tinnove_gamezone.png",
        "com_wt_funbox.png",
        "com_autopai_album.png",
        "com_wtcl_electronicdirections.png",
        // 添加icon_数字.png格式的图标
        "icon_4.png",
        "icon_5.png",
        "icon_16.png",
        "icon_17.png",
        "icon_20.png",
        "icon_23.png",
        "icon_29.png",
      ];

      // 模拟API返回的图标数据
      const iconPromises = iconFiles.map(async (fileName, index) => {
        // 检查图片是否存在
        const exists = await checkImagePath(`${basePath}/${fileName}`);
        if (exists) {
          return {
            id: index + 1,
            file_name: fileName,
            url: `${basePath}/${fileName}`,
            width: 512,
            height: 512,
            type: "icon" as "icon",
          };
        }
        return null;
      });

      // 等待所有图片检查完成
      const iconResults = await Promise.all(iconPromises);

      // 过滤掉不存在的图标
      iconPreviews.value = iconResults.filter(
        (icon) => icon !== null
      ) as PreviewImage[];

      // 打印加载的图标数量
      console.log(
        "测试模式图标预览加载成功:",
        iconPreviews.value.length,
        "个图标"
      );

      // 打印所有图标的URL，方便调试
      iconPreviews.value.forEach((icon, index) => {
        console.log(`图标 ${index + 1}: ${icon.file_name} - ${icon.url}`);
      });

      return;
    }

    try {
      // 原有的正常API请求逻辑
      const response = await themeApi.getPreviewImagesMinio(taskId.value, "icon");
      console.log("图标API响应:", response);

      if (response.previews && response.previews.length > 0) {
        // 修复URL路径
        iconPreviews.value = response.previews.map((preview) => {
          // 检查URL是否已经包含完整的http(s)://前缀
          if (!preview.url.startsWith("http")) {
            // 使用后端服务器地址
            const baseUrl = "http://localhost:8000"; // 后端服务器地址
            const previewImageUrl = `${baseUrl}/api/v1/preview-image/${
              taskId.value
            }/${preview.type || "icon"}/${preview.file_name}`;

            console.log("修正后的图标URL路径 (后端直接地址):", previewImageUrl);

            return {
              ...preview,
              url: previewImageUrl,
            };
          }
          return preview;
        });

        console.log("图标预览加载成功(修正URL后):", iconPreviews.value);
        return;
      } else {
        console.warn("图标API返回空结果，尝试直接访问文件");
      }
    } catch (apiError) {
      console.error("图标API请求失败:", apiError);
    }

    // 如果API请求失败，尝试直接搜索图标目录
    console.log("尝试直接访问图标文件目录");
    const timestamp = taskId.value.split("_")[1] || "";
    const baseId = taskId.value.split("_")[0] || taskId.value;
    const baseUrl = "http://localhost:8000"; // 后端服务器地址

    // 常见的图标文件名
    const commonIconNames = [
      "settings.png",
      "browser.png",
      "camera.png",
      "gallery.png",
      "music.png",
      "phone.png",
      "messages.png",
      "contacts.png",
    ];

    // 尝试各种可能的图标路径
    const iconsFound = [];
    const iconPaths = [
      `${baseUrl}/api/v1/preview-image/${taskId.value}/icon/`,
      `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/releases/icons/`,
      `${baseUrl}/ui_source/mainLine_${baseId}_${timestamp}/icons/`,
    ];

    for (const basePath of iconPaths) {
      for (const iconName of commonIconNames) {
        const iconPath = `${basePath}${iconName}`;
        console.log("检查图标:", iconPath);
        const exists = await checkImagePath(iconPath);
        if (exists) {
          console.log("找到图标:", iconPath);
          iconsFound.push({
            id: iconsFound.length + 1,
            file_name: iconName,
            url: iconPath,
            width: 192,
            height: 192,
            type: "icon" as "icon",
          });

          // 如果找到了几个图标就足够了
          if (iconsFound.length >= 4) break;
        }
      }
      if (iconsFound.length > 0) break;
    }

    if (iconsFound.length > 0) {
      iconPreviews.value = iconsFound;
    }
  } catch (error) {
    console.error("加载图标预览失败:", error);
  } finally {
    loadingIcons.value = false;
  }
};

// 初始加载
onMounted(() => {
  if (taskId.value) {
    loadWallpaperPreviews();
    loadIconPreviews();
  }
});

// 监听任务ID变化
watch(
  () => taskId.value,
  () => {
    activeTab.value = "wallpaper";
    loadWallpaperPreviews();
    loadIconPreviews();
  }
);

// 添加展开/折叠功能
const showAllWallpapers = ref(false);
const showAllIcons = ref(false);

// 添加错误处理函数
const handleIconError = (event: Event, icon: PreviewImage) => {
  console.error(`图标加载失败: ${icon.file_name} - ${icon.url}`);
};

// 添加跳转到车机仪表盘界面的函数
const goToDashboard = () => {
  // 带上任务ID和测试模式参数跳转到车机仪表盘界面
  if (isTestMode.value) {
    router.push({
      path: `/theme-dashboard/${taskId.value}`,
      query: { test_mode: "true" }
    });
  } else {
    router.push(`/theme-dashboard/${taskId.value}`);
  }
};
</script>

<style scoped>
.theme-preview-container {
  min-height: 100vh;
  width: 100%;
  color: white;
  background-color: #0f172a;
}

.page-content {
  position: relative;
  z-index: 10;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 美化顶部导航条 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 1.25rem 2.5rem;
  border-radius: 1rem;
  position: relative;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.back-button {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.back-button:hover {
  color: white;
  transform: translateX(-3px);
}

.title-text {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0;
  background: linear-gradient(135deg, #64b5f6, #8a2be2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 1px;
}

.task-info {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-left: auto;
}

/* 美化标签页按钮 */
.preview-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  background: rgba(30, 41, 59, 0.4);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.tab-button:hover {
  background: rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2);
}

.tab-button.active {
  background: rgba(99, 102, 241, 0.6);
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.4);
}

/* 玻璃卡片预览效果 */
.glass-preview-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.glass-preview-card:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass-preview-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass-preview-card:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

.preview-img-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.75rem 0.75rem 0 0;
}

.wallpaper-card .preview-img-container {
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 */
}

.icon-card .preview-img-container {
  height: 0;
  padding-bottom: 100%; /* 1:1 比例 */
}

.preview-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.wallpaper-card .preview-img {
  object-fit: cover;
}

.icon-card .preview-img {
  object-fit: contain;
  padding: 1rem;
}

.glass-preview-card:hover .preview-img {
  transform: scale(1.05);
}

.preview-card-info {
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
}

.preview-filename {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-dimensions {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.25rem;
}

.preview-section {
  margin-bottom: 3rem;
}

.previews-grid {
  display: grid;
  gap: 1.5rem;
}

.wallpaper-grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.icon-grid {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 展开/折叠按钮样式 */
.expand-collapse-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background: rgba(99, 102, 241, 0.3);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.expand-collapse-btn:hover {
  background: rgba(99, 102, 241, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .wallpaper-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: 1rem;
  }

  .preview-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .wallpaper-grid {
    grid-template-columns: 1fr;
  }

  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .nav-bar {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
  }

  .back-button {
    margin-bottom: 0.5rem;
  }

  .task-info {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}
</style> 