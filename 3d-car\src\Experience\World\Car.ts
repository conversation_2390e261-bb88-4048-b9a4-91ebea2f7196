import * as kokomi from "kokomi.js";
import * as THREE from "three";
import gsap from "gsap";

import type Experience from "../Experience";
import { useCarColorStore } from "../Utils/Store";

export default class Car extends kokomi.Component {
  declare base: Experience;
  model: any; // GLTF模型
  modelParts: THREE.Object3D[];
  bodyMat!: THREE.MeshStandardMaterial;
  wheelModel!: THREE.Group;
  unsubscribe: (() => void) | null;
  
  constructor(base: Experience) {
    super(base);

    const model = this.base.am.items["sm_car"];
    this.model = model;

    const modelParts = kokomi.flatModel(model.scene);
    kokomi.printModel(modelParts);
    this.modelParts = modelParts;

    this.unsubscribe = null;
    
    this.handleModel();
    this.subscribeToColorChange();
  }
  
  addExisting() {
    this.container.add(this.model.scene);
  }
  
  update(): void {
    this.wheelModel?.children.forEach((item) => {
      item.rotateZ(-this.base.params.speed * 0.03);
    });
  }
  
  handleModel() {
    const body = this.modelParts[2] as THREE.Mesh;
    const bodyMat = body.material as THREE.MeshStandardMaterial;
    this.bodyMat = bodyMat;
    
    // 设置初始颜色
    bodyMat.color = new THREE.Color("#26d6e9");
    if (this.base.params.isFurina) {
      bodyMat.color = new THREE.Color("white");
      bodyMat.map = this.base.am.items["decal"];
    }

    console.log("Car body material initialized with color:", bodyMat.color);

    // @ts-ignore
    this.modelParts.forEach((item: THREE.Mesh) => {
      if (item.isMesh) {
        const mat = item.material as THREE.MeshStandardMaterial;
        mat.aoMap = this.base.am.items["ut_car_body_ao"];
      }
    });

    const Wheel = this.modelParts[35] as THREE.Group;
    this.wheelModel = Wheel;
  }
  
  setBodyEnvmapIntensity(value: number) {
    if (this.bodyMat) {
      this.bodyMat.envMapIntensity = value;
    }
  }

  // 订阅颜色变化
  subscribeToColorChange() {
    const currentColor = useCarColorStore.getState().bodyColor;
    console.log("初始化车身颜色:", currentColor);
    
    // 初始化颜色
    if (this.bodyMat && !this.base.params.isFurina) {
      this.bodyMat.color.set(currentColor);
      console.log("设置初始车身颜色:", this.bodyMat.color);
    }
    
    // 订阅颜色变化
    this.unsubscribe = useCarColorStore.subscribe((state) => {
      console.log("颜色状态更新:", state.bodyColor);
      if (this.bodyMat && !this.base.params.isFurina) {
        this.changeColor(state.bodyColor);
      }
    });
    
    console.log("已订阅颜色变化");
  }

  // 颜色渐变过渡效果
  changeColor(targetColor: string) {
    if (!this.bodyMat) {
      console.error("bodyMat未初始化，无法更新颜色");
      return;
    }
    
    console.log("开始颜色变换，目标颜色:", targetColor);
    
    const currentColor = { 
      r: this.bodyMat.color.r, 
      g: this.bodyMat.color.g, 
      b: this.bodyMat.color.b 
    };
    const newColor = new THREE.Color(targetColor);
    
    gsap.to(currentColor, {
      r: newColor.r,
      g: newColor.g,
      b: newColor.b,
      duration: 0.5,
      ease: "power2.out",
      onUpdate: () => {
        this.bodyMat.color.setRGB(
          currentColor.r,
          currentColor.g,
          currentColor.b
        );
      },
      onComplete: () => {
        console.log("颜色变换完成:", this.bodyMat.color);
      }
    });
  }

  // 应用车衣贴图
  applySkin(skinId: string | null) {
    if (!this.bodyMat) {
      console.error("bodyMat未初始化，无法更新车衣");
      return;
    }
    
    console.log("开始应用车衣:", skinId);
    
    // 创建一个闪光效果
    this.createFlashEffect();
    
    if (skinId) {
      // 应用贴图，设置白色底色以不影响贴图显示
      gsap.to(this.bodyMat.color, {
        r: 1, g: 1, b: 1,
        duration: 0.3,
        ease: "power2.out",
        onComplete: () => {
          // 动态加载贴图避免缓存
          if (skinId === "decal") {
            // 为贴图URL添加时间戳参数，防止浏览器缓存
            const textureLoader = new THREE.TextureLoader();
            const timestamp = Date.now();
            const decalUrl = `texture/decal.png?t=${timestamp}`;
            console.log("动态加载贴图:", decalUrl);
            
            textureLoader.load(decalUrl, (texture) => {
              // 保持贴图定向一致
              texture.flipY = false;
              texture.colorSpace = THREE.SRGBColorSpace;
              
              // 应用贴图
              this.bodyMat.map = texture;
              this.bodyMat.needsUpdate = true;
              console.log("已应用最新车衣贴图");
            });
          } else if (skinId.startsWith('http://') || skinId.startsWith('https://')) {
            // 为贴图URL添加时间戳参数，防止浏览器缓存
            const textureLoader = new THREE.TextureLoader();
            console.log("动态加载贴图:", skinId);
            
            textureLoader.load(skinId, (texture) => {
              // 保持贴图定向一致
              texture.flipY = false;
              texture.colorSpace = THREE.SRGBColorSpace;
              
              // 应用贴图
              this.bodyMat.map = texture;
              this.bodyMat.needsUpdate = true;
              console.log("已应用最新车衣贴图");
            });
          } else { 
            // 其他贴图使用预加载的资源
            this.bodyMat.map = this.base.am.items[skinId];
            this.bodyMat.needsUpdate = true;
            console.log("已应用车衣贴图:", skinId);
          }
        }
      });
    } else {
      // 移除贴图，恢复颜色
      this.bodyMat.map = null;
      this.bodyMat.needsUpdate = true;
      
      // 获取当前颜色并应用
      const currentColor = useCarColorStore.getState().bodyColor;
      this.changeColor(currentColor);
      console.log("已移除车衣贴图，恢复颜色:", currentColor);
    }
  }

  // 创建闪光特效
  createFlashEffect() {
    // 创建一个平面作为闪光效果
    const flashGeometry = new THREE.PlaneGeometry(5, 5);
    const flashMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0,
      side: THREE.DoubleSide,
      blending: THREE.AdditiveBlending
    });
    
    const flash = new THREE.Mesh(flashGeometry, flashMaterial);
    flash.position.set(0, 0.5, 0); // 调整位置为车身周围
    flash.rotation.x = Math.PI / 2; // 水平放置
    this.container.add(flash);
    
    // 创建闪光动画
    gsap.timeline()
      .to(flashMaterial, {
        opacity: 0.8,
        duration: 0.2,
        ease: "power1.in"
      })
      .to(flash.scale, {
        x: 3,
        y: 3,
        z: 3,
        duration: 0.5,
        ease: "power1.out"
      }, "-=0.2")
      .to(flashMaterial, {
        opacity: 0,
        duration: 0.5,
        ease: "power1.out",
        onComplete: () => {
          // 动画完成后移除闪光效果
          this.container.remove(flash);
          // 释放资源
          flashGeometry.dispose();
          flashMaterial.dispose();
        }
      }, "-=0.3");
  }

  // 组件销毁时取消订阅
  dispose() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
      console.log("已取消颜色变化订阅");
    }
  }
}
