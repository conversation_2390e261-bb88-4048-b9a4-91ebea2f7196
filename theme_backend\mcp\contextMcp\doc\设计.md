# MCP上下文存储服务设计文档

## 1. 概述

MCP（Model Context Protocol）上下文存储服务是一个独立的服务组件，负责AI交互会话信息的存储和管理。该服务遵循MCP协议规范，使用Python SDK实现，为AI代理（Agent）提供会话存储、检索和管理功能，支持临时缓存（Redis）和永久存储（MongoDB）的双层存储架构。

### 1.1 系统目标

- 提供符合MCP协议的上下文存储和检索服务
- 确保会话数据的持久性和可追溯性
- 支持实时交互和历史查询
- 与Agent系统无缝集成
- 保证数据安全和用户隐私

### 1.2 主要功能

- 会话创建与管理
- 消息存储与检索
- 会话状态跟踪
- 数据持久化
- 用户关联
- 元数据管理

## 2. 系统架构

### 2.1 整体架构

```
┌─────────────────┐       ┌───────────────┐       ┌────────────────┐
│                 │       │               │       │                │
│  WebSocket服务  │───────│  Agent服务    │───────│  MCP服务接口   │
│                 │       │               │       │                │
└─────────────────┘       └───────┬───────┘       └────────┬───────┘
                                  │                        │
                                  │                        │
                                  │                        ▼
                                  │               ┌────────────────┐
                                  │               │                │
                                  │               │  Redis(临时)   │
                                  │               │                │
                                  │               └────────┬───────┘
                                  │                        │
                                  │                        │
                                  ▼                        ▼
                          ┌────────────────┐      ┌────────────────┐
                          │                │      │                │
                          │  用户会话记录  │      │  MongoDB(永久) │
                          │                │      │                │
                          └────────────────┘      └────────────────┘
```

### 2.2 组件说明

- **MCP服务接口**：基于Python SDK实现的MCP协议服务端点
- **Redis存储**：用于临时存储活跃会话数据，支持高速读写
- **MongoDB存储**：用于永久存储已完成会话，支持历史查询
- **Agent服务**：调用MCP服务的客户端，负责业务逻辑处理
- **WebSocket服务**：与前端交互的接口，与MCP服务无直接关联

### 2.3 数据流向

1. Agent服务调用MCP服务创建会话
2. MCP服务在Redis中创建临时会话记录
3. Agent服务通过MCP服务添加消息到会话
4. 会话完成后，MCP服务将数据从Redis迁移到MongoDB
5. Redis中的数据设置过期时间，MongoDB中保存永久记录

## 3. 数据模型

### 3.1 Redis存储结构

```json
{
  "session:{session_id}": {
    "user_id": "admin",  // 用户ID，默认为admin
    "prompt": "原始提示词",
    "status": "running|completed|error",
    "created_at": 1623456789,
    "last_updated": 1623456999,
    "ttl": 86400  // 24小时过期
  },
  
  "session:{session_id}:messages": [
    {
      "timestamp": 1623456790,
      "event_type": "thinking",
      "content": "思考过程内容"
    },
    {
      "timestamp": 1623456800,
      "event_type": "content",
      "content": "生成的内容片段",
      "is_final": false
    },
    {
      "timestamp": 1623456900,
      "event_type": "content",
      "content": "最终增强提示词",
      "is_final": true
    }
  ]
}
```

### 3.2 MongoDB存储结构

```json
{
  "_id": "ObjectId",
  "session_id": "唯一会话ID",
  "user_id": "admin",  // 用户ID，默认为admin
  "prompt": "原始提示词",
  "result": "最终增强提示词",
  "status": "completed|error",
  "created_at": "2023-06-12T10:00:00Z",
  "completed_at": "2023-06-12T10:02:00Z",
  "duration_ms": 120000,
  "messages": [
    {
      "timestamp": "2023-06-12T10:00:10Z",
      "event_type": "thinking",
      "content": "思考过程内容"
    },
    {
      "timestamp": "2023-06-12T10:01:00Z",
      "event_type": "content",
      "content": "生成的内容片段",
      "is_final": false
    },
    {
      "timestamp": "2023-06-12T10:02:00Z",
      "event_type": "content",
      "content": "最终增强提示词",
      "is_final": true
    }
  ],
  "metadata": {
    "device": "browser/mobile",
    "browser": "Chrome/Firefox/Safari",
    "os": "Windows/MacOS/iOS",
    "ip_address": "127.0.0.1"  // 匿名化处理或加密存储
  }
}
```

## 4. MCP服务接口设计

### 4.1 服务配置

```python
# MCP服务配置
MCP_PORT = 19220
MCP_HOST = "0.0.0.0"
MCP_SERVER_NAME = "context_storage"
```

### 4.2 存储配置

```python
# Redis配置
REDIS_HOST = "***********"
REDIS_PORT = 5182
REDIS_PASSWORD = "kLKe3NFM4RZMgXhA"
REDIS_EXPIRY_TIME = 60 * 60 * 24  # 24小时过期（秒）

# MongoDB配置
MONGO_HOST = "************"
MONGO_PORT = 37817
MONGO_USERNAME = "admin"
MONGO_PASSWORD = "kLKe3NFM4RZMgXhA"
MONGO_DB_NAME = "admin"
MONGO_COLLECTION = "sessions"
```

### 4.3 MCP工具定义

```json
{
  "tools": [
    {
      "name": "create_session",
      "description": "创建新的会话记录",
      "parameters": {
        "type": "object",
        "properties": {
          "user_id": {
            "type": "string",
            "description": "用户ID，默认为admin"
          },
          "prompt": {
            "type": "string",
            "description": "原始提示词"
          },
          "metadata": {
            "type": "object",
            "description": "会话元数据"
          }
        },
        "required": ["prompt"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "创建的会话ID"
          },
          "status": {
            "type": "string",
            "description": "会话状态"
          }
        }
      }
    },
    {
      "name": "add_message",
      "description": "向会话添加新消息",
      "parameters": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "会话ID"
          },
          "event_type": {
            "type": "string",
            "description": "事件类型：thinking, content, code, error"
          },
          "content": {
            "type": "string",
            "description": "消息内容"
          },
          "is_final": {
            "type": "boolean",
            "description": "是否为最终消息"
          }
        },
        "required": ["session_id", "event_type", "content"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "success": {
            "type": "boolean"
          },
          "message_id": {
            "type": "string"
          }
        }
      }
    },
    {
      "name": "complete_session",
      "description": "标记会话完成并持久化",
      "parameters": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "会话ID"
          },
          "result": {
            "type": "string",
            "description": "最终结果"
          },
          "status": {
            "type": "string",
            "description": "完成状态: completed, error"
          }
        },
        "required": ["session_id", "status"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "success": {
            "type": "boolean"
          }
        }
      }
    },
    {
      "name": "get_session",
      "description": "获取会话信息",
      "parameters": {
        "type": "object",
        "properties": {
          "session_id": {
            "type": "string",
            "description": "会话ID"
          }
        },
        "required": ["session_id"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "session": {
            "type": "object",
            "description": "会话信息"
          },
          "messages": {
            "type": "array",
            "description": "会话消息列表"
          }
        }
      }
    },
    {
      "name": "list_user_sessions",
      "description": "列出用户的历史会话",
      "parameters": {
        "type": "object",
        "properties": {
          "user_id": {
            "type": "string",
            "description": "用户ID，默认为admin"
          },
          "limit": {
            "type": "integer",
            "description": "返回结果数量限制"
          },
          "offset": {
            "type": "integer",
            "description": "结果偏移量"
          }
        },
        "required": ["user_id"]
      },
      "returns": {
        "type": "object",
        "properties": {
          "sessions": {
            "type": "array",
            "description": "会话列表"
          },
          "total": {
            "type": "integer",
            "description": "总会话数"
          }
        }
      }
    }
  ]
}
```

## 5. 实现详情

### 5.1 服务初始化

```python
from mcp.server import MCPServer
from mcp.shared import Tool, Prompt, Resource

import redis
import pymongo
import uuid
import time
import json
from datetime import datetime

# 创建MCP服务
context_server = MCPServer(name="context_storage")

# 初始化Redis客户端
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    decode_responses=True
)

# 初始化MongoDB客户端
mongo_client = pymongo.MongoClient(
    host=MONGO_HOST,
    port=MONGO_PORT,
    username=MONGO_USERNAME,
    password=MONGO_PASSWORD
)
mongo_db = mongo_client[MONGO_DB_NAME]
sessions_collection = mongo_db[MONGO_COLLECTION]
```

### 5.2 工具实现

```python
@context_server.tool
async def create_session(user_id: str = "admin", prompt: str = "", metadata: dict = None):
    """创建新的会话记录"""
    session_id = str(uuid.uuid4())
    timestamp = time.time()
    
    # Redis存储临时会话
    redis_client.hset(
        f"session:{session_id}",
        mapping={
            "user_id": user_id,
            "prompt": prompt,
            "status": "created",
            "created_at": timestamp,
            "last_updated": timestamp
        }
    )
    redis_client.expire(f"session:{session_id}", REDIS_EXPIRY_TIME)  # 设置过期时间
    
    # 如果有元数据，一并存储
    if metadata:
        redis_client.hset(f"session:{session_id}:metadata", mapping=metadata)
        redis_client.expire(f"session:{session_id}:metadata", REDIS_EXPIRY_TIME)
    
    return {
        "session_id": session_id,
        "status": "created"
    }

@context_server.tool
async def add_message(session_id: str, event_type: str, content: str, is_final: bool = False):
    """向会话添加新消息"""
    if not redis_client.exists(f"session:{session_id}"):
        return {"success": False, "error": "Session not found"}
    
    message = {
        "timestamp": time.time(),
        "event_type": event_type,
        "content": content,
        "is_final": is_final
    }
    
    # 添加消息到Redis列表
    message_id = str(uuid.uuid4())
    message["id"] = message_id
    redis_client.rpush(f"session:{session_id}:messages", json.dumps(message))
    redis_client.hset(f"session:{session_id}", "last_updated", time.time())
    
    # 更新过期时间
    redis_client.expire(f"session:{session_id}", REDIS_EXPIRY_TIME)
    redis_client.expire(f"session:{session_id}:messages", REDIS_EXPIRY_TIME)
    
    return {"success": True, "message_id": message_id}

@context_server.tool
async def complete_session(session_id: str, result: str = "", status: str = "completed"):
    """标记会话完成并持久化到MongoDB"""
    if not redis_client.exists(f"session:{session_id}"):
        return {"success": False, "error": "Session not found"}
    
    # 获取会话数据
    session_data = redis_client.hgetall(f"session:{session_id}")
    messages_data = redis_client.lrange(f"session:{session_id}:messages", 0, -1)
    messages = [json.loads(m) for m in messages_data]
    
    # 尝试获取元数据
    metadata = {}
    if redis_client.exists(f"session:{session_id}:metadata"):
        metadata = redis_client.hgetall(f"session:{session_id}:metadata")
    
    # 更新Redis中的会话状态
    completed_time = time.time()
    redis_client.hset(
        f"session:{session_id}", 
        mapping={
            "status": status,
            "result": result,
            "completed_at": completed_time
        }
    )
    
    # 构建MongoDB文档
    mongo_doc = {
        "session_id": session_id,
        "user_id": session_data.get("user_id", "admin"),
        "prompt": session_data.get("prompt", ""),
        "result": result,
        "status": status,
        "created_at": datetime.fromtimestamp(float(session_data.get("created_at", 0))),
        "completed_at": datetime.fromtimestamp(completed_time),
        "duration_ms": int((completed_time - float(session_data.get("created_at", 0))) * 1000),
        "messages": messages,
        "metadata": metadata
    }
    
    # 存储到MongoDB
    sessions_collection.insert_one(mongo_doc)
    
    return {"success": True}

@context_server.tool
async def get_session(session_id: str):
    """获取会话信息"""
    # 先尝试从Redis获取（活跃会话）
    if redis_client.exists(f"session:{session_id}"):
        session_data = redis_client.hgetall(f"session:{session_id}")
        messages_data = redis_client.lrange(f"session:{session_id}:messages", 0, -1)
        messages = [json.loads(m) for m in messages_data]
        
        # 尝试获取元数据
        metadata = {}
        if redis_client.exists(f"session:{session_id}:metadata"):
            metadata = redis_client.hgetall(f"session:{session_id}:metadata")
        
        session_data["metadata"] = metadata
        return {
            "session": session_data,
            "messages": messages,
            "source": "redis"
        }
    
    # 如果Redis中不存在，从MongoDB获取（历史会话）
    mongo_session = sessions_collection.find_one({"session_id": session_id})
    if mongo_session:
        # 转换ObjectId为字符串
        mongo_session["_id"] = str(mongo_session["_id"])
        # 转换日期为ISO格式字符串
        for key in ["created_at", "completed_at"]:
            if key in mongo_session and isinstance(mongo_session[key], datetime):
                mongo_session[key] = mongo_session[key].isoformat()
        
        return {
            "session": mongo_session,
            "messages": mongo_session.get("messages", []),
            "source": "mongodb"
        }
    
    return {"success": False, "error": "Session not found"}

@context_server.tool
async def list_user_sessions(user_id: str = "admin", limit: int = 10, offset: int = 0):
    """列出用户的历史会话"""
    # 从MongoDB获取历史会话
    mongo_sessions = list(sessions_collection.find(
        {"user_id": user_id},
        sort=[("created_at", -1)],
        skip=offset,
        limit=limit
    ))
    
    # 转换ObjectId为字符串
    for session in mongo_sessions:
        session["_id"] = str(session["_id"])
        # 转换日期为ISO格式字符串
        for key in ["created_at", "completed_at"]:
            if key in session and isinstance(session[key], datetime):
                session[key] = session[key].isoformat()
    
    # 获取总会话数
    total_count = sessions_collection.count_documents({"user_id": user_id})
    
    return {
        "sessions": mongo_sessions,
        "total": total_count
    }
```

### 5.3 启动服务

```python
def start_mcp_server():
    """启动MCP服务"""
    context_server.run(host=MCP_HOST, port=MCP_PORT)

if __name__ == "__main__":
    start_mcp_server()
```

## 6. 客户端调用示例

### 6.1 Agent服务中的调用

```python
from mcp.client import MCPClient

class ContextAgent:
    def __init__(self, websocket_manager=None):
        self.mcp_client = MCPClient("http://localhost:19220")
        self.websocket_manager = websocket_manager
    
    async def create_and_use_session(self, prompt, user_id="admin"):
        # 创建会话
        result = await self.mcp_client.call_tool(
            "create_session",
            {
                "user_id": user_id,
                "prompt": prompt,
                "metadata": {
                    "source": "web",
                    "type": "prompt_enhancement"
                }
            }
        )
        
        session_id = result["session_id"]
        
        # 记录思考过程
        await self.mcp_client.call_tool(
            "add_message",
            {
                "session_id": session_id,
                "event_type": "thinking",
                "content": "分析提示词需求..."
            }
        )
        
        # 如果存在WebSocket管理器，发送消息给前端
        if self.websocket_manager:
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "thinking",
                    "data": {
                        "content": "分析提示词需求...",
                        "is_final": False
                    }
                }
            )
        
        # 记录最终结果
        enhanced_prompt = f"增强版: {prompt}"
        await self.mcp_client.call_tool(
            "add_message",
            {
                "session_id": session_id,
                "event_type": "content",
                "content": enhanced_prompt,
                "is_final": True
            }
        )
        
        # 完成会话
        await self.mcp_client.call_tool(
            "complete_session",
            {
                "session_id": session_id,
                "result": enhanced_prompt,
                "status": "completed"
            }
        )
        
        return {
            "session_id": session_id,
            "result": enhanced_prompt
        }
```

## 7. 部署与配置

### 7.1 依赖项

```
# requirements.txt
mcp==0.4.0
redis==4.5.5
pymongo==4.3.3
fastapi==0.95.0
uvicorn==0.22.0
```

### 7.2 环境变量配置

```
# .env
MCP_PORT=19220
MCP_HOST=0.0.0.0

REDIS_HOST=***********
REDIS_PORT=5182
REDIS_PASSWORD=kLKe3NFM4RZMgXhA

MONGO_HOST=************
MONGO_PORT=37817
MONGO_USERNAME=admin
MONGO_PASSWORD=kLKe3NFM4RZMgXhA
MONGO_DB_NAME=admin
MONGO_COLLECTION=sessions
```

### 7.3 部署方式

#### Docker部署

```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "main.py"]
```

#### 系统服务部署

```ini
# /etc/systemd/system/mcp-context.service
[Unit]
Description=MCP Context Storage Service
After=network.target

[Service]
User=mcp
WorkingDirectory=/opt/mcp-context
ExecStart=/opt/mcp-context/venv/bin/python main.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

## 8. 安全考虑

### 8.1 数据安全

- 敏感用户信息加密存储
- Redis设置强密码和访问控制
- MongoDB启用身份验证和授权
- 定期数据备份和恢复机制

### 8.2 网络安全

- MCP服务仅在内部网络暴露
- 配置防火墙限制访问
- 实现IP白名单机制
- 考虑API密钥或token认证

### 8.3 合规性

- 遵循数据保护法规
- 实现数据删除机制
- 记录操作审计日志
- 确保用户数据隔离

## 9. 扩展性考虑

### 9.1 水平扩展

- Redis可配置为集群模式
- MongoDB支持分片部署
- MCP服务可多实例部署
- 负载均衡器前置

### 9.2 功能扩展

- 支持更多消息类型和格式
- 添加数据分析和统计功能
- 实现完整的用户认证和授权系统
- 提供会话共享和协作功能

### 9.3 集成扩展

- 与其他AI系统集成
- 提供REST API补充MCP接口
- 支持更多存储后端（如S3）
- 实现数据迁移和同步工具

## 10. 监控与运维

### 10.1 监控指标

- 服务可用性
- 请求延迟
- 错误率
- 存储使用量
- 活跃会话数

### 10.2 日志管理

- 标准化日志格式
- 集中式日志存储
- 日志级别配置
- 异常捕获和记录

### 10.3 故障恢复

- 自动重启机制
- 数据一致性检查
- 降级服务策略
- 备份恢复流程

## 2. 环境安装

### 2.1 前置要求
- Python 3.10+
- 包管理工具：推荐使用uv（替代pip）
- 虚拟环境：建议使用Python虚拟环境

### 2.2 安装步骤

#### 使用uv管理（推荐）
```bash
# 创建新项目目录
uv init mcp-context
cd mcp-context

# 创建并激活虚拟环境
uv venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装MCP核心库和CLI工具
uv add "mcp[cli]" httpx redis pymongo

# 验证安装
mcp --help
```

#### 使用传统pip安装
```bash
# 创建并激活虚拟环境
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装核心依赖
pip install mcp redis pymongo httpx

# 安装CLI工具（可选）
pip install "mcp[cli]"

# 验证安装
mcp --version
```

### 2.3 依赖说明
| 包名称  | 版本     | 用途                       |
| ------- | -------- | -------------------------- |
| mcp     | >=0.4.0  | MCP协议核心实现            |
| redis   | >=4.5.5  | Redis客户端连接            |
| pymongo | >=4.3.3  | MongoDB官方驱动            |
| httpx   | >=0.27.0 | HTTP客户端（用于工具调用） |
| uvicorn | >=0.22.0 | ASGI服务器（服务部署）     |

### 2.4 开发工具安装
```bash
# 安装开发依赖
uv add "mcp[dev]" --dev

# 或使用pip
pip install "mcp[dev]"

# 包含工具：
# - pytest       : 单元测试框架
# - black        : 代码格式化工具
# - mypy         : 静态类型检查
# - ruff         : 代码质量检查