# theme_backend/agent/sd_prompt_agent.py

import os
import asyncio
import logging
from typing import Dict, Any, Optional
import json

# 使用与 promptAgent.py 相同的模型客户端导入
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 配置日志记录 (可以复用或独立配置)
logging.basicConfig(
    level=logging.INFO, # 可以调整为 DEBUG 获取更详细信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- 模型客户端配置 (类似 promptAgent.py) ---
def get_sd_model_client():
    """获取用于 SD 提示词增强的模型客户端"""
    logger.info("初始化 SD Prompt Agent 模型客户端 - Qwen2.5-32B-Instruct-AWQ")
    # 注意：如果需要不同的模型行为，可以调整配置或使用不同的模型
    return OpenAIChatCompletionClient(
        model="Qwen2.5-32B-Instruct-AWQ",
        api_key="1", # 根据实际情况配置
        base_url="http://27.159.93.61:8007/v1/",
        model_capabilities={
            "json_output": False, # SD prompt 通常是纯文本
            "vision": False,
            "function_calling": False, # 不需要函数调用
        },
        # 如果模型需要特定的额外参数，可以在这里添加
        # extra_body={ },
        # response_wrapper=... # 如果需要处理特殊响应格式
    )

# --- SD 提示词增强 Agent ---
class SdPromptEnhancerAgent:
    """
    使用 AutoGen AssistantAgent 来增强 SD 提示词。
    将用户输入转换为详细的、英文的、逗号分隔的 SD 提示词。
    """
    def __init__(self):
        logger.info("正在创建 SD 提示词增强 Agent 实例...")
        try:
            self.model_client = get_sd_model_client()
            self.agent = AssistantAgent(
                name="sd_prompt_enhancer",
                model_client=self.model_client,
                system_message=self._get_system_message(),
                # 不需要流式输出，直接获取最终结果
                # model_client_stream=False
            )
            logger.info("SD 提示词增强 Agent 实例创建成功")
        except Exception as e:
            logger.error(f"创建 SD 提示词增强 Agent 时出错: {e}", exc_info=True)
            raise

    def _get_system_message(self) -> str:
        """定义 Agent 的系统指令，指导其生成 SD 提示词"""
        # 这个 Prompt 很关键，需要精心设计
        return """You are an expert Stable Diffusion prompt generator. Your task is to take a user's input, which might be brief or in a language other than English (like Chinese), understand its core meaning, and transform it into a detailed, effective, comma-separated English prompt suitable for AI image generation models like Stable Diffusion.

**Instructions:**
1.  **Translate & Enhance:** If the input is not in English, translate the core concept. Then, enhance it with relevant details, artistic style (e.g., photorealistic, illustration, anime, watercolor), subject details, environment, lighting (e.g., soft light, cinematic lighting), composition, and quality keywords.
2.  **Format:** Output **ONLY** the final comma-separated English prompt string. Do not include any explanations, greetings, apologies, markdown formatting, or any text other than the prompt itself.
3.  **Keywords:** Use descriptive keywords separated by commas and spaces.
4.  **Quality Tags:** Always include positive quality tags like "masterpiece, best quality, high resolution, highly detailed, intricate details". You can also add negative prompts if needed, separated by a newline and starting with "Negative prompt:", but prioritize the positive prompt first. For this task, only generate the positive prompt unless specifically asked for negative.
5.  **Conciseness:** While detailed, keep the prompt focused and avoid unnecessary words.

**Example Input (Chinese):** 一只可爱的猫咪在窗边看雨
**Example Output:** masterpiece, best quality, high resolution, highly detailed, intricate details, 1 cute fluffy cat sitting on a windowsill, looking out at rain through the window, cozy atmosphere, soft lighting, detailed fur, photorealistic style

**Example Input:** A dragon flying over a castle
**Example Output:** masterpiece, best quality, high resolution, highly detailed, intricate details, majestic dragon flying over a medieval stone castle, dramatic sky, volumetric lighting, fantasy art, epic scale, cinematic composition
"""

    async def enhance_prompt(self, user_prompt: str) -> Optional[str]:
        """
        接收用户提示词并返回增强后的英文 SD 提示词。

        Args:
            user_prompt: 用户输入的原始提示词 (可能为中文)。

        Returns:
            增强后的英文 SD 提示词字符串，如果出错则返回 None。
        """
        logger.info(f"SD Agent 接收到增强请求: '{user_prompt[:50]}...'")
        task_message = f"Please enhance the following user input into a detailed, comma-separated English Stable Diffusion prompt:\n\nUser Input: \"{user_prompt}\"\n\nRemember to follow all instructions, especially outputting ONLY the prompt string."

        try:
            # 检查run或run_async方法是否可用并执行
            if hasattr(self.agent, 'run_async'):
                logger.info("使用 run_async 方法运行")
                response = await self.agent.run_async(task=task_message)
            # 检查run方法
            elif hasattr(self.agent, 'run'):
                # 检查run是否是协程函数
                import inspect
                if inspect.iscoroutinefunction(self.agent.run):
                    logger.info("agent.run是一个协程函数，直接await")
                    # 如果run本身是异步函数，直接await它
                    response = await self.agent.run(task=task_message)
                else:
                    logger.info("agent.run是同步函数，使用asyncio.to_thread")
                    # 如果run是同步函数，使用to_thread
                    response = await asyncio.to_thread(self.agent.run, task=task_message)
                
                logger.info(f"run执行完成，获得响应类型: {type(response)}")
            else:
                logger.error("Agent has neither run_async nor run method.")
                return None

            # ===== 调试信息：打印完整的模型响应 =====
            logger.info("===== 模型完整响应(调试) =====")
            try:
                # 尝试不同方式获取和打印完整响应
                # 1. 直接打印响应对象
                logger.info(f"模型响应类型: {type(response)}")
                logger.info(f"模型原始响应: {response}")
                
                # 2. 如果有 messages 属性
                if hasattr(response, 'messages') and response.messages:
                    logger.info(f"响应消息数量: {len(response.messages)}")
                    for i, msg in enumerate(response.messages):
                        logger.info(f"消息 {i+1} - 来源: {msg.source if hasattr(msg, 'source') else getattr(msg, 'role', 'unknown')}")
                        logger.info(f"消息 {i+1} - 内容: {msg.content}")
                
                # 3. 尝试将响应转换为字典并打印
                if hasattr(response, '__dict__'):
                    try:
                        response_dict = response.__dict__
                        logger.info(f"响应属性: {json.dumps(response_dict, default=str, ensure_ascii=False, indent=2)}")
                    except:
                        logger.info("无法将响应对象转换为字典")
            except Exception as debug_error:
                logger.error(f"打印调试信息时出错: {debug_error}")
            logger.info("===== 调试信息结束 =====")
            # ===== 调试信息打印结束 =====

            # 从响应中提取最终的增强提示词
            enhanced_prompt = None
            if isinstance(response, str):
                enhanced_prompt = response.strip()
                logger.info(f"从字符串响应提取: {enhanced_prompt}")
            elif hasattr(response, 'messages') and response.messages:
                last_assistant_message = None
                for msg in reversed(response.messages):
                    # 检查 source 或 role，取决于 AutoGen 版本和配置
                    if msg.source == self.agent.name or getattr(msg, 'role', None) == 'assistant':
                        last_assistant_message = msg.content
                        break
                enhanced_prompt = last_assistant_message.strip() if last_assistant_message else None
                logger.info(f"从消息列表提取: {enhanced_prompt}")
            elif hasattr(response, 'content'):
                enhanced_prompt = response.content.strip()
                logger.info(f"从content属性提取: {enhanced_prompt}")
            else:
                logger.warning(f"SD Agent 未能识别的响应格式: {type(response)}")

            if enhanced_prompt:
                # 清理可能的额外文本
                lines = enhanced_prompt.splitlines()
                final_prompt = lines[-1].strip() if lines else enhanced_prompt
                # 可选：更严格的清理
                # if not all(c in string.ascii_letters + string.digits + ', ' for c in final_prompt):
                #     logger.warning(f"Final prompt may contain unexpected characters: {final_prompt}")

                logger.info(f"SD Agent 增强完成: '{final_prompt[:100]}...'")
                return final_prompt
            else:
                logger.error(f"SD Agent 未能从响应中提取增强后的提示词。响应: {response}")
                return None

        except Exception as e:
            logger.error(f"SD Agent 在增强提示词时出错: {e}", exc_info=True)
            return None

# --- 创建一个全局实例供外部调用 ---
try:
    sd_prompt_enhancer = SdPromptEnhancerAgent()
    logger.info("全局 SD Prompt Enhancer 实例已创建")
except Exception as e:
    logger.error("创建全局 SD Prompt Enhancer 实例失败!", exc_info=True)
    sd_prompt_enhancer = None # 标记为失败 