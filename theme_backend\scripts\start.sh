#!/bin/bash

echo "Starting ComfyUI Backend Service..."

# 检查Python环境
if ! command -v python3 &>/dev/null; then
    echo "Python3 is not installed"
    exit 1
fi

# 检查依赖
if ! python3 -m pip show uvicorn &>/dev/null; then
    echo "Installing dependencies..."
    pip3 install -r ../requirements.txt
fi

# 创建必要的目录
mkdir -p ../public/ui_source

# 设置目录权限
chmod -R 755 ../public

# 启动服务
cd ..
echo "Starting FastAPI server..."
uvicorn app.main:app --reload --port 8000 --host 0.0.0.0
