"""
AutoGen适配器
提供AutoGen调用MCP服务的工具接口
"""

import json
import logging
import requests
import aiohttp
import asyncio
from typing import Dict, List, Any, Optional, Union, Callable

logger = logging.getLogger(__name__)

class MCPToolkit:
    """MCP工具箱，用于AutoGen调用MCP服务"""
    
    def __init__(self, server_url: str):
        """
        初始化MCP工具箱
        
        Args:
            server_url: MCP服务器URL（如http://localhost:8000）
        """
        self.server_url = server_url.rstrip("/")
        self.api_url = f"{self.server_url}/api/v1"
        self.session_id = None
        self.agent_name = "autogen"
        
        # 缓存工具定义
        self.tools_cache = None
    
    def get_tools_config(self) -> List[Dict[str, Any]]:
        """
        获取AutoGen工具配置
        
        Returns:
            用于AutoGen的工具配置列表
        """
        tools = self.get_tools_definitions()
        
        # 转换为AutoGen格式
        autogen_tools = []
        for tool in tools:
            autogen_tool = {
                "name": tool["name"],
                "description": tool["description"],
                "parameters": tool["input_schema"]
            }
            autogen_tools.append(autogen_tool)
        
        return autogen_tools
    
    def get_tools_definitions(self) -> List[Dict[str, Any]]:
        """
        获取工具定义
        
        Returns:
            工具定义列表
        """
        if self.tools_cache is not None:
            return self.tools_cache
        
        try:
            response = requests.get(f"{self.api_url}/tools")
            response.raise_for_status()
            tools = response.json().get("tools", [])
            self.tools_cache = tools
            return tools
        except Exception as e:
            logger.error(f"获取工具定义失败: {str(e)}")
            return []
    
    def init_session(self, session_name: str, meta_data: Optional[Dict[str, Any]] = None) -> str:
        """
        初始化会话
        
        Args:
            session_name: 会话名称
            meta_data: 会话元数据
            
        Returns:
            会话ID
        """
        response = self.call_tool("create_session", {
            "session_name": session_name,
            "meta_data": meta_data or {}
        })
        
        if response.get("success"):
            self.session_id = response.get("session_id")
            return self.session_id
        else:
            raise Exception(f"创建会话失败: {response.get('error')}")
    
    def close_current_session(self) -> bool:
        """
        关闭当前会话
        
        Returns:
            是否成功
        """
        if not self.session_id:
            return False
        
        response = self.call_tool("close_session", {
            "session_id": self.session_id
        })
        
        if response.get("success"):
            self.session_id = None
            return True
        else:
            logger.error(f"关闭会话失败: {response.get('error')}")
            return False
    
    def set_agent_name(self, agent_name: str):
        """
        设置代理名称
        
        Args:
            agent_name: 代理名称
        """
        self.agent_name = agent_name
    
    def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            params: 工具参数
            
        Returns:
            工具调用结果
        """
        try:
            response = requests.post(
                f"{self.api_url}/tools/{tool_name}",
                json=params,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"工具调用失败: {str(e)}")
            return {"success": False, "error": str(e)}
        except json.JSONDecodeError:
            logger.error("解析响应失败")
            return {"success": False, "error": "解析响应失败"}
    
    def stream_thinking(self, content: str) -> bool:
        """
        流式输出思考步骤
        
        Args:
            content: 思考内容
            
        Returns:
            是否成功
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("stream_thinking", {
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "content": content
        })
        
        return response.get("success", False)
    
    def stream_tokens(self, content: str, is_final: bool = False) -> bool:
        """
        流式输出文本内容
        
        Args:
            content: 输出内容
            is_final: 是否为最后的内容块
            
        Returns:
            是否成功
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("stream_tokens", {
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "content": content,
            "is_final": is_final
        })
        
        return response.get("success", False)
    
    def publish_code_block(self, code: str, language: str = "python") -> bool:
        """
        发布代码块
        
        Args:
            code: 代码内容
            language: 代码语言
            
        Returns:
            是否成功
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("publish_code_block", {
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "code": code,
            "language": language
        })
        
        return response.get("success", False)
    
    def publish_tool_call(self, tool_name: str, tool_input: Dict[str, Any], 
                         tool_output: Optional[Dict[str, Any]] = None) -> bool:
        """
        发布工具调用事件
        
        Args:
            tool_name: 工具名称
            tool_input: 工具输入参数
            tool_output: 工具输出结果（可选）
            
        Returns:
            是否成功
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("publish_tool_call", {
            "session_id": self.session_id,
            "agent_name": self.agent_name,
            "tool_name": tool_name,
            "tool_input": tool_input,
            "tool_output": tool_output
        })
        
        return response.get("success", False)
    
    def save_context(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        保存上下文数据
        
        Args:
            key: 上下文键
            value: 上下文值
            ttl: 过期时间（秒）
            
        Returns:
            是否成功
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("save_context", {
            "session_id": self.session_id,
            "key": key,
            "value": value,
            "ttl": ttl
        })
        
        return response.get("success", False)
    
    def get_context(self, key: str) -> Any:
        """
        获取上下文数据
        
        Args:
            key: 上下文键
            
        Returns:
            上下文值
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("get_context", {
            "session_id": self.session_id,
            "key": key
        })
        
        if response.get("success"):
            return response.get("value")
        else:
            logger.error(f"获取上下文失败: {response.get('error')}")
            return None
    
    def get_session_messages(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取会话消息历史
        
        Args:
            limit: 消息数量限制
            
        Returns:
            会话消息列表
        """
        if not self.session_id:
            raise ValueError("未初始化会话")
        
        response = self.call_tool("get_session_messages", {
            "session_id": self.session_id,
            "limit": limit
        })
        
        if response.get("success"):
            return response.get("messages", [])
        else:
            logger.error(f"获取会话消息失败: {response.get('error')}")
            return []


# AutoGen工具函数包装器
def create_streaming_functions(mcp_toolkit: MCPToolkit) -> Dict[str, Callable]:
    """
    创建包装后的流式输出工具函数
    
    Args:
        mcp_toolkit: MCP工具箱实例
        
    Returns:
        工具函数字典
    """
    def thinking(content: str) -> str:
        """输出思考步骤"""
        success = mcp_toolkit.stream_thinking(content)
        return "思考步骤已发送" if success else "发送思考步骤失败"
    
    def stream_output(content: str, is_final: bool = False) -> str:
        """流式输出文本"""
        success = mcp_toolkit.stream_tokens(content, is_final)
        return "内容已流式输出" if success else "流式输出失败"
    
    def show_code(code: str, language: str = "python") -> str:
        """显示代码块"""
        success = mcp_toolkit.publish_code_block(code, language)
        return "代码块已发布" if success else "发布代码块失败"
    
    def save_data(key: str, value: Any, ttl: Optional[int] = None) -> str:
        """保存数据到上下文"""
        success = mcp_toolkit.save_context(key, value, ttl)
        return f"数据已保存到键 {key}" if success else f"保存数据到键 {key} 失败"
    
    def load_data(key: str) -> Any:
        """从上下文加载数据"""
        value = mcp_toolkit.get_context(key)
        return value
    
    def log_tool_call(tool_name: str, tool_input: Dict[str, Any], 
                     tool_output: Optional[Dict[str, Any]] = None) -> str:
        """记录工具调用"""
        success = mcp_toolkit.publish_tool_call(tool_name, tool_input, tool_output)
        return f"工具调用 {tool_name} 已记录" if success else f"记录工具调用 {tool_name} 失败"
    
    return {
        "thinking": thinking,
        "stream_output": stream_output,
        "show_code": show_code,
        "save_data": save_data,
        "load_data": load_data,
        "log_tool_call": log_tool_call
    }


class StreamingOutputManager:
    """流式输出管理器，用于将LLM响应实时流式输出"""
    
    def __init__(self, mcp_toolkit: MCPToolkit):
        """
        初始化流式输出管理器
        
        Args:
            mcp_toolkit: MCP工具箱实例
        """
        self.mcp_toolkit = mcp_toolkit
        self.buffer = ""
        self.thinking_buffer = ""
        self.current_mode = "normal"  # normal, thinking, code
        self.code_buffer = ""
        self.code_language = "python"
    
    def start_thinking(self):
        """开始思考模式"""
        self.flush_buffer()
        self.current_mode = "thinking"
        self.thinking_buffer = ""
    
    def end_thinking(self):
        """结束思考模式"""
        if self.thinking_buffer:
            self.mcp_toolkit.stream_thinking(self.thinking_buffer)
            self.thinking_buffer = ""
        self.current_mode = "normal"
    
    def start_code(self, language: str = "python"):
        """
        开始代码模式
        
        Args:
            language: 代码语言
        """
        self.flush_buffer()
        self.current_mode = "code"
        self.code_buffer = ""
        self.code_language = language
    
    def end_code(self):
        """结束代码模式"""
        if self.code_buffer:
            self.mcp_toolkit.publish_code_block(self.code_buffer, self.code_language)
            self.code_buffer = ""
        self.current_mode = "normal"
    
    def process_chunk(self, chunk: str):
        """
        处理LLM响应块
        
        Args:
            chunk: 响应文本块
        """
        if self.current_mode == "thinking":
            self.thinking_buffer += chunk
        elif self.current_mode == "code":
            self.code_buffer += chunk
        else:
            self.buffer += chunk
            # 可以根据需要触发实时输出
            if len(self.buffer) > 50 or "\n" in self.buffer:
                self.flush_buffer()
    
    def flush_buffer(self, is_final: bool = False):
        """
        刷新缓冲区
        
        Args:
            is_final: 是否为最后的内容
        """
        if self.buffer:
            self.mcp_toolkit.stream_tokens(self.buffer, is_final)
            self.buffer = ""
    
    def finalize(self):
        """结束输出"""
        # 根据当前模式执行相应的结束操作
        if self.current_mode == "thinking":
            self.end_thinking()
        elif self.current_mode == "code":
            self.end_code()
        
        # 输出最后的缓冲区
        self.flush_buffer(is_final=True)
    
    def detect_mode_changes(self, text: str):
        """
        检测并处理模式变化
        
        Args:
            text: 输入文本
        """
        # 检测思考模式
        if "```thinking" in text:
            self.start_thinking()
            return True
        elif "```" in text and self.current_mode == "thinking":
            self.end_thinking()
            return True
        
        # 检测代码模式
        code_start = text.find("```")
        if code_start != -1 and self.current_mode != "code" and self.current_mode != "thinking":
            language_end = text.find("\n", code_start)
            if language_end != -1:
                language = text[code_start+3:language_end].strip()
                if language:
                    self.start_code(language)
                    return True
        elif "```" in text and self.current_mode == "code":
            self.end_code()
            return True
        
        return False 