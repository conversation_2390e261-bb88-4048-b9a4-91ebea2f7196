import React, { useRef, useEffect, useState } from "react";
import { createRoot } from "react-dom/client";

export function SparklesCore({
  background = "transparent",
  minSize = 0.4,
  maxSize = 1,
  particleDensity = 80,
  particleColor = "#FFF",
  className,
  particleSpeed = 0.5,
}) {
  const canvasRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const context = useRef(null);
  const particles = useRef([]);
  const animationRef = useRef(null);
  const [dimension, setDimension] = useState({ width: 0, height: 0 });

  const resizeCanvas = () => {
    if (canvasContainerRef.current && canvasRef.current) {
      const { width, height } = canvasContainerRef.current.getBoundingClientRect();
      setDimension({ width, height });
      canvasRef.current.width = width;
      canvasRef.current.height = height;
    }
  };

  useEffect(() => {
    resizeCanvas();
    window.addEventListener("resize", resizeCanvas);

    return () => {
      window.removeEventListener("resize", resizeCanvas);
    };
  }, []);

  useEffect(() => {
    if (canvasRef.current) {
      context.current = canvasRef.current.getContext("2d");
    }

    const initiateParticles = () => {
      particles.current = [];
      const { width, height } = dimension;
      const particleCount = Math.floor((width * height) / 10000) * particleDensity;

      for (let i = 0; i < particleCount; i++) {
        const x = Math.random() * width;
        const y = Math.random() * height;
        const size = Math.random() * (maxSize - minSize) + minSize;
        const speed = Math.random() * particleSpeed;
        const opacity = Math.random();

        particles.current.push({
          x,
          y,
          size,
          speed,
          opacity,
          factor: 1,
        });
      }
    };

    const animate = () => {
      if (particles.current.length === 0) initiateParticles();

      if (context.current && canvasRef.current) {
        context.current.clearRect(0, 0, dimension.width, dimension.height);
        context.current.fillStyle = background;
        context.current.fillRect(0, 0, dimension.width, dimension.height);

        particles.current.forEach((particle) => {
          const { x, y, size, opacity } = particle;

          context.current.globalAlpha = opacity;
          context.current.fillStyle = particleColor;
          context.current.beginPath();
          context.current.arc(x, y, size, 0, 2 * Math.PI);
          context.current.fill();

          // 更新粒子位置
          particle.y -= particle.speed;
          if (particle.y < -size) {
            particle.y = dimension.height + size;
            particle.x = Math.random() * dimension.width;
          }

          // 添加闪烁效果
          particle.opacity = Math.abs(Math.sin(Date.now() * 0.001 * particle.speed));
        });
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [
    dimension,
    particleColor,
    background,
    minSize,
    maxSize,
    particleDensity,
    particleSpeed
  ]);

  return (
    <div className={className} ref={canvasContainerRef} style={{ position: "relative", width: "100%", height: "100%" }}>
      <canvas
        ref={canvasRef}
        style={{
          position: "absolute",
          inset: 0,
          width: "100%",
          height: "100%",
        }}
      />
    </div>
  );
} 