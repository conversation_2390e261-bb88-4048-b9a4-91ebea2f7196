# 提示词增强功能交互设计

## 1. 系统架构概述

提示词增强功能主要由以下几个部分组成：

1. **前端界面**：Vue组件 `PromptEnhancer.vue`，用户在这里输入初始提示词
2. **WebSocket接口**：`webscoket.py`，负责前后端实时通信
3. **提示词增强代理**：位于`theme_backend/agent`目录下的AI代理，使用AutoGen框架实现，负责增强用户输入的提示词
4. **MCP存储服务**：独立的Model Context Protocol服务，负责会话存储和持久化

### 1.1 theme_backend服务架构

在`theme_backend`应用中，API服务和WebSocket服务是紧密集成的两个部分：

1. **统一应用架构**：
   - API服务和WebSocket服务均是同一个FastAPI应用的组成部分
   - 共享同一个端口、同一个进程和同一个应用上下文
   - 分别通过不同的路由路径提供服务

2. **协同工作模式**：
   - API服务负责创建会话、接收初始请求和返回结果
   - WebSocket服务负责维持长连接、推送实时更新
   - 共享会话状态和数据存储

3. **架构关系图**：
```
  theme_backend应用
  ┌────────────────────────────────────────────┐
  │                                            │
  │  ┌─────────────┐        ┌───────────────┐  │
  │  │             │        │               │  │
  │  │  API服务    │◄──────►│ WebSocket服务 │  │
  │  │             │  共享  │               │  │
  │  └─────┬───────┘  会话  └───────┬───────┘  │
  │        │          状态          │          │
  │        ▼                       ▼          │
  │  ┌─────────────┐        ┌───────────────┐  │
  │  │ 会话初始化  │        │ 实时数据传输  │  │
  │  └─────────────┘        └───────────────┘  │
  │                                            │
  └────────────────────────────────────────┬───┘
                                          │
                                          ▼
                                  ┌───────────────┐
                                  │               │
                                  │ AutoGen代理   │
                                  │               │
                                  └───────────────┘
```

### 1.2 MCP与WebSocket的关系

MCP（Model Context Protocol）服务与WebSocket服务是两个完全独立的服务，它们之间没有直接关系和交互：

1. **完全独立的服务**：
   - WebSocket服务：负责前端和后端之间的实时通信通道
   - MCP服务：负责会话存储和上下文管理
   - 两者完全解耦，互不依赖，不直接通信

2. **Agent服务作为协调者**：
   - Agent服务同时与WebSocket服务和MCP服务交互
   - 与WebSocket服务交互：发送实时更新消息给前端
   - 与MCP服务交互：存储会话数据和消息历史
   - 两个服务对彼此的存在完全无感知

3. **数据流向**：
   ```
   前端 <---> WebSocket服务 <---> Agent服务 <---> MCP服务 <---> 存储系统(Redis/MongoDB)
           (实时通信通道)      (协调者)       (数据存储)        (临时/永久存储)
   ```

4. **解耦设计的优势**：
   - 服务可以独立扩展和部署
   - 单个服务故障不会完全中断系统功能
   - 便于独立维护和更新
   - 职责明确，降低系统复杂性

## 2. 交互流程

```
┌───────────────┐    1. HTTP请求    ┌─────────────────┐    2. 创建会话    ┌────────────────┐
│               │ ----------------> │                 │ ----------------> │                │
│  前端界面     │                   │   后端API       │                   │  AI代理服务    │
│ (Vue组件)     │ <---------------- │ (FastAPI)       │ <---------------- │  (Agent)       │
│               │  8. 最终结果      │                 │  7. 增强结果      │                │
└───────┬───────┘                   └────────┬────────┘                   └────────────────┘
        │                                    │
        │ 3. 返回会话ID                      │
        │ <----------------------------------┘
        │
        │ 4. 建立WebSocket连接
        │ -----------------------------------> ┌────────────────┐
        │                                      │                │
        │ 5. 实时交互                          │  WebSocket服务 │
        │ <-----------------------------------> │  (webscoket.py)│
        │                                      │                │
        │ 6. 推送思考过程和中间结果            │                │
        │ <----------------------------------- │                │
        │                                      └────────────────┘
```

## 3. 详细流程说明

1. **初始化请求**：
   - 用户在前端输入初始提示词，点击"开始增强"按钮
   - 前端向后端API发送HTTP POST请求，包含原始提示词

2. **创建会话**：
   - 后端API接收请求，创建一个唯一的会话ID
   - 后端启动Agent服务处理任务
   - Agent服务调用MCP服务的`create_session`工具创建新会话记录，包含用户信息(默认admin)
   - MCP服务在Redis中创建临时会话记录

3. **返回会话信息**：
   - 后端API立即返回会话ID给前端
   - 前端使用此会话ID建立WebSocket连接

4. **WebSocket连接**：
   - 前端使用会话ID连接到WebSocket服务
   - WebSocket服务验证会话ID并建立长连接
   - WebSocket服务通知Agent服务开始处理特定会话

5. **实时交互**：
   - Agent开始生成增强提示词
   - 生成过程中的思考步骤、中间结果由Agent记录
   - Agent直接调用MCP服务的`add_message`工具记录到Redis中
   - 同时，Agent通过WebSocket服务推送消息给前端
   - 前端实时显示思考过程和部分生成结果

6. **最终结果**：
   - 增强提示词生成完成后，Agent调用MCP服务的`complete_session`工具将会话标记为完成
   - MCP服务将完整会话从Redis导出到MongoDB进行永久存储
   - Agent通过WebSocket服务发送最终结果给前端
   - 前端显示完整的增强提示词
   - WebSocket连接可以保持活跃或关闭

## 4. 技术实现细节

### 4.1 WebSocket接口 (webscoket.py)

WebSocket接口将基于FastAPI的WebSocket支持实现，主要功能：

- 建立WebSocket连接
- 验证会话ID
- 管理连接生命周期
- 转发AI代理的消息
- 处理心跳和断线重连

#### 4.1.1 端口配置设计

WebSocket服务将与主应用在同一个FastAPI实例上运行，共享相同的端口。这样设计的优势：

1. **简化部署**：不需要额外部署单独的WebSocket服务
2. **共享资源**：可以共享应用上下文、认证中间件等
3. **降低复杂性**：避免跨域、安全配置等问题

配置示例：
```python
# config.py
PORT = 8000  # 应用统一端口
```

通过FastAPI的路由系统，HTTP请求和WebSocket连接将使用相同的端口但不同的路由路径：
- HTTP API: `http://localhost:8000/api/enhance`
- WebSocket: `ws://localhost:8000/api/ws/enhance/{session_id}`

这样设计可以避免端口冲突，简化客户端配置。

### 4.2 提示词增强代理

提示词增强代理将使用AutoGen框架和AI模型来增强提示词，主要功能：

- 接收原始提示词
- 使用AI模型进行提示词增强
- 将思考过程和中间结果实时推送给WebSocket服务
- 返回最终增强结果

#### 4.2.1 AutoGen实现细节

提示词增强代理将基于Microsoft AutoGen v0.4框架实现，以便利用其多代理协作能力和流式输出特性：

1. **Agent设计**：
   - 创建专门的提示词增强Agent，具有特定的提示工程知识
   - 可选择使用多个协作Agent，如创意Agent、格式化Agent等
   - 使用AutoGen的AssistantAgent并启用流式输出

2. **流式输出实现**：
   - 利用AutoGen的`run_stream`方法实现流式生成
   - 实现事件监听器捕获生成过程
   - 将流式输出转发到WebSocket连接

3. **AutoGen Agent代码示例**：
```python
import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.ui import Console

# 定义模型客户端
model_client = OpenAIChatCompletionClient(
    model="gpt-4-turbo"
)

# 创建主要的提示词增强Agent
enhancer_agent = AssistantAgent(
    name="prompt_enhancer",
    model_client=model_client,
    system_message="你是一个专业的提示词增强专家，负责将简短的初始提示词扩展为详细、具体且富有创意的提示词。",
    model_client_stream=True  # 启用流式输出
)

# 创建评审Agent
reviewer_agent = AssistantAgent(
    name="reviewer",
    model_client=model_client,
    system_message="你是一个提示词评审专家，负责评估提示词的质量并提供改进建议。",
    model_client_stream=True
)

# 运行提示词增强流程
async def run_prompt_enhancement(prompt, session_id, websocket_manager):
    # 创建任务
    task = f"增强以下提示词：{prompt}"
    
    # 使用流式输出运行Agent
    async for message in enhancer_agent.run_stream(task=task):
        # 将消息转发到WebSocket
        event_type = "thinking" if "思考" in message or "分析" in message else "content"
        await websocket_manager.send_message(
            session_id,
            {
                "event_type": event_type,
                "data": {
                    "content": message,
                    "is_final": False
                }
            }
        )
    
    # 流式输出完成后，发送最终消息
    await websocket_manager.send_message(
        session_id,
        {
            "event_type": "content",
            "data": {
                "content": enhancer_agent.last_message(),
                "is_final": True
            }
        }
    )
```

4. **多代理协作**：
   - 配置Writer、Editor等不同角色的Agent
   - 使用GroupChat模式实现协作增强提示词
   - 每个Agent的思考和输出都通过事件处理器推送到前端

### 4.3 消息格式

WebSocket消息格式将使用JSON，包含以下字段：

```json
{
  "event_type": "思考|内容|代码|错误",
  "data": {
    "session_id": "会话ID",
    "content": "消息内容",
    "is_final": false
  }
}
```

事件类型说明：
- `thinking`: 思考过程
- `content`: 生成的内容
- `code`: 代码块
- `error`: 错误信息

## 5. 接口设计

### 5.1 HTTP API

**提示词增强初始化**

```
POST /api/enhance
```

请求体：
```json
{
  "prompt": "用户输入的原始提示词"
}
```

响应：
```json
{
  "session_id": "唯一会话ID",
  "status": "processing"
}
```

### 5.2 WebSocket API

**连接URL**：
```
ws://server/api/ws/enhance/{session_id}
```

**事件类型**：

1. 思考事件：
```json
{
  "event_type": "thinking",
  "data": {
    "content": "AI正在思考的内容"
  }
}
```

2. 内容事件：
```json
{
  "event_type": "content",
  "data": {
    "content": "生成的提示词部分内容",
    "is_final": false
  }
}
```

3. 最终结果：
```json
{
  "event_type": "content",
  "data": {
    "content": "完整的增强提示词",
    "is_final": true
  }
}
```

## 6. 异常处理

1. **连接断开**：
   - 前端定期发送心跳包
   - 如果连接断开，前端尝试重新连接
   - 后端保存会话状态，允许在一定时间内重连
   - **页面离开处理**：当用户离开页面时，前端主动关闭WebSocket连接，后端清理相关资源

2. **错误处理**：
   - 如果AI代理遇到错误，通过WebSocket发送错误消息
   - 前端显示错误信息并允许用户重试

3. **Agent任务管理**：
   - WebSocket断开时，优雅地暂停或终止相关Agent任务
   - 使用try-except确保Agent执行过程中的异常被捕获而不会导致系统崩溃
   - 维护Agent任务状态映射表，实时追踪每个任务的状态（运行中、暂停、已完成、已终止）

## 7. 安全考虑

1. **会话验证**：使用会话ID验证WebSocket连接请求
2. **超时处理**：设置生成任务超时时间，防止资源占用
3. **速率限制**：对API请求和WebSocket连接实施速率限制

## 8. 未来扩展

1. **多种增强模型**：支持用户选择不同的增强模型或风格
2. **提示词历史**：保存用户历史提示词及增强结果
3. **个性化设置**：允许用户设置增强参数，如创意度、详细程度等

## 9. AutoGen与WebSocket集成方案

### 9.1 整体架构

```
┌───────────────┐           ┌─────────────────┐           ┌────────────────────┐
│               │           │                 │           │                    │
│  前端Vue组件  │◄─────────►│  WebSocket服务  │◄─────────►│  AutoGen代理系统   │
│               │           │                 │           │                    │
└───────────────┘           └─────────────────┘           └────────────────────┘
```

### 9.2 AutoGen事件流

AutoGen的事件流与WebSocket消息推送的映射关系：

1. **AutoGen事件** → **WebSocket事件**
   - `on_text_delta` → 内容事件 (`content`)
   - `on_run_step_created` → 思考事件 (`thinking`)
   - `on_tool_call` → 代码事件 (`code`)
   - 错误和异常 → 错误事件 (`error`)

2. **消息传递流程**：
   ```
   AutoGen Agent生成内容 → 事件处理器捕获 → WebSocket管理器 → 推送给前端Vue组件
   ```

### 9.3 前端Vue组件处理

前端Vue组件需要处理从WebSocket接收到的不同类型的事件：

1. **思考事件**：显示AI思考过程
2. **内容事件**：实时更新生成内容
3. **代码事件**：格式化显示代码块
4. **错误事件**：显示错误信息

**页面生命周期管理**：
- 在组件挂载时（`onMounted`）请求新的sessionID并建立WebSocket连接
- 在组件卸载时（`onUnmounted`）主动关闭WebSocket连接
- 每次用户重新访问页面时，生成全新的sessionID，确保会话隔离

### 9.4 代码示例

**Frontend: Vue组件生命周期管理**

```javascript
// Vue组件内的生命周期钩子
import { onMounted, onUnmounted, ref } from 'vue';

// 会话状态
const sessionId = ref('');
let wsConnection = null;

// 组件挂载时初始化
onMounted(async () => {
  // 请求新的会话ID
  const response = await fetch(`${apiBaseUrl}/api/enhance/session`, {
    method: 'POST',
  });
  const data = await response.json();
  sessionId.value = data.session_id;
  
  // 建立WebSocket连接
  initWebSocketConnection();
});

// 组件卸载时清理
onUnmounted(() => {
  // 主动关闭WebSocket连接
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    console.log(`断开WebSocket连接，会话ID: ${sessionId.value}`);
    wsConnection.close();
  }
  
  // 通知后端会话结束（可选）
  fetch(`${apiBaseUrl}/api/enhance/session/${sessionId.value}`, {
    method: 'DELETE',
  }).catch(err => console.error('清理会话时出错:', err));
});
```

**Backend: WebSocket连接管理**

```python
# 会话管理
sessions = {}

# WebSocket连接端点
@app.websocket("/api/ws/enhance/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()
    
    # 将WebSocket连接添加到会话管理器
    websocket_manager.connect(websocket, session_id)
    
    try:
        # 处理WebSocket消息
        while True:
            # 接收消息（如心跳检测）
            data = await websocket.receive_text()
            # 处理消息...
            
    except WebSocketDisconnect:
        # 连接断开时清理WebSocket
        websocket_manager.disconnect(session_id)
        
        # 安全停止相关的Agent任务
        await task_manager.stop_task(session_id)
        
        # 更新会话状态
        if session_id in sessions:
            sessions[session_id]["status"] = "disconnected"
            # 可选：设置延迟清理任务
            
    except Exception as e:
        # 处理其他错误
        print(f"WebSocket错误: {e}")
        if session_id in websocket_manager.active_connections:
            websocket_manager.disconnect(session_id)
        
        # 确保任务被停止
        await task_manager.stop_task(session_id)
```

### 9.5 会话生命周期管理

为确保资源合理使用和系统稳定性，会话的生命周期管理至关重要：

1. **会话创建**：
   - 每次页面加载时，前端请求创建新的sessionID
   - 后端生成唯一的会话标识，并预留相关资源

2. **会话活跃**：
   - WebSocket连接建立，代理服务开始工作
   - 前端定期发送心跳包，确认会话活跃

3. **会话终止**：
   - 用户离开页面，前端主动关闭WebSocket连接
   - 后端检测到连接关闭，清理资源
   - 已生成的结果可选择性保留一段时间，允许用户返回查看

4. **资源回收**：
   - 针对异常情况（如浏览器崩溃），设置会话超时机制
   - 定期检查并清理长时间不活跃的会话

### 9.6 Agent任务异常处理

为确保WebSocket断开连接后相关Agent任务不会产生错误，需要实现以下机制：

1. **任务管理器**：
   - 创建中央任务管理器，跟踪所有运行中的Agent任务
   - 每个任务关联到特定的会话ID
   - 允许在WebSocket断开时安全地终止任务

2. **优雅终止机制**：
   - 实现信号机制，在WebSocket断开时向Agent任务发送终止信号
   - 确保任务能够在适当的检查点停止，避免中断导致数据不一致
   - 使用异步取消令牌（cancellation token）模式实现可中断的任务

3. **错误隔离**：
   - 使用任务包装器，确保Agent任务的错误不会传播到WebSocket处理逻辑
   - 实现全局异常处理机制，捕获并记录Agent执行过程中的所有异常
   - 避免在日志中暴露敏感信息

**任务管理器代码示例**：

```python
# Agent任务管理器
class AgentTaskManager:
    def __init__(self):
        self.tasks = {}  # session_id -> task
        self.lock = asyncio.Lock()
    
    async def start_task(self, session_id, prompt):
        """启动与会话关联的Agent任务"""
        async with self.lock:
            # 创建可取消的任务
            task = asyncio.create_task(self._run_agent(session_id, prompt))
            self.tasks[session_id] = {
                "task": task,
                "status": "running",
                "start_time": time.time(),
                "prompt": prompt
            }
            return session_id
    
    async def stop_task(self, session_id):
        """安全停止任务"""
        async with self.lock:
            if session_id in self.tasks and self.tasks[session_id]["status"] == "running":
                # 更新状态
                self.tasks[session_id]["status"] = "stopping"
                
                try:
                    # 取消任务
                    task = self.tasks[session_id]["task"]
                    task.cancel()
                    
                    # 等待任务完成取消（有一个短暂的超时）
                    try:
                        await asyncio.wait_for(task, timeout=2.0)
                    except asyncio.TimeoutError:
                        print(f"任务 {session_id} 未能在超时内完成取消")
                    except asyncio.CancelledError:
                        print(f"任务 {session_id} 已成功取消")
                    
                    self.tasks[session_id]["status"] = "stopped"
                    print(f"已停止任务 {session_id}")
                except Exception as e:
                    print(f"停止任务 {session_id} 时出错: {e}")
                    # 即使出错，我们也将状态标记为stopped
                    self.tasks[session_id]["status"] = "error"
    
    async def _run_agent(self, session_id, prompt):
        """运行Agent的包装函数，包含异常处理"""
        try:
            # 创建事件处理器
            event_handler = PromptEnhancerEventHandler(websocket_manager, session_id)
            
            # 创建Agent并运行
            client = AsyncClient()
            # ... Agent初始化代码 ...
            
            # 检查取消
            while not asyncio.current_task().cancelled():
                # 执行Agent的一个步骤
                # ... Agent执行代码 ...
                
                # 模拟一些工作
                await asyncio.sleep(0.1)
            
            return "任务完成"
        except asyncio.CancelledError:
            # 正常取消，清理资源
            print(f"Agent任务 {session_id} 被取消")
            # ... 清理代码 ...
            raise  # 重新抛出以便调用者知道任务被取消
        except Exception as e:
            # 捕获所有其他异常
            print(f"Agent任务 {session_id} 执行出错: {e}")
            # 记录详细错误信息供调试
            import traceback
            traceback.print_exc()
            # 尝试通知前端（如果WebSocket还在）
            try:
                await websocket_manager.send_message(
                    session_id,
                    {
                        "event_type": "error",
                        "data": {
                            "message": f"处理过程中出错: {str(e)}",
                            "is_final": True
                        }
                    }
                )
            except:
                # 忽略发送错误的错误
                pass
            return None
```

**WebSocket连接处理与任务集成**：

```python
# 全局任务管理器
task_manager = AgentTaskManager()

# WebSocket连接端点
@app.websocket("/api/ws/enhance/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket.accept()
    
    # 将WebSocket连接添加到会话管理器
    websocket_manager.connect(websocket, session_id)
    
    try:
        # 处理WebSocket消息
        while True:
            # 接收消息（如心跳检测）
            data = await websocket.receive_text()
            # 处理消息...
            
    except WebSocketDisconnect:
        # 连接断开时清理WebSocket
        websocket_manager.disconnect(session_id)
        
        # 安全停止相关的Agent任务
        await task_manager.stop_task(session_id)
        
        # 更新会话状态
        if session_id in sessions:
            sessions[session_id]["status"] = "disconnected"
            # 可选：设置延迟清理任务
            
    except Exception as e:
        # 处理其他错误
        print(f"WebSocket错误: {e}")
        if session_id in websocket_manager.active_connections:
            websocket_manager.disconnect(session_id)
        
        # 确保任务被停止
        await task_manager.stop_task(session_id)
```

### 9.6 AutoGen与WebSocket集成方案

AutoGen与WebSocket的集成方案主要涉及以下几个方面：

1. **AutoGen事件流与WebSocket消息推送的映射关系**：
   - `on_text_delta` → 内容事件 (`content`)
   - `on_run_step_created` → 思考事件 (`thinking`)
   - `on_tool_call` → 代码事件 (`code`)
   - 错误和异常 → 错误事件 (`error`)

2. **消息传递流程**：
   ```
   AutoGen Agent生成内容 → 事件处理器捕获 → WebSocket管理器 → 推送给前端Vue组件
   ```

3. **前端Vue组件处理**：
   - 实现思考事件、内容事件、代码事件和错误事件的逻辑处理
   - 实现页面生命周期管理，确保会话隔离和资源回收

4. **WebSocket连接管理**：
   - 实现WebSocket连接的建立、验证和断开处理
   - 实现WebSocket消息的接收和处理

5. **AutoGen与WebSocket集成方案**：
   - 实现AutoGen与WebSocket的集成，确保AutoGen生成内容能够通过WebSocket推送给前端
   - 实现AutoGen与WebSocket的集成，确保AutoGen生成内容能够通过WebSocket推送给前端

## 10. 系统部署和配置

### 10.1 端口配置和服务设计

本系统采用单一应用实例部署模式，HTTP API和WebSocket服务共享同一个FastAPI应用实例和端口：

1. **统一端口设计**：
   - 应用统一使用单一端口（默认8000）
   - WebSocket和HTTP API共享此端口

2. **路由区分**：
   - HTTP API路由：`/api/enhance`
   - WebSocket路由：`/api/ws/enhance/{session_id}`

3. **配置示例**：
```python
# main.py
import uvicorn
from fastapi import FastAPI
from app.config import PORT

app = FastAPI()

# 引入路由
from app.core.websocket import router as ws_router
from app.api.enhance import router as enhance_router

app.include_router(ws_router)
app.include_router(enhance_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=PORT)
```

4. **环境变量配置**：
```
# .env
PORT=8000
```

这种设计可以避免端口冲突，简化部署和维护，同时确保前端配置的一致性。

### 10.2 AutoGen配置和依赖

提示词增强代理使用AutoGen v0.4版本实现，需要安装以下依赖：

```
# requirements.txt中添加
autogen-agentchat>=0.4.0
autogen-core>=0.4.0
autogen-ext>=0.4.0
```

AutoGen配置示例：

```python
# config/agent_config.py

# AutoGen模型配置
AUTOGEN_CONFIG = {
    "model": "gpt-4-turbo",
    "temperature": 0.7,
    "max_tokens": 4000
}

# AutoGen Agent系统消息
PROMPT_ENHANCER_SYSTEM = """
你是一个专业的提示词增强专家，擅长将简短的初始提示词扩展为详细、具体且富有创意的提示词。
你的任务是分析用户的初始提示词，并从以下几个方面增强它：
1. 场景和环境描述
2. 风格和氛围
3. 技术细节和视觉效果
4. 创意元素和独特视角

请保持原始提示词的核心意图不变，只是让它变得更加详细和专业。
"""
```

### 10.3 API与WebSocket服务关系

在`theme_backend`中，API服务和WebSocket服务是协同工作的两个组件，它们之间的关系如下：

1. **组件关系**：
   - 两者都是`FastAPI`应用的路由组件
   - API路由处理HTTP请求，WebSocket路由处理WebSocket连接
   - 共享应用的全局资源和中间件

2. **数据流转**：
   - API服务通过`POST /api/enhance`接收用户初始提示词，创建会话ID
   - 会话ID通过HTTP响应返回给前端
   - 前端使用会话ID通过`ws://server/api/ws/enhance/{session_id}`连接WebSocket服务
   - WebSocket服务验证会话ID并建立长连接
   - API服务启动后台任务，由WebSocket服务推送结果

3. **代码组织**：
```
theme_backend/
├── app/
│   ├── core/
│   │   ├── webscoket.py    # WebSocket服务实现
│   │   └── config.py       # 共享配置
│   ├── api/
│   │   └── enhance.py      # API服务实现
│   └── main.py             # 应用入口，聚合所有路由
└── agent/
    └── enhancer.py         # AutoGen代理实现
```

4. **流程示例**：
```python
# app/api/enhance.py - API路由
@router.post("/enhance")
async def enhance_prompt(request: EnhanceRequest):
    # 创建会话
    session_id = str(uuid.uuid4())
    sessions[session_id] = {
        "id": session_id,
        "prompt": request.prompt,
        "status": "created",
        "created_at": time.time()
    }
    
    # 返回会话ID给前端，前端使用此ID连接WebSocket
    return {
        "session_id": session_id,
        "status": "processing"
    }

# app/core/webscoket.py - WebSocket路由
@router.websocket("/enhance/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    # 验证会话并建立连接
    if session_id not in sessions:
        await websocket.close(code=1008)
        return
    
    await websocket_manager.connect(session_id, websocket)
    
    # 启动代理任务
    asyncio.create_task(
        task_manager.start_task(
            session_id, 
            sessions[session_id]["prompt"]
        )
    )
    
    # ... 处理WebSocket消息 ...
```

这种设计确保了API服务和WebSocket服务能够无缝协作，共享会话状态和资源，同时保持各自职责的清晰分离。

## 11. 会话存储与持久化

### 11.1 存储架构

提示词增强功能采用双层存储架构，通过MCP服务进行统一管理：

1. **临时存储（Redis）**：
   - 存储活跃会话的状态和消息
   - 支持高速读写和实时更新
   - 设置自动过期机制（默认24小时）

2. **永久存储（MongoDB）**：
   - 存储已完成会话的完整记录
   - 支持历史查询和数据分析
   - 包含详细的会话元数据和用户信息

### 11.2 用户关联

每个会话都关联到特定用户，当前设计中：

1. **用户标识**：
   - 默认用户ID为"admin"
   - 预留字段，以支持后续实现完整的用户认证系统

2. **用户信息存储**：
   - 临时存储：Redis中`session:{session_id}`哈希表包含`user_id`字段
   - 永久存储：MongoDB中会话文档包含用户标识和相关元数据

### 11.3 MCP服务调用流程

Agent服务与MCP服务的交互流程（注意WebSocket服务和MCP服务之间没有直接交互）：

```
独立服务架构
┌────────────────┐                              ┌────────────────┐
│                │                              │                │
│  WebSocket服务 │                              │    MCP服务     │
│                │                              │                │
└───────┬────────┘                              └────────┬───────┘
        │                                                │
        │ (1)消息推送                                     │ (2)数据存储
        │                                                │
        ▼                                                ▼
┌────────────────────────────────────────────────────────────────┐
│                                                                │
│                         Agent服务                              │
│                                                                │
└────────────────────────────────────────────────────────────────┘
        │                                                │
        │ (3)用户界面更新                                 │ (4)持久化
        ▼                                                ▼
┌────────────────┐                              ┌────────────────┐
│                │                              │                │
│  前端Vue组件   │                              │ 存储系统       │
│                │                              │(Redis/MongoDB) │
└────────────────┘                              └────────────────┘
```

在上图中，WebSocket服务和MCP服务是完全分离的，它们仅与Agent服务交互，Agent服务充当协调者的角色。

### 11.4 数据模型

1. **Redis中的会话结构**：
```json
{
  "session:{session_id}": {
    "user_id": "admin",  // 用户ID，默认为admin
    "prompt": "原始提示词",
    "status": "running|completed|error",
    "created_at": 1623456789,
    "last_updated": 1623456999
  },
  
  "session:{session_id}:messages": [
    // 消息序列
  ]
}
```

2. **MongoDB中的会话文档**：
```json
{
  "session_id": "唯一会话ID",
  "user_id": "admin",  // 用户ID，默认为admin
  "prompt": "原始提示词",
  "result": "最终增强提示词",
  "status": "completed|error",
  "created_at": "时间戳",
  "completed_at": "时间戳",
  "duration_ms": 120000,
  "messages": [
    // 完整消息历史
  ],
  "metadata": {
    // 会话元数据
  }
}
```

### 11.5 Agent服务中的MCP调用示例

```python
# Agent服务中的MCP调用示例
class PromptEnhancerAgent:
    def __init__(self, websocket_manager):
        self.mcp_client = MCPClient(MCP_SERVER_URL)
        self.websocket_manager = websocket_manager
    
    async def enhance_prompt(self, session_id, prompt, user_id="admin"):
        # 创建会话
        result = await self.mcp_client.call_tool(
            "create_session",
            {
                "user_id": user_id,
                "prompt": prompt,
                "metadata": {
                    "source": "web",
                    "type": "prompt_enhancement"
                }
            }
        )
        
        # 开始生成增强提示词
        try:
            # 记录思考过程并发送到前端
            await self._think_and_record(session_id, "分析提示词结构和要点...")
            await self._think_and_record(session_id, "确定增强方向...")
            
            # 模拟生成过程
            enhanced_prompt = f"增强版: {prompt}\n\n更详细的描述和更丰富的细节..."
            
            # 记录最终结果
            await self.mcp_client.call_tool(
                "add_message",
                {
                    "session_id": session_id,
                    "event_type": "content",
                    "content": enhanced_prompt,
                    "is_final": True
                }
            )
            
            # 完成会话
            await self.mcp_client.call_tool(
                "complete_session",
                {
                    "session_id": session_id,
                    "result": enhanced_prompt,
                    "status": "completed"
                }
            )
            
            # 通过WebSocket发送结果
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "content",
                    "data": {
                        "content": enhanced_prompt,
                        "is_final": True
                    }
                }
            )
            
            return enhanced_prompt
            
        except Exception as e:
            # 记录错误
            await self.mcp_client.call_tool(
                "add_message",
                {
                    "session_id": session_id,
                    "event_type": "error",
                    "content": str(e)
                }
            )
            
            # 标记会话为错误状态
            await self.mcp_client.call_tool(
                "complete_session",
                {
                    "session_id": session_id,
                    "status": "error"
                }
            )
            
            # 通过WebSocket发送错误
            await self.websocket_manager.send_message(
                session_id,
                {
                    "event_type": "error",
                    "data": {
                        "content": f"处理过程中出错: {str(e)}",
                        "is_final": True
                    }
                }
            )
            
            raise
    
    async def _think_and_record(self, session_id, thought):
        """记录思考过程并通过WebSocket发送"""
        await self.mcp_client.call_tool(
            "add_message",
            {
                "session_id": session_id,
                "event_type": "thinking",
                "content": thought
            }
        )
        
        await self.websocket_manager.send_message(
            session_id,
            {
                "event_type": "thinking",
                "data": {
                    "content": thought,
                    "is_final": False
                }
            }
        )
```
