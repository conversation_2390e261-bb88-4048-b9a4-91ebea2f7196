<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">资源列表</h1>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in">
        <GlassCard
            v-if="taskType === 'image'"
            v-for="theme in themes"
            :key="theme.id"
            :title="theme.query"
            :description="theme.task_id"
        >
          <template #icon>
            <div class="previews-grid wallpaper-grid">
              <!-- 添加玻璃反光效果的卡片 -->
              <div>
                <div class="preview-img-container" @click="openImageViewer(`${theme.presigned_url}`)">
                  <img :src="`${theme.presigned_url}`" class="preview-img" />
                </div>
              </div>
            </div>
          </template>
        </GlassCard>
        <GlassCard
            v-if="taskType === 'theme'"
            v-for="theme in themes"
            :key="theme.id"
            :title="theme.query"
            :description="theme.task_id"
            buttonText="查看详情"
            color="secondary"
            @click="$router.push(`/theme-preview/${theme.task_id}`)"
        >
          <template #icon>
            <div class="previews-grid wallpaper-grid">
              <!-- 添加玻璃反光效果的卡片 -->
              <div>
                <div class="preview-img-container">
                  <img :src="`${theme.presigned_url}`" class="preview-img" />
                </div>
              </div>
            </div>
          </template>
        </GlassCard>
        <GlassCard
            v-if="taskType === 'video'"
            v-for="theme in themes"
            :key="theme.id"
            :title="theme.query"
            :description="theme.task_id"
        >
          <template #icon>
            <div class="previews-grid wallpaper-grid">
              <!-- 添加玻璃反光效果的卡片 -->
              <div>
                <div class="preview-img-container">
                  <video controls class="w-full h-auto" :src="`${theme.presigned_url}`" preload="metadata">
                    您的浏览器不支持视频标签
                  </video>
                </div>
              </div>
            </div>
          </template>
        </GlassCard>
      </div>
      <div class="flex justify-center mt-10">
        <button @click="prevPage" :disabled="currentPage === 1" class="mr-2">上一页</button>
        <span>第 {{ currentPage }} 页</span>
        <button @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
      </div>
      <div class="flex justify-center mt-10">
        <span>总 {{ totalPages }} 页</span><span>共 {{ total }} 条</span>
      </div>
    </div>
    <!-- 图片查看器 -->
    <ImageViewer v-model="showImageViewer" :images="viewerImages" :initialIndex="currentImageIndex" />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from "vue";

const GlassCard = defineAsyncComponent(
  () => import("../components/GlassCard.vue")
);
const GlassPanel = defineAsyncComponent(
  () => import("../components/GlassPanel.vue")
);
const SparkleEffect = defineAsyncComponent(
  () => import("../components/SparkleEffect.vue")
);
const StarCanvasBackground = defineAsyncComponent(
  () => import("../components/StarCanvasBackground.vue")
);

import { ref, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { themeApi } from '@/api/themeApi';
import ImageViewer from "@/components/ImageViewer.vue"; // 假设fetchThemes方法在该路径下
// 图片查看器状态
const showImageViewer = ref(false);
const viewerImages = ref<string[]>([]);
const currentImageIndex = ref(0);

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  viewerImages.value = [imageUrl];
  currentImageIndex.value = 0;
  showImageViewer.value = true;
};
// 获取路由参数
const router = useRouter();
const taskType = router.currentRoute.value.query.taskType;

const themes = ref([]);
const currentPage = ref(1);
const pageSize = ref(9);
const totalPages = ref(0);
const total = ref(0);

const loadThemes = async () => {
  const response = await themeApi.getTaskList({page: currentPage.value, size: pageSize.value, taskType: taskType});
  themes.value = response.data;
  totalPages.value = Math.ceil(response.total / pageSize.value);
  total.value = response.total;
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    loadThemes();
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    loadThemes();
  }
};

onMounted(() => {
  loadThemes();
});

</script>

<style scoped>
.title-container {
  position: relative;
  margin-bottom: 1rem;
  animation: float 5s ease-in-out infinite;
}

.title-text {
  position: relative;
  z-index: 2;
  color: white;
  font-weight: 800;
  letter-spacing: 1px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
}

/* 发光效果 - 使用多层伪元素 */
.title-text::before {
  content: "AI智能创作系统";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: #6366f1;
  filter: blur(6px);
  opacity: 0.5;
  animation: pulse 3s ease-in-out infinite;
}

.title-text::after {
  content: "AI智能创作系统";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  color: #8b5cf6;
  filter: blur(12px);
  opacity: 0.3;
  animation: pulse 4s ease-in-out infinite reverse;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    filter: blur(6px);
  }
  50% {
    opacity: 0.8;
    filter: blur(10px);
  }
}

/* 卡片基础样式 */
.glass {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 卡片悬浮效果 */
.glass:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 增强毛玻璃效果 */
.glass-darker {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* SVG图标动画效果 */
.icon-svg {
  transition: transform 0.3s ease;
}

.glass:hover .icon-svg {
  transform: scale(1.1);
}

.icon-path,
.icon-path-delay1,
.icon-path-delay2,
.icon-border {
  stroke-dasharray: 100;
  stroke-dashoffset: 0;
  transition: all 0.5s ease;
}

.glass:hover .icon-path {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease forwards;
}

.glass:hover .icon-path-delay1 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.2s forwards;
}

.glass:hover .icon-path-delay2 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.4s forwards;
}

.glass:hover .icon-border {
  stroke-dashoffset: 100;
  animation: dash 2s ease 0.1s forwards;
}

@keyframes dash {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* 调整描述文字位置，避免被粒子效果遮挡 */
.animate-slide-in {
  position: relative;
  z-index: 10;
}

/* 全屏背景效果容器 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

/* 确保内容在背景上方 */
.container {
  position: relative;
  z-index: 1;
}

/* 标题容器调整 */
.title-container {
  position: relative;
  z-index: 10; /* 确保标题在发光线上方 */
}
</style>