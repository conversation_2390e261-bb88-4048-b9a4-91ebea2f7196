{"name": "shader-template-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/howler": "^2.2.11", "@types/node": "^22.13.14", "@types/three": "^0.162.0", "@vitejs/plugin-vue": "^5.2.3", "typescript": "^5.4.3", "vite": "^5.2.7", "vite-plugin-glsl": "^1.3.0"}, "dependencies": {"dayjs": "^1.11.13", "furina": "^1.0.3", "howler": "^2.2.4", "kokomi.js": "^1.9.99", "lil-gui": "^0.19.2", "lygia": "^1.1.3", "postprocessing": "^6.35.3", "three": "^0.162.0", "uuid": "^11.1.0"}}