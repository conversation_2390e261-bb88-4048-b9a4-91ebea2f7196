/**
 * 这个脚本用于将node_modules中的字体和图标资源复制到项目的本地目录
 * 确保所有资源都是本地化的，而不依赖CDN
 */

const fs = require('fs-extra');
const path = require('path');

console.log('正在复制资源文件到本地目录...');

// 定义源目录和目标目录
const REMIX_ICON_SRC = path.resolve(__dirname, '../node_modules/remixicon/fonts');
const INTER_FONT_SRC = path.resolve(__dirname, '../node_modules/@fontsource/inter');
const FONTS_DEST = path.resolve(__dirname, '../src/assets/fonts');

// 确保目标目录存在
fs.ensureDirSync(FONTS_DEST);

// 复制Remix Icon字体
console.log('复制Remix Icon字体...');
try {
  fs.copySync(REMIX_ICON_SRC, path.join(FONTS_DEST, 'remixicon'), {
    filter: (src) => {
      // 只复制字体文件
      const ext = path.extname(src).toLowerCase();
      return fs.statSync(src).isDirectory() || 
             ['.eot', '.woff', '.woff2', '.ttf', '.svg'].includes(ext);
    }
  });
  console.log('✓ Remix Icon字体复制成功');
} catch (err) {
  console.error('× Remix Icon字体复制失败:', err);
}

// 复制Inter字体
console.log('复制Inter字体...');
try {
  fs.copySync(path.join(INTER_FONT_SRC, 'files'), path.join(FONTS_DEST, 'inter'), {
    filter: (src) => {
      // 只复制字体文件
      const ext = path.extname(src).toLowerCase();
      return fs.statSync(src).isDirectory() || 
             ['.woff', '.woff2'].includes(ext);
    }
  });
  console.log('✓ Inter字体复制成功');
} catch (err) {
  console.error('× Inter字体复制失败:', err);
}

// 创建本地CSS文件
console.log('创建本地字体CSS文件...');

// 创建Remix Icon的本地CSS
try {
  const remixiconCss = `@font-face {
  font-family: "remixicon";
  src: url('../assets/fonts/remixicon/remixicon.eot?t=1690990375672');
  src: url('../assets/fonts/remixicon/remixicon.eot?t=1690990375672#iefix') format('embedded-opentype'),
       url('../assets/fonts/remixicon/remixicon.woff2?t=1690990375672') format('woff2'),
       url('../assets/fonts/remixicon/remixicon.woff?t=1690990375672') format('woff'),
       url('../assets/fonts/remixicon/remixicon.ttf?t=1690990375672') format('truetype'),
       url('../assets/fonts/remixicon/remixicon.svg?t=1690990375672#remixicon') format('svg');
  font-display: swap;
}

[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`;

  fs.writeFileSync(path.resolve(__dirname, '../src/styles/remixicon.css'), remixiconCss);
  console.log('✓ 本地Remix Icon CSS创建成功');
} catch (err) {
  console.error('× 本地Remix Icon CSS创建失败:', err);
}

// 创建Inter字体的本地CSS
try {
  const interCss = `/* inter-400normal - latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: local(''),
       url('../assets/fonts/inter/inter-latin-400-normal.woff2') format('woff2'),
       url('../assets/fonts/inter/inter-latin-400-normal.woff') format('woff');
}

/* inter-500normal - latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  font-weight: 500;
  src: local(''),
       url('../assets/fonts/inter/inter-latin-500-normal.woff2') format('woff2'),
       url('../assets/fonts/inter/inter-latin-500-normal.woff') format('woff');
}

/* inter-600normal - latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  font-weight: 600;
  src: local(''),
       url('../assets/fonts/inter/inter-latin-600-normal.woff2') format('woff2'),
       url('../assets/fonts/inter/inter-latin-600-normal.woff') format('woff');
}

/* inter-700normal - latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  font-weight: 700;
  src: local(''),
       url('../assets/fonts/inter/inter-latin-700-normal.woff2') format('woff2'),
       url('../assets/fonts/inter/inter-latin-700-normal.woff') format('woff');
}`;

  fs.writeFileSync(path.resolve(__dirname, '../src/styles/inter.css'), interCss);
  console.log('✓ 本地Inter CSS创建成功');
} catch (err) {
  console.error('× 本地Inter CSS创建失败:', err);
}

console.log('资源复制完成！'); 