from fastapi import APIRouter, UploadFile, File, Form, HTTPException
import json
import traceback
from .common import logger, load_workflow, upload_image, send_prompt, wait_for_audio
from ..core.minio_util import async_put_file, minioGetPresigned_url  # 假设你有一个minio_client模块来处理MinIO操作

router = APIRouter()
server_name="voice_to_voice_server"
def build_voice_to_voice_workflow(prompt: str, audio_name: str, task_id: str) -> dict:
    """构建语音转语音工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("voice_to_voice.json")

        # 设置提示词
        if "2" not in workflow:
            logger.error("工作流中找不到节点2")
            raise HTTPException(status_code=500, detail="Node 2 not found in workflow")

        workflow["2"]["inputs"]["text"] = prompt
        logger.info(f"设置文本: {prompt}")

        # 设置音频名称
        if "3" not in workflow:
            logger.error("工作流中找不到节点3")
            raise HTTPException(status_code=500, detail="Node 3 not found in workflow")

        workflow["3"]["inputs"]["audio"] = audio_name
        logger.info(f"设置音频名称: {audio_name}")

        return workflow, "5"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
@router.post("/voice-to-voice", summary="语音转换", description="根据上传的文本和音频文件进行语音转换")
async def voice_to_voice(text: str = Form(...),
                         task_id: str = Form(...),
                         audio: UploadFile = File(None),
                         audio_name: str = Form(None)):
    try:
        logger.info(f"\n========== 开始语音转换 ==========")
        logger.info(f"文本: {text}")
        logger.info(f"任务ID: {task_id}")
        logger.info(f"音频名称: {audio_name}")

        if audio:
            # 上传文件到远程服务器
            uploaded_filename = await upload_image(server_name, audio.file, "待转换音频")
            logger.info(f"成功上传音频文件，获得文件名称: {uploaded_filename}")
            audio_name = uploaded_filename

        if not audio_name:
            logger.error("没有提供音频文件或音频名称")
            raise HTTPException(status_code=400, detail="Audio file or audio name is required")

        # 构建工作流
        workflow, output_node_id = build_voice_to_voice_workflow(
            text,
            audio_name,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待音频文件生成
        audio_file_path = await wait_for_audio(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的音频文件路径: {audio_file_path}")

        # # 使用MinIO上传音频文件
        # minio_filename = await upload_to_minio(audio_file_path)
        # logger.info(f"成功上传到MinIO，获得文件名称: {minio_filename}")
        #
        # # 获取MinIO预览地址
        # minio_url = await get_minio_url(minio_filename)
        # logger.info(f"MinIO预览地址: {minio_url}")

        return {
            "prompt_id": data["prompt_id"],
            "audio_url": audio_file_path
        }
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用voice_to_voice接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
