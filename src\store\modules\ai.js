// AI服务Store - 智能内容生成和场景分析
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAiStore = defineStore('ai', () => {
  // === 状态 ===
  const isProcessing = ref(false)
  const currentTask = ref(null)
  const taskQueue = ref([])
  const generatedContent = ref([])
  const error = ref(null)
  
  // AI服务配置
  const aiConfig = ref({
    baseUrl: 'http://localhost:8000', // 后端API地址
    timeout: 30000,
    retryAttempts: 3
  })

  // 场景分析结果
  const sceneAnalysis = ref({
    timeOfDay: 'morning', // morning, afternoon, evening, night
    weather: 'sunny', // sunny, cloudy, rainy, snowy
    location: 'city', // city, highway, rural, parking
    drivingMode: 'normal', // normal, sport, eco, comfort
    passengers: 1,
    mood: 'neutral' // happy, neutral, focused, relaxed
  })

  // 推荐的界面布局
  const recommendedLayout = ref(null)

  // === 计算属性 ===
  const isIdle = computed(() => !isProcessing.value && taskQueue.value.length === 0)
  
  const queueLength = computed(() => taskQueue.value.length)
  
  const recentContent = computed(() => {
    return generatedContent.value.slice(-10) // 最近10个生成内容
  })

  // === 方法 ===
  const analyzeCurrentScene = async () => {
    isProcessing.value = true
    error.value = null
    
    try {
      // 模拟场景分析
      const analysis = await simulateSceneAnalysis()
      sceneAnalysis.value = analysis
      
      // 基于场景分析推荐布局
      const layout = await generateRecommendedLayout(analysis)
      recommendedLayout.value = layout
      
      return analysis
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isProcessing.value = false
    }
  }

  const simulateSceneAnalysis = async () => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const currentHour = new Date().getHours()
    let timeOfDay = 'morning'
    
    if (currentHour >= 6 && currentHour < 12) timeOfDay = 'morning'
    else if (currentHour >= 12 && currentHour < 18) timeOfDay = 'afternoon'
    else if (currentHour >= 18 && currentHour < 22) timeOfDay = 'evening'
    else timeOfDay = 'night'
    
    return {
      timeOfDay,
      weather: ['sunny', 'cloudy', 'rainy'][Math.floor(Math.random() * 3)],
      location: ['city', 'highway', 'rural'][Math.floor(Math.random() * 3)],
      drivingMode: 'normal',
      passengers: Math.floor(Math.random() * 4) + 1,
      mood: ['happy', 'neutral', 'focused', 'relaxed'][Math.floor(Math.random() * 4)]
    }
  }

  const generateRecommendedLayout = async (scene) => {
    // 基于场景生成推荐布局
    const layouts = {
      morning: {
        priority: ['weather-card', 'navigation-card', 'media-card'],
        theme: 'serenity',
        components: [
          { id: 'weather-card', visible: true, priority: 1 },
          { id: 'navigation-card', visible: true, priority: 2 },
          { id: 'calendar-card', visible: true, priority: 3 },
          { id: 'media-card', visible: false, priority: 4 }
        ]
      },
      afternoon: {
        priority: ['navigation-card', 'traffic-card', 'weather-card'],
        theme: 'glassmorphism',
        components: [
          { id: 'navigation-card', visible: true, priority: 1 },
          { id: 'traffic-card', visible: true, priority: 2 },
          { id: 'weather-card', visible: true, priority: 3 },
          { id: 'media-card', visible: true, priority: 4 }
        ]
      },
      evening: {
        priority: ['media-card', 'navigation-card', 'restaurant-card'],
        theme: 'cyberpunk',
        components: [
          { id: 'media-card', visible: true, priority: 1 },
          { id: 'navigation-card', visible: true, priority: 2 },
          { id: 'restaurant-card', visible: true, priority: 3 },
          { id: 'weather-card', visible: false, priority: 4 }
        ]
      },
      night: {
        priority: ['navigation-card', 'media-card', 'hotel-card'],
        theme: 'glassmorphism',
        components: [
          { id: 'navigation-card', visible: true, priority: 1 },
          { id: 'media-card', visible: true, priority: 2 },
          { id: 'hotel-card', visible: true, priority: 3 },
          { id: 'weather-card', visible: false, priority: 4 }
        ]
      }
    }
    
    return layouts[scene.timeOfDay] || layouts.morning
  }

  const generateContent = async (type, prompt, options = {}) => {
    const taskId = `task-${Date.now()}`
    
    const task = {
      id: taskId,
      type, // 'text-to-image', 'text-to-video', 'layout', 'theme'
      prompt,
      options,
      status: 'pending',
      createdAt: new Date().toISOString(),
      progress: 0
    }
    
    taskQueue.value.push(task)
    
    try {
      const result = await processTask(task)
      
      // 添加到生成内容历史
      generatedContent.value.push({
        ...task,
        result,
        completedAt: new Date().toISOString(),
        status: 'completed'
      })
      
      return result
    } catch (err) {
      task.status = 'failed'
      task.error = err.message
      throw err
    } finally {
      // 从队列中移除任务
      const index = taskQueue.value.findIndex(t => t.id === taskId)
      if (index > -1) {
        taskQueue.value.splice(index, 1)
      }
    }
  }

  const processTask = async (task) => {
    isProcessing.value = true
    currentTask.value = task
    
    try {
      switch (task.type) {
        case 'text-to-image':
          return await generateImage(task.prompt, task.options)
        case 'text-to-video':
          return await generateVideo(task.prompt, task.options)
        case 'layout':
          return await generateLayout(task.prompt, task.options)
        case 'theme':
          return await generateTheme(task.prompt, task.options)
        default:
          throw new Error(`Unknown task type: ${task.type}`)
      }
    } finally {
      isProcessing.value = false
      currentTask.value = null
    }
  }

  const generateImage = async (prompt, options) => {
    // 模拟图像生成
    await simulateProgress()
    
    return {
      type: 'image',
      url: `https://picsum.photos/800/600?random=${Date.now()}`,
      prompt,
      metadata: {
        width: 800,
        height: 600,
        format: 'jpg',
        ...options
      }
    }
  }

  const generateVideo = async (prompt, options) => {
    // 模拟视频生成
    await simulateProgress()
    
    return {
      type: 'video',
      url: `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`,
      prompt,
      metadata: {
        duration: 10,
        width: 1280,
        height: 720,
        format: 'mp4',
        ...options
      }
    }
  }

  const generateLayout = async (prompt, options) => {
    // 模拟布局生成
    await simulateProgress()
    
    return {
      type: 'layout',
      components: [
        { id: 'weather-card', position: { column: 1, row: 2, colspan: 2, rowspan: 2 } },
        { id: 'navigation-card', position: { column: 3, row: 2, colspan: 4, rowspan: 2 } },
        { id: 'media-card', position: { column: 7, row: 2, colspan: 2, rowspan: 1 } }
      ],
      prompt,
      metadata: {
        gridColumns: 8,
        gridRows: 4,
        ...options
      }
    }
  }

  const generateTheme = async (prompt, options) => {
    // 模拟主题生成
    await simulateProgress()
    
    return {
      type: 'theme',
      colors: {
        primary: '#007aff',
        secondary: '#5856d6',
        accent: '#ff9500'
      },
      prompt,
      metadata: {
        style: 'modern',
        mood: 'energetic',
        ...options
      }
    }
  }

  const simulateProgress = async () => {
    const steps = 10
    for (let i = 0; i <= steps; i++) {
      if (currentTask.value) {
        currentTask.value.progress = (i / steps) * 100
      }
      await new Promise(resolve => setTimeout(resolve, 200))
    }
  }

  const cancelTask = (taskId) => {
    const index = taskQueue.value.findIndex(t => t.id === taskId)
    if (index > -1) {
      taskQueue.value.splice(index, 1)
    }
    
    if (currentTask.value?.id === taskId) {
      currentTask.value = null
      isProcessing.value = false
    }
  }

  const clearHistory = () => {
    generatedContent.value = []
    saveContentHistory()
  }

  const saveContentHistory = () => {
    try {
      // 只保存最近的50个内容
      const recentHistory = generatedContent.value.slice(-50)
      localStorage.setItem('ai-hmi-content-history', JSON.stringify(recentHistory))
    } catch (error) {
      console.warn('Failed to save content history:', error)
    }
  }

  const loadContentHistory = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-content-history')
      if (saved) {
        generatedContent.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('Failed to load content history:', error)
    }
  }

  const updateAiConfig = (newConfig) => {
    aiConfig.value = { ...aiConfig.value, ...newConfig }
    saveAiConfig()
  }

  const saveAiConfig = () => {
    try {
      localStorage.setItem('ai-hmi-ai-config', JSON.stringify(aiConfig.value))
    } catch (error) {
      console.warn('Failed to save AI config:', error)
    }
  }

  const loadAiConfig = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-ai-config')
      if (saved) {
        aiConfig.value = { ...aiConfig.value, ...JSON.parse(saved) }
      }
    } catch (error) {
      console.warn('Failed to load AI config:', error)
    }
  }

  const initializeAi = () => {
    loadAiConfig()
    loadContentHistory()
    
    // 启动时进行场景分析
    analyzeCurrentScene()
  }

  return {
    // 状态
    isProcessing,
    currentTask,
    taskQueue,
    generatedContent,
    error,
    aiConfig,
    sceneAnalysis,
    recommendedLayout,
    
    // 计算属性
    isIdle,
    queueLength,
    recentContent,
    
    // 方法
    analyzeCurrentScene,
    generateContent,
    cancelTask,
    clearHistory,
    updateAiConfig,
    initializeAi
  }
})
