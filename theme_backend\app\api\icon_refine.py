from fastapi import APIRouter, UploadFile, HTTPException
import json
import os
from datetime import datetime
import uuid
import aiofiles
import httpx
import traceback
from pathlib import Path
import random
import logging
from .common import (
    wait_for_image,
    upload_image,
    load_workflow,
    send_prompt,
    logger
)

# 基础路径常量
COMFYUI_OUTPUT_BASE = "/ssd2/ComfyUI/output"
SOURCE_ICON_BASE = "/ssd2/changan/theme/main_line/source_icon/list"

router = APIRouter()
server_name="theme_icon_refine_server"
def generate_seed() -> int:
    """生成10位随机整数作为种子"""
    return random.randint(1000000000, 9999999999)

def build_release_path(task_id: str) -> str:
    """构建发布路径（相对路径，用于67节点）"""
    return f"changan/mainLine_{task_id}/releases/icon/icon"

def build_icon_refine_workflow(style: str, index: int, task_id: str) -> dict:
    """构建图标细化工作流"""
    try:
        # 加载工作流
        workflow_file = f"icon_{style}.json"
        workflow = load_workflow(workflow_file)
        
        if style == "dunhuang":

            if "35" not in workflow:
                logger.error("工作流中找不到节点35")
                raise HTTPException(status_code=500, detail="Node 35 not found in workflow")

            # 设置风格图标路径和索引
            workflow["35"]["inputs"]["file_path"] = SOURCE_ICON_BASE
            workflow["35"]["inputs"]["index_variable"] = index
            logger.info(f"设置源图标索引: {index}")

            if "36" not in workflow:
                logger.error("工作流中找不到节点36")
                raise HTTPException(status_code=500, detail="Node 36 not found in workflow")

            # 设置风格图标路径和索引
            style_path = f"{COMFYUI_OUTPUT_BASE}/changan/mainLine_{task_id}/styleIcon"
            workflow["36"]["inputs"]["file_path"] = style_path
            workflow["36"]["inputs"]["index_variable"] = index
            logger.info(f"设置风格图标路径: {style_path}, 索引: {index}")

            if "58" not in workflow:
                logger.error("工作流中找不到节点58")
                raise HTTPException(status_code=500, detail="Node 58 not found in workflow")

            seed = generate_seed()
            workflow["58"]["inputs"]["seed"] = seed
            logger.info(f"设置第一个随机种子: {seed}")

            if "11" not in workflow:
                logger.error("工作流中找不到节点11")
                raise HTTPException(status_code=500, detail="Node 11 not found in workflow")

            workflow["11"]["inputs"]["seed"] = seed
            logger.info(f"设置第二个随机种子: {seed}")
            # 设置保存路径
            if "67" not in workflow:
                logger.error("工作流中找不到节点67")
                raise HTTPException(status_code=500, detail="Node 67 not found in workflow")

            release_path = build_release_path(task_id)
            workflow["67"]["inputs"]["filename_prefix"] = release_path
            logger.info(f"设置保存路径: {release_path}")
            return workflow, "67"  # 67是保存图片的节点ID

        if style == "bing":

            if "215" not in workflow:
                logger.error("工作流中找不到节点215")
                raise HTTPException(status_code=500, detail="Node 215 not found in workflow")

            # 设置风格图标路径和索引
            workflow["215"]["inputs"]["file_path"] = SOURCE_ICON_BASE
            workflow["215"]["inputs"]["index_variable"] = index
            logger.info(f"设置源图标索引: {index}")

            if "211" not in workflow:
                logger.error("工作流中找不到节点211")
                raise HTTPException(status_code=500, detail="Node 211 not found in workflow")

            # 设置风格图标路径和索引
            style_path = f"{COMFYUI_OUTPUT_BASE}/changan/mainLine_{task_id}/styleIcon"
            workflow["211"]["inputs"]["file_path"] = style_path
            workflow["211"]["inputs"]["index_variable"] = index
            logger.info(f"设置风格图标路径: {style_path}, 索引: {index}")

            if "47" not in workflow:
                logger.error("工作流中找不到节点47")
                raise HTTPException(status_code=500, detail="Node 47 not found in workflow")

            seed = generate_seed()
            workflow["47"]["inputs"]["noise_seed"] = seed
            logger.info(f"设置第一个随机种子: {seed}")
            # 设置保存路径
            if "210" not in workflow:
                logger.error("工作流中找不到节点210")
                raise HTTPException(status_code=500, detail="Node 210 not found in workflow")

            release_path = build_release_path(task_id)
            workflow["210"]["inputs"]["filename_prefix"] = release_path
            logger.info(f"设置保存路径: {release_path}")
            return workflow, "210"  # 67是保存图片的节点ID

        if style == "jianzhi":
            # 剪纸风格使用新的节点
            if "76" not in workflow:
                logger.error("工作流中找不到节点76")
                raise HTTPException(status_code=500, detail="Node 76 not found in workflow")
            
            # 设置源图标路径和索引
            workflow["76"]["inputs"]["file_path"] = SOURCE_ICON_BASE
            workflow["76"]["inputs"]["index_variable"] = index
            logger.info(f"设置源图标索引: {index}")

            if "79" not in workflow:
                logger.error("工作流中找不到节点79")
                raise HTTPException(status_code=500, detail="Node 79 not found in workflow")
            
            # 设置风格图标路径和索引
            style_path = f"{COMFYUI_OUTPUT_BASE}/changan/mainLine_{task_id}/styleIcon"
            workflow["79"]["inputs"]["file_path"] = style_path
            workflow["79"]["inputs"]["index_variable"] = index
            logger.info(f"设置风格图标路径: {style_path}, 索引: {index}")

            # 剪纸风格使用新的随机种子节点
            if "58" not in workflow:
                logger.error("工作流中找不到节点58")
                raise HTTPException(status_code=500, detail="Node 58 not found in workflow")
            
            seed = generate_seed()
            workflow["58"]["inputs"]["seed"] = seed
            logger.info(f"设置第一个随机种子: {seed}")

            if "11" not in workflow:
                logger.error("工作流中找不到节点11")
                raise HTTPException(status_code=500, detail="Node 11 not found in workflow")
            
            workflow["11"]["inputs"]["seed"] = seed
            logger.info(f"设置第二个随机种子: {seed}")

            # 设置保存路径
            if "67" not in workflow:
                logger.error("工作流中找不到节点67")
                raise HTTPException(status_code=500, detail="Node 67 not found in workflow")
            
            release_path = build_release_path(task_id)
            workflow["67"]["inputs"]["filename_prefix"] = release_path
            logger.info(f"设置保存路径: {release_path}")
            return workflow, "67"  # 67是保存图片的节点ID

        raise HTTPException(status_code=500, detail=f"Error building workflow: style {style}")
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/icon-refine")
async def refine_icon(style: str, index: int, task_id: str):
    try:
        logger.info(f"\n========== 开始处理第 {index} 个图标 ==========")
        logger.info(f"选择的风格: {style}")
        logger.info(f"任务ID: {task_id}")

        if not style or not task_id:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 构建工作流
        workflow, output_node_id = build_icon_refine_workflow(
            style,
            index,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 构建子文件夹路径
        subfolder = f"changan/mainLine_{task_id}/releases/icon"
        
        # 等待图片生成
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{task_id}/releases/icon"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_name = f"icon_{index}.png"
        image_path = save_dir / image_name
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                
                # 构建公开访问URL
                public_url = f"/ui_source/mainLine_{task_id}/releases/icon/{image_name}"
                logger.info(f"保存图片: {public_url}")
            except Exception as e:
                logger.error(f"保存图片失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")

        logger.info(f"========== 第 {index} 个图标处理完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "image_url": public_url
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理图标细化请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e)) 