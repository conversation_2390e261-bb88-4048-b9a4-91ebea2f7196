/* AI HMI 布局系统 - 8x4网格桌面 */

/* === 主桌面容器 === */
.desktop-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  grid-template-rows: auto repeat(var(--grid-rows), 1fr);
  gap: var(--grid-gap);
  padding: var(--spacing-md);
  background: transparent;
  overflow: hidden;
}

/* === 动态岛区域 === */
.dynamic-island {
  grid-column: 1 / -1;
  grid-row: 1;
  height: 44px;
  z-index: var(--z-navigation);
}

/* === 主内容区域 === */
.main-content {
  grid-column: 1 / -1;
  grid-row: 2 / -1;
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  grid-template-rows: repeat(var(--grid-rows), 1fr);
  gap: var(--grid-gap);
  position: relative;
}

/* === 网格项目基础样式 === */
.grid-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-medium);
  transition: all var(--transition-normal);
}

/* === 标准卡片尺寸 === */
.card-1x1 {
  grid-column: span 1;
  grid-row: span 1;
}

.card-2x1 {
  grid-column: span 2;
  grid-row: span 1;
}

.card-2x2 {
  grid-column: span 2;
  grid-row: span 2;
}

.card-4x1 {
  grid-column: span 4;
  grid-row: span 1;
}

.card-4x2 {
  grid-column: span 4;
  grid-row: span 2;
}

.card-4x4 {
  grid-column: span 4;
  grid-row: span 4;
}

.card-8x1 {
  grid-column: span 8;
  grid-row: span 1;
}

/* === VPA数字人定位 === */
.vpa-avatar {
  position: absolute;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  width: calc(2 * (100vw - 2 * var(--spacing-md) - 7 * var(--grid-gap)) / var(--grid-columns));
  height: calc(2 * (100vh - 44px - 2 * var(--spacing-md) - 3 * var(--grid-gap)) / var(--grid-rows));
  z-index: var(--z-vpa);
  pointer-events: none; /* 让数字人不阻挡背景交互 */
}

/* === 临时对话框定位 === */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.dialog-panel {
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

/* === 响应式布局 === */
@media (max-width: 1024px) {
  .desktop-container {
    grid-template-columns: repeat(6, 1fr);
    --grid-columns: 6;
  }
  
  .card-8x1 {
    grid-column: span 6;
  }
  
  .card-4x2,
  .card-4x4 {
    grid-column: span 3;
  }
}

@media (max-width: 768px) {
  .desktop-container {
    grid-template-columns: repeat(4, 1fr);
    --grid-columns: 4;
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .card-8x1,
  .card-4x1,
  .card-4x2,
  .card-4x4 {
    grid-column: span 4;
  }
  
  .card-2x2 {
    grid-column: span 2;
    grid-row: span 2;
  }
}

/* === 布局动画 === */
.grid-item-enter-active,
.grid-item-leave-active {
  transition: all var(--transition-normal);
}

.grid-item-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
}

.grid-item-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(-20px);
}

/* === 拖拽状态 === */
.grid-item.dragging {
  z-index: var(--z-overlay);
  transform: scale(1.05);
  box-shadow: var(--shadow-large);
}

.grid-item.drop-target {
  background: rgba(var(--color-primary), 0.1);
  border: 2px dashed var(--color-primary);
}

/* === 焦点和悬停状态 === */
.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.grid-item:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* === 空状态 === */
.empty-slot {
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-medium);
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
}

.empty-slot:hover {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
}

/* === 加载状态 === */
.grid-item.loading {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.grid-item.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === 错误状态 === */
.grid-item.error {
  border: 2px solid var(--color-error);
  background: rgba(var(--color-error), 0.1);
}

/* === 隐藏状态 === */
.grid-item.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0);
}
