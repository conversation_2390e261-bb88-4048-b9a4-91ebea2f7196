# 长安主题生成服务部署说明

## 1. 环境要求
- Docker 19.03+
- Docker Compose 1.29+

## 2. 部署步骤
docker build -t changan-theme:latest
### 2.1 加载镜像
```bash
docker load -i changan_theme.tar
```

### 2.2 配置环境变量
编辑 .env 文件：
```env
COMFYUI_BASE_URL=http://your-comfyui-host:port
COMFYUI_WS_URL=ws://your-comfyui-host:port/ws
```

### 2.3 启动服务

#### 开发环境启动

使用以下命令启动开发服务器：

```bash
# 正确的启动命令
cd theme_backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

注意：不要使用 `uvicorn main:app` 的形式，这会导致模块导入错误。

#### 生产环境启动

使用Docker Compose启动：

```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 2.4 验证服务
```bash
# 检查服务状态
docker ps | grep changan-theme-backend

# 测试接口
curl http://localhost:18632/api/v1/pipeline \
  -H "Content-Type: application/json" \
  -d '{"prompt": "生成一个中国风的主题"}'
```

## 3. 目录结构
- public/: 生成文件目录
- workApi/: 工作流配置目录

## 4. 常用命令
```bash
# 查看日志
docker logs -f changan-theme-backend

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

## 5. 注意事项
1. 确保 ComfyUI 服务可访问
2. 确保目录权限正确
3. 建议定期清理 public 目录