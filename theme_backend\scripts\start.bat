@echo off
echo Starting ComfyUI Backend Service...

:: 检查Python环境
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in PATH
    exit /b 1
)

:: 检查依赖
python -m pip show uvicorn >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing dependencies...
    pip install -r ../requirements.txt
)

:: 创建必要的目录
if not exist "../public" mkdir "../public"
if not exist "../public/ui_source" mkdir "../public/ui_source"

:: 启动服务
cd ..
echo Starting FastAPI server...
uvicorn app.main:app --reload --port 8000 --host 0.0.0.0 