{"10": {"inputs": {"toggle": true, "mode": "simple", "num_loras": 2, "lora_1_name": "1.5/碎石堆砌_碎石堆砌V1.safetensors", "lora_1_strength": 0.85, "lora_1_model_strength": 1, "lora_1_clip_strength": 1, "lora_2_name": "1.5/金色祥云_金色祥云V1.safetensors", "lora_2_strength": 0.5, "lora_2_model_strength": 1, "lora_2_clip_strength": 1, "lora_3_name": "1.5/国风/哩布独家_UIA中国风壁画_v1.0.safetensors", "lora_3_strength": 1, "lora_3_model_strength": 1, "lora_3_clip_strength": 1, "lora_4_name": "None", "lora_4_strength": 1, "lora_4_model_strength": 1, "lora_4_clip_strength": 1, "lora_5_name": "None", "lora_5_strength": 1, "lora_5_model_strength": 1, "lora_5_clip_strength": 1, "lora_6_name": "None", "lora_6_strength": 1, "lora_6_model_strength": 1, "lora_6_clip_strength": 1, "lora_7_name": "None", "lora_7_strength": 1, "lora_7_model_strength": 1, "lora_7_clip_strength": 1, "lora_8_name": "None", "lora_8_strength": 1, "lora_8_model_strength": 1, "lora_8_clip_strength": 1, "lora_9_name": "None", "lora_9_strength": 1, "lora_9_model_strength": 1, "lora_9_clip_strength": 1, "lora_10_name": "None", "lora_10_strength": 1, "lora_10_model_strength": 1, "lora_10_clip_strength": 1}, "class_type": "easy loraStack", "_meta": {"title": "简易Lora堆"}}, "11": {"inputs": {"steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "image_output": "<PERSON>de", "link_id": 0, "save_prefix": "ComfyUI", "seed": 515808909946780, "pipe": ["12", 0], "model": ["31", 0], "positive": ["12", 4], "negative": ["12", 5], "vae": ["12", 2]}, "class_type": "easy fullkSampler", "_meta": {"title": "简易k采样器（完整版）"}}, "12": {"inputs": {"ckpt_name": "sd1.5/revAnimated_v2Rebirth.safetensors", "config_name": "<PERSON><PERSON><PERSON>", "vae_name": "vae-ft-mse-840000-ema-pruned.ckpt", "clip_skip": -2, "lora_name": "None", "lora_model_strength": 1, "lora_clip_strength": 1, "resolution": "width x height (custom)", "empty_latent_width": 1024, "empty_latent_height": 616, "positive": ["65", 0], "positive_token_normalization": "length+mean", "positive_weight_interpretation": "A1111", "negative": "low quality,watermark,text,bad anatomy,bad hand,extra hands,extra fingers,(extra digit and hands and fingers and legs and arms),(deformed fingers),extra legs,lowres,bad anatomy,bad hands,text,error,missing fingers,fewer digits,cropped,worst quality,low quality,normal quality,jpeg artifacts,signature,watermark,username,blurry", "negative_token_normalization": "length+mean", "negative_weight_interpretation": "A1111", "batch_size": 1, "a1111_prompt_style": true, "optional_lora_stack": ["10", 0], "optional_controlnet_stack": ["21", 0]}, "class_type": "easy fullLoader", "_meta": {"title": "简易加载器 (完整版)"}}, "21": {"inputs": {"toggle": true, "mode": "simple", "num_controlnet": 3, "controlnet_1": "control_v11p_sd15_canny.pth", "controlnet_1_strength": 1, "start_percent_1": 0, "end_percent_1": 1, "scale_soft_weight_1": 1, "controlnet_2": "control_v11f1p_sd15_depth.pth", "controlnet_2_strength": 0.6, "start_percent_2": 0, "end_percent_2": 1, "scale_soft_weight_2": 1, "controlnet_3": "control_v11f1e_sd15_tile.pth", "controlnet_3_strength": 0.8, "start_percent_3": 0, "end_percent_3": 1, "scale_soft_weight_3": 1, "image_1": ["22", 0], "image_2": ["23", 0], "image_3": ["36", 0]}, "class_type": "easy controlnetStack", "_meta": {"title": "简易 ControlNet 堆"}}, "22": {"inputs": {"preprocessor": "CannyEdgePreprocessor", "resolution": 512, "image": ["39", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "23": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 512, "image": ["39", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "31": {"inputs": {"weight": 0.35000000000000003, "weight_type": "strong style transfer", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["12", 1], "ipadapter": ["32", 1], "image": ["33", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "32": {"inputs": {"preset": "PLUS (high strength)", "model": ["12", 1]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "33": {"inputs": {"image": "新中式｜说这些图是AI出的竟然很多人不信……_1_F君的小小世界_来自小红书网页版 (1).jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "35": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "36": {"inputs": {"file_path": "/ssd2/ComfyUI/output/changan/mainLine_29a3fa74f8784400_20250321161305/styleIcon/", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "37": {"inputs": {"detail_range": 8, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "image": ["35", 0]}, "class_type": "LayerMask: RemBgUltra", "_meta": {"title": "LayerMask: RemBgUltra"}}, "38": {"inputs": {"blend_factor": 0.5, "blend_mode": "multiply", "image1": ["35", 0], "image2": ["36", 0]}, "class_type": "ImageBlend", "_meta": {"title": "混合图像"}}, "39": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["37", 0], "layer_image": ["38", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V2", "_meta": {"title": "LayerUtility: ImageBlendAdvance V2"}}, "40": {"inputs": {"image": "icon_huabu3.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "53": {"inputs": {"image": "icon_mask_huabu2.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "54": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["58", 1], "mask": ["53", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "Cut By Mask"}}, "56": {"inputs": {"invert_mask": false, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["40", 0], "layer_image": ["11", 1]}, "class_type": "LayerUtility: ImageBlendAdvance V2", "_meta": {"title": "LayerUtility: ImageBlendAdvance V2"}}, "58": {"inputs": {"steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 0.3, "image_output": "<PERSON>de", "link_id": 0, "save_prefix": "ComfyUI", "seed": 541414488482940, "pipe": ["59", 0], "positive": ["59", 4], "negative": ["59", 5], "latent": ["62", 0], "vae": ["59", 2]}, "class_type": "easy fullkSampler", "_meta": {"title": "简易k采样器（完整版）"}}, "59": {"inputs": {"ckpt_name": "sd1.5/revAnimated_v2Rebirth.safetensors", "config_name": "<PERSON><PERSON><PERSON>", "vae_name": "vae-ft-mse-840000-ema-pruned.ckpt", "clip_skip": -2, "lora_name": "None", "lora_model_strength": 1, "lora_clip_strength": 1, "resolution": "width x height (custom)", "empty_latent_width": 1280, "empty_latent_height": 680, "positive": ["65", 0], "positive_token_normalization": "length+mean", "positive_weight_interpretation": "A1111", "negative": "low quality,watermark,text,bad anatomy,bad hand,extra hands,extra fingers,(extra digit and hands and fingers and legs and arms),(deformed fingers),extra legs,lowres,bad anatomy,bad hands,text,error,missing fingers,fewer digits,cropped,worst quality,low quality,normal quality,jpeg artifacts,signature,watermark,username,blurry", "negative_token_normalization": "length+mean", "negative_weight_interpretation": "A1111", "batch_size": 1, "a1111_prompt_style": true, "optional_controlnet_stack": ["60", 0]}, "class_type": "easy fullLoader", "_meta": {"title": "简易加载器 (完整版)"}}, "60": {"inputs": {"toggle": true, "mode": "simple", "num_controlnet": 1, "controlnet_1": "control_v11f1e_sd15_tile.pth", "controlnet_1_strength": 0.5, "start_percent_1": 0, "end_percent_1": 1, "scale_soft_weight_1": 1, "controlnet_2": "control_v11f1p_sd15_depth.pth", "controlnet_2_strength": 1, "start_percent_2": 0, "end_percent_2": 1, "scale_soft_weight_2": 1, "controlnet_3": "None", "controlnet_3_strength": 1, "start_percent_3": 0, "end_percent_3": 1, "scale_soft_weight_3": 1, "image_1": ["61", 0], "image_2": ["23", 0]}, "class_type": "easy controlnetStack", "_meta": {"title": "简易 ControlNet 堆"}}, "61": {"inputs": {"preprocessor": "TilePreprocessor", "resolution": 512, "image": ["56", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "62": {"inputs": {"pixels": ["56", 0], "vae": ["59", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "65": {"inputs": {"String": "Chinese painting, ink wash, Dunhuang style, big red lantern,3D,red lantern"}, "class_type": "String", "_meta": {"title": "String"}}, "67": {"inputs": {"filename_prefix": "UI/dunhuan/comfyui", "images": ["54", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}