/* 毛玻璃效果全局样式 */

/* 基础毛玻璃效果 */
.glass {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 卡片悬浮效果 */
.glass:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 更深色的毛玻璃效果 */
.glass-darker {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 静态毛玻璃效果 - 没有悬停动画 */
.glass-static {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* 浅色毛玻璃效果 */
.glass-light {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 毛玻璃按钮效果 */
.glass-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  border-radius: 0.5rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  color: white;
  background: rgba(25, 35, 50, 0.4);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.glass-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 7px 15px rgba(0, 0, 0, 0.2), 0 0 5px rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(30, 40, 55, 0.5);
}

.glass-button:active {
  transform: translateY(-1px) scale(0.99);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.glass-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.15) 0%,
    transparent 70%
  );
  transform: translate(-100%, -100%);
  transition: all 0.5s ease;
  z-index: 1;
  opacity: 0;
  border-radius: 50%;
}

.glass-button:hover::before {
  opacity: 1;
  transform: translate(0%, 0%);
}

/* 按钮颜色变体 */
.glass-button-primary {
  background: rgba(99, 102, 241, 0.3);
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.1);
}
.glass-button-primary:hover {
  background: rgba(99, 102, 241, 0.4);
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2), 0 0 10px rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.glass-button-secondary {
  background: rgba(20, 184, 166, 0.3);
  box-shadow: 0 2px 10px rgba(20, 184, 166, 0.1);
}
.glass-button-secondary:hover {
  background: rgba(20, 184, 166, 0.4);
  box-shadow: 0 5px 15px rgba(20, 184, 166, 0.2), 0 0 10px rgba(20, 184, 166, 0.1);
  border: 1px solid rgba(20, 184, 166, 0.3);
}

.glass-button-info {
  background: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.1);
}
.glass-button-info:hover {
  background: rgba(59, 130, 246, 0.4);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2), 0 0 10px rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.glass-button-success {
  background: rgba(34, 197, 94, 0.3);
  box-shadow: 0 2px 10px rgba(34, 197, 94, 0.1);
}
.glass-button-success:hover {
  background: rgba(34, 197, 94, 0.4);
  box-shadow: 0 5px 15px rgba(34, 197, 94, 0.2), 0 0 10px rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.glass-button-warning {
  background: rgba(234, 179, 8, 0.3);
  box-shadow: 0 2px 10px rgba(234, 179, 8, 0.1);
}
.glass-button-warning:hover {
  background: rgba(234, 179, 8, 0.4);
  box-shadow: 0 5px 15px rgba(234, 179, 8, 0.2), 0 0 10px rgba(234, 179, 8, 0.1);
  border: 1px solid rgba(234, 179, 8, 0.3);
}

.glass-button-danger {
  background: rgba(239, 68, 68, 0.3);
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.1);
}
.glass-button-danger:hover {
  background: rgba(239, 68, 68, 0.4);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.2), 0 0 10px rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 修饰类 */
.glass-rounded {
  border-radius: 0.75rem;
}

.glass-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 辅助动画 */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 150%;
  }
}

.glass-animate-shimmer::before {
  animation: shimmer 2s infinite;
} 