<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <!-- 星空背景 -->
    <div class="fullscreen-background">
      <StarCanvasBackground :particleDensity="80" primaryColor="rgba(99, 102, 241, 0.5)" secondaryColor="rgba(59, 130, 246, 0.5)" particleColor="rgba(255, 255, 255, 0.6)" :minSize="0.1" :maxSize="0.7" :speed="0.2" />
    </div>

    <div class="container mx-auto px-4">
      <!-- 添加顶部导航栏 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">AI界面生成器</h1>
      </div>

      <div class="text-center mb-10 relative entrance-animation">
        <h1 class="title-container mb-0">
          <SparkleEffect>
            <span class="text-4xl font-bold text-white title-text advanced-entrance">AI界面生成器</span>
          </SparkleEffect>
        </h1>
        <p class="text-xl text-gray-300 mt-4 fade-in-sequence">
          <span class="inline-block fade-in-item">输入您的界面需求，</span>
          <span class="inline-block fade-in-item">AI将为您生成精美的HTML界面代码</span>
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 左侧输入区域 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.1s' }">
          <GlassPanel class="h-full">
            <div class="p-6 flex flex-col h-full">
              <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                <svg class="w-6 h-6 mr-2 text-primary-400" viewBox="0 0 24 24" fill="none">
                  <path d="M12 4V20M4 12H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>
                界面需求输入
              </h2>

              <div class="flex-grow">
                <textarea v-model="userInput" placeholder="描述您需要的界面，例如：设计一个简洁的待办事项应用界面，包含任务列表和添加任务按钮..." class="w-full h-64 bg-gray-900/50 text-white rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none backdrop-blur-sm border border-gray-700"></textarea>
              </div>

              <div class="flex items-center justify-between mt-4">
                <div class="text-gray-400 text-sm">
                  <span v-if="userInput">已输入 {{ userInput.length }} 字符</span>
                </div>
                <div class="flex space-x-3">
                  <button @click="clearInput" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none">
                      <path d="M19 7L18.1327 19.1425C18.0579 20.1891 17.187 21 16.1378 21H7.86224C6.81296 21 5.94208 20.1891 5.86732 19.1425L5 7M10 11V17M14 11V17M3 7H21M16 7L15.133 4.26604C14.9462 3.71762 14.4317 3.35205 13.8504 3.35205H10.1496C9.56832 3.35205 9.05384 3.71762 8.86695 4.26604L8 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    清空
                  </button>
                  <button @click="generateUI" :disabled="!userInput || isLoading" class="px-6 py-2 bg-primary-600 hover:bg-primary-500 text-white rounded-lg transition-colors duration-200 flex items-center disabled:opacity-50 disabled:cursor-not-allowed">
                    <template v-if="isLoading">
                      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      生成中...
                    </template>
                    <template v-else>
                      <svg class="w-5 h-5 mr-1" viewBox="0 0 24 24" fill="none">
                        <path d="M13 5L21 12M21 12L13 19M21 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      生成界面
                    </template>
                  </button>
                </div>
              </div>

              <!-- 添加"试一试"标签区域 -->
              <div class="mt-4">
                <p class="text-sm text-gray-400 mb-2">试一试：</p>
                <div class="flex flex-wrap gap-2">
                  <button 
                    v-for="(tag, index) in exampleTags" 
                    :key="index"
                    @click="useExampleTag(tag)"
                    class="px-3 py-1 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 rounded-lg transition-colors duration-200 text-sm"
                  >
                    {{ tag }}
                  </button>
                </div>
              </div>
            </div>
          </GlassPanel>
        </div>

        <!-- 右侧预览区域 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.2s' }">
          <GlassPanel class="h-full">
            <div class="p-6 flex flex-col h-full">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-white flex items-center">
                  <svg class="w-6 h-6 mr-2 text-secondary-400" viewBox="0 0 24 24" fill="none">
                    <path d="M9 4.45962C9.91153 4.16968 10.9104 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 10.9104 4.16968 9.91153 4.45962 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                    <path d="M9 10H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                    <path d="M12 7L15 10L12 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  渲染预览
                </h2>

                <div class="flex space-x-2">
                  <button v-if="generatedHtml" @click="copyHtmlCode" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center text-sm">
                    <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none">
                      <path d="M8 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H16C17.1046 21 18 20.1046 18 19V17M8 5V3C8 1.89543 8.89543 1 10 1H18C19.1046 1 20 1.89543 20 3V15C20 16.1046 19.1046 17 18 17H10C8.89543 17 8 16.1046 8 15V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    复制代码
                  </button>

                  <button v-if="generatedHtml" @click="downloadHtml" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center text-sm">
                    <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none">
                      <path d="M12 4V16M12 16L8 12M12 16L16 12M6 20H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    下载HTML
                  </button>
                </div>
              </div>

              <!-- 渲染结果区域 -->
              <div class="flex-grow bg-white dark:bg-gray-800 rounded-lg overflow-hidden relative">
                <!-- 占位内容 -->
                <div v-if="!generatedHtml && !isLoading" class="absolute inset-0 flex flex-col items-center justify-center p-6 text-center">
                  <img src="https://images.unsplash.com/photo-1579403124614-197f69d8187b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="界面设计" class="w-32 h-32 object-cover rounded-lg mb-4 opacity-80">
                  <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">等待生成界面</h3>
                  <p class="text-gray-600 dark:text-gray-400 max-w-md">
                    在左侧输入您的界面需求并点击"生成界面"按钮，AI将为您创建自定义界面。
                  </p>
                </div>

                <!-- 加载动画 -->
                <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                  <div class="relative">
                    <div class="w-20 h-20 border-4 border-primary-200 border-t-primary-500 rounded-full animate-spin"></div>
                    <div class="absolute inset-0 flex items-center justify-center text-primary-500 font-medium">AI</div>
                  </div>
                </div>

                <!-- HTML渲染器 -->
                <html-sandbox v-if="generatedHtml && !isLoading" :html-content="generatedHtml" class="w-full h-full"></html-sandbox>
              </div>

              <div v-if="generatedHtml" class="mt-4">
                <p class="text-sm text-gray-400">生成的界面可以直接复制HTML代码使用，也可以下载为HTML文件。</p>
              </div>
            </div>
          </GlassPanel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent } from 'vue';
import { themeApi } from '../api/themeApi';

// 异步组件导入
const GlassPanel = defineAsyncComponent(() => import('../components/GlassPanel.vue'));
const SparkleEffect = defineAsyncComponent(() => import('../components/SparkleEffect.vue'));
const StarCanvasBackground = defineAsyncComponent(() => import('../components/StarCanvasBackground.vue'));
const HtmlSandbox = defineAsyncComponent(() => import('../components/HtmlSandbox.vue'));

// 响应式状态
const userInput = ref('');
const generatedHtml = ref('');
const isLoading = ref(false);

// 添加示例标签数组
const exampleTags = ref([
  '设计一个简洁的待办事项应用界面，包含任务列表和添加任务按钮',
  '创建一个现代化的登录页面，包含用户名、密码输入框和登录按钮',
  '设计一个产品展示卡片，包含图片、标题、描述和价格'
]);

// 添加使用示例标签的方法
const useExampleTag = (tag) => {
  userInput.value = tag;
  generateUI();
};

// 清空输入
const clearInput = () => {
  userInput.value = '';
  generatedHtml.value = '';
};

// 生成UI界面
const generateUI = async () => {
  if (!userInput.value) return;

  isLoading.value = true;
  try {
    // 调用API生成界面
    const response = await themeApi.textToHtml({
      "model": "Qwen2.5-32B-Instruct-AWQ",
      // "model": "QwQ-32B-AWQ",
      "messages": [
        {
          "role": "user",
          "content": `请根据用户要求生成一个美观的界面HTML代码。\n\n用户要求如下：${userInput.value.trim()}\n\n要求：\n1. 生成完整的HTML代码,不要包含任何其他内容\n2. 使用现代设计风格，确保美观\n3. 代码需要可以直接运行\n4. 响应式设计，适配不同屏幕尺寸`
        }
      ],
      "max_tokens": 2048,
      "temperature": 0.7
    });

    // 解析返回的HTML代码
    if (response.choices && response.choices[0].message && response.choices[0].message.content) {
      const content = response.choices[0].message.content;
      console.log(content);
      // 提取HTML代码部分
      const htmlMatch = content.match(/```html\n([\s\S]*?)\n```/);
      console.log(htmlMatch);
      if (htmlMatch) {
        console.log(htmlMatch[1]);
        generatedHtml.value = htmlMatch[1];
      } else {
        generatedHtml.value = content;
      }

       // 示例HTML代码 - 使用字符串拼接避免模板字符串问题
    const htmlCode = [
      '<!DOCTYPE html>',
      '<html lang="zh-CN">',
      '<head>',
      '  <meta charset="UTF-8">',
      '  <meta name="viewport" content="width=device-width, initial-scale=1.0">',
      '  <title>生成的界面</title>',
      '  <script src="https://cdn.tailwindcss.com"><\/script>',
      '</head>',
      '<body class="bg-gray-100 font-sans">',
      '  <div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4">',
      '    <div class="p-8">',
      '      <div class="flex justify-between items-center mb-6">',
      '        <h1 class="text-xl font-bold text-gray-900">我的任务清单</h1>',
      '        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">今日</span>',
      '      </div>',
      '      <div class="space-y-4">',
      '        <div class="flex items-center p-3 bg-gray-50 rounded-lg">',
      '          <input type="checkbox" class="h-5 w-5 text-blue-600" checked>',
      '          <span class="ml-3 text-gray-500 line-through">完成设计稿</span>',
      '        </div>',
      '        <div class="flex items-center p-3 bg-gray-50 rounded-lg">',
      '          <input type="checkbox" class="h-5 w-5 text-blue-600">',
      '          <span class="ml-3 text-gray-800">回复邮件</span>',
      '        </div>',
      '        <div class="flex items-center p-3 bg-gray-50 rounded-lg">',
      '          <input type="checkbox" class="h-5 w-5 text-blue-600">',
      '          <span class="ml-3 text-gray-800">准备会议材料</span>',
      '        </div>',
      '      </div>',
      '      <div class="mt-6">',
      '        <form class="flex space-x-2">',
      '          <input type="text" placeholder="添加新任务..." class="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">',
      '          <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">',
      '            添加',
      '          </button>',
      '        </form>',
      '      </div>',
      '    </div>',
      '  </div>',
      '</body>',
      '</html>'
    ].join('\n');
    // generatedHtml.value = htmlCode;

    }
  } catch (error) {
    console.error('生成界面失败:', error);
    generatedHtml.value = '';
  } finally {
    isLoading.value = false;
  }
};

// 复制HTML代码
const copyHtmlCode = () => {
  if (!generatedHtml.value) return;

  navigator.clipboard.writeText(generatedHtml.value)
    .then(() => {
      alert('HTML代码已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
    });
};

// 下载HTML文件
const downloadHtml = () => {
  if (!generatedHtml.value) return;

  const blob = new Blob([generatedHtml.value], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');

  a.href = url;
  a.download = 'generated-ui.html';
  document.body.appendChild(a);
  a.click();

  // 清理
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 100);
};
</script>

<style scoped>
.title-container {
  position: relative;
  margin-bottom: 1rem;
  animation: float 5s ease-in-out infinite;
}

.title-text {
  position: relative;
  z-index: 2;
  font-weight: 800;
  letter-spacing: 1px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
}

/* 发光效果 - 使用多层伪元素 */
.title-text::before {
  content: "AI界面生成器";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: #6366f1;
  filter: blur(6px);
  opacity: 0.5;
  animation: pulse 3s ease-in-out infinite;
}

.title-text::after {
  content: "AI界面生成器";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  color: #8b5cf6;
  filter: blur(12px);
  opacity: 0.3;
  animation: pulse 4s ease-in-out infinite reverse;
}

/* 增强版标题进入动画 */
.advanced-entrance {
  display: inline-block;
  opacity: 0;
  transform: perspective(1000px) translateY(-50px) translateZ(-100px)
    rotateX(30deg) scale(0.9);
  filter: blur(8px);
  animation: advanced-entrance 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

@keyframes advanced-entrance {
  0% {
    opacity: 0;
    transform: perspective(1000px) translateY(-50px) translateZ(-100px)
      rotateX(30deg) scale(0.9);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: perspective(1000px) translateY(0) translateZ(0) rotateX(0)
      scale(1);
    filter: blur(0);
  }
}

/* 文本分段淡入效果 */
.fade-in-sequence {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.2rem;
  opacity: 0;
  animation: fade-in 0.5s ease-out 0.8s forwards;
}

.fade-in-item {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-item:nth-child(1) {
  animation: fade-in-slide-up 0.6s ease-out 1s forwards;
}

.fade-in-item:nth-child(2) {
  animation: fade-in-slide-up 0.6s ease-out 1.2s forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-in-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题入场动画 */
.entrance-animation {
  opacity: 0;
  transform: translateY(-30px);
  animation: fadeInDown 1s ease forwards;
  animation-delay: 0.2s;
}

/* 卡片入场动画 */
.card-entrance-animation {
  opacity: 0;
  transform: scale(0.8) translateY(50px);
  animation: cardExpand 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardExpand {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    filter: blur(6px);
  }
  50% {
    opacity: 0.8;
    filter: blur(10px);
  }
}

/* 全屏背景效果容器 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

/* 确保内容在背景上方 */
.container {
  position: relative;
  z-index: 1;
}
</style> 