import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import VideoGeneration from './components/VideoGeneration';
import HomePage from './components/HomePage';

const App: React.FC = () => {
  return (
    <Router>
      <Toaster position="top-right" />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/video-generation" element={<VideoGeneration />} />
      </Routes>
    </Router>
  );
};

export default App; 