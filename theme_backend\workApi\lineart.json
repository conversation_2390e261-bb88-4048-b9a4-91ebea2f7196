{"5": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "6": {"inputs": {"text": ["39", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["31", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "8": {"inputs": {"samples": ["13", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "10": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "11": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "13": {"inputs": {"noise": ["25", 0], "guider": ["22", 0], "sampler": ["16", 0], "sigmas": ["17", 0], "latent_image": ["5", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "16": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "17": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["31", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "22": {"inputs": {"model": ["31", 0], "conditioning": ["37", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "25": {"inputs": {"noise_seed": 422901394566806}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "28": {"inputs": {"lora_name": "flux/FLUX.1-Turbo-Alpha.safetensors", "strength_model": 1.0000000000000002, "strength_clip": 1, "model": ["30", 0], "clip": ["11", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}, "30": {"inputs": {"unet_name": "ReVAnimated_v168动漫通用F.1大模型_v1.0.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "31": {"inputs": {"lora_name": "flux/儿童黑白线条填色画_v1.0.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["28", 0], "clip": ["28", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "加载LoRA"}}, "35": {"inputs": {"prompt": "可爱的小女孩在公园，公园里有摩天轮，粗笔画，黑白线稿", "model_name": "Qwen2.5-32B-Instruct-AWQ", "base_url": "http://************:8007/v1", "api_key": "1", "is_enable": true, "seed": 1924, "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "mini_flux_prompt", "_meta": {"title": "☁️Mini FLUX Prompt Generator"}}, "37": {"inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "39": {"inputs": {"text_1": ["40", 0], "text_3": ["35", 0]}, "class_type": "LayerUtility: Text<PERSON>oin", "_meta": {"title": "LayerUtility: Text<PERSON>oin"}}, "40": {"inputs": {"string": "Black and white linear coloring book"}, "class_type": "Primitive string [Crysto<PERSON>]", "_meta": {"title": "🪛 Primitive string"}}, "41": {"inputs": {"text_0": "Black and white linear coloring book, A cute little girl with pigtails and a smile stands in a park, surrounded by trees and a large Ferris wheel in the background. The scene is rendered in bold, black-and-white line art, emphasizing the simplicity and charm of the moment. The Ferris wheel has distinct, circular shapes with cabins visible, adding a playful element to the illustration. The girl’s dress flutters slightly in the breeze, captured in a few flowing lines. Trees are depicted with simple, leafy branches, creating a serene and whimsical atmosphere. The overall style is minimalist yet expressive, focusing on the joy and innocence of childhood.", "text": ["39", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "47": {"inputs": {"custom_path": "", "filename_prefix": "蛇年数据集/she", "timestamp": "None", "format": "jpg", "quality": 100, "meta_data": false, "blind_watermark": "", "save_workflow_as_json": false, "preview": true, "images": ["8", 0]}, "class_type": "LayerUtility: SaveImagePlus", "_meta": {"title": "图层工具：保存图像 Plus（高级）"}}, "48": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}}