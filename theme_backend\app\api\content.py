from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import json
import traceback
from pathlib import Path
import httpx
import aiofiles
from .common import (
    load_workflow,
    send_prompt,
    wait_for_image,
    logger
)

router = APIRouter()

class ContentRequest(BaseModel):
    task_id: str
server_name="theme_content_server"
def build_content_workflow(task_id: str) -> dict:
    """构建内容页面生成工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("content.json")
        
        # 设置输入壁纸路径
        if "117" not in workflow:
            logger.error("工作流中找不到节点117")
            raise HTTPException(status_code=500, detail="Node 117 not found in workflow")
            
        workflow["117"]["inputs"]["file_path"] = f"/ssd2/ComfyUI/output/changan/mainLine_{task_id}/releases/wallpaper"
        logger.info(f"设置壁纸路径: {workflow['117']['inputs']['file_path']}")

        # 设置源内容图片路径
        if "116" not in workflow:
            logger.error("工作流中找不到节点116")
            raise HTTPException(status_code=500, detail="Node 116 not found in workflow")
            
        workflow["116"]["inputs"]["file_path"] = "/ssd2/changan/theme/main_line/source_content"
        logger.info(f"设置源内容路径: {workflow['116']['inputs']['file_path']}")

        # 设置保存路径
        if "42" not in workflow:
            logger.error("工作流中找不到节点42")
            raise HTTPException(status_code=500, detail="Node 42 not found in workflow")
        
        workflow["42"]["inputs"]["filename_prefix"] = f"changan/mainLine_{task_id}/content/content"
        logger.info(f"设置保存路径: {workflow['42']['inputs']['filename_prefix']}")
            
        return workflow, "42"  # 42是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/generate-content")
async def generate_content(request: ContentRequest):
    """生成内容页面的端点"""
    try:
        logger.info(f"\n========== 开始生成内容页面 ==========")
        logger.info(f"任务ID: {request.task_id}")

        if not request.task_id:
            raise HTTPException(status_code=400, detail="缺少任务ID")

        # 构建工作流
        workflow, output_node_id = build_content_workflow(request.task_id)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        subfolder = f"changan/mainLine_{request.task_id}/content"
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URL: {image_url}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{request.task_id}/content"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存图片
        image_path = save_dir / "content.png"
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(image_url)
                response.raise_for_status()
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)
                logger.info(f"内容页面已保存到: {image_path}")
            except Exception as e:
                logger.error(f"保存内容页面失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to save content page: {str(e)}")

        # 构建公开访问URL
        public_url = f"/ui_source/mainLine_{request.task_id}/content/content.png"
        logger.info(f"公开访问URL: {public_url}")

        logger.info(f"========== 内容页面生成完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "image_url": public_url
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理内容页面生成请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))
