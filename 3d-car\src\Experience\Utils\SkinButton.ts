import type Experience from "../Experience";

export class SkinButton {
  container: HTMLButtonElement;
  experience: Experience;
  hasSkin: boolean;
  
  constructor(experience: Experience) {
    this.experience = experience;
    this.container = document.createElement('button');
    this.hasSkin = false;
    
    this.init();
  }
  
  init() {
    const button = this.container;
    button.textContent = '应用赛车贴图';
    
    const buttonStyles = {
      position: 'fixed',
      bottom: '20px',
      right: '150px',
      padding: '12px 20px',
      backgroundColor: 'rgba(20, 20, 20, 0.7)',
      backdropFilter: 'blur(5px)',
      color: 'white',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '30px',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: 'bold',
      zIndex: '10000',
      boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
      transition: 'transform 0.2s ease, background-color 0.2s ease',
    };
    
    Object.assign(button.style, buttonStyles as any);
    // 添加webkit前缀
    (button.style as any)['-webkit-backdrop-filter'] = 'blur(5px)';
    
    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'rgba(30, 30, 30, 0.8)';
      button.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      button.style.transform = 'translateY(0)';
    });
    
    // 点击事件 - 切换车衣
    button.addEventListener('click', () => {
      this.toggleSkin();
    });
    
    document.body.appendChild(button);
    
    console.log('Skin toggle button added to the DOM');
  }
  
  toggleSkin() {
    this.experience.params.hasSkin = !this.experience.params.hasSkin;
    
    const car = this.experience.world.car;
    if(car) {
      if(this.experience.params.hasSkin) {
        // 应用贴图
        if(this.experience.params.currentSkinUrl) {
          // 应用贴图
          car.applySkin(this.experience.params.currentSkinUrl);
        } else {
          car.applySkin('decal');
        }
        this.container.textContent = '移除赛车贴图';
        this.container.style.backgroundColor = 'rgba(38, 214, 233, 0.7)';
      } else {
        // 移除贴图
        car.applySkin(null);
        this.container.textContent = '应用赛车贴图';
        this.container.style.backgroundColor = 'rgba(20, 20, 20, 0.7)';
      }
    }
  }
  
  dispose() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
