# 粒子特效组件使用指南

## 组件概述

本项目提供了三种粒子特效组件，可以根据不同需求选择使用：

1. **ParticleEffect** - 基础粒子效果组件，使用DOM方式渲染粒子
2. **StarBackground** - 全屏星空背景组件，专为全屏背景设计
3. **ParticleCanvas** - 基于Canvas的粒子效果组件，性能更优（推荐用于大量粒子场景）
4. **GlowingLine** - 兼容旧版的粒子效果组件，内部使用ParticleEffect

所有组件都基于同一个粒子引擎（ParticleEngine）构建，共享相同的配置选项和API。

## 快速开始

### 安装

组件已内置在项目中，无需额外安装。

### 基本使用

```vue
<template>
  <!-- 基础粒子效果 -->
  <ParticleEffect 
    width="100%" 
    height="300px" 
    :particleDensity="50"
    particleColor="rgba(255, 255, 255, 0.6)"
  />
  
  <!-- 全屏星空背景 -->
  <StarBackground 
    :particleDensity="120"
    primaryColor="rgba(99, 102, 241, 0.6)"
    secondaryColor="rgba(59, 130, 246, 0.6)"
  />
  
  <!-- 高性能Canvas渲染 -->
  <ParticleCanvas 
    width="100%" 
    height="100vh"
    :particleDensity="200"
    :minSize="0.2"
    :maxSize="1.2"
  />
</template>

<script setup>
import { ParticleEffect, StarBackground, ParticleCanvas } from '../components/';
</script>
```

## 配置选项

所有粒子组件支持以下属性：

| 属性            | 类型   | 默认值                     | 描述                                |
| --------------- | ------ | -------------------------- | ----------------------------------- |
| width           | String | "100%"                     | 组件宽度                            |
| height          | String | "10rem"                    | 组件高度                            |
| primaryColor    | String | "rgba(99, 102, 241, 0.7)"  | 主要粒子颜色                        |
| secondaryColor  | String | "rgba(59, 130, 246, 0.7)"  | 次要粒子颜色                        |
| particleColor   | String | "rgba(255, 255, 255, 0.6)" | 默认粒子颜色                        |
| particleDensity | Number | 50                         | 粒子密度 (每1000平方像素的粒子数量) |
| minSize         | Number | 0.2                        | 最小粒子尺寸                        |
| maxSize         | Number | 1.0                        | 最大粒子尺寸                        |
| speed           | Number | 0.3                        | 粒子移动速度                        |

StarBackground 组件额外支持：

| 属性   | 类型   | 默认值 | 描述                        |
| ------ | ------ | ------ | --------------------------- |
| zIndex | Number | 0      | CSS z-index值，控制叠放次序 |

## 方法

所有组件都暴露以下方法，可以通过ref访问：

| 方法           | 描述           |
| -------------- | -------------- |
| reinitialize() | 重新初始化粒子 |
| pause()        | 暂停动画       |
| resume()       | 恢复动画       |

示例：

```vue
<template>
  <ParticleEffect ref="particleRef" />
  <button @click="pauseParticles">暂停</button>
  <button @click="resumeParticles">继续</button>
</template>

<script setup>
import { ref } from 'vue';
import { ParticleEffect } from '../components/';

const particleRef = ref(null);

const pauseParticles = () => {
  particleRef.value.pause();
};

const resumeParticles = () => {
  particleRef.value.resume();
};
</script>
```

## 事件

组件触发以下事件：

| 事件        | 描述                 |
| ----------- | -------------------- |
| initialized | 组件初始化完成时触发 |
| destroyed   | 组件销毁时触发       |

## 性能建议

1. 对于大量粒子场景（particleDensity > 100），推荐使用 `ParticleCanvas` 组件
2. 全屏背景效果推荐使用 `StarBackground` 组件
3. 对于简单场景，`ParticleEffect` 组件足够

## 自定义样式

所有组件都支持通过CSS自定义样式。具体类名请查看各组件的源代码。

## 兼容性

- 所有现代浏览器（Chrome, Firefox, Safari, Edge）
- 移动设备友好
- IE不支持 