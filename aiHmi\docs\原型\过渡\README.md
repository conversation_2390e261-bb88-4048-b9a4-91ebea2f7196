# AI HMI 过渡特效系统

基于设计文档中的过渡特效规范，实现的高性能蒙版切换效果系统。

## 📁 文件结构

```
过渡/
├── transition_effects_prototype.html    # 过渡特效原型演示
├── transition_manager.js               # 过渡特效管理器
├── integrated_demo.html                # 集成演示页面
└── README.md                          # 说明文档
```

## 🎯 核心特性

### 1. 蒙版切换特效库

基于设计文档中的规范，实现了以下过渡效果：

#### 面板式揭示 (Panel Reveal)
- **滑动面板 (Sliding Panel)**: 目标界面从屏幕一侧平滑进入
- **斜切面板 (Angled Panel)**: 带有倾斜角度的面板进入，增强速度感

#### 扩展式揭示 (Expansion Reveal)
- **圆形扩展 (Circle Expand)**: 从屏幕中央点向外圆形扩展

#### 开合式揭示 (Symmetrical Reveal)
- **幕布开启 (Curtain Open)**: 像拉开剧院幕布一样从中轴线向两侧展开

### 2. 技术实现

- **核心技术**: GSAP + CSS `clip-path`
- **性能优化**: 60fps 流畅动画
- **兼容性**: 现代浏览器支持
- **回退机制**: 自动降级到淡入淡出效果

## 🚀 快速开始

### 1. 查看演示

打开以下文件体验不同的过渡效果：

```bash
# 基础特效演示
open transition_effects_prototype.html

# 集成演示（推荐）
open integrated_demo.html
```

### 2. 在项目中使用

#### 引入依赖

```html
<!-- GSAP 动画库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

<!-- 过渡管理器 -->
<script src="./transition_manager.js"></script>
```

#### 基本用法

```javascript
// 自动初始化
// TransitionManager 会在页面加载完成后自动初始化

// 手动触发过渡
window.transitionManager.transitionToPage(
    'target_page.html',     // 目标页面
    'circle_expand',        // 过渡效果
    { centerX: 50, centerY: 50 }  // 选项
);
```

#### 链接标记

为链接添加 `data-transition` 属性来指定过渡效果：

```html
<a href="page.html" data-transition="sliding_panel">自然风格</a>
<a href="page.html" data-transition="angled_panel">赛博朋克</a>
<a href="page.html" data-transition="circle_expand">玻璃拟态</a>
<a href="page.html" data-transition="curtain_open">新拟态</a>
```

#### 键盘快捷键

系统内置了键盘快捷键支持：

- `Ctrl + 1`: 自然风格 (滑动面板)
- `Ctrl + 2`: 赛博朋克 (斜切面板)
- `Ctrl + 3`: 玻璃拟态 (圆形扩展)
- `Ctrl + 4`: 新拟态 (幕布开启)
- `Ctrl + 5`: 可爱风格 (圆形扩展)

## 🎨 过渡效果详解

### 1. 滑动面板 (sliding_panel)

```javascript
// 实现原理
gsap.set(element, { 
    clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' 
});
gsap.to(element, {
    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
    duration: 1.2,
    ease: 'power2.inOut'
});
```

**适用场景**: 从信息浏览切换到导航模式

### 2. 斜切面板 (angled_panel)

```javascript
// 实现原理
gsap.set(element, { 
    clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' 
});
gsap.to(element, {
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
    duration: 1.0,
    ease: 'power3.inOut'
});
```

**适用场景**: 切换到科技感主题（如赛博朋克）

### 3. 圆形扩展 (circle_expand)

```javascript
// 实现原理
gsap.set(element, { 
    clipPath: 'circle(0% at 50% 50%)' 
});
gsap.to(element, {
    clipPath: 'circle(150% at 50% 50%)',
    duration: 1.5,
    ease: 'power2.inOut'
});
```

**适用场景**: 生成新桌面、魔法般的创造效果

### 4. 幕布开启 (curtain_open)

```javascript
// 实现原理
gsap.set(element, { 
    clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)' 
});
gsap.to(element, {
    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
    duration: 1.8,
    ease: 'power2.inOut'
});
```

**适用场景**: 进入影院模式、专注模式

## 🔧 API 参考

### TransitionManager 类

#### 构造函数

```javascript
const manager = new TransitionManager();
```

#### 方法

##### transitionToPage(targetUrl, effectType, options)

执行页面过渡

**参数**:
- `targetUrl` (string): 目标页面URL
- `effectType` (string): 过渡效果类型
  - `'sliding_panel'`
  - `'angled_panel'`
  - `'circle_expand'`
  - `'curtain_open'`
  - `'fade'` (默认回退效果)
- `options` (object): 过渡选项
  - `centerX` (number): 圆形扩展中心X坐标 (0-100)
  - `centerY` (number): 圆形扩展中心Y坐标 (0-100)

**返回**: Promise

##### triggerTransition(targetUrl, effectType, options)

手动触发过渡的别名方法

#### 属性

- `isTransitioning` (boolean): 是否正在执行过渡
- `currentPage` (string): 当前页面
- `transitionContainer` (HTMLElement): 过渡容器元素

## 🎯 设计原则

### 1. 性能优先

- 使用 GPU 加速的 CSS `clip-path` 属性
- GSAP 优化的动画时间线
- 60fps 流畅度保证

### 2. 用户体验

- 过渡时间控制在 0.8-1.8 秒
- 提供视觉反馈和加载状态
- 支持键盘快捷键操作

### 3. 可扩展性

- 模块化的特效系统
- 易于添加新的过渡效果
- 灵活的配置选项

### 4. 兼容性

- 自动回退机制
- 跨浏览器兼容
- 移动端适配

## 🔍 技术细节

### 实现原理

1. **预渲染**: 在后台渲染目标界面
2. **快照捕获**: 将当前界面转换为静态快照
3. **动画执行**: 使用 `clip-path` 动画化显示过程
4. **清理**: 移除临时元素，完成过渡

### 性能优化

- 使用 `transform` 和 `clip-path` 避免重排
- GPU 加速的 CSS 属性
- 事件节流和防抖
- 内存管理和清理

### 浏览器支持

- Chrome 55+
- Firefox 54+
- Safari 13.1+
- Edge 79+

## 🐛 故障排除

### 常见问题

1. **过渡效果不显示**
   - 检查 GSAP 是否正确加载
   - 确认浏览器支持 `clip-path`
   - 查看控制台错误信息

2. **性能问题**
   - 减少同时运行的动画数量
   - 检查 GPU 加速是否启用
   - 优化页面复杂度

3. **跨域问题**
   - 确保页面在同一域名下
   - 使用本地服务器测试
   - 检查 CORS 设置

### 调试模式

在控制台中启用调试：

```javascript
// 启用详细日志
window.transitionManager.debug = true;

// 查看当前状态
console.log(window.transitionManager.isTransitioning);
```

## 📝 更新日志

### v1.0.0 (2024-01-XX)

- ✨ 初始版本发布
- 🎨 实现四种核心过渡效果
- 🚀 集成 GSAP 动画引擎
- 📱 移动端适配
- ⌨️ 键盘快捷键支持
- 🔧 完整的 API 文档

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [GSAP](https://greensock.com/gsap/) - 强大的动画库
- [MDN Web Docs](https://developer.mozilla.org/) - CSS clip-path 文档
- AI HMI 设计团队 - 设计规范和指导

---

**注意**: 本系统基于 AI HMI 设计文档中的过渡特效规范实现，确保与整体设计系统的一致性和协调性。