import axios from 'axios';
import { API_CONFIG } from '../config/api'; // 导入 API 配置

// RAG 相关配置保持不变
export const RAG_API_KEY = 'ragflow-QwYTJkNjQ2NDBkZDExZjBiM2JhYmU2Nj';
const RAG_API_BASE_URL = 'http://************:18980/api/v1';
export const RAG_API_ASK_URL = 'http://************:18980/api/v1/chats/aac79ff2405f11f0a958be6680633785/completions';

// 预览图片信息接口
export interface PreviewImage {
    id: number;
    file_name: string;
    url: string;
    width: number;
    height: number;
    type: 'wallpaper' | 'icon';
}

// 预览响应接口
export interface PreviewsResponse {
    previews: PreviewImage[];
}

// 涂鸦启动请求接口
export interface DoodleStartRequest {
    sketch_image: string; // Base64编码的PNG图像
    task_id: string;
    prompt: string; // 用户输入的提示词
    initial_context?: string; // 可选的初始上下文
}

// 涂鸦更新提示词请求接口
export interface DoodleUpdatePromptRequest {
    prompt: string;
}

// 主题生成API接口
export const themeApi = {
    /**
     * 启动主题生成任务
     * @param prompt 主题描述文本
     * @param taskId 任务ID
     * @returns Promise 包含任务信息的响应
     */
    startPipeline: async (prompt: string, taskId: string) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(API_CONFIG.ENDPOINTS.PIPELINE), {
                prompt,
                task_id: taskId
            });
            return response.data;
        } catch (error) {
            console.error('启动主题生成任务失败:', error);
            throw error;
        }
    },

    /**
     * 查询任务状态
     * @param taskId 任务ID
     * @returns Promise 包含任务状态的响应
     */
    getTaskStatus: async (taskId: string) => {
        try {
            const response = await axios.get(API_CONFIG.getApiUrl(`${API_CONFIG.ENDPOINTS.PIPELINE_STATUS}/${taskId}`));
            return response.data;
        } catch (error) {
            console.error('查询任务状态失败:', error);
            throw error;
        }
    },

    /**
     * 终止任务
     * @param taskId 任务ID
     * @returns Promise 包含终止操作结果的响应
     */
    terminateTask: async (taskId: string) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`${API_CONFIG.ENDPOINTS.TERMINATE_TASK}/${taskId}`));
            return response.data;
        } catch (error) {
            console.error('终止任务失败:', error);
            throw error;
        }
    },

    /**
     * 获取下载链接
     * @param taskId 任务ID
     * @returns 下载链接
     */
    getDownloadUrl: (taskId: string) => {
        return `${API_CONFIG.BASE_URL}/download-archive/${taskId}`;
    },

    /**
     * 获取主题预览图片列表
     * @param taskId 任务ID
     * @param category 预览类别，支持'wallpaper'或'icon'
     * @returns Promise 包含预览图片列表的响应
     */
    getPreviewImages: async (taskId: string, category: 'wallpaper' | 'icon'): Promise<PreviewsResponse> => {
        try {
            const response = await axios.get(API_CONFIG.getPreviewUrl(taskId, category));
            return response.data;
        } catch (error) {
            console.error(`获取${category}预览图片失败:`, error);
            throw error;
        }
    },
    getPreviewImagesMinio: async (taskId: string, category: 'wallpaper' | 'icon'): Promise<PreviewsResponse> => {
        try {
            const response = await axios.get(API_CONFIG.getApiUrl(`/preview-minio/${taskId}/${category}`));
            return response.data;
        } catch (error) {
            console.error(`获取${category}预览图片失败:`, error);
            throw error;
        }
    },

    /**
     * 获取预览图片URL
     * @param taskId 任务ID
     * @param category 预览类别，支持'wallpaper'或'icon'
     * @param fileName 图片文件名
     * @returns 图片URL
     */
    getPreviewImageUrl: (taskId: string, category: 'wallpaper' | 'icon', fileName: string): string => {
        return API_CONFIG.getPreviewImageUrl(taskId, category, fileName);
    },
    getPreviewImageMinioUrl: async (taskId: string, category: 'wallpaper' | 'icon', fileName: string): Promise<string> => {
        try {
            const response = await axios.get(API_CONFIG.getApiUrl(`/preview-image-minio/${taskId}/${category}/${fileName}`));
            return response.data;
        } catch (error) {
            console.error(`获取${category}预览图片地址失败:`, error);
            throw error;
        }
    },
    /**
     * 文本生成视频
     * @param prompt 视频描述文本
     * @param taskId 任务ID
     * @returns Promise 包含视频生成任务信息的响应
     */
    textToVideo: async (prompt: string, taskId: string) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/wan/text-to-video`), {
                prompt,
                task_id: taskId
            });
            return response.data;
        } catch (error) {
            console.error('启动视频生成任务失败:', error);
            throw error;
        }
    },
    /**
     * 文本生成壁纸
     * @param prompt 壁纸描述文本
     * @param taskId 任务ID
     * @returns Promise 包含壁纸生成任务信息的响应
     */
    textToImage: async (prompt: string, taskId: string) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/kolors/text-to-image`), {
                prompt,
                task_id: taskId
            });
            return response.data;
        } catch (error) {
            console.error('启动壁纸生成任务失败:', error);
            throw error;
        }
    },
    /**
     * 图片生成文本
     * @param prompt 文本描述
     * @param taskId 任务ID
     * @returns Promise 包含图片生成文本任务信息的响应
     */
    imageToText: async (formData: FormData) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/image-to-text`), formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
            return response.data;
        } catch (error) {
            console.error('启动图像识别任务失败:', error);
            throw error;
        }
    },
    processAudio: async (formData: FormData) => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/voice-to-voice`), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error('处理音频任务失败:', error);
            throw error;
        }
    },
    /**
     * 查询任务列表
     */
    getTaskList: async (params: {}) => {
        try {
            const response = await axios.get(API_CONFIG.getApiUrl(`/task/list`), { params });
            return response.data;
        } catch (error) {
            console.error('查询任务列表失败:', error);
            throw error;
        }
    },
    retrieval: async (data: any): Promise<any> => {
        const response = await axios.post(`${RAG_API_BASE_URL}/retrieval`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${RAG_API_KEY}`
            }
        });
        return response.data;
    },
    ask: async (data: any): Promise<any> => {
        const response = await axios.post(`${RAG_API_BASE_URL}/chats/ed0edfc4054f11f083ad0242ac130006/completions`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${RAG_API_KEY}`
            }
        });
        return response.data;
    },
    textToScene: async (data: any): Promise<any> => {
        const response = await axios.post(`http://*************:7860/v1/chat/completions`, data);
        return response.data;
    },
    magicCamera: async (formData: FormData): Promise<any> => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/magic-camera`), formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
            return response.data;
        } catch (error) {
            console.error('启动魔法相机任务失败:', error);
            throw error;
        }
    },
    imageToVideo: async (formData: FormData): Promise<any> => {
        try {
            const response = await axios.post(API_CONFIG.getApiUrl(`/image-to-video`), formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
            return response.data;
        } catch (error) {
            console.error('启动图生视频任务失败:', error);
            throw error;
        }
    },
    textToHtml: async (data: any): Promise<any> => {
        const response = await axios.post(`http://************:8007/v1/chat/completions`, data);
        return response.data;
    },
    /**
     * 启动涂鸦生成任务
     * @param formData 包含涂鸦图像和任务ID的数据对象
     * @returns Promise 包含任务信息的响应
     */
    doodleStart: async (formData: FormData): Promise<{ task_id: string; enhanced_prompt: string }> => {
        const resp = await fetch(API_CONFIG.getApiUrl(`/doodle/start`), {
            method: "POST",
            body: formData,
        });

        if (!resp.ok) {
            throw new Error(`启动涂鸦任务失败: ${resp.statusText}`);
        }

        return resp.json();
    },
    /**
     * 查询涂鸦任务状态
     * @param taskId 任务ID
     * @returns Promise 包含任务状态的响应
     */
    doodleStatus: async (taskId: string): Promise<any> => {
        try {
            const response = await axios.get(API_CONFIG.getApiUrl(`/doodle/status/${taskId}`));
            return response.data;
        } catch (error) {
            console.error('查询涂鸦任务状态失败:', error);
            throw error;
        }
    },
    /**
     * 更新涂鸦提示词并重新生成
     * @param taskId 任务ID
     * @param data 包含新提示词的数据对象
     * @returns Promise 包含更新操作结果的响应
     */
    doodleUpdatePrompt: async (taskId: string, data: DoodleUpdatePromptRequest): Promise<any> => {
        try {
            const response = await axios.put(API_CONFIG.getApiUrl(`/doodle/prompt/${taskId}`), data);
            return response.data;
        } catch (error) {
            console.error('更新涂鸦提示词失败:', error);
            throw error;
        }
    },
};

export default themeApi; 