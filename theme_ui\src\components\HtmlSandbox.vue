<template>
  <div class="html-sandbox" ref="sandboxContainer">
    <iframe v-if="iframeSource" :srcdoc="iframeSource" class="w-full h-full border-0" sandbox="allow-scripts allow-same-origin" ref="sandboxIframe"></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";

const props = defineProps<{
  htmlContent: string;
}>();

const sandboxContainer = ref<HTMLElement | null>(null);
const sandboxIframe = ref<HTMLIFrameElement | null>(null);
const iframeSource = ref<string>("");

// 处理HTML内容
const processHtmlContent = () => {
  if (!props.htmlContent) return;

  // 创建安全的iframe内容
  iframeSource.value = props.htmlContent;
};

// 监听HTML内容变化
watch(
  () => props.htmlContent,
  () => {
    processHtmlContent();
  },
  { immediate: true }
);

// 调整iframe高度
const adjustIframeHeight = () => {
  if (!sandboxIframe.value) return;

  // 监听iframe加载完成
  sandboxIframe.value.onload = () => {
    try {
      // 尝试调整高度以适应内容
      const iframeDocument = sandboxIframe.value?.contentDocument;
      if (iframeDocument && sandboxContainer.value) {
        const height = iframeDocument.documentElement.scrollHeight;
        sandboxContainer.value.style.height = `${height}px`;
      }
    } catch (e) {
      console.error("无法调整iframe高度:", e);
    }
  };
};

onMounted(() => {
  processHtmlContent();
  adjustIframeHeight();
});
</script>

<style scoped>
.html-sandbox {
  position: relative;
  min-height: 200px;
  width: 100%;
  overflow: hidden;
  transition: height 0.3s ease;
}
</style> 