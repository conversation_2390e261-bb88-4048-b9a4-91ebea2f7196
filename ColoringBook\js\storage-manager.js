/**
 * StorageManager - 用户偏好设置的本地存储管理器
 * 负责管理用户的个性化设置，包括图片比例选择、生成数量等偏好
 */
class StorageManager {
    constructor() {
        this.storageKey = 'magicColoringBook_userPreferences';
        this.defaultPreferences = {
            selectedRatio: '1:1',
            defaultCount: 1,
            lastPrompt: '',
            version: '1.0.0'
        };
        
        // 初始化时检查并迁移旧版本数据
        this.initializeStorage();
    }

    /**
     * 初始化存储，处理版本兼容性
     */
    initializeStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (!stored) {
                // 首次使用，保存默认设置
                this.saveUserPreferences(this.defaultPreferences);
                console.log('StorageManager: 初始化默认用户偏好设置');
            } else {
                // 检查版本兼容性
                const preferences = JSON.parse(stored);
                if (!preferences.version || preferences.version !== this.defaultPreferences.version) {
                    // 版本不匹配，合并设置并更新版本
                    const mergedPreferences = { ...this.defaultPreferences, ...preferences };
                    mergedPreferences.version = this.defaultPreferences.version;
                    this.saveUserPreferences(mergedPreferences);
                    console.log('StorageManager: 更新用户偏好设置版本');
                }
            }
        } catch (error) {
            console.error('StorageManager: 初始化存储失败', error);
            // 如果出错，重置为默认设置
            this.resetToDefaults();
        }
    }

    /**
     * 保存用户偏好设置
     * @param {Object} preferences - 用户偏好对象
     * @param {string} preferences.selectedRatio - 选中的图片比例
     * @param {number} preferences.defaultCount - 默认生成数量
     * @param {string} preferences.lastPrompt - 上次使用的提示词
     */
    saveUserPreferences(preferences) {
        try {
            // 验证输入数据
            const validatedPreferences = this.validatePreferences(preferences);
            
            // 添加时间戳
            validatedPreferences.lastUpdated = new Date().toISOString();
            
            localStorage.setItem(this.storageKey, JSON.stringify(validatedPreferences));
            console.log('StorageManager: 用户偏好设置已保存', validatedPreferences);
            
            // 触发自定义事件，通知其他组件设置已更新
            this.dispatchPreferencesUpdatedEvent(validatedPreferences);
            
            return true;
        } catch (error) {
            console.error('StorageManager: 保存用户偏好设置失败', error);
            return false;
        }
    }

    /**
     * 加载用户偏好设置
     * @returns {Object} 用户偏好对象
     */
    loadUserPreferences() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (!stored) {
                console.log('StorageManager: 未找到存储的偏好设置，返回默认值');
                return { ...this.defaultPreferences };
            }

            const preferences = JSON.parse(stored);
            const validatedPreferences = this.validatePreferences(preferences);
            
            console.log('StorageManager: 用户偏好设置已加载', validatedPreferences);
            return validatedPreferences;
        } catch (error) {
            console.error('StorageManager: 加载用户偏好设置失败', error);
            // 如果加载失败，返回默认设置
            return { ...this.defaultPreferences };
        }
    }

    /**
     * 更新特定的偏好设置项
     * @param {string} key - 设置项键名
     * @param {*} value - 设置项值
     */
    updatePreference(key, value) {
        try {
            const currentPreferences = this.loadUserPreferences();
            currentPreferences[key] = value;
            return this.saveUserPreferences(currentPreferences);
        } catch (error) {
            console.error(`StorageManager: 更新偏好设置 ${key} 失败`, error);
            return false;
        }
    }

    /**
     * 获取特定的偏好设置项
     * @param {string} key - 设置项键名
     * @param {*} defaultValue - 默认值
     * @returns {*} 设置项值
     */
    getPreference(key, defaultValue = null) {
        try {
            const preferences = this.loadUserPreferences();
            return preferences.hasOwnProperty(key) ? preferences[key] : defaultValue;
        } catch (error) {
            console.error(`StorageManager: 获取偏好设置 ${key} 失败`, error);
            return defaultValue;
        }
    }

    /**
     * 清除用户数据并恢复默认设置
     */
    clearUserData() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('StorageManager: 用户数据已清除');
            
            // 重新初始化默认设置
            this.saveUserPreferences(this.defaultPreferences);
            
            // 触发清除事件
            this.dispatchDataClearedEvent();
            
            return true;
        } catch (error) {
            console.error('StorageManager: 清除用户数据失败', error);
            return false;
        }
    }

    /**
     * 重置为默认设置
     */
    resetToDefaults() {
        try {
            const resetPreferences = { ...this.defaultPreferences };
            resetPreferences.lastUpdated = new Date().toISOString();
            
            localStorage.setItem(this.storageKey, JSON.stringify(resetPreferences));
            console.log('StorageManager: 已重置为默认设置');
            
            // 触发重置事件
            this.dispatchResetEvent(resetPreferences);
            
            return true;
        } catch (error) {
            console.error('StorageManager: 重置默认设置失败', error);
            return false;
        }
    }

    /**
     * 验证偏好设置数据
     * @param {Object} preferences - 待验证的偏好设置
     * @returns {Object} 验证后的偏好设置
     */
    validatePreferences(preferences) {
        const validated = { ...this.defaultPreferences };
        
        // 验证图片比例
        const validRatios = ['1:1', '4:3', '3:4', '16:9'];
        if (preferences.selectedRatio && validRatios.includes(preferences.selectedRatio)) {
            validated.selectedRatio = preferences.selectedRatio;
        }
        
        // 验证生成数量
        if (preferences.defaultCount && 
            typeof preferences.defaultCount === 'number' && 
            preferences.defaultCount >= 1 && 
            preferences.defaultCount <= 10) {
            validated.defaultCount = preferences.defaultCount;
        }
        
        // 验证提示词
        if (preferences.lastPrompt && 
            typeof preferences.lastPrompt === 'string' && 
            preferences.lastPrompt.length <= 1000) {
            validated.lastPrompt = preferences.lastPrompt;
        }
        
        // 保留版本信息
        if (preferences.version) {
            validated.version = preferences.version;
        }
        
        // 保留时间戳
        if (preferences.lastUpdated) {
            validated.lastUpdated = preferences.lastUpdated;
        }
        
        return validated;
    }

    /**
     * 检查本地存储是否可用
     * @returns {boolean} 是否支持localStorage
     */
    isStorageAvailable() {
        try {
            const testKey = 'storageTest';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.warn('StorageManager: localStorage不可用', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用情况信息
     */
    getStorageInfo() {
        try {
            const preferences = this.loadUserPreferences();
            const dataSize = JSON.stringify(preferences).length;
            
            return {
                isAvailable: this.isStorageAvailable(),
                dataSize: dataSize,
                lastUpdated: preferences.lastUpdated || null,
                version: preferences.version || null
            };
        } catch (error) {
            console.error('StorageManager: 获取存储信息失败', error);
            return {
                isAvailable: false,
                dataSize: 0,
                lastUpdated: null,
                version: null
            };
        }
    }

    /**
     * 导出用户设置（用于备份）
     * @returns {string} JSON格式的用户设置
     */
    exportSettings() {
        try {
            const preferences = this.loadUserPreferences();
            return JSON.stringify(preferences, null, 2);
        } catch (error) {
            console.error('StorageManager: 导出设置失败', error);
            return null;
        }
    }

    /**
     * 导入用户设置（用于恢复）
     * @param {string} settingsJson - JSON格式的用户设置
     * @returns {boolean} 是否导入成功
     */
    importSettings(settingsJson) {
        try {
            const preferences = JSON.parse(settingsJson);
            return this.saveUserPreferences(preferences);
        } catch (error) {
            console.error('StorageManager: 导入设置失败', error);
            return false;
        }
    }

    /**
     * 触发偏好设置更新事件
     * @param {Object} preferences - 更新后的偏好设置
     */
    dispatchPreferencesUpdatedEvent(preferences) {
        const event = new CustomEvent('userPreferencesUpdated', {
            detail: { preferences }
        });
        document.dispatchEvent(event);
    }

    /**
     * 触发数据清除事件
     */
    dispatchDataClearedEvent() {
        const event = new CustomEvent('userDataCleared');
        document.dispatchEvent(event);
    }

    /**
     * 触发重置事件
     * @param {Object} preferences - 重置后的偏好设置
     */
    dispatchResetEvent(preferences) {
        const event = new CustomEvent('userPreferencesReset', {
            detail: { preferences }
        });
        document.dispatchEvent(event);
    }
}

// 导出StorageManager类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageManager;
}