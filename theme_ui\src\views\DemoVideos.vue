<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">视频展示</h1>
      </div>

      <!-- 视频分类区域 -->
      <div v-for="(category, categoryIndex) in videoCategories" :key="categoryIndex" class="mb-10">
<!--        <h2 class="text-2xl font-bold text-purple-200 mb-6">{{ category.name }}</h2>-->
        <GlassPanel class="p-6 h-full flex flex-col">
          <div class="w-full bg-dark-800/60 rounded-lg p-4 flex-grow overflow-y-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="(video, videoIndex) in category.videos" 
                   :key="videoIndex" 
                   class="relative cursor-pointer transition-all duration-300 overflow-hidden group"
                   @click="openVideoModal(video.url)">
                <video :src="video.url" 
                       :alt="video.name" 
                       class="w-full h-auto rounded-lg pointer-events-none" 
                       style="aspect-ratio: 4 / 3;"></video> 
                <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent">
                  <span class="text-white font-medium text-sm">{{ video.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </GlassPanel>
      </div>
    </div>

    <!-- 视频播放模态框（网页全屏） -->
    <div v-if="isModalOpen" class="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center">
      <!-- 关闭按钮 -->
      <button @click="closeVideoModal" class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 z-10 hover:bg-opacity-75">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      
      <!-- 视频播放器 -->
      <div class="w-full h-full flex items-center justify-center">
        <video 
          v-if="selectedVideoUrl" 
          ref="videoPlayer" 
          :src="selectedVideoUrl" 
          controls 
          autoplay 
          class="max-w-full max-h-full w-auto h-auto"
        ></video>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
// 移除未使用的 onMounted
import { ref, defineAsyncComponent } from "vue";

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);

// 视频分类及视频数据
const videoCategories = [
  {
    name: "演示视频",
    videos: [
      {
        url: "/videos/demo/车衣大模型.mp4",
        name: "星云幻甲",
      },
      {
        url: "/videos/demo/魔法相机.mp4",
        name: "幻影画匠",
      },
      {
        url: "/videos/demo/数字人.mp4",
        name: "数字人",
      },
      {
        url: "/videos/demo/文生主题.mp4",
        name: "文生主题",
      },
      {
        url: "/videos/demo/用车助手.mp4",
        name: "用车助手",
      },
    ],
  },
];

const selectedVideoUrl = ref<string | null>(null);
const isModalOpen = ref(false);
const videoPlayer = ref<HTMLVideoElement | null>(null);

const openVideoModal = (videoUrl: string) => {
  selectedVideoUrl.value = videoUrl;
  isModalOpen.value = true;
};

const closeVideoModal = () => {
  isModalOpen.value = false;
  selectedVideoUrl.value = null;
  // 暂停视频播放
  if (videoPlayer.value) {
    videoPlayer.value.pause();
  }
};

</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

/* 玻璃预览卡片 */
.glass-preview-card {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.glass-preview-card:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-img-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.wallpaper-card .preview-img-container {
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 */
}

.icon-card .preview-img-container {
  height: 0;
  padding-bottom: 100%; /* 1:1 比例 */
}

.preview-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.wallpaper-card .preview-img {
  object-fit: cover;
}

.icon-card .preview-img {
  object-fit: contain;
  padding: 0.5rem;
}

.glass-preview-card:hover .preview-img {
  transform: scale(1.05);
}

.preview-card-info {
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
}

.preview-filename {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 添加一个简单的播放按钮覆盖效果 */
.group:hover .opacity-0 {
  opacity: 1;
}

/* 防止视频本身的点击事件 */
.pointer-events-none {
  pointer-events: none;
}

</style>
