import * as kokomi from "kokomi.js";
import * as THREE from "three";

import World from "./World/World";

import Debug from "./Debug";

import Postprocessing from "./Postprocessing";

import { resources } from "./resources";
import { ColorPicker } from "./Utils/ColorPicker";
import { JumpButton } from "./Utils/JumpButton";
import { SkinButton } from "./Utils/SkinButton";
import { UserInput } from "./Utils/UserInput";

export default class Experience extends kokomi.Base {
  params;
  controls: kokomi.CameraControls;
  world: World;
  debug: Debug;
  am: kokomi.AssetManager;
  post: Postprocessing;
  colorPicker?: ColorPicker;
  skinButton?: SkinButton;
  jumpButton?: JumpButton;
  userInput?: UserInput;

  constructor(sel = "#sketch") {
    super(sel, {
      autoAdaptMobile: true,
    });

    (window as any).experience = this;

    this.params = {
      speed: 0,
      cameraPos: {
        x: 0,
        y: 0.8,
        z: -11,
      },
      isCameraMoving: false,
      lightAlpha: 0,
      lightIntensity: 0,
      envIntensity: 0,
      envWeight: 0,
      reflectIntensity: 0,
      lightOpacity: 1,
      floorLerpColor: 0,
      carBodyEnvIntensity: 1,
      cameraShakeIntensity: 0,
      bloomLuminanceSmoothing: 1.6,
      bloomIntensity: 1,
      speedUpOpacity: 0,
      cameraFov: 33.4,
      furinaLerpColor: 0,
      isRushing: false,
      disableInteract: false,
      isFurina: window.location.hash === "#furina",
      prompt: '',
      hasSkin: false,
      currentSkinUrl: ''
    };

    this.debug = new Debug();

    this.renderer.toneMapping = THREE.CineonToneMapping;

    let resourcesToLoad = resources;
    if (!this.params.isFurina) {
      resourcesToLoad = resourcesToLoad.filter(
        (item) => !item.name.includes("driving")
      );
    }
    console.log(resourcesToLoad);

    this.am = new kokomi.AssetManager(this, resourcesToLoad, {
      useMeshoptDecoder: true,
    });

    const camera = this.camera as THREE.PerspectiveCamera;
    camera.fov = this.params.cameraFov;
    camera.updateProjectionMatrix();
    const cameraPos = new THREE.Vector3(
      this.params.cameraPos.x,
      this.params.cameraPos.y,
      this.params.cameraPos.z
    );
    camera.position.copy(cameraPos);
    const lookAt = new THREE.Vector3(0, 0.8, 0);
    camera.lookAt(lookAt);

    const controls = new kokomi.CameraControls(this);
    controls.controls.setTarget(lookAt.x, lookAt.y, lookAt.z);
    this.controls = controls;

    this.world = new World(this);

    this.post = new Postprocessing(this);

    // 当资源加载完成后初始化颜色选择器和跳转按钮
    this.am.on("ready", () => {
      if (!this.params.isFurina) {
        this.initColorPicker();
        this.initSkinButton();
        this.initUserInput();
      }
      this.initJumpButton();
    });

    // this.update(() => {
    //   const target = new THREE.Vector3();
    //   console.log(JSON.stringify(controls.controls.getPosition(target)));
    // });

    this.update(() => {
      if (this.params.isCameraMoving) {
        this.controls.controls.enabled = false;
        this.controls.controls.setPosition(
          this.params.cameraPos.x,
          this.params.cameraPos.y,
          this.params.cameraPos.z
        );
      } else {
        this.controls.controls.enabled = true;
      }
    });

    // 添加清理方法
    window.addEventListener('beforeunload', this.cleanUp.bind(this));
  }

  // 初始化颜色选择器
  initColorPicker() {
    // 立即创建颜色选择器
    this.colorPicker = new ColorPicker();

    // 如果需要在加载屏幕消失后立即显示颜色选择器面板
    document.querySelector('.loader-screen')?.addEventListener('transitionend', () => {
      if (this.colorPicker) {
        this.colorPicker.toggleVisibility();
      }
    }, { once: true });
  }

  // 初始化车衣选择器
  initSkinButton() {
    // 创建车衣选择器
    this.skinButton = new SkinButton(this);
  }

  // 初始化跳转按钮
  initJumpButton() {
    // 创建跳转按钮
    this.jumpButton = new JumpButton();
  }

  // 初始化输入按钮组件
  initUserInput() {
    this.userInput = new UserInput(this);
  }
  // 清理资源
  cleanUp() {
    if (this.colorPicker) {
      this.colorPicker.dispose();
    }

    if (this.skinButton) {
      this.skinButton.dispose();
    }

    if (this.jumpButton) {
      this.jumpButton.dispose();
    }

    if (this.userInput) {
      this.userInput.dispose();
    }
  }
}
