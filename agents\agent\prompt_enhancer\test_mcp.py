#!/usr/bin/env python
"""
测试MCP服务连接
"""

import asyncio
import httpx
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

async def test_mcp_connection(url: str = "http://127.0.0.1:19220"):
    """测试MCP服务连接"""
    # 测试/api/v1/info端点
    info_url = f"{url}/api/v1/info"
    logger.info(f"测试MCP服务info端点: {info_url}")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(info_url)
            logger.info(f"状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                logger.info("✅ MCP服务info端点测试成功!")
            else:
                logger.error(f"❌ MCP服务info端点测试失败: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ 请求MCP服务info端点时出错: {str(e)}")
    
    # 测试/api/v1/tools端点
    tools_url = f"{url}/api/v1/tools"
    logger.info(f"测试MCP服务tools端点: {tools_url}")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(tools_url)
            logger.info(f"状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                logger.info("✅ MCP服务tools端点测试成功!")
            else:
                logger.error(f"❌ MCP服务tools端点测试失败: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ 请求MCP服务tools端点时出错: {str(e)}")

    # 测试/api/v1/sse端点
    sse_url = f"{url}/api/v1/sse"
    logger.info(f"测试MCP服务sse端点: {sse_url}")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
            response = await client.get(sse_url, headers=headers)
            logger.info(f"状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text[:100]}")  # 只显示前100个字符
            
            if response.status_code == 200:
                logger.info("✅ MCP服务sse端点测试成功!")
            else:
                logger.error(f"❌ MCP服务sse端点测试失败: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ 请求MCP服务sse端点时出错: {str(e)}")

async def main():
    """主函数"""
    await test_mcp_connection()

if __name__ == "__main__":
    asyncio.run(main()) 