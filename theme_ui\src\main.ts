import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// PrimeVue
import PrimeVue from 'primevue/config'
// 使用本地样式而非 CDN

// 导入本地字体和图标
import './styles/remixicon.css'  // 本地Remix Icon样式
import './styles/inter.css'      // 本地Inter字体样式

// 导入全局样式
import './assets/main.css'
import './styles/glass.css'  // 导入全局毛玻璃样式

const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()

// 使用 Pinia
app.use(pinia)
app.use(router)
app.use(PrimeVue, {
    // PrimeVue 配置
    ripple: true,
    inputStyle: 'filled'
})

app.mount('#app') 