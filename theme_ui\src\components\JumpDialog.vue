<template>
  <Teleport to="body">
    <div v-if="isVisible" class="fixed inset-0 z-50 flex items-center justify-center p-4">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-dark-900/60 backdrop-blur-sm" @click="onClose"></div>
      
      <!-- 对话框内容 -->
      <div class="glass-card-dense relative w-full max-w-md p-6 rounded-xl z-10 transform transition-all duration-300 ease-out scale-100 opacity-100" 
        :class="{ 'scale-95 opacity-0': !isVisible }">
        
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-xl font-semibold text-white/90">配置跳转地址</h3>
          <button @click="onClose" class="text-gray-400 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label for="car3dUrl" class="block text-sm font-medium text-gray-300 mb-1">3D汽车展示模块地址</label>
            <input
              id="car3dUrl"
              v-model="car3dUrl"
              type="text"
              class="w-full bg-dark-800/60 text-white border border-gray-700 focus:border-purple-500 rounded-lg px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-purple-500/30"
              placeholder="例如: http://localhost:5173"
            />
            <p class="mt-1 text-xs text-gray-500">用于跳转到3D汽车展示模块的URL</p>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button
              @click="onClose"
              class="px-4 py-2 bg-gray-700/50 hover:bg-gray-700/80 text-white rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              @click="onSave"
              class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 text-white rounded-lg transition-colors"
            >
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { useJumpConfig } from '../config/jumpConfig';

// 属性
const props = defineProps<{
  visible: boolean
}>();

// 事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'saved'): void
}>();

// 状态
const isVisible = ref(props.visible);
const car3dUrl = ref(useJumpConfig.getCar3dUrl());

// 监听visible属性变化
watchEffect(() => {
  isVisible.value = props.visible;
  
  if (props.visible) {
    // 当对话框打开时，重新加载配置
    car3dUrl.value = useJumpConfig.getCar3dUrl();
  }
});

// 保存配置
const onSave = () => {
  if (car3dUrl.value && car3dUrl.value.trim() !== '') {
    useJumpConfig.setCar3dUrl(car3dUrl.value.trim());
    emit('saved');
    onClose();
  }
};

// 关闭对话框
const onClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.glass-card-dense {
  background: rgba(15, 23, 42, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}
</style> 