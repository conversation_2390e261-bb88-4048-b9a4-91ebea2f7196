<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玻璃拟态等待 (Glassmorphism Wait)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'system-ui', sans-serif;
            background-color: #f5f7fa; /* Fallback color */
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            overflow: hidden; /* 禁止滚动 */
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        /* 动态氛围层 - 玻璃拟态风格 */
        .ambiance-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        .light-rays {
            position: absolute;
            width: 3px;
            height: 120px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(173, 216, 230, 0.5), transparent);
            animation: lightMove 4s infinite ease-in-out;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        .light-rays:nth-child(1) { left: 15%; animation-delay: 0s; }
        .light-rays:nth-child(2) { left: 35%; animation-delay: 1s; }
        .light-rays:nth-child(3) { left: 55%; animation-delay: 2s; }
        .light-rays:nth-child(4) { left: 75%; animation-delay: 3s; }
        .floating-bubbles {
            position: absolute;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            backdrop-filter: blur(10px);
            animation: bubbleFloat 8s infinite ease-in-out;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
        }
        .floating-bubbles:nth-child(5) { left: 10%; width: 20px; height: 20px; animation-delay: 1s; }
        .floating-bubbles:nth-child(6) { left: 60%; width: 25px; height: 25px; animation-delay: 3s; }
        .floating-bubbles:nth-child(7) { left: 85%; width: 15px; height: 15px; animation-delay: 5s; }
        @keyframes lightMove {
            0%, 100% { transform: translateY(-50px) rotate(15deg); opacity: 0; }
            50% { transform: translateY(50vh) rotate(15deg); opacity: 1; }
        }
        @keyframes bubbleFloat {
            0%, 100% { transform: translateY(100vh) scale(0); opacity: 0; }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 0.8; }
        }
        .card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            color: #333;
            margin: 0.5rem; /* 额外的卡片边距 */
        }
        .card:hover {
            transform: scale(1.02);
            box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.3);
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 2rem; /* 增加卡片间距 */
            width: 100%;
            height: 100%;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        .icon-style {
            color: rgba(100, 100, 100, 0.8);
        }
        
        /* 返回按钮样式 - 玻璃拟态风格 */
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 1rem 1.5rem;
            color: #4a5568;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.3);
        }
        
        .back-button i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body style="background-image: url('https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80');">

    <!-- 返回按钮 -->
    <a href="过渡/complete_transition_demo.html" class="back-button" data-transition="circle_expand">
        <i class="fas fa-arrow-left"></i>
        返回演示
    </a>

    <!-- 动态氛围层 -->
    <div class="ambiance-layer">
        <div class="light-rays"></div>
        <div class="light-rays"></div>
        <div class="light-rays"></div>
        <div class="light-rays"></div>
        <div class="floating-bubbles"></div>
        <div class="floating-bubbles"></div>
        <div class="floating-bubbles"></div>
    </div>

    <div class="grid-container">

        <!-- Dynamic Island (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-parking fa-lg icon-style"></i>
                <div>
                    <p class="text-lg font-bold">车辆已停泊</p>
                    <p class="text-sm opacity-80">剩余：2小时30分钟</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold">15:45</p>
                <p class="text-sm opacity-80">PM</p>
            </div>
        </div>

        <!-- Weather Card (4x1) -->
        <div class="card col-span-4 row-span-1 flex items-center justify-between px-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cloud-sun fa-lg icon-style"></i>
                <div>
                    <p class="text-lg font-bold">多云转晴</p>
                    <p class="text-sm opacity-80">空气质量：良好</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-3xl font-bold">22°C</p>
                <p class="text-sm opacity-80">体感 24°C</p>
            </div>
        </div>

        <!-- VPA Avatar (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col items-center justify-center p-4">
            <img src="https://media.giphy.com/media/3oKIPnAiaMCws8nOsE/giphy.gif" alt="VPA Avatar" class="w-32 h-32 rounded-full border-4 border-white/30 object-cover shadow-lg">
            <p class="mt-4 text-xl font-semibold">小绿</p>
            <p class="opacity-80">休息模式</p>
        </div>

        <!-- Video Player Card (4x2) -->
        <div class="card col-span-4 row-span-2 flex flex-col p-6 bg-cover bg-center" style="background-image: url('https://source.unsplash.com/800x400/?movie,scene');">
            <div class="mt-auto flex items-center justify-around text-4xl text-white bg-black/30 p-4 rounded-lg">
                <i class="fas fa-backward-step cursor-pointer hover:text-cyan-400"></i>
                <i class="fas fa-play-circle cursor-pointer hover:text-cyan-400"></i>
                <i class="fas fa-forward-step cursor-pointer hover:text-cyan-400"></i>
                <i class="fas fa-expand cursor-pointer hover:text-cyan-400"></i>
            </div>
        </div>

        <!-- News Card (2x2) -->
        <div class="card col-span-2 row-span-2 flex flex-col justify-between p-6">
            <div>
                <h3 class="text-2xl font-bold mb-2">今日热点</h3>
                <p class="text-lg opacity-90 leading-tight">AI芯片取得新突破，算力提升300%。</p>
            </div>
            <a href="#" class="text-cyan-400 font-semibold self-end hover:underline">查看详情 &rarr;</a>
        </div>

        <!-- Quick Control Card (8x1) -->
        <div class="card col-span-8 row-span-1 flex items-center justify-around px-8 text-3xl">
            <div class="flex flex-col items-center cursor-pointer hover:text-cyan-400">
                <i class="fas fa-chair icon-style"></i>
                <span class="text-base mt-2">座椅按摩</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer hover:text-cyan-400">
                <i class="fas fa-wind icon-style"></i>
                <span class="text-base mt-2">新风模式</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer hover:text-cyan-400">
                <i class="fas fa-blinds-open icon-style"></i>
                <span class="text-base mt-2">天窗遮阳</span>
            </div>
            <div class="flex flex-col items-center cursor-pointer hover:text-cyan-400">
                <i class="fas fa-lightbulb-on icon-style"></i>
                <span class="text-base mt-2">氛围灯</span>
            </div>
        </div>

    </div>

    <!-- 引入过渡管理器 -->
    <script src="过渡/enhanced_transition_manager.js"></script>
    
    <script>
        // 页面加载完成后初始化过渡管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已有全局过渡管理器
            if (!window.enhancedTransitionManager) {
                window.enhancedTransitionManager = new EnhancedTransitionManager({
                    enableDebug: false,
                    enableSnapshots: true,
                    snapshotQuality: 0.8,
                    transitionDuration: 1.2
                });
            }
        });
    </script>

</body>
</html>