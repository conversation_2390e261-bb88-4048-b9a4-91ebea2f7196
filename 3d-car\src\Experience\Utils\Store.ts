// 简单的事件发布/订阅模式
type Listener<T> = (state: T) => void;

// 创建一个简单的状态管理器
class SimpleStore<T> {
  private state: T;
  private listeners: Set<Listener<T>> = new Set();

  constructor(initialState: T) {
    this.state = initialState;
  }

  getState(): T {
    return this.state;
  }

  setState(partial: Partial<T>) {
    this.state = { ...this.state, ...partial };
    this.notify();
  }

  subscribe(listener: Listener<T>): () => void {
    this.listeners.add(listener);
    
    // 返回取消订阅的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notify() {
    for (const listener of this.listeners) {
      listener(this.state);
    }
  }
}

// 定义颜色状态接口
interface CarColorState {
  bodyColor: string;
  setBodyColor: (color: string) => void;
}

// 创建车身颜色状态存储
const initialState: CarColorState = {
  bodyColor: '#26d6e9',
  setBodyColor: () => {} // 这会在下面被覆盖
};

// 创建状态管理器实例
export const carColorStore = new SimpleStore<CarColorState>(initialState);

// 添加setBodyColor方法
carColorStore.setState({
  setBodyColor: (color: string) => carColorStore.setState({ bodyColor: color })
});

// 导出统一的API
export const useCarColorStore = {
  getState: () => carColorStore.getState(),
  setState: (partial: Partial<CarColorState>) => carColorStore.setState(partial),
  subscribe: (listener: Listener<CarColorState>) => carColorStore.subscribe(listener)
};

// 颜色预设
export const colorPresets = [
  {
    name: '极速蓝',
    color: '#26d6e9',
  },
  {
    name: '森林绿',
    color: '#444c3c',
  },
  {
    name: '炫酷灰',
    color: '#5d5d5d',
  },
  {
    name: '银翼白',
    color: '#8a8a8a',
  },
  {
    name: '深邃紫',
    color: '#3e3543',
  },
  {
    name: '熔岩红',
    color: '#822817',
  },
  {
    name: '海洋蓝',
    color: '#354860',
  },
  {
    name: '午夜蓝',
    color: '#273647',
  },
  {
    name: '曜石黑',
    color: '#121117',
  },
]; 

// 定义车衣状态接口
interface CarSkinState {
  currentSkin: string | null;
  setSkin: (skinId: string | null) => void;
}

// 创建车衣状态存储
const initialSkinState: CarSkinState = {
  currentSkin: null, // 初始状态无车衣
  setSkin: () => {} // 这会在下面被覆盖
};

// 创建车衣状态管理器实例
export const carSkinStore = new SimpleStore<CarSkinState>(initialSkinState);

// 添加setSkin方法
carSkinStore.setState({
  setSkin: (skinId: string | null) => carSkinStore.setState({ currentSkin: skinId })
});

// 导出统一的API
export const useCarSkinStore = {
  getState: () => carSkinStore.getState(),
  setState: (partial: Partial<CarSkinState>) => carSkinStore.setState(partial),
  subscribe: (listener: Listener<CarSkinState>) => carSkinStore.subscribe(listener)
};

// 车衣预设
export const skinPresets = [
  {
    id: null,
    name: '默认',
    thumbnail: null, // 无缩略图，显示纯色
    description: '默认车身，无贴图'
  },
  {
    id: 'decal',
    name: '赛车风格',
    thumbnail: 'decal_thumb',
    description: '专业赛车外观'
  },
  // 可以添加更多车衣预设
];