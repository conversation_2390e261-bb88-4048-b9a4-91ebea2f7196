# Product Overview

This repository contains a comprehensive **AI-powered theme generation and 3D visualization platform** with multiple interconnected applications:

## Core Products

### 1. Text-to-Video Generation System (文生视频系统)
- **Purpose**: Generate high-quality videos up to 3 minutes from text descriptions
- **Features**: Configurable resolution, frame rate, real-time progress tracking
- **Architecture**: React frontend (theme_ui) + FastAPI backend (theme_backend)

### 2. Magic Coloring Book Generator (魔法画册生成器)
- **Purpose**: AI-powered coloring book creation with Studio Ghibli aesthetic
- **Features**: ComfyUI integration, custom workflow support, online coloring functionality
- **Target**: Creative users seeking personalized coloring materials

### 3. 3D Car Showcase (超维3D汽车展示)
- **Purpose**: Interactive 3D car visualization with customizable themes
- **Features**: Three.js-based rendering, dynamic decal application, real-time color changing
- **Model**: Xiaomi SU7 replica with special themed versions (e.g., Furina edition)

### 4. Theme Generation Service (长安主题生成服务)
- **Purpose**: Automated UI theme generation using ComfyUI workflows
- **Features**: Multi-stage processing (wallpaper → UI → components → optimization)
- **Output**: Complete theme packages with icons, wallpapers, and color schemes

## Key Value Propositions
- **AI-First**: Leverages multiple AI models (LLM, diffusion, vision) for content generation
- **Modular Architecture**: Microservices design enabling independent scaling
- **Creative Tools**: Empowers users to create personalized digital content
- **Production Ready**: Docker deployment, load balancing, comprehensive error handling