<template>
  <svg
    :class="iconClasses"
    :width="size"
    :height="size"
    :viewBox="viewBox"
    :style="iconStyles"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    :aria-label="ariaLabel"
  >
    <component :is="iconComponent" />
  </svg>
</template>

<script>
import { computed, defineAsyncComponent } from 'vue'

export default {
  name: 'BaseIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: [String, Number],
      default: 24
    },
    color: {
      type: String,
      default: 'currentColor'
    },
    category: {
      type: String,
      default: 'system',
      validator: (value) => ['system', 'weather', 'media', 'automotive'].includes(value)
    },
    animated: {
      type: Boolean,
      default: false
    },
    rotation: {
      type: [String, Number],
      default: 0
    },
    ariaLabel: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const iconClasses = computed(() => [
      'base-icon',
      `base-icon--${props.category}`,
      {
        'base-icon--animated': props.animated
      }
    ])

    const iconStyles = computed(() => ({
      color: props.color,
      transform: props.rotation ? `rotate(${props.rotation}deg)` : undefined
    }))

    const viewBox = computed(() => '0 0 24 24')

    // 动态加载图标组件
    const iconComponent = computed(() => {
      const categoryMap = {
        system: 'SystemIcons',
        weather: 'WeatherIcons', 
        media: 'MediaIcons',
        automotive: 'AutomotiveIcons'
      }
      
      const categoryFolder = categoryMap[props.category]
      const iconName = props.name.charAt(0).toUpperCase() + props.name.slice(1) + 'Icon'
      
      return defineAsyncComponent({
        loader: () => import(`../icons/${categoryFolder}/${iconName}.vue`).catch(() => {
          // 如果找不到图标，返回默认图标
          return import('../icons/SystemIcons/DefaultIcon.vue')
        }),
        loadingComponent: () => null,
        errorComponent: () => null
      })
    })

    return {
      iconClasses,
      iconStyles,
      viewBox,
      iconComponent
    }
  }
}
</script>

<style scoped>
.base-icon {
  display: inline-block;
  flex-shrink: 0;
  transition: all var(--transition-fast);
  vertical-align: middle;
}

.base-icon--animated {
  transition: all var(--transition-normal);
}

.base-icon--animated:hover {
  transform: scale(1.1);
}

/* === 分类特定样式 === */
.base-icon--system {
  color: var(--text-primary);
}

.base-icon--weather {
  color: var(--color-primary);
}

.base-icon--media {
  color: var(--color-accent);
}

.base-icon--automotive {
  color: var(--color-secondary);
}

/* === 动画效果 === */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* === 特殊动画类 === */
.base-icon.pulse {
  animation: pulse 2s ease-in-out infinite;
}

.base-icon.rotate {
  animation: rotate 2s linear infinite;
}

.base-icon.bounce {
  animation: bounce 1s ease-in-out;
}

/* === 悬停效果 === */
.base-icon:hover {
  color: var(--color-primary);
}

/* === 焦点样式 === */
.base-icon:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 2px;
}

/* === 禁用状态 === */
.base-icon.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* === 响应式尺寸 === */
@media (max-width: 768px) {
  .base-icon {
    /* 在小屏幕上稍微增大图标以便触摸 */
    min-width: 24px;
    min-height: 24px;
  }
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  .base-icon--system {
    color: var(--text-primary);
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  .base-icon {
    filter: contrast(1.2);
  }
}

/* === 减少动画模式 === */
@media (prefers-reduced-motion: reduce) {
  .base-icon,
  .base-icon--animated {
    transition: none;
  }
  
  .base-icon.pulse,
  .base-icon.rotate,
  .base-icon.bounce {
    animation: none;
  }
}
</style>
