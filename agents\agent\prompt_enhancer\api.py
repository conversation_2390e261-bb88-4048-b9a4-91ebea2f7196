#!/usr/bin/env python
"""
提示词增强服务API
提供HTTP接口，接收前端请求并调用提示词增强智能体
"""

import os
import time
import uuid
import json
import asyncio
import logging
from typing import Optional, Dict, Any

import aiohttp
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# 导入提示词增强智能体，使用绝对导入替代相对导入
import prompt_enhancer_agent
from prompt_enhancer_agent import PromptEnhancerAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="提示词增强服务",
    description="提供文生图提示词增强功能的API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源的请求
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
)

# 全局智能体实例
enhancer_agent = None

# 请求模型
class EnhancePromptRequest(BaseModel):
    """增强提示词请求模型"""
    prompt: str
    session_id: Optional[str] = None

class EnhancePromptResponse(BaseModel):
    """增强提示词响应模型"""
    session_id: str
    status: str = "processing"
    message: str = "提示词增强请求已接收，正在处理中"

@app.on_event("startup")
async def startup_event():
    """应用启动时执行，初始化智能体"""
    global enhancer_agent
    
    # 获取MCP服务URL
    mcp_url = os.environ.get("MCP_URL", "http://localhost:19220")
    
    # 获取模型配置 - 注意：当前PromptEnhancerAgent实现不使用这些参数
    model_name = os.environ.get("MODEL_NAME")
    api_base = os.environ.get("API_BASE")
    api_key = os.environ.get("API_KEY")
    
    logger.info(f"启动服务，MCP URL: {mcp_url}")
    
    # 初始化智能体 - 只传递mcp_url参数，其他参数在智能体内部已经设置
    enhancer_agent = PromptEnhancerAgent(
        mcp_url=mcp_url
    )
    
    # 检查MCP服务是否可用
    try:
        await check_mcp_health(mcp_url)
    except Exception as e:
        logger.warning(f"MCP服务健康检查失败: {str(e)}")
        logger.warning("服务将继续启动，但可能无法正常工作，请确保MCP服务已启动")

async def check_mcp_health(mcp_url: str, timeout: int = 5) -> bool:
    """
    检查MCP服务健康状态
    
    Args:
        mcp_url: MCP服务URL
        timeout: 请求超时时间（秒）
        
    Returns:
        bool: 服务是否健康
    """
    # 尝试不同的健康检查路径
    health_paths = ["health", "healthz", ""]
    
    async with aiohttp.ClientSession() as session:
        for path in health_paths:
            try:
                # 构建健康检查URL
                health_url = mcp_url
                if health_url.endswith('/'):
                    health_url += path
                else:
                    health_url += f"/{path}" if path else ""
                
                logger.info(f"检查MCP服务健康状态: {health_url}")
                
                # 发送请求
                async with session.get(health_url, timeout=timeout) as response:
                    if response.status == 200:
                        logger.info("MCP服务健康状态: 正常")
                        return True
                    else:
                        logger.warning(f"MCP服务健康检查失败，状态码: {response.status}")
            except Exception as e:
                logger.warning(f"MCP服务健康检查异常: {str(e)}")
                continue
    
    # 所有检查路径都失败
    raise Exception("MCP服务不可用，无法连接到任何健康检查端点")

@app.get("/health")
async def health_check():
    """
    健康检查端点
    
    Returns:
        字典: 健康状态信息
    """
    return {
        "status": "ok",
        "service": "prompt-enhancer-api",
        "timestamp": time.time()
    }

async def process_prompt_enhancement(session_id: str, prompt: str):
    """
    后台处理提示词增强任务
    
    Args:
        session_id: 会话ID
        prompt: 用户提示词
    """
    try:
        # 初始化MCP工具（如果尚未初始化）
        if not enhancer_agent.mcp_tools:
            await enhancer_agent.init_mcp_tools()
        
        # 流式增强提示词
        await enhancer_agent.enhance_prompt_stream(prompt, session_id)
        logger.info(f"提示词增强完成，会话ID: {session_id}")
    except Exception as e:
        logger.error(f"提示词增强处理失败: {str(e)}")
        # 在实际场景中，这里应该通过某种方式通知前端处理失败

@app.post("/api/enhance", response_model=EnhancePromptResponse)
async def enhance_prompt(request: EnhancePromptRequest, background_tasks: BackgroundTasks):
    """
    增强提示词API端点
    
    Args:
        request: 增强提示词请求
        background_tasks: 后台任务
        
    Returns:
        EnhancePromptResponse: 响应，包含会话ID
    """
    try:
        # 获取或生成会话ID
        session_id = request.session_id or f"session-{uuid.uuid4()}"
        
        # 检查提示词
        prompt = request.prompt.strip()
        if not prompt:
            raise HTTPException(status_code=400, detail="提示词不能为空")
        
        # 添加后台任务
        background_tasks.add_task(process_prompt_enhancement, session_id, prompt)
        
        return EnhancePromptResponse(
            session_id=session_id,
            status="processing",
            message="提示词增强请求已接收，请通过WebSocket连接获取实时结果"
        )
    except Exception as e:
        logger.error(f"处理增强请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理增强请求失败: {str(e)}")

@app.get("/api/mcp-info")
async def get_mcp_info():
    """
    获取MCP服务信息
    
    Returns:
        字典: MCP服务信息
    """
    mcp_url = os.environ.get("MCP_URL", "http://localhost:19220")
    
    # 构建WebSocket URL
    ws_url = mcp_url.replace("http://", "ws://").replace("https://", "wss://")
    if ws_url.endswith('/'):
        ws_url += "api/v1/connect"
    else:
        ws_url += "/api/v1/connect"
    
    return {
        "mcp_url": mcp_url,
        "mcp_ws_url": ws_url,
        "connected": enhancer_agent is not None and enhancer_agent.mcp_tools is not None
    }

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": str(exc)},
    )

def main():
    """主函数，用于直接启动"""
    import uvicorn
    
    host = os.environ.get("HOST", "0.0.0.0")
    port = int(os.environ.get("PORT", 8100))
    
    logger.info(f"启动提示词增强API服务，地址: {host}:{port}")
    uvicorn.run("api:app", host=host, port=port, reload=True)

if __name__ == "__main__":
    main() 