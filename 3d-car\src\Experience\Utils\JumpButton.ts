import { useConfigStore } from './ConfigStore';

export class JumpButton {
  container: HTMLDivElement;
  button: HTMLDivElement;
  particleContainer: HTMLDivElement;
  settingsButton: HTMLButtonElement;
  settingsPanel: HTMLDivElement;
  urlInput: HTMLInputElement;
  saveButton: HTMLButtonElement;
  cancelButton: HTMLButtonElement;
  isSettingsVisible: boolean = false;

  constructor() {
    // 创建容器
    this.container = document.createElement('div');
    this.button = document.createElement('div');
    this.particleContainer = document.createElement('div');
    this.settingsButton = document.createElement('button');
    this.settingsPanel = document.createElement('div');
    this.urlInput = document.createElement('input');
    this.saveButton = document.createElement('button');
    this.cancelButton = document.createElement('button');

    this.init();
  }

  init() {
    // 按钮容器样式
    Object.assign(this.container.style, {
      position: 'fixed',
      bottom: '30px',
      left: '30px',
      zIndex: '10000',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      gap: '10px',
    });

    // 按钮样式 - 毛玻璃效果
    Object.assign(this.button.style, {
      padding: '15px 25px',
      borderRadius: '30px',
      cursor: 'pointer',
      background: 'rgba(20, 20, 20, 0.4)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      color: 'white',
      fontSize: '16px',
      fontWeight: 'bold',
      boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
      transition: 'all 0.3s ease',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '180px',
      height: '50px',
    });

    // 设置按钮内容
    this.button.textContent = '前往超维智能座舱数字全息系统';

    // 粒子容器
    Object.assign(this.particleContainer.style, {
      position: 'absolute',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      overflow: 'hidden',
    });

    this.button.appendChild(this.particleContainer);

    // 设置按钮样式
    Object.assign(this.settingsButton.style, {
      width: '30px',
      height: '30px',
      borderRadius: '50%',
      backgroundColor: 'rgba(20, 20, 20, 0.4)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      color: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      fontSize: '14px',
      transition: 'all 0.3s ease',
    });

    this.settingsButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/><path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/></svg>';

    // 设置面板样式
    Object.assign(this.settingsPanel.style, {
      position: 'absolute',
      bottom: '60px',
      left: '0',
      width: '280px',
      padding: '15px',
      borderRadius: '10px',
      backgroundColor: 'rgba(20, 20, 20, 0.7)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
      color: 'white',
      display: 'none',
      flexDirection: 'column',
      gap: '10px',
    });

    // 创建标题
    const title = document.createElement('div');
    title.textContent = '设置跳转地址';
    title.style.fontSize = '14px';
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '5px';

    // URL输入框样式
    Object.assign(this.urlInput.style, {
      width: '100%',
      padding: '8px 12px',
      borderRadius: '5px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      color: 'white',
      fontSize: '14px',
      boxSizing: 'border-box',
    });

    this.urlInput.placeholder = '输入主题系统URL';
    this.urlInput.value = useConfigStore.getThemeUIUrl();

    // 按钮容器
    const buttonContainer = document.createElement('div');
    Object.assign(buttonContainer.style, {
      display: 'flex',
      gap: '10px',
      marginTop: '10px',
    });

    // 保存按钮样式
    Object.assign(this.saveButton.style, {
      padding: '6px 12px',
      borderRadius: '5px',
      border: 'none',
      backgroundColor: 'rgba(38, 214, 233, 0.8)',
      color: 'white',
      fontSize: '14px',
      cursor: 'pointer',
      flex: '1',
    });

    this.saveButton.textContent = '保存';

    // 取消按钮样式
    Object.assign(this.cancelButton.style, {
      padding: '6px 12px',
      borderRadius: '5px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      backgroundColor: 'rgba(60, 60, 60, 0.4)',
      color: 'white',
      fontSize: '14px',
      cursor: 'pointer',
      flex: '1',
    });

    this.cancelButton.textContent = '取消';

    // 组装设置面板
    buttonContainer.appendChild(this.saveButton);
    buttonContainer.appendChild(this.cancelButton);

    this.settingsPanel.appendChild(title);
    this.settingsPanel.appendChild(this.urlInput);
    this.settingsPanel.appendChild(buttonContainer);

    // 添加到容器
    this.container.appendChild(this.button);
    this.container.appendChild(this.settingsButton);
    this.container.appendChild(this.settingsPanel);

    // 添加到文档
    document.body.appendChild(this.container);

    // 添加事件
    this.addEvents();

    // 创建粒子
    this.createParticles();
  }

  addEvents() {
    // 鼠标悬停效果
    this.button.addEventListener('mouseenter', () => {
      this.button.style.transform = 'scale(1.05) translateY(-2px)';
      this.button.style.boxShadow = '0 15px 25px rgba(0, 0, 0, 0.4)';
      this.animateParticles(true);
    });

    this.button.addEventListener('mouseleave', () => {
      this.button.style.transform = 'scale(1) translateY(0)';
      this.button.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.3)';
      this.animateParticles(false);
    });

    // 点击跳转
    this.button.addEventListener('click', () => {
      const url = useConfigStore.getThemeUIUrl();
      window.location.href = url;
    });

    // 设置按钮悬停效果
    this.settingsButton.addEventListener('mouseenter', () => {
      this.settingsButton.style.backgroundColor = 'rgba(40, 40, 40, 0.6)';
    });

    this.settingsButton.addEventListener('mouseleave', () => {
      this.settingsButton.style.backgroundColor = 'rgba(20, 20, 20, 0.4)';
    });

    // 设置按钮点击
    this.settingsButton.addEventListener('click', () => {
      this.toggleSettings();
    });

    // 保存按钮点击
    this.saveButton.addEventListener('click', () => {
      const url = this.urlInput.value.trim();
      if (url) {
        useConfigStore.setThemeUIUrl(url);
        this.toggleSettings();
      }
    });

    // 取消按钮点击
    this.cancelButton.addEventListener('click', () => {
      this.urlInput.value = useConfigStore.getThemeUIUrl();
      this.toggleSettings();
    });
  }

  toggleSettings() {
    this.isSettingsVisible = !this.isSettingsVisible;

    if (this.isSettingsVisible) {
      this.settingsPanel.style.display = 'flex';
      this.settingsButton.style.backgroundColor = 'rgba(38, 214, 233, 0.6)';
    } else {
      this.settingsPanel.style.display = 'none';
      this.settingsButton.style.backgroundColor = 'rgba(20, 20, 20, 0.4)';
    }
  }

  createParticles() {
    // 创建30个粒子
    for (let i = 0; i < 30; i++) {
      const particle = document.createElement('div');

      // 随机尺寸
      const size = Math.random() * 6 + 2;

      // 随机位置
      const x = Math.random() * 100;
      const y = Math.random() * 100;

      // 随机颜色
      const hue = Math.random() * 60 + 180; // 蓝绿色系

      // 设置样式
      Object.assign(particle.style, {
        position: 'absolute',
        width: `${size}px`,
        height: `${size}px`,
        borderRadius: '50%',
        backgroundColor: `hsla(${hue}, 80%, 60%, 0.8)`,
        boxShadow: `0 0 ${size * 2}px hsla(${hue}, 100%, 70%, 0.8)`,
        left: `${x}%`,
        top: `${y}%`,
        opacity: '0',
        transform: 'scale(0)',
        transition: 'all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1)',
      });

      this.particleContainer.appendChild(particle);
    }
  }

  animateParticles(show: boolean) {
    // 获取所有粒子
    const particles = this.particleContainer.children;

    // 动画每个粒子
    for (let i = 0; i < particles.length; i++) {
      const particle = particles[i] as HTMLDivElement;

      // 错开动画时间
      const delay = Math.random() * 0.2;
      particle.style.transitionDelay = `${delay}s`;

      if (show) {
        particle.style.opacity = Math.random() * 0.6 + 0.4 + '';
        particle.style.transform = 'scale(1)';

        // 添加移动动画
        const x = parseFloat(particle.style.left);
        const y = parseFloat(particle.style.top);

        // 随机移动距离
        const dx = (Math.random() - 0.5) * 20;
        const dy = (Math.random() - 0.5) * 20;

        particle.style.left = `${x + dx}%`;
        particle.style.top = `${y + dy}%`;
      } else {
        particle.style.opacity = '0';
        particle.style.transform = 'scale(0)';
      }
    }
  }

  // 销毁组件
  dispose() {
    // 移除DOM元素
    if (this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
} 