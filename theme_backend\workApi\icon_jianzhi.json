{"11": {"inputs": {"steps": 15, "cfg": 7, "sampler_name": "dpmpp_2m_sde", "scheduler": "karras", "denoise": 1, "image_output": "<PERSON>de", "link_id": 0, "save_prefix": "ComfyUI", "seed": 170093210256594, "pipe": ["12", 0], "model": ["12", 1], "positive": ["12", 4], "negative": ["12", 5], "vae": ["12", 2]}, "class_type": "easy fullkSampler", "_meta": {"title": "简易k采样器（完整版）"}}, "12": {"inputs": {"ckpt_name": "sd1.5/revAnimated_v2Rebirth.safetensors", "config_name": "<PERSON><PERSON><PERSON>", "vae_name": "vae-ft-mse-840000-ema-pruned.ckpt", "clip_skip": -2, "lora_name": "None", "lora_model_strength": 1, "lora_clip_strength": 1, "resolution": "width x height (custom)", "empty_latent_width": 1024, "empty_latent_height": 616, "positive": ["71", 0], "positive_token_normalization": "length+mean", "positive_weight_interpretation": "A1111", "negative": "low quality,watermark,text,bad anatomy,bad hand,extra hands,extra fingers,(extra digit and hands and fingers and legs and arms),(deformed fingers),extra legs,lowres,bad anatomy,bad hands,text,error,missing fingers,fewer digits,cropped,worst quality,low quality,normal quality,jpeg artifacts,signature,watermark,username,blurry", "negative_token_normalization": "length+mean", "negative_weight_interpretation": "A1111", "batch_size": 1, "a1111_prompt_style": true, "speak_and_recognation": true, "model_override": ["82", 0], "optional_lora_stack": ["73", 0], "optional_controlnet_stack": ["21", 0]}, "class_type": "easy fullLoader", "_meta": {"title": "简易加载器 (完整版)"}}, "21": {"inputs": {"toggle": true, "mode": "simple", "num_controlnet": 3, "controlnet_1": "control_v11p_sd15_canny.pth", "controlnet_1_strength": 0.6, "start_percent_1": 0, "end_percent_1": 1, "scale_soft_weight_1": 1, "controlnet_2": "control_v11f1p_sd15_depth.pth", "controlnet_2_strength": 1, "start_percent_2": 0, "end_percent_2": 1, "scale_soft_weight_2": 1, "controlnet_3": "control_v11f1e_sd15_tile.pth", "controlnet_3_strength": 0.6, "start_percent_3": 0, "end_percent_3": 1, "scale_soft_weight_3": 1, "optional_controlnet_stack": ["74", 0], "image_1": ["22", 0], "image_2": ["23", 0], "image_3": ["69", 0]}, "class_type": "easy controlnetStack", "_meta": {"title": "EasyControlnetStack"}}, "22": {"inputs": {"preprocessor": "CannyEdgePreprocessor", "resolution": 512, "image": ["39", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "Aux集成预处理器"}}, "23": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 512, "image": ["39", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "Aux集成预处理器"}}, "36": {"inputs": {"image": "icon_frame2.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "39": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["76", 0], "layer_image": ["76", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V2", "_meta": {"title": "LayerUtility: ImageBlendAdvance V2"}}, "40": {"inputs": {"image": "icon_huabu3.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "53": {"inputs": {"image": "icon_mask_huabu2.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "54": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["58", 1], "mask": ["53", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "遮罩裁剪"}}, "56": {"inputs": {"invert_mask": false, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["40", 0], "layer_image": ["11", 1]}, "class_type": "LayerUtility: ImageBlendAdvance V2", "_meta": {"title": "LayerUtility: ImageBlendAdvance V2"}}, "58": {"inputs": {"steps": 15, "cfg": 7, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 0.3, "image_output": "<PERSON>de", "link_id": 0, "save_prefix": "ComfyUI", "seed": 392309654398562, "pipe": ["59", 0], "positive": ["59", 4], "negative": ["59", 5], "latent": ["62", 0], "vae": ["59", 2]}, "class_type": "easy fullkSampler", "_meta": {"title": "简易k采样器（完整版）"}}, "59": {"inputs": {"ckpt_name": "sd1.5/revAnimated_v2Rebirth.safetensors", "config_name": "<PERSON><PERSON><PERSON>", "vae_name": "vae-ft-mse-840000-ema-pruned.ckpt", "clip_skip": -2, "lora_name": "1.5/add_detail.safetensors", "lora_model_strength": 1, "lora_clip_strength": 1, "resolution": "width x height (custom)", "empty_latent_width": 1280, "empty_latent_height": 680, "positive": ["71", 0], "positive_token_normalization": "length+mean", "positive_weight_interpretation": "A1111", "negative": "low quality,watermark,text,bad anatomy,bad hand,extra hands,extra fingers,(extra digit and hands and fingers and legs and arms),(deformed fingers),extra legs,lowres,bad anatomy,bad hands,text,error,missing fingers,fewer digits,cropped,worst quality,low quality,normal quality,jpeg artifacts,signature,watermark,username,blurry", "negative_token_normalization": "length+mean", "negative_weight_interpretation": "A1111", "batch_size": 1, "a1111_prompt_style": true, "speak_and_recognation": true, "model_override": ["82", 0], "optional_lora_stack": ["73", 0], "optional_controlnet_stack": ["60", 0]}, "class_type": "easy fullLoader", "_meta": {"title": "简易加载器 (完整版)"}}, "60": {"inputs": {"toggle": true, "mode": "simple", "num_controlnet": 1, "controlnet_1": "control_v11f1e_sd15_tile.pth", "controlnet_1_strength": 0.5, "start_percent_1": 0, "end_percent_1": 1, "scale_soft_weight_1": 1, "controlnet_2": "control_v11f1p_sd15_depth.pth", "controlnet_2_strength": 1, "start_percent_2": 0, "end_percent_2": 1, "scale_soft_weight_2": 1, "controlnet_3": "None", "controlnet_3_strength": 1, "start_percent_3": 0, "end_percent_3": 1, "scale_soft_weight_3": 1, "image_1": ["61", 0], "image_2": ["23", 0]}, "class_type": "easy controlnetStack", "_meta": {"title": "EasyControlnetStack"}}, "61": {"inputs": {"preprocessor": "TilePreprocessor", "resolution": 512, "image": ["56", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "Aux集成预处理器"}}, "62": {"inputs": {"pixels": ["56", 0], "vae": ["59", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "65": {"inputs": {"String": "Festival Poster Design Paper-cut Effect Three-dimensional Sense Chinese Traditional Festival Chinese 16K Ultra-fine Festival", "speak_and_recognation": true}, "class_type": "String", "_meta": {"title": "字符串"}}, "67": {"inputs": {"filename_prefix": "/ssd2/ComfyUI/output/changan/mainLine_111/releases/icon", "images": ["54", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "69": {"inputs": {"preprocessor": "TilePreprocessor", "resolution": 1024, "image": ["70", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "Aux集成预处理器"}}, "70": {"inputs": {"upscale_method": "nearest-exact", "width": 1024, "height": 616, "crop": "center", "image": ["79", 0]}, "class_type": "ImageScale", "_meta": {"title": "图像缩放"}}, "71": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["65", 0], "text_b": ["72", 0], "text_c": "", "speak_and_recognation": true, "result": "Festival Poster Design Paper-cut Effect Three-dimensional Sense Chinese Traditional Festival Chinese 16K Ultra-fine Festival, solo, simple_background, no_humans, black_background, purple_background, purple_theme"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "字符串操作"}}, "72": {"inputs": {"model": "wd-v1-4-moat-tagger-v2", "threshold": 0.35, "character_threshold": 0.85, "replace_underscore": false, "trailing_comma": false, "exclude_tags": "1girl", "tags": "solo, simple_background, no_humans, black_background, purple_background, purple_theme", "image": ["79", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14反推提示词"}}, "73": {"inputs": {"switch_1": "On", "lora_name_1": "1.5/国风/纸雕_叠层立体剪纸艺术_v1.0.safetensors", "model_weight_1": 0.5, "clip_weight_1": 1, "switch_2": "On", "lora_name_2": "1.5/Dark Majic_中国刺绣繁花似锦_v1.0.safetensors", "model_weight_2": 0.4, "clip_weight_2": 1, "switch_3": "On", "lora_name_3": "1.5/国风/非遗文化 _ 掐丝珐琅艺术_非遗文化 _ 掐丝珐琅艺术.safetensors", "model_weight_3": 0.4, "clip_weight_3": 1}, "class_type": "CR LoRA Stack", "_meta": {"title": "LoRA堆"}}, "74": {"inputs": {"toggle": false, "mode": "simple", "num_controlnet": 1, "controlnet_1": "t2iadapter_color_sd14v1.pth", "controlnet_1_strength": 1.5, "start_percent_1": 0, "end_percent_1": 1, "scale_soft_weight_1": 1, "controlnet_2": "None", "controlnet_2_strength": 1, "start_percent_2": 0, "end_percent_2": 1, "scale_soft_weight_2": 1, "controlnet_3": "None", "controlnet_3_strength": 1, "start_percent_3": 0, "end_percent_3": 1, "scale_soft_weight_3": 1, "image_1": ["75", 0]}, "class_type": "easy controlnetStack", "_meta": {"title": "EasyControlnetStack"}}, "75": {"inputs": {"resolution": 512, "image": ["70", 0]}, "class_type": "ColorPreprocessor", "_meta": {"title": "Color颜色预处理器"}}, "76": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "79": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "82": {"inputs": {"ckpt_name": "sd1.5/revAnimated_v2Rebirth.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器(简易)"}}}