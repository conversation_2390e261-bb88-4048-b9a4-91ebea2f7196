# 提示词增强智能体设计文档

## 1. 系统概述

提示词增强智能体是一个专门用于将用户简单描述转化为详细、结构化的文生图提示词的AI代理系统。该系统基于AutoGen框架实现，集成了MCP服务进行会话管理和数据流处理。

### 1.1 核心功能

- 接收用户简单描述
- 分析理解核心意图和关键元素
- 扩展详细内容（主体元素、风格、技术参数等）
- 生成结构化输出（正面提示词/负面提示词）
- 实时返回思考过程和生成结果

## 2. 系统架构

### 2.1 整体架构

```
+----------------+            +----------------+            +-------------------+
|                |  HTTP请求   |                |  调用       |                   |
|   前端界面     +------------>+  API服务       +------------>+  提示词增强智能体  |
|                |             |                |            |                   |
+--------+-------+             +--------+-------+            +---------+---------+
         ^                              ^                              |
         |                              |                              |
         |                              |                              |
         |           WebSocket          |             数据流            |
         +------------------------------+                              |
                                        |                              |
                                        |                              |
                                        |                              |
                                        |                              |
                                +-------+---------+                    |
                                |                 |                    |
                                |   MCP服务       |<-------------------+
                                |                 |    存储会话数据
                                +-----------------+
```

### 2.2 组件说明

1. **前端界面**：提供用户交互界面，发送提示词请求，接收实时更新
2. **API服务**：处理HTTP请求，创建会话，启动智能体任务
3. **WebSocket服务**：维持长连接，推送实时更新
4. **提示词增强智能体**：核心AI代理，处理提示词增强逻辑
5. **MCP服务**：管理会话数据，支持数据持久化

## 3. 智能体设计

### 3.1 AutoGen多代理体系

提示词增强智能体采用AutoGen框架的多代理协作模式，包含以下角色：

1. **增强专家(Enhancer)**：
   - 主要负责将简单描述扩展为详细提示词
   - 关注内容扩展和创意发掘

2. **评审专家(Reviewer)**：
   - 负责检查增强提示词的质量
   - 提供改进建议和问题指出

3. **编辑专家(Editor)**：
   - 根据评审意见修改完善提示词
   - 确保语言流畅、结构清晰
   - 负责最终版本的输出

### 3.2 代理协作流程

```
                +-------------+
                |  用户输入   |
                +------+------+
                       |
                       v
                +------+------+
                |  增强专家   |
                +------+------+
                       |
                       v
                +------+------+
                |  评审专家   |
                +------+------+
                       |
                       v
                +------+------+
                |  编辑专家   |
                +------+------+
                       |
                       v
                +------+------+
                | 最终提示词  |
                +-------------+
```

### 3.3 核心代码实现

```python
class PromptEnhancerAgent:
    """提示词增强Agent，使用AutoGen框架和流式输出"""
    
    def __init__(self, websocket_manager: WebSocketManager, config: Dict[str, Any] = None):
        self.websocket_manager = websocket_manager
        self.config = config or {}
        
        # 初始化模型客户端
        self.model_client = get_model_client()
        
        # 创建增强Agent
        self.enhancer_agent = AssistantAgent(
            name="prompt_enhancer",
            model_client=self.model_client,
            system_message=self._get_system_message("enhancer"),
            model_client_stream=True
        )
        
        # 创建评审Agent
        self.reviewer_agent = AssistantAgent(
            name="reviewer",
            model_client=self.model_client,
            system_message=self._get_system_message("reviewer"),
            model_client_stream=True
        )
        
        # 创建编辑Agent
        self.editor_agent = AssistantAgent(
            name="editor",
            model_client=self.model_client,
            system_message=self._get_system_message("editor"),
            model_client_stream=True
        )
        
        # 创建Group Chat
        self.team = RoundRobinGroupChat(
            participants=[self.enhancer_agent, self.reviewer_agent, self.editor_agent],
            max_turns=self.config.get("max_turns", 6)
        )
    
    async def enhance_prompt(self, session_id: str, prompt: str):
        """提示词增强主函数"""
        event_handler = PromptEnhancerEventHandler(self.websocket_manager, session_id)
        
        # 创建人类代理以启动对话
        user_proxy = UserProxyClient(name="User")
        
        # 设置初始消息
        initial_message = f"请将以下简单描述转换为详细的文生图提示词：\n\n{prompt}"
        
        # 启动Group Chat并实时流式输出结果
        await user_proxy.chat_stream(
            llm_config=None,
            recipient=self.team,
            message=initial_message,
            stream_handler=event_handler
        )
        
        # 返回最终结果
        final_result = self.editor_agent.last_message()
        return final_result
```

## 4. 通信与事件处理

### 4.1 实时通信设计

使用WebSocket建立前后端实时通信，支持以下事件类型：

- **thinking**: 智能体思考过程
- **content**: 生成的内容片段
- **final**: 最终完成的提示词
- **error**: 错误信息

### 4.2 事件处理器

```python
class PromptEnhancerEventHandler:
    """处理提示词增强过程中的事件"""
    
    def __init__(self, websocket_manager: WebSocketManager, session_id: str):
        self.websocket_manager = websocket_manager
        self.session_id = session_id
    
    async def on_message(self, message: str, agent_name: str, is_final: bool = False):
        """处理智能体消息事件"""
        # 根据Agent类型确定事件类型
        if agent_name == "prompt_enhancer":
            event_type = "thinking"
        elif agent_name == "reviewer":
            event_type = "review"
        elif agent_name == "editor":
            event_type = "content"
        else:
            event_type = "system"
        
        # 发送消息到WebSocket
        await self.websocket_manager.send_message(
            self.session_id,
            {
                "event_type": event_type,
                "data": {
                    "content": message,
                    "agent": agent_name,
                    "is_final": is_final
                }
            }
        )
    
    async def on_error(self, error: str):
        """处理错误事件"""
        await self.websocket_manager.send_message(
            self.session_id,
            {
                "event_type": "error",
                "data": {
                    "message": error,
                    "is_final": True
                }
            }
        )
```

## 5. 会话管理

### 5.1 会话创建流程

1. 前端发送HTTP请求创建会话
2. 后端生成唯一session_id
3. 前端使用session_id建立WebSocket连接
4. 后端启动智能体任务
5. 智能体开始处理并实时发送结果

### 5.2 会话存储

使用MCP服务存储会话数据：

- **Redis**：存储活跃会话数据
- **MongoDB**：长期存储已完成会话

## 6. 部署与配置

### 6.1 部署架构

提示词增强智能体作为theme_backend的一部分部署：

```
                  +------------------+
                  |                  |
+---------+       |  theme_backend   |
|         |       |                  |
| 前端应用 +------>+  - HTTP API      |
|         |       |  - WebSocket     |
+---------+       |  - 提示词智能体   |
                  |                  |
                  +--------+---------+
                           |
                           v
                  +--------+---------+
                  |                  |
                  |    MCP服务       |
                  |                  |
                  +------------------+
```

### 6.2 配置说明

在`.env`文件中配置：

```ini
# 模型服务
MODEL_BASE_URL=http://************:8007/v1/

# MCP服务
MCP_SERVER_URL=http://localhost:19220

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
```

## 7. 使用说明

### 7.1 API接口

**创建提示词增强会话**

```
POST /api/enhance
```

请求体：
```json
{
  "prompt": "中国风山水画"
}
```

响应：
```json
{
  "session_id": "sess_abc123",
  "status": "processing"
}
```

**WebSocket连接**

```
ws://server/api/ws/enhance/{session_id}
```

### 7.2 示例流程

1. 前端发送请求创建会话
2. 接收session_id并建立WebSocket连接
3. 监听WebSocket消息事件
4. 显示实时生成过程
5. 接收并展示最终结果
