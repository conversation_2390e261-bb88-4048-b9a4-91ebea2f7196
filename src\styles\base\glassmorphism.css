/* 毛玻璃效果样式库 - 与壁纸完美整合 */

/* === 基础毛玻璃效果 === */
.glassmorphism {
  background: rgba(255, 255, 255, var(--transparency-medium));
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium)) 
    brightness(var(--brightness-medium));
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  box-shadow: 
    var(--shadow-medium),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* === 不同强度的毛玻璃效果 === */
.glassmorphism-light {
  background: rgba(255, 255, 255, var(--transparency-high));
  backdrop-filter: 
    blur(var(--blur-light)) 
    saturate(var(--saturate-light)) 
    brightness(var(--brightness-light));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-small);
  box-shadow: var(--shadow-small);
}

.glassmorphism-medium {
  background: rgba(255, 255, 255, var(--transparency-medium));
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium)) 
    brightness(var(--brightness-medium));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-medium);
}

.glassmorphism-heavy {
  background: rgba(255, 255, 255, var(--transparency-low));
  backdrop-filter: 
    blur(var(--blur-heavy)) 
    saturate(var(--saturate-heavy)) 
    brightness(var(--brightness-heavy));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-large);
}

/* === VPA数字人专用透明背景 === */
.vpa-transparent {
  background: transparent !important;
  backdrop-filter: none !important;
  border: none !important;
  box-shadow: 
    var(--shadow-glow),
    0 0 40px rgba(0, 0, 0, 0.05);
}

/* === 动态岛专用毛玻璃 === */
.dynamic-island-glass {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: 
    blur(20px) 
    saturate(1.8) 
    brightness(1.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* === 卡片组件毛玻璃效果 === */
.card-glass {
  background: rgba(255, 255, 255, var(--transparency-medium));
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-large);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
}

.card-glass:hover {
  background: rgba(255, 255, 255, calc(var(--transparency-medium) + 0.1));
  backdrop-filter: 
    blur(calc(var(--blur-medium) + 2px)) 
    saturate(calc(var(--saturate-medium) + 0.2));
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* === 临时对话框毛玻璃 === */
.dialog-glass {
  background: rgba(255, 255, 255, var(--transparency-low));
  backdrop-filter: 
    blur(var(--blur-heavy)) 
    saturate(var(--saturate-heavy));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-xl);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  .glassmorphism,
  .glassmorphism-light,
  .glassmorphism-medium,
  .glassmorphism-heavy,
  .card-glass,
  .dialog-glass {
    background: rgba(28, 28, 30, var(--transparency-medium));
    border-color: rgba(255, 255, 255, 0.1);
  }

  .card-glass:hover {
    background: rgba(28, 28, 30, calc(var(--transparency-medium) + 0.1));
  }

  .dynamic-island-glass {
    background: rgba(0, 0, 0, 0.9);
  }
}

/* === 壁纸亮度自适应 === */
.glassmorphism-adaptive {
  background: rgba(
    calc(255 * var(--wallpaper-brightness)), 
    calc(255 * var(--wallpaper-brightness)), 
    calc(255 * var(--wallpaper-brightness)), 
    var(--transparency-medium)
  );
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium)) 
    brightness(calc(1 + (1 - var(--wallpaper-brightness)) * 0.3));
}

/* === 特殊效果 === */
.glassmorphism-glow {
  box-shadow: 
    0 0 20px rgba(var(--color-primary), 0.3),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glassmorphism-pulse {
  animation: glass-pulse 2s ease-in-out infinite;
}

@keyframes glass-pulse {
  0%, 100% {
    backdrop-filter: 
      blur(var(--blur-medium)) 
      saturate(var(--saturate-medium)) 
      brightness(var(--brightness-medium));
  }
  50% {
    backdrop-filter: 
      blur(calc(var(--blur-medium) + 4px)) 
      saturate(calc(var(--saturate-medium) + 0.3)) 
      brightness(calc(var(--brightness-medium) + 0.1));
  }
}

/* === 浏览器兼容性回退 === */
@supports not (backdrop-filter: blur(10px)) {
  .glassmorphism,
  .glassmorphism-light,
  .glassmorphism-medium,
  .glassmorphism-heavy,
  .card-glass,
  .dialog-glass {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  @media (prefers-color-scheme: dark) {
    .glassmorphism,
    .glassmorphism-light,
    .glassmorphism-medium,
    .glassmorphism-heavy,
    .card-glass,
    .dialog-glass {
      background: rgba(28, 28, 30, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}
