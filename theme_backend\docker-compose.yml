version: '3.3'

services:
  comfyui-backend:
    build:
      context: .
      dockerfile: Dockerfile
    image: changan_theme:latest
    container_name: changan-theme-backend
    ports:
      - "18632:8000"
    volumes:
      - ./public:/app/public
    environment:
      - COMFYUI_BASE_URL=http://27.159.93.61:8193
      - <PERSON><PERSON><PERSON><PERSON>_WS_URL=ws://27.159.93.61:8193/ws
    restart: unless-stopped
    networks:
      - comfyui-network

networks:
  comfyui-network:
    driver: bridge
