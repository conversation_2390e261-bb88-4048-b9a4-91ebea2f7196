# 1. 产品需求文档 (Product Requirements Document)

- **文档版本:** 9.0 (Refactored)
- **状态:** 正式版

---

## 1. 项目愿景与核心哲学

### 1.1. 愿景

打造一个主动式、情景感知、高度个性化的智能座舱体验。我们的目标是让车载系统从一个被动的工具，进化为一个能理解用户意图、预测用户需求、并主动提供服务的**"数字出行伴侣" (Virtual Personal Assistant - VPA)**。

### 1.2. 核心哲学：AI作为"座舱设计师"

本项目的核心是**"生成式UI引擎"**。AI的角色不再是执行预设脚本，而是像一个经验丰富的设计师，根据实时情景，从一个预设的、丰富的"素材库"中，为用户**决策、组合和渲染**出最佳的座舱体验，确保在任何时刻，都为用户呈现最合理、最高效、最美观的界面。

### 1.3. 核心机制：基于桌面画布的动态组合

AI的"设计"工作并非凭空创造，而是基于一个**统一的、结构化的桌面画布**。这个画布由以下两个核心元素定义：

1.  **网格系统 (The Grid)**: 一个覆盖全屏的、标准的8x4网格，是所有UI元素布局的唯一基准。
2.  **组件库 (The Component Library)**: 一套预先定义好的、具有固定尺寸和功能的标准化UI组件（如天气卡片、音乐卡片等）。

**AI HMI的全部动态表现，其本质都是AI在这个统一的桌面画布上，对组件库中的组件进行智能化的"选择"、"放置"和"更新"的排列组合过程。**

`2_Design_System.md` 中定义的**"默认桌面布局"**，可以被视为这个排列组合过程的一个**最基础、最高频的"预设方案"**。它是系统的初始状态、简单场景下的首选方案，以及所有复杂布局变化的起点和回退终点。

---

## 2. 核心概念

- **VPA 数字人 (Virtual Personal Assistant)**: 系统的智能核心与视觉化身。
- **灵动岛 (Dynamic Island)**: 屏幕顶部的持久化状态栏。
- **动态卡片 (Dynamic Cards)**: 由AI根据场景动态选择、布局的信息载体。
- **临时交互面板 (Temporary Dialog Panel)**: 用于AI发起高优先级主动服务的模态对话框。
- **样式主题 (Style Themes)**: 一套完整的视觉语言（如“宁静”、“赛博朋克”），由AI选择应用。
- **过渡效果 (Transition Effects)**: 用于场景切换时的酷炫动画（如“深度模糊”、“蒙版擦除”）。

---

## 3. LLM驱动机制

AI(LLM)完成情景分析和决策后，**不会生成任何UI代码**。相反，它会输出一个标准化的**JSON对象**，我们称之为“桌面渲染计划”。前端系统的唯一职责就是解析这份计划，并将其渲染为用户看到的界面。这种机制将AI的“创造性思维”与前端的“工程实现”完全解耦，是整个系统的基石。

*(详细数据结构定义见 `4_API_Specification.md`)*

---

## 4. 高层级场景剧本：用户早高峰通勤

本剧本旨在说明系统的核心业务逻辑，而非具体的UI布局。

### **步骤 1.0: 首次进入与引导**

*   **用户故事**: 作为一个用户，当我启动车辆时，我希望VPA能欢迎我，并提供一些快速启动选项，让我能快速开始我的旅程。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                         沉浸桌面壁纸                                     |
    |                                                                          |
    |  +--------------------------------+                                    |
    |  | 你好, 我是xx, 欢迎来到AI-HMI,  |                                    |
    |  | 我是智能桌面精灵, 可以根据场   |                                    |
    |  | 景给您定制专属桌面, 试试对我说:|             (o.o)                  |
    |  |                                |           <-- VPA形象 -->            |
    |  |  [  生成通勤桌面  ]             |                                    |
    |  |  [   导航到xx    ]             |                                    |
    |  |  [帮我规划一个独处的桌面]       |                                    |
    |  +--------------------------------+                                    |
    |                                                                          |
    +--------------------------------------------------------------------------+
    ```

### **步骤 2.0: 生成桌面与AI思考过程展示**

*   **用户故事**: 当我发出指令后，我希望能看到系统正在为我工作的反馈，这让我感到安心。

*   **ASCII 原型图 (分为两阶段)**:

    *   *阶段A: 正在生成*
        ```
        +--------------------------------------------------------------------------+
        |                                                                          |
        |                         沉浸桌面壁纸                                     |
        |                                                                          |
        |  +--------------------------+                                          |
        |  | 正在为您生成早高峰通勤桌面 |                                          |
        |  |                          |                                          |
        |  |      好的 (2s)           |                                          |
        |  +--------------------------+                                          |
        |                                                                          |
        |   (o.o)                                                                  |
        |                                                                          |
        +--------------------------------------------------------------------------+
        ```
    *   *阶段B: AI推理与地图呈现*
        ```
        +--------------------------------------------------------------------------+
        |                                     导航地图                             |
        |                                                                          |
        |  +--------------------------+                         公司(B)            |
        |  | AI推理引擎               |                            /               |
        |  | 1. 理解用户意图...       |                           /                |
        |  | 2. 检索日程/偏好...      |        幼儿园(Y)---------+                  |
        |  | 3. 任务规划...           |              |                             |
        |  +--------------------------+              |                             |
        |                                          家(H)                           |
        | (o.o)                                                                    |
        |                                                                          |
        +--------------------------------------------------------------------------+
        ```

### **步骤 3.0: 通勤开始，动态卡片加载**

*   **用户故事**: 当导航开始后，我希望能一目了然地看到关键信息，比如路况、天气和我的日程，而不需要切换屏幕。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 8:00 前往: XX幼儿园, 预计: 30分到达] <-- 灵动岛                       |
    |                                                                          |
    |                                     导航地图 (路线高亮)                    |
    |                                                               公司(B)    |
    |                                                                 /        |
    |                                                                /         |
    |                                             幼儿园(Y)---------+          |
    |                                                  |                       |
    |                                                家(H)                     |
    | (o.o)                                                                    |
    | +------------------+ +-------------------+ +----------------------------+ |
    | |  唐诗三百首      | | 今日待办          | | 29°C 晴 西北风3级          | |
    | |  正在收听...     | | - 客户交流 09:30 | | 北京市 轻度污染            | |
    | |  K  ||  >       | | - 项目汇报 11:00 | |                            | |
    | +------------------+ +-------------------+ +----------------------------+ |
    +--------------------------------------------------------------------------+
    ```

### **步骤 4.0: 到达途经点与智能切换**

*   **用户故事**: 当我到达一个途经点时，我希望系统能提醒我，并智能地为我的下一段行程做好准备，比如切换我喜欢听的音频。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 已到达途经点: XX幼儿园]                                               |
    |                                                                          |
    |                                     导航地图 (第二段路线高亮)              |
    |  +----------------------+                               公司(B)           |
    |  | 孩子已送达, 将为您 |                                  /                |
    |  | 自动切换音频:      |                                 /                 |
    |  |【明朝那些事儿】    |               幼儿园(Y)---------+                 |
    |  |      好的 (2s)      |                                                 |
    |  +----------------------+                                                 |
    | (o.o)                                                                    |
    | +------------------+ +-------------------+ +----------------------------+ |
    | | 明朝那些事儿     | | 今日待办          | | 29°C 晴 西北风3级          | |
    | |  正在收听...      | | - 客户交流 09:30 | | 北京市 轻度污染            | |
    | |  K  ||  >        | | - 项目汇报 11:00 | |                            | |
    | +------------------+ +-------------------+ +----------------------------+ |
    +--------------------------------------------------------------------------+
    ```

### **步骤 5.0: LLM驱动的主动服务推荐**

*   **用户故事**: 如果我有额外的空闲时间，我希望我的VPA能像个真正的助理一样，智能地为我推荐一些放松方式，比如去买杯咖啡。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 前往: 公司, 预计: 8:53到达]                                          |
    |                                                                          |
    |                                     导航地图                             |
    |  +----------------------+                         公司(B)                |
    |  | 现在时间充足, 附近 |                            /                   |
    |  | 有家常去的咖啡厅,  |                           /  (咖啡厅)          |
    |  | 要不要去坐坐?      |        幼儿园(Y)---------+      |               |
    |  |                    |              |                  |               |
    |  | [取消]  [好的]    |              |                  |               |
    |  +----------------------+           家(H)                |               |
    | (o.o)                                                                    |
    | +------------------+  ... (其他卡片) ...                                |
    +--------------------------------------------------------------------------+
    ```

### **步骤 6.0: 确认并更新行程**

*   **用户故事**: 当我接受VPA的建议后，我希望系统能立刻、无缝地为我更新导航路线，无需我手动操作。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 导航: xx咖啡厅, 预计8:33到达]                                        |
    |                                                                          |
    |                                     导航地图 (路线更新)                    |
    |                                                              公司(B)     |
    |                                                                /         |
    |                                                               /          |
    |                                             幼儿园(Y)--咖啡厅--+         |
    |                                                  |                       |
    |  +----------------------+                      家(H)                     |
    |  | 已为您重新规划路线...|                                                |
    |  +----------------------+                                                |
    | (o.o)                                                                    |
    | ... (底部卡片保持不变) ...                                               |
    +--------------------------------------------------------------------------+
    ```

### **步骤 7.0: 恢复原始行程与智能提醒**

*   **用户故事**: 当我完成临时安排（买咖啡）并重新上车后，我希望系统能自动恢复我之前的行程，并提醒我接下来的工作，让我无缝衔接。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 导航: 公司, 预计8:53到达]                                           |
    |                                                                          |
    |                                     导航地图 (恢复路线)                    |
    |                                                              公司(B)     |
    |                                                                /         |
    |                                                               /          |
    |  +----------------------+                   幼儿园(Y)--咖啡厅--+         |
    |  | 已恢复行程。提醒您 |                                                  |
    |  | 上午9:30有客户交流 |                                                  |
    |  |      好的 (2s)      |                                                  |
    |  +----------------------+                                                 |
    | (o.o)                                                                    |
    | ... (底部卡片) ...                                                       |
    +--------------------------------------------------------------------------+
    ```

### **步骤 8.0: 抵达目的地与场景结束**

*   **用户故事**: 当我到达公司后，我希望导航自动结束，界面能恢复到一个简洁的状态，只保留我需要关注的核心信息。

*   **ASCII 原型图**:
    ```
    +--------------------------------------------------------------------------+
    | [<- 已到目的地]                                                          |
    |                                                                          |
    |                         沉浸桌面壁纸                                     |
    |                                                                          |
    |  +-----------------------------+                                       |
    |  | 已到达目的地。别忘了上午   |                                       |
    |  | 的会议，祝您工作顺利!      |                                       |
    |  |        好的 (2s)           |                                       |
    |  +-----------------------------+                                       |
    |                                                                          |
    |   (o.o)                                                                  |
    |                      +-------------------+                               |
    |                      | 今日待办          |                               |
    |                      | - 客户交流 09:30 |                               |
    |                      | - 项目汇报 11:00 |                               |
    |                      +-------------------+                               |
    +--------------------------------------------------------------------------+
    ```

---

## 5. AI决策原则与约束

- **布局稳定性原则**: 优先使用核心场景的“默认布局模板”，避免界面频繁剧烈变化。
- **信息过载保护原则**: 任何时候卡片总数不超过3个，保证界面简洁。
- **用户干预与学习原则**: 允许用户“不喜欢”某个布局，AI会记录该偏好并进行学习。
