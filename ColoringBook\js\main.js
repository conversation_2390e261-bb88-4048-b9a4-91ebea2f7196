document.addEventListener('DOMContentLoaded', () => {

    // --- 元素获取 (吉卜力风格) ---
    const promptInput = document.getElementById('prompt-input');
    const ratioButtons = document.getElementById('ratio-buttons');
    const countInput = document.getElementById('count-input');
    const generateBtn = document.getElementById('generate-btn');
    const selectModeToggle = document.getElementById('select-mode-toggle');
    const printBtn = document.getElementById('print-btn');
    const galleryGrid = document.getElementById('gallery-grid');

    // --- 初始化存储管理器 ---
    const storageManager = new StorageManager();

    // --- 状态管理 ---
    let selectedRatio = '1:1';
    let isSelectMode = false;

    // --- 初始化集成任务系统 ---
    console.log('[Main] 开始初始化集成任务系统...');
    const taskSystem = new IntegratedTaskSystem();
    console.log('[Main] 集成任务系统初始化完成:', taskSystem);

    // --- 加载用户偏好设置 ---
    loadUserPreferences();

    // --- 事件监听 ---

    // F2.1: 比例选择 (木制符文)
    ratioButtons.addEventListener('click', (e) => {
        const button = e.target.closest('.rune-btn');
        if (button) {
            ratioButtons.querySelectorAll('.rune-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            selectedRatio = button.dataset.ratio;
            
            // 保存用户选择的比例偏好 (Requirement 6.1)
            storageManager.updatePreference('selectedRatio', selectedRatio);
        }
    });

    // 生成数量输入变化事件 (Requirement 6.2)
    countInput.addEventListener('change', (e) => {
        const count = parseInt(e.target.value) || 1;
        if (count >= 1 && count <= 10) {
            storageManager.updatePreference('defaultCount', count);
        }
    });

    // 提示词输入变化事件 (保存最后使用的提示词)
    promptInput.addEventListener('blur', (e) => {
        const prompt = e.target.value.trim();
        if (prompt) {
            storageManager.updatePreference('lastPrompt', prompt);
        }
    });

    // F4.2: 选择模式切换 (萤火虫开关)
    selectModeToggle.addEventListener('change', (e) => {
        isSelectMode = e.target.checked;
        updatePrintButtonState();
        if (!isSelectMode) {
            galleryGrid.querySelectorAll('.enhanced-gallery-frame.selected').forEach(card => card.classList.remove('selected'));
            updatePrintButtonState();
        }
    });

    // F1.1: 生成按钮点击事件 (魔法护符)
    generateBtn.addEventListener('click', async () => {
        console.log('[Main] 生成按钮被点击');
        const prompt = promptInput.value.trim();
        const count = parseInt(countInput.value) || 1;
        
        console.log(`[Main] 输入参数 - 提示词: "${prompt}", 数量: ${count}, 比例: ${selectedRatio}`);

        // 输入验证
        if (!prompt) {
            console.log('[Main] 验证失败: 提示词为空');
            showErrorMessage('请输入魔法咒语（提示词）', 'VALIDATION_ERROR');
            return;
        }

        if (count < 1 || count > 10) {
            console.log('[Main] 验证失败: 数量超出范围');
            showErrorMessage('生成数量必须在1-10之间', 'VALIDATION_ERROR');
            return;
        }

        try {
            // 显示加载状态
            console.log('[Main] 设置加载状态');
            setGeneratingState(true);
            
            // 使用集成任务系统开始生成
            console.log('[Main] 调用任务系统开始生成...');
            const results = await taskSystem.startGeneration(prompt, selectedRatio, count);
            
            console.log(`[Main] 生成完成: ${results.length}/${count} 个任务成功`, results);
            
        } catch (error) {
            console.error('[Main] 生成失败:', error);
            const errorType = detectErrorType(error);
            const friendlyMessage = getFriendlyErrorMessage(error, errorType);
            showErrorMessage(friendlyMessage, errorType, true);
        } finally {
            console.log('[Main] 重置加载状态');
            setGeneratingState(false);
        }
    });

    // F4.1: 打印功能
    printBtn.addEventListener('click', () => {
        const selectedCards = document.querySelectorAll('.enhanced-gallery-frame.selected');
        if (selectedCards.length === 0) {
            showErrorMessage('请先选择要打印的图片', 'VALIDATION_ERROR');
            return;
        }

        // 创建打印窗口
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>魔法画册打印</title>
                <style>
                    body { margin: 0; padding: 20px; }
                    .print-page { page-break-after: always; margin-bottom: 20px; }
                    .print-page:last-child { page-break-after: auto; }
                    img { max-width: 100%; height: auto; display: block; margin: 0 auto; }
                    @media print {
                        body { margin: 0; }
                        .print-page { margin: 0; }
                    }
                </style>
            </head>
            <body>
        `);

        selectedCards.forEach((card, index) => {
            const img = card.querySelector('.gallery-image');
            if (img && img.src) {
                printWindow.document.write(`
                    <div class="print-page">
                        <img src="${img.src}" alt="魔法画册 ${index + 1}">
                    </div>
                `);
            }
        });

        printWindow.document.write('</body></html>');
        printWindow.document.close();
        
        // 等待图片加载后打印
        setTimeout(() => {
            printWindow.print();
        }, 1000);
    });

    // --- 用户偏好设置函数 ---

    /**
     * 加载用户偏好设置并应用到界面 (Requirements 6.3)
     */
    function loadUserPreferences() {
        try {
            const preferences = storageManager.loadUserPreferences();
            
            // 恢复图片比例选择 (Requirement 6.1)
            if (preferences.selectedRatio) {
                selectedRatio = preferences.selectedRatio;
                ratioButtons.querySelectorAll('.rune-btn').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.ratio === selectedRatio) {
                        btn.classList.add('active');
                    }
                });
            }
            
            // 恢复生成数量设置 (Requirement 6.2)
            if (preferences.defaultCount) {
                countInput.value = preferences.defaultCount;
            }
            
            // 恢复最后使用的提示词
            if (preferences.lastPrompt) {
                promptInput.value = preferences.lastPrompt;
            }
            
            console.log('用户偏好设置已加载并应用到界面', preferences);
        } catch (error) {
            console.error('加载用户偏好设置失败:', error);
            // 如果加载失败，使用默认设置
            applyDefaultPreferences();
        }
    }

    /**
     * 应用默认偏好设置 (Requirement 6.4)
     */
    function applyDefaultPreferences() {
        selectedRatio = '1:1';
        countInput.value = 1;
        promptInput.value = '';
        
        // 更新UI状态
        ratioButtons.querySelectorAll('.rune-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.ratio === '1:1') {
                btn.classList.add('active');
            }
        });
        
        console.log('已应用默认偏好设置');
    }

    /**
     * 清除用户数据并重置设置 (Requirement 6.4)
     */
    function clearUserDataAndReset() {
        if (confirm('确定要清除所有用户数据并重置设置吗？此操作不可撤销。')) {
            const success = storageManager.clearUserData();
            if (success) {
                applyDefaultPreferences();
                showSuccessMessage('用户数据已清除，设置已重置为默认值');
            } else {
                showErrorMessage('清除用户数据失败，请重试', 'UNKNOWN_ERROR');
            }
        }
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    function showSuccessMessage(message) {
        // 移除现有的成功提示
        const existingSuccess = document.querySelector('.success-notification');
        if (existingSuccess) {
            existingSuccess.remove();
        }

        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full bg-green-100 border-green-400 text-green-700 border-l-4';
        
        successDiv.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 text-xl mr-3">✅</div>
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                </div>
                <button class="close-btn ml-2 text-xl hover:opacity-70 transition-opacity">&times;</button>
            </div>
        `;

        document.body.appendChild(successDiv);

        // 显示动画
        setTimeout(() => {
            successDiv.classList.remove('translate-x-full');
        }, 100);

        // 关闭按钮事件
        successDiv.querySelector('.close-btn').addEventListener('click', () => {
            hideSuccessMessage(successDiv);
        });

        // 自动隐藏
        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                hideSuccessMessage(successDiv);
            }
        }, 3000);
    }

    /**
     * 隐藏成功消息
     * @param {HTMLElement} successDiv - 成功消息元素
     */
    function hideSuccessMessage(successDiv) {
        successDiv.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(successDiv)) {
                successDiv.remove();
            }
        }, 300);
    }

    // --- 辅助函数 ---

    /**
     * 设置生成状态
     * @param {boolean} isGenerating - 是否正在生成
     */
    function setGeneratingState(isGenerating) {
        const loadingIndicator = document.getElementById('loading-indicator');
        
        if (isGenerating) {
            generateBtn.disabled = true;
            generateBtn.classList.add('loading');
            generateBtn.textContent = '施法中...';
            
            if (loadingIndicator) {
                loadingIndicator.classList.remove('hidden');
            }
        } else {
            generateBtn.disabled = false;
            generateBtn.classList.remove('loading');
            generateBtn.textContent = '施展魔法';
            
            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 更新打印按钮状态 (委托给任务系统处理)
     */
    function updatePrintButtonState() {
        if (taskSystem && taskSystem.updatePrintButtonState) {
            taskSystem.updatePrintButtonState();
        }
    }

    /**
     * 检测错误类型
     * @param {Error} error - 错误对象
     * @returns {string} - 错误类型
     */
    function detectErrorType(error) {
        // 网络连接错误
        if (error.name === 'TypeError' && (error.message.includes('fetch') || error.message.includes('Failed to fetch'))) {
            return 'NETWORK_ERROR';
        }
        if (error.message.includes('NetworkError') || error.message.includes('ERR_NETWORK')) {
            return 'NETWORK_ERROR';
        }
        if (error.message.includes('ERR_CONNECTION_REFUSED') || error.message.includes('ECONNREFUSED')) {
            return 'NETWORK_ERROR';
        }
        
        // 服务器错误
        if (error.message.includes('HTTP 5') || error.message.includes('服务器内部错误')) {
            return 'SERVER_ERROR';
        }
        
        // 超时错误
        if (error.message.includes('超时') || error.message.includes('timeout') || error.message.includes('TimeoutError')) {
            return 'TIMEOUT_ERROR';
        }
        
        // 验证错误
        if (error.message.includes('验证') || error.message.includes('validation') || error.message.includes('请求失败')) {
            return 'VALIDATION_ERROR';
        }
        
        return 'UNKNOWN_ERROR';
    }

    /**
     * 获取用户友好的错误消息
     * @param {Error} error - 错误对象
     * @param {string} type - 错误类型
     * @returns {string} - 用户友好的错误消息
     */
    function getFriendlyErrorMessage(error, type) {
        switch (type) {
            case 'NETWORK_ERROR':
                return '无法连接到魔法服务器，请检查网络连接或后端服务是否正常运行';
            case 'SERVER_ERROR':
                return '魔法服务器遇到了问题，请稍后再试';
            case 'TIMEOUT_ERROR':
                return '魔法施展时间过长，请稍后重试';
            case 'VALIDATION_ERROR':
                return '输入的魔法咒语有误，请检查后重试';
            default:
                return error.message || '发生了未知的魔法异常，请重试';
        }
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     * @param {string} type - 错误类型
     * @param {boolean} allowRetry - 是否显示重试按钮
     */
    function showErrorMessage(message, type = 'UNKNOWN_ERROR', allowRetry = false) {
        // 移除现有的错误提示
        const existingError = document.querySelector('.error-notification');
        if (existingError) {
            existingError.remove();
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full';
        
        // 根据错误类型设置样式
        let bgColor, icon;
        switch (type) {
            case 'NETWORK_ERROR':
                bgColor = 'bg-red-100 border-red-400 text-red-700';
                icon = '🌐';
                break;
            case 'SERVER_ERROR':
                bgColor = 'bg-orange-100 border-orange-400 text-orange-700';
                icon = '⚠️';
                break;
            case 'TIMEOUT_ERROR':
                bgColor = 'bg-yellow-100 border-yellow-400 text-yellow-700';
                icon = '⏰';
                break;
            case 'VALIDATION_ERROR':
                bgColor = 'bg-blue-100 border-blue-400 text-blue-700';
                icon = '📝';
                break;
            default:
                bgColor = 'bg-gray-100 border-gray-400 text-gray-700';
                icon = '❌';
        }

        errorDiv.className += ` ${bgColor} border-l-4`;
        
        errorDiv.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 text-xl mr-3">${icon}</div>
                <div class="flex-1">
                    <p class="font-medium">${message}</p>
                    ${allowRetry ? '<button class="retry-btn mt-2 px-3 py-1 bg-white rounded text-sm hover:bg-gray-50 transition-colors">重试</button>' : ''}
                </div>
                <button class="close-btn ml-2 text-xl hover:opacity-70 transition-opacity">&times;</button>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // 显示动画
        setTimeout(() => {
            errorDiv.classList.remove('translate-x-full');
        }, 100);

        // 关闭按钮事件
        errorDiv.querySelector('.close-btn').addEventListener('click', () => {
            hideErrorMessage(errorDiv);
        });

        // 重试按钮事件
        if (allowRetry) {
            errorDiv.querySelector('.retry-btn').addEventListener('click', () => {
                hideErrorMessage(errorDiv);
                generateBtn.click();
            });
        }

        // 自动隐藏（除非是网络错误）
        if (type !== 'NETWORK_ERROR') {
            setTimeout(() => {
                if (document.body.contains(errorDiv)) {
                    hideErrorMessage(errorDiv);
                }
            }, 5000);
        }
    }

    /**
     * 隐藏错误消息
     * @param {HTMLElement} errorDiv - 错误消息元素
     */
    function hideErrorMessage(errorDiv) {
        errorDiv.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                errorDiv.remove();
            }
        }, 300);
    }

    // --- 存储事件监听 ---
    
    // 监听用户偏好设置更新事件
    document.addEventListener('userPreferencesUpdated', (e) => {
        console.log('用户偏好设置已更新:', e.detail.preferences);
    });

    // 监听用户数据清除事件
    document.addEventListener('userDataCleared', () => {
        console.log('用户数据已清除');
        applyDefaultPreferences();
    });

    // 监听用户偏好设置重置事件
    document.addEventListener('userPreferencesReset', (e) => {
        console.log('用户偏好设置已重置:', e.detail.preferences);
        loadUserPreferences();
    });

    // 暴露清除用户数据函数到全局作用域（用于调试和测试）
    window.clearUserDataAndReset = clearUserDataAndReset;
    window.storageManager = storageManager;

    // 初始化打印按钮状态
    updatePrintButtonState();
});