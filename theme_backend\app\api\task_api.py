from fastapi import APIRouter, Query
from ..core.mysql_util import connect_PBDB, execute_query, get_page_str
from ..core.minio_util import minioGet, minioGetPresigned_url
import logging
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/task/list")
def get_task_list(page: int = Query(..., description="页码"), size: int = Query(..., description="每页显示的数量")
                  , taskType: str = Query('theme', description="任务类型")):
    connection = None
    try:
        connection = connect_PBDB()
        condition = ''
        if taskType and taskType != '':
            condition = f" and task_type = '{taskType}'"
        baseSql = f"select * from theme_task_log ttl where task_status in ('completed','success') {condition}"
        totalQuery = f"select count(1) cnt from ({baseSql}) t"
        total = execute_query(connection, totalQuery)
        pageStr = get_page_str(page,size)
        mysqlQuery = f"{baseSql} order by create_time desc {pageStr} "
        result = execute_query(connection, mysqlQuery)
        for item in result:
            if taskType and taskType == 'theme':
                item['presigned_url'] = minioGetPresigned_url('changan-theme', f"{item['task_id']}/wallpaper.jpg")
            if taskType and taskType == 'video':
                item['presigned_url'] = minioGetPresigned_url('changan-video', f"{item['task_id']}/{item['task_id']}.mp4")
            if taskType and taskType == 'image':
                item['presigned_url'] = minioGetPresigned_url('changan-image', f"{item['task_id']}/{item['task_id']}.png")
        return {'total':total[0]['cnt'] if total else 0,'data':result}
    except Exception as e:
        logger.error(f"查询数据库获取任务列表出错: {str(e)}")
    finally:
        if connection:
            connection.close()
