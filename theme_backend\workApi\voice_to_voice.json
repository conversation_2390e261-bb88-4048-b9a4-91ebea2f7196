{"2": {"inputs": {"speed": 1, "text": "AutoGen是微软研发的开源框架，旨在构建可自主行动或与人类协作的多智能体AI应用程序。通过多智能体对话的方式，AutoGen可以简化复杂LLM工作流的编排、自动化和优化，最大限度地提高LLM模型的性能并克服其局限性。\n\n下面是AutoGen的三层架构设计，从底层的核心功能到上层的扩展能力，形成了一个完整的智能体生态系统。", "audio": ["3", 0]}, "class_type": "NTCosyVoiceCrossLingualSampler", "_meta": {"title": "NTCosyVoiceCrossLingualSampler"}}, "3": {"inputs": {"audio": "春.MP3"}, "class_type": "LoadAudio", "_meta": {"title": "加载音频"}}, "5": {"inputs": {"filename_prefix": "audio/ComfyUI", "audio": ["2", 0]}, "class_type": "SaveAudio", "_meta": {"title": "保存音频"}}}