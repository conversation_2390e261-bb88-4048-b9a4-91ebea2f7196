<template>
  <transition name="fade">
    <div v-if="modelValue" class="image-viewer-overlay" @click="closeViewer">
      <div class="viewer-content glass-effect" @click.stop>
        <button class="close-button" @click="closeViewer">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>

        <div class="image-container">
          <img :src="currentImage" alt="图片查看器" class="viewer-image" :class="{ 'zoomed': isZoomed }" @click="toggleZoom" />
        </div>

        <div class="controls">
          <button v-if="hasMultipleImages" class="nav-button prev" @click="prevImage" :disabled="currentIndex <= 0">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <button v-if="hasMultipleImages" class="nav-button next" @click="nextImage" :disabled="currentIndex >= images.length - 1">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>

        <div class="toolbar">
          <button class="tool-button" @click="toggleZoom" :class="{ 'active': isZoomed }">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              <line x1="11" y1="8" x2="11" y2="14"></line>
              <line x1="8" y1="11" x2="14" y2="11"></line>
            </svg>
            <span>缩放</span>
          </button>
        </div>

        <div v-if="hasMultipleImages" class="pagination">
          {{ currentIndex + 1 }} / {{ images.length }}
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "ImageViewer",
});
</script>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";

const props = defineProps({
  // 弹窗控制值
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 图片列表
  images: {
    type: Array as () => string[],
    default: () => [],
  },
  // 当前显示的图片索引
  initialIndex: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["update:modelValue", "close"]);

// 当前图片索引
const currentIndex = ref(props.initialIndex);
// 是否放大
const isZoomed = ref(false);

// 监听 initialIndex 变化
watch(
  () => props.initialIndex,
  (newValue) => {
    currentIndex.value = newValue;
    isZoomed.value = false; // 切换图片时重置缩放状态
  }
);

// 当前图片URL
const currentImage = computed(() => {
  if (props.images.length === 0) return "";
  return props.images[currentIndex.value] || props.images[0];
});

// 是否有多张图片
const hasMultipleImages = computed(() => {
  return props.images.length > 1;
});

// 关闭查看器
const closeViewer = () => {
  isZoomed.value = false;
  emit("update:modelValue", false);
  emit("close");
};

// 上一张图片
const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    isZoomed.value = false;
  }
};

// 下一张图片
const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    currentIndex.value++;
    isZoomed.value = false;
  }
};

// 切换缩放状态
const toggleZoom = () => {
  isZoomed.value = !isZoomed.value;
};

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (!props.modelValue) return;

  switch (event.key) {
    case "Escape":
      closeViewer();
      break;
    case "ArrowLeft":
      prevImage();
      break;
    case "ArrowRight":
      nextImage();
      break;
    case " ": // 空格键切换缩放
      toggleZoom();
      event.preventDefault();
      break;
  }
};

// 组件挂载时添加键盘事件监听
onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
});

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
});
</script>

<style scoped>
.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(8px);
}

.viewer-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 玻璃效果 */
.glass-effect {
  border-radius: 12px;
  background: rgba(30, 41, 59, 0.3);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  padding: 20px;
}

.glass-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
}

.glass-effect:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.viewer-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease;
  cursor: zoom-in;
}

.viewer-image.zoomed {
  transform: scale(1.5);
  max-height: none;
  cursor: zoom-out;
}

.close-button {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(30, 41, 59, 0.6);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-button {
  background: rgba(30, 41, 59, 0.6);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  pointer-events: auto;
}

.nav-button:hover:not(:disabled) {
  background-color: rgba(99, 102, 241, 0.6);
  transform: scale(1.1);
}

.nav-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.toolbar {
  position: absolute;
  bottom: -50px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.tool-button {
  background: rgba(30, 41, 59, 0.6);
  border: none;
  color: white;
  border-radius: 20px;
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  transition: all 0.3s;
}

.tool-button:hover,
.tool-button.active {
  background: rgba(99, 102, 241, 0.6);
}

.pagination {
  position: absolute;
  bottom: -80px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  font-size: 14px;
  background: rgba(30, 41, 59, 0.6);
  padding: 5px 15px;
  border-radius: 20px;
  width: fit-content;
  margin: 0 auto;
}

/* 淡入淡出过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .nav-button {
    width: 40px;
    height: 40px;
  }

  .close-button {
    top: 10px;
    right: 10px;
  }

  .toolbar {
    bottom: 10px;
  }

  .pagination {
    bottom: 60px;
  }

  .viewer-image.zoomed {
    transform: scale(1.2);
  }
}
</style> 