# AI HMI 场景布局生成器测试流程说明

## 📋 概述

我已经为您创建了一个全新的 n8n 工作流程，用于基于场景描述生成 AI HMI 布局。该流程接收用户的场景描述，通过注入的知识库分析场景特点，自动选择合适的风格和组件，调用 GLM-4.1V-9B-Thinking 模型生成完整的 HMI 界面布局。

## 🔧 工作流程详情

**工作流程 ID:** `hDRbwIumsQ52bUAs`
**工作流程名称:** AI HMI 场景布局生成器

### 节点说明

1. **接收布局请求** (Webhook 触发器)
   - 监听 POST 请求到路径 `/ai-hmi-layout`
   - 接收场景、风格和布局参数

2. **准备提示词** (Code 节点)
   - 根据输入参数构建完整的提示词
   - 使用您文档中的提示词模板
   - 支持默认布局配置

3. **调用GLM模型** (HTTP Request 节点)
   - 调用 GLM-4.1V-9B-Thinking 模型
   - API 地址: `http://************:8194/v1/chat/completions`
   - 最大令牌数: 32768

4. **处理响应** (Code 节点)
   - 提取生成的 HTML 内容
   - 处理代码块格式
   - 格式化返回数据

5. **返回结果** (Respond to Webhook 节点)
   - 返回 JSON 格式的结果
   - 包含生成的 HTML 和元数据

## 🚀 使用步骤

### 1. 激活工作流程

首先需要在 n8n 中激活这个工作流程：

```bash
# 如果您有 n8n CLI 访问权限
curl -X PATCH "http://your-n8n-instance/api/v1/workflows/hBVVuBtKp2tDIyze" \
  -H "Content-Type: application/json" \
  -d '{"active": true}'
```

### 2. 配置 API 密钥

⚠️ **重要**: 需要在 "调用GLM模型" 节点中配置正确的 API 密钥：

1. 打开 n8n 工作流编辑器
2. 点击 "调用GLM模型" 节点
3. 在 Authorization 头部中，将 `your-api-key-here` 替换为实际的 API 密钥

### 3. 获取 Webhook URL

激活后，您可以获取 webhook URL：
```
http://your-n8n-instance.com/webhook/ai-hmi-layout
```

### 4. 使用测试页面

我已经为您创建了一个测试页面 `test-ai-hmi-workflow.html`，您可以：

1. 在浏览器中打开该文件
2. 输入您的 webhook URL
3. 选择场景和风格
4. 可选择输入自定义布局 JSON
5. 点击"生成 HMI 布局"按钮

## 📝 API 接口说明

### 请求格式

```http
POST /webhook/ai-hmi-layout
Content-Type: application/json

{
  "scene": "cyberpunk_drive",
  "style": "cyberpunk",
  "layout": {
    // 可选的自定义布局 JSON
  }
}
```

### 支持的场景 (Scene)

- `natural_commute` - 自然通勤
- `cyberpunk_drive` - 赛博朋克驾驶
- `glassmorphism_wait` - 玻璃拟物等待
- `neumorphism_rainy` - 新拟物雨天
- `kawaii_family_trip` - 可爱家庭旅行

### 支持的风格 (Style)

- `natural` - 自然
- `cyberpunk` - 赛博朋克
- `glassmorphism` - 玻璃拟物
- `neumorphism` - 新拟物
- `kawaii` - 可爱卡通

### 响应格式

```json
{
  "success": true,
  "generatedHtml": "生成的HTML代码",
  "rawResponse": "模型的原始响应",
  "timestamp": "2025-07-29T02:54:15.447Z",
  "usage": {
    // 模型使用统计
  }
}
```

## 🧪 测试示例

### 示例 1: 赛博朋克驾驶界面

```bash
curl -X POST "http://your-n8n-instance.com/webhook/ai-hmi-layout" \
  -H "Content-Type: application/json" \
  -d '{
    "scene": "cyberpunk_drive",
    "style": "cyberpunk"
  }'
```

### 示例 2: 可爱家庭旅行界面

```bash
curl -X POST "http://your-n8n-instance.com/webhook/ai-hmi-layout" \
  -H "Content-Type: application/json" \
  -d '{
    "scene": "kawaii_family_trip",
    "style": "kawaii",
    "layout": {
      "layout": {
        "container_id": "three-column-grid-container",
        "slots": {
          "col-1": { "component_id": "weather-card" },
          "col-2": { "component_id": "music-control-card" },
          "col-3": { "component_id": "todo-card" }
        }
      }
    }
  }'
```

## 🔍 故障排除

### 常见问题

1. **工作流程未激活**
   - 确保在 n8n 中激活了工作流程

2. **API 密钥错误**
   - 检查 "调用GLM模型" 节点中的 Authorization 头部配置

3. **网络连接问题**
   - 确保 n8n 实例可以访问 `http://************:8194`

4. **JSON 格式错误**
   - 检查自定义布局 JSON 的格式是否正确

### 调试方法

1. 在 n8n 中查看工作流程执行历史
2. 检查每个节点的输入输出数据
3. 查看错误日志和响应详情

## 📚 相关文档

- 场景定义: `aiHmi/docs/提示词/Scene_Element_KB.json`
- 风格定义: `aiHmi/docs/提示词/Style_KB.json`
- 组件定义: `aiHmi/docs/提示词/Component_KB.json`
- 布局定义: `aiHmi/docs/提示词/Layout_KB.json`
- 提示词模板: `aiHmi/docs/提示词/提示词.md`

## 🎯 下一步

1. 激活工作流程并配置 API 密钥
2. 使用测试页面进行初步测试
3. 根据需要调整提示词模板
4. 扩展支持更多场景和风格
5. 集成到您的应用程序中

如果您需要任何修改或有问题，请告诉我！
