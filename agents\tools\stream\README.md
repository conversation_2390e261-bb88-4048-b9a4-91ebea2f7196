# 流式输出 MCP 服务

流式输出 MCP (Model Context Protocol) 服务是一个标准化的工具，用于提供 LLM（大型语言模型）的流式输出能力，支持实时思考步骤显示、代码块展示和上下文管理等功能。该服务使用 MCP 协议封装，可被 AutoGen 等智能体框架调用。

## 功能特点

- **流式输出**: 支持实时流式输出 LLM 生成的内容，提升用户体验
- **思考过程可视化**: 展示 LLM 的思考步骤，增强透明度
- **代码块处理**: 专门处理和展示代码块，支持语法高亮
- **工具调用记录**: 记录智能体工具调用过程
- **上下文管理**: 提供会话和上下文数据的存储与检索
- **标准化接口**: 基于 MCP 协议设计，支持多种调用方式
- **AutoGen 集成**: 与 AutoGen 框架无缝集成，提供流畅的智能体体验

## 安装依赖

```bash
pip install -r agents/tools/stream/requirements.txt
```

主要依赖包括：

- Redis: 用于事件发布与订阅
- MongoDB: 用于存储会话和上下文数据
- WebSockets: 提供实时通信能力
- aiohttp: 实现 HTTP API 服务
- AutoGen: 智能体框架集成

## 服务启动

### 命令行启动

```bash
python -m agents.tools.stream.run_server --host 0.0.0.0 --port 19280
```

### 参数说明

- `--host`: 服务主机地址，默认为 0.0.0.0
- `--port`: 服务端口，默认为 19280
- `--name`: 服务名称，默认为 "streaming-mcp"
- `--description`: 服务描述，默认为 "流式输出MCP服务器"

### 编程方式启动

```python
import asyncio
from agents.tools.stream import create_mcp_server

async def start_server():
    server = create_mcp_server(
        name="my-streaming-service",
        description="自定义流式输出服务",
        version="1.0.0"
    )
    await server.start(host="0.0.0.0", port=19280)

asyncio.run(start_server())
```

## 环境配置

服务依赖以下环境变量，可通过 `.env` 文件或系统环境变量设置：

```
# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# MongoDB 配置
MONGO_URI=mongodb://localhost:27017
MONGO_DB=stream_service
MONGO_CONTEXT_COLLECTION=contexts
MONGO_SESSION_COLLECTION=sessions
MONGO_MESSAGE_COLLECTION=messages

# 服务配置
MCP_URL=http://localhost:19280
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8765
```

## 使用方法

### 作为 MCP 服务调用

服务启动后，提供以下 HTTP API 接口：

- `GET /`: 服务首页
- `GET /healthz`: 健康检查
- `GET /capabilities`: 获取服务能力列表
- `GET /api/v1`: API 信息
- `GET /api/v1/tools`: 获取工具列表
- `POST /api/v1/tools/{tool_name}`: 调用工具
- `GET /api/v1/connect`: WebSocket 连接

### 通过 AutoGen 使用

使用提供的 `MCPToolkit` 类可轻松与 AutoGen 集成：

```python
from agents.tools.stream import MCPToolkit, create_streaming_functions
from autogen import AssistantAgent, UserProxyAgent

# 创建 MCP 工具箱
toolkit = MCPToolkit("http://localhost:19280")

# 初始化会话
session_id = toolkit.init_session("示例会话")

# 获取工具配置和函数
tool_configs, functions = streaming_capability.get_tools()

# 创建带有流式输出能力的代理
assistant = AssistantAgent(
    name="StreamingAssistant",
    system_message="你是一个具有流式输出能力的AI助手...",
    llm_config={
        "config_list": [{"model": "gpt-4", "api_key": "你的API密钥"}],
        "tools": tool_configs
    }
)

# 注册工具函数
for tool_name, tool_fn in functions.items():
    assistant.register_for_llm(tool_name=tool_name, tool_fn=tool_fn)

# 创建用户代理
user_proxy = UserProxyAgent(name="User", human_input_mode="ALWAYS")

# 开始对话
user_proxy.initiate_chat(assistant, message="请解释一下流式输出是如何工作的?")
```

完整示例请参考 `example_autogen.py`。

### 可用工具列表

| 工具名称             | 说明             | 必要参数                                      |
| -------------------- | ---------------- | --------------------------------------------- |
| create_session       | 创建新的会话     | session_name                                  |
| close_session        | 关闭现有会话     | session_id                                    |
| stream_thinking      | 流式输出思考步骤 | session_id, agent_name, content               |
| stream_tokens        | 流式输出文本内容 | session_id, agent_name, content               |
| publish_code_block   | 发布代码块       | session_id, agent_name, code, language        |
| publish_tool_call    | 发布工具调用事件 | session_id, agent_name, tool_name, tool_input |
| save_context         | 保存上下文数据   | session_id, key, value                        |
| get_context          | 获取上下文数据   | session_id, key                               |
| get_session_messages | 获取会话消息历史 | session_id                                    |

## 架构组件

服务由以下主要组件构成：

1. **MCP服务器** (`mcp_server.py`): 提供符合 MCP 协议的 HTTP API
2. **MCP工具** (`mcp_tools.py`): 实现流式输出相关的工具集
3. **AutoGen适配器** (`autogen_adapter.py`): 连接 AutoGen 和 MCP 服务
4. **流管理器** (`stream_manager.py`): 管理会话和事件发布
5. **WebSocket服务器** (`websocket_server.py`): 提供实时通信
6. **数据存储** (`mongo_client.py`, `redis_client.py`): 管理数据持久化和事件分发

## 实时演示

启动服务后，可以使用 WebSocket 客户端连接到 `/api/v1/connect` 端点查看实时输出。例如：

```javascript
// 浏览器 JavaScript 示例
const ws = new WebSocket('ws://localhost:19280/api/v1/connect');

ws.onopen = () => {
  console.log('连接成功');
  
  // 创建会话
  ws.send(JSON.stringify({
    cmd: 'call_tool',
    tool_name: 'create_session',
    params: {
      session_name: 'web_demo'
    }
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('收到消息:', data);
};
```

## 常见问题

### 服务启动失败

1. 检查 Redis 和 MongoDB 服务是否正常运行
2. 确认环境变量配置是否正确
3. 查看端口是否被占用

### 工具调用失败

1. 确认会话是否已创建
2. 检查参数是否完整
3. 查看服务日志获取详细错误信息

### AutoGen 集成问题

1. 确保 AutoGen 版本与要求兼容
2. 检查工具注册是否正确
3. 确认 LLM 配置是否支持工具调用

## 许可证

[MIT License](LICENSE)
