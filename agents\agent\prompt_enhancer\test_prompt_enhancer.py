#!/usr/bin/env python
"""
提示词增强代理测试脚本
用于测试提示词增强代理的功能
"""

import os
import sys
import asyncio
import logging
import uuid
import argparse
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 确保当前目录在路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入提示词增强代理
from prompt_enhancer_agent import PromptEnhancerAgent

# 导入工具类型修复补丁
from fix_tools import apply_patches

# 应用补丁
apply_patches()

async def test_prompt_enhancer(prompt: str, mcp_url: str, max_retries: int = 3):
    """
    测试提示词增强代理
    
    Args:
        prompt: 测试用提示词
        mcp_url: MCP服务URL
        max_retries: 最大重试次数
    """
    logger.info(f"开始测试提示词增强代理, MCP URL: {mcp_url}")
    
    # 创建提示词增强代理
    enhancer = PromptEnhancerAgent(mcp_url=mcp_url)
    
    # 会话ID
    session_id = f"test-{uuid.uuid4()}"
    logger.info(f"会话ID: {session_id}")
    
    # 带重试的初始化MCP工具
    tools = None
    for retry in range(max_retries):
        try:
            logger.info(f"初始化MCP工具... (尝试 {retry+1}/{max_retries})")
            # 每次重试前等待一点时间
            if retry > 0:
                await asyncio.sleep(2)
            tools = await enhancer.init_mcp_tools(session_id)
            logger.info(f"获取到 {len(tools)} 个MCP工具")
            break
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            if retry == max_retries - 1:  # 最后一次尝试
                logger.error("已达到最大重试次数，放弃尝试")
                raise
    
    # 检查是否成功获取工具
    if not tools:
        logger.error("无法获取MCP工具，请检查MCP服务是否正常运行")
        return "无法连接到MCP服务"
    
    # 创建助手
    logger.info("创建助手...")
    await enhancer.create_assistant()
    
    # 测试非流式增强
    logger.info(f"开始增强提示词: {prompt}")
    try:
        enhanced_prompt = await enhancer.enhance_prompt(prompt, session_id)
        logger.info(f"增强后的提示词: {enhanced_prompt}")
        return enhanced_prompt
    except Exception as e:
        logger.error(f"提示词增强失败: {str(e)}")
        return f"提示词增强失败: {str(e)}"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试提示词增强代理")
    parser.add_argument("--prompt", type=str, default="一个未来城市的夜景", help="测试用提示词")
    parser.add_argument("--mcp-url", type=str, default="http://127.0.0.1:19220", help="MCP服务URL")
    parser.add_argument("--retries", type=int, default=3, help="连接重试次数")
    
    args = parser.parse_args()
    
    # 运行测试
    try:
        enhanced_prompt = asyncio.run(test_prompt_enhancer(args.prompt, args.mcp_url, args.retries))
        
        # 打印结果
        print("\n== 增强后的提示词 ==")
        print(enhanced_prompt)
        print("\n测试完成!")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 