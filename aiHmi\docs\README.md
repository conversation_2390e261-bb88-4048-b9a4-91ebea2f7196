# 沉浸式AI智能座舱 - 项目文档

欢迎来到沉浸式AI智能座舱项目。本项目的目标是打造一个由大型语言模型（LLM）驱动的、能够动态生成UI的下一代汽车人机交互界面。

本文档库旨在为所有团队成员（产品、设计、开发）提供清晰、统一、无冗余的指导。

---

## 文档结构

请按以下顺序阅读文档，以全面了解本项目：

1.  **[1_Product_Requirements.md](./1_Product_Requirements.md)**
    - **面向人群**: 产品经理、项目经理、设计师、开发负责人。
    - **内容**: 定义了产品的愿景、核心哲学、用户故事和高层级的业务逻辑。回答了“我们要做什么”和“为什么这么做”。

2.  **[2_Design_System.md](./2_Design_System.md)**
    - **面向人群**: UI/UX设计师、前端工程师。
    - **内容**: 定义了系统的视觉语言。包括组件库的详细规格、ASCII原型图、设计主题库以及场景过渡效果的设计理念。

3.  **[3_Technical_Specification.md](./3_Technical_Specification.md)**
    - **面向人群**: 前端工程师、系统架构师。
    - **内容**: 唯一的“技术实现”文档。详细说明了基于Vue.js的实现架构、层级关系、状态管理、第三方库（如GSAP）的选型与集成策略。

4.  **[4_API_Specification.md](./4_API_Specification.md)** ⭐ **已重大更新**
    - **面向人群**: 前端工程师、后端工程师。
    - **内容**: 定义了前端UI与后端AI之间通信所使用的JSON数据结构，是前后端开发的契约。
    - **新增内容**: AI内容生成接口规范、ComfyUI集成架构说明、现有能力复用指南。

---

## 最新更新 (2025-07-27)

### API接口文档重大更新

在 `4_API_Specification.md` 中新增了完整的AI内容生成接口规范：

#### 🎯 新增接口规范
- **文生图接口**: 壁纸生成能力 (`/kolors/text-to-image`)
- **涂色书接口**: 儿童线稿生成 (`/coloring-book/start`)
- **主题生成**: 完整主题流水线 (`/pipeline`)
- **文生视频**: 视频内容生成 (`/wan/text-to-video`)
- **图生视频**: 图片转视频 (`/image-to-video`)
- **图像识别**: 图片内容识别 (`/image-to-text`)

#### 🏗️ ComfyUI集成架构
- 统一的工作流调用模式
- 通用模块复用指南 (`theme_backend/app/api/common.py`)
- 工作流配置说明 (`theme_backend/workApi/*.json`)
- 错误处理最佳实践

#### 📁 参考路径指引
- **后端接口**: `d:\code\pythonWork\theme\theme_backend\app\api\`
- **前端调用**: `d:\code\pythonWork\theme\theme_ui\src\api\themeApi.ts`
- **客户端示例**: `d:\code\pythonWork\theme\ColoringBook\js\`
- **工作流配置**: `d:\code\pythonWork\theme\theme_backend\workApi\`

#### ⚠️ 重要原则
**不能重复造轮子** - 文档详细说明了如何复用现有的AI生成能力，避免重复开发。

---

## 历史存档

所有过时或已合并的文档都存放在 `archive/` 目录中，仅供历史追溯使用。
