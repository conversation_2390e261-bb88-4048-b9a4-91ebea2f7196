from fastapi import APIRouter, UploadFile, File, Form, HTTPException
import json
import traceback
from .common import logger, load_workflow, upload_image, send_prompt, wait_for_video

router = APIRouter()

effect_to_prompt_list = [
    {
        "effect_type": "man_takes_off",
         "prompt": "In the video, man takes off from the ground into the air.  hestretches he arms and leaps into the air, creating a graceful ascending motion, causing a sb9527sb flying effect.  The man continues to fly through the city buildings, further showing the sb9527sb flying effect.",
        "lora": "wan/人物飞翔.safetensors"
    },
    {
        "effect_type": "da_bai_chui",
        "prompt": "dabaichui，someone holds someone's head with both hands and twists someone's waist and hips left and right ，making dabaichui motion",
        "lora": "wan/dbc.safetensors"
    },
    # 其他效果文本到提示词的映射
]
server_name="image_to_video_server"
def get_prompt_from_effect(effect_type):
    """
    根据 effect_type 获取对应的 prompt，
    :param effect_type: 效果文本
    :return: 包含 effect_type, prompt, lora 的字典
    """
    # 根据 effect_type 获取对应的项
    prompt_dict = next((item for item in effect_to_prompt_list if item["effect_type"] == effect_type), None)

    if prompt_dict is None:
        logger.error(f"工作流中找不到效果类型: {effect_type}")
        raise HTTPException(status_code=500, detail=f"Effect type {effect_type} not found in workflow")

    return prompt_dict

def build_image_to_video_workflow(prompt: str, effect_type: str, task_id: str) -> dict:
    """构建图生视频工作流"""
    try:
        prompt_dict = get_prompt_from_effect(effect_type)
        # 加载工作流
        workflow = load_workflow("image_to_video.json")

        # 设置图片
        if "220" not in workflow:
            logger.error("工作流中找不到节点220")
            raise HTTPException(status_code=500, detail="Node 220 not found in workflow")

        workflow["220"]["inputs"]["image"] = prompt
        logger.info(f"设置图片: {prompt}")

        # 设置提示词
        if "213" not in workflow:
            logger.error("工作流中找不到节点213")
            raise HTTPException(status_code=500, detail="Node 213 not found in workflow")

        workflow["213"]["inputs"]["positive_prompt"] = prompt_dict["prompt"]
        logger.info(f"设置提示词: {prompt_dict['prompt']}")

        # 设置lora
        if "228" not in workflow:
            logger.error("工作流中找不到节点228")
            raise HTTPException(status_code=500, detail="Node 228 not found in workflow")

        workflow["228"]["inputs"]["lora"] = prompt_dict["lora"]
        logger.info(f"设置lora: {prompt_dict['lora']}")

        # 设置保存路径
        if "80" not in workflow:
            logger.error("工作流中找不到节点80")
            raise HTTPException(status_code=500, detail="Node 80 not found in workflow")

        workflow["80"]["inputs"]["filename_prefix"] = f"wan/{task_id}"
        logger.info(f"设置filename: wan/{task_id}")

        return workflow, "80"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
@router.post("/image-to-video", summary="图片转换为视频", description="根据上传的图片和特效文本生成新的视频")
async def image_to_video(file: UploadFile = File(...),
                         effect_type: str = Form(...),
                         task_id: str = Form(...)):
    try:
        logger.info(f"\n========== 开始转换图片为视频 ==========")
        logger.info(f"文件名: {file.filename}")
        logger.info(f"特效文本: {effect_type}")
        logger.info(f"任务ID: {task_id}")

        # 上传文件到远程服务器
        uploaded_filename = await upload_image(server_name, file.file, "待转换图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")

        # 构建工作流
        workflow, output_node_id = build_image_to_video_workflow(
            uploaded_filename,
            effect_type,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待视频生成
        video_url = await wait_for_video(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的视频URL: {video_url}")

        return {
            "prompt_id": data["prompt_id"],
            "video_url": video_url
        }
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_video接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
