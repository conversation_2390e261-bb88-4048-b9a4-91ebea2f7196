// VPA数字人Store - 虚拟个人助手管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useVpaStore = defineStore('vpa', () => {
  // === 状态 ===
  const isActive = ref(false) // VPA是否激活
  const isListening = ref(false) // 是否正在监听语音
  const isSpeaking = ref(false) // 是否正在说话
  const currentEmotion = ref('neutral') // 当前情绪状态
  const currentAction = ref('idle') // 当前动作状态
  const conversationHistory = ref([]) // 对话历史
  const currentMessage = ref('') // 当前消息
  
  // VPA配置
  const vpaConfig = ref({
    name: 'AI助手',
    avatar: '/avatars/default-avatar.png',
    voice: 'zh-CN-XiaoxiaoNeural',
    personality: 'friendly', // friendly, professional, casual
    responseSpeed: 'normal', // fast, normal, slow
    animationStyle: 'subtle' // subtle, expressive, minimal
  })

  // 可用的情绪状态
  const availableEmotions = ref([
    'neutral', 'happy', 'excited', 'thinking', 'confused', 
    'sad', 'surprised', 'focused', 'relaxed'
  ])

  // 可用的动作状态
  const availableActions = ref([
    'idle', 'talking', 'listening', 'thinking', 'gesturing',
    'nodding', 'pointing', 'waving', 'typing'
  ])

  // === 计算属性 ===
  const isInteracting = computed(() => {
    return isListening.value || isSpeaking.value || currentAction.value !== 'idle'
  })

  const currentStatus = computed(() => {
    if (isSpeaking.value) return 'speaking'
    if (isListening.value) return 'listening'
    if (currentAction.value === 'thinking') return 'thinking'
    if (isActive.value) return 'active'
    return 'idle'
  })

  const recentMessages = computed(() => {
    return conversationHistory.value.slice(-5) // 最近5条消息
  })

  // === 方法 ===
  const activateVpa = () => {
    isActive.value = true
    setEmotion('happy')
    setAction('waving')
    
    setTimeout(() => {
      setAction('idle')
    }, 2000)
  }

  const deactivateVpa = () => {
    isActive.value = false
    isListening.value = false
    isSpeaking.value = false
    setEmotion('neutral')
    setAction('idle')
  }

  const startListening = () => {
    if (!isActive.value) return
    
    isListening.value = true
    setEmotion('focused')
    setAction('listening')
  }

  const stopListening = () => {
    isListening.value = false
    setAction('idle')
  }

  const startSpeaking = (message) => {
    if (!isActive.value) return
    
    isSpeaking.value = true
    currentMessage.value = message
    setEmotion('happy')
    setAction('talking')
    
    // 添加到对话历史
    addToConversation('assistant', message)
  }

  const stopSpeaking = () => {
    isSpeaking.value = false
    currentMessage.value = ''
    setAction('idle')
    setEmotion('neutral')
  }

  const setEmotion = (emotion) => {
    if (availableEmotions.value.includes(emotion)) {
      currentEmotion.value = emotion
    }
  }

  const setAction = (action) => {
    if (availableActions.value.includes(action)) {
      currentAction.value = action
    }
  }

  const addToConversation = (role, content, metadata = {}) => {
    const message = {
      id: `msg-${Date.now()}`,
      role, // 'user' or 'assistant'
      content,
      timestamp: new Date().toISOString(),
      emotion: currentEmotion.value,
      action: currentAction.value,
      ...metadata
    }
    
    conversationHistory.value.push(message)
    
    // 限制历史记录长度
    if (conversationHistory.value.length > 100) {
      conversationHistory.value = conversationHistory.value.slice(-50)
    }
    
    saveConversationHistory()
  }

  const clearConversation = () => {
    conversationHistory.value = []
    saveConversationHistory()
  }

  const processUserInput = async (input, inputType = 'text') => {
    if (!isActive.value) return
    
    // 添加用户消息到历史
    addToConversation('user', input, { inputType })
    
    // 设置思考状态
    setEmotion('thinking')
    setAction('thinking')
    
    try {
      // 这里应该调用AI服务处理用户输入
      const response = await simulateAiResponse(input)
      
      // 开始说话
      startSpeaking(response)
      
      // 模拟说话时间
      setTimeout(() => {
        stopSpeaking()
      }, response.length * 50) // 根据文本长度估算说话时间
      
    } catch (error) {
      console.error('Failed to process user input:', error)
      setEmotion('confused')
      setAction('idle')
    }
  }

  const simulateAiResponse = async (input) => {
    // 模拟AI响应延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    // 简单的响应逻辑
    const responses = [
      '我明白了，让我为您处理这个请求。',
      '好的，我来帮您解决这个问题。',
      '这是一个很好的问题，让我想想...',
      '我已经为您找到了相关信息。',
      '根据您的需求，我建议...'
    ]
    
    return responses[Math.floor(Math.random() * responses.length)]
  }

  const updateVpaConfig = (newConfig) => {
    vpaConfig.value = { ...vpaConfig.value, ...newConfig }
    saveVpaConfig()
  }

  const performGesture = (gestureType) => {
    const gestures = {
      wave: () => {
        setAction('waving')
        setTimeout(() => setAction('idle'), 2000)
      },
      nod: () => {
        setAction('nodding')
        setTimeout(() => setAction('idle'), 1000)
      },
      point: () => {
        setAction('pointing')
        setTimeout(() => setAction('idle'), 1500)
      },
      think: () => {
        setEmotion('thinking')
        setAction('thinking')
        setTimeout(() => {
          setEmotion('neutral')
          setAction('idle')
        }, 3000)
      }
    }
    
    if (gestures[gestureType]) {
      gestures[gestureType]()
    }
  }

  const expressEmotion = (emotion, duration = 3000) => {
    const previousEmotion = currentEmotion.value
    setEmotion(emotion)
    
    setTimeout(() => {
      setEmotion(previousEmotion)
    }, duration)
  }

  // 保存配置到本地存储
  const saveVpaConfig = () => {
    try {
      localStorage.setItem('ai-hmi-vpa-config', JSON.stringify(vpaConfig.value))
    } catch (error) {
      console.warn('Failed to save VPA config:', error)
    }
  }

  const saveConversationHistory = () => {
    try {
      // 只保存最近的20条消息
      const recentHistory = conversationHistory.value.slice(-20)
      localStorage.setItem('ai-hmi-conversation-history', JSON.stringify(recentHistory))
    } catch (error) {
      console.warn('Failed to save conversation history:', error)
    }
  }

  const loadVpaConfig = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-vpa-config')
      if (saved) {
        vpaConfig.value = { ...vpaConfig.value, ...JSON.parse(saved) }
      }
    } catch (error) {
      console.warn('Failed to load VPA config:', error)
    }
  }

  const loadConversationHistory = () => {
    try {
      const saved = localStorage.getItem('ai-hmi-conversation-history')
      if (saved) {
        conversationHistory.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('Failed to load conversation history:', error)
    }
  }

  // 语音识别相关
  const startVoiceRecognition = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported')
      return
    }
    
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    const recognition = new SpeechRecognition()
    
    recognition.lang = 'zh-CN'
    recognition.continuous = false
    recognition.interimResults = false
    
    recognition.onstart = () => {
      startListening()
    }
    
    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      processUserInput(transcript, 'voice')
    }
    
    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error)
      stopListening()
    }
    
    recognition.onend = () => {
      stopListening()
    }
    
    recognition.start()
  }

  // 文本转语音
  const speakText = (text) => {
    if (!('speechSynthesis' in window)) {
      console.warn('Speech synthesis not supported')
      return
    }
    
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.lang = 'zh-CN'
    utterance.rate = vpaConfig.value.responseSpeed === 'fast' ? 1.2 : 
                     vpaConfig.value.responseSpeed === 'slow' ? 0.8 : 1.0
    
    utterance.onstart = () => {
      startSpeaking(text)
    }
    
    utterance.onend = () => {
      stopSpeaking()
    }
    
    speechSynthesis.speak(utterance)
  }

  const initializeVpa = () => {
    loadVpaConfig()
    loadConversationHistory()
  }

  return {
    // 状态
    isActive,
    isListening,
    isSpeaking,
    currentEmotion,
    currentAction,
    conversationHistory,
    currentMessage,
    vpaConfig,
    availableEmotions,
    availableActions,
    
    // 计算属性
    isInteracting,
    currentStatus,
    recentMessages,
    
    // 方法
    activateVpa,
    deactivateVpa,
    startListening,
    stopListening,
    startSpeaking,
    stopSpeaking,
    setEmotion,
    setAction,
    addToConversation,
    clearConversation,
    processUserInput,
    updateVpaConfig,
    performGesture,
    expressEmotion,
    startVoiceRecognition,
    speakText,
    initializeVpa
  }
})
