<template>
  <div class="mt-4">
    <h4 class="text-sm font-semibold text-purple-200 mb-2">已生成资源</h4>
    <div class="flex flex-wrap gap-2">
      <div v-for="(resource, index) in resources" :key="index" class="w-16 h-16 rounded-lg bg-cover bg-center border border-purple-500/30 hover:border-purple-500 transition-colors generated-resource" :style="{ backgroundImage: `url(${resource.thumbnail})` }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  resources: Array<{
    type: string;
    url: string;
    thumbnail: string;
  }>;
}>();
</script> 