FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 设置Python环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONIOENCODING=UTF-8
ENV PYTHONPATH=/app
# 设置线程相关环境变量
ENV PYTHONTHREADDEBUG=1
ENV PYTHONASYNCIODEBUG=1
ENV PYTHON_THREADPOOL_SIZE=20

# 复制项目文件
COPY requirements.txt .
COPY app/ ./app/
COPY public/ ./public/
COPY agent/ ./agent/

# 安装Python依赖
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 创建必要的目录并设置权限
RUN mkdir -p public/ui_source && \
    mkdir -p /app/temp && \
    chmod -R 755 public && \
    chmod -R 777 /app/temp

# 暴露端口
EXPOSE 18632

# 使用gunicorn启动，设置worker数和线程数
CMD ["gunicorn", "app.main:app", \
    "--workers", "2", \
    "--worker-class", "uvicorn.workers.UvicornWorker", \
    "--threads", "4", \
    "--worker-connections", "1000", \
    "--timeout", "300", \
    "--keep-alive", "120", \
    "--max-requests", "1000", \
    "--max-requests-jitter", "50", \
    "--bind", "0.0.0.0:18632"] 