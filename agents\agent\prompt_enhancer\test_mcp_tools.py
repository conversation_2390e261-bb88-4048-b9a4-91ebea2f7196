#!/usr/bin/env python
"""
测试MCP工具连接脚本
"""

import os
import sys
import asyncio
import logging
import argparse
from typing import Optional, Dict, Any, List

# 添加当前目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 导入PromptEnhancerAgent
from prompt_enhancer_agent import PromptEnhancerAgent

async def test_mcp_tools(mcp_url: str, session_id: Optional[str] = None):
    """
    测试MCP工具
    
    Args:
        mcp_url: MCP服务URL
        session_id: 会话ID（可选）
    """
    logger.info(f"使用MCP URL: {mcp_url}")
    
    # 创建一个会话ID（如果未提供）
    if not session_id:
        import uuid
        session_id = f"test-{uuid.uuid4()}"
    
    logger.info(f"会话ID: {session_id}")
    
    # 创建PromptEnhancerAgent实例
    enhancer = PromptEnhancerAgent(mcp_url=mcp_url)
    
    # 测试初始化MCP工具
    try:
        logger.info("初始化MCP工具...")
        tools = await enhancer.init_mcp_tools(session_id)
        logger.info(f"成功获取工具列表，工具数量: {len(tools)}")
        
        # 打印工具列表
        for i, tool in enumerate(tools):
            logger.info(f"工具 {i+1}: {tool.get('name')} - {tool.get('description')}")
        
        return True
    except Exception as e:
        logger.error(f"初始化MCP工具失败: {str(e)}")
        return False

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试MCP工具连接")
    parser.add_argument("--mcp-url", type=str, default="http://127.0.0.1:19220", help="MCP服务URL")
    parser.add_argument("--session-id", type=str, help="会话ID（可选）")
    
    args = parser.parse_args()
    
    # 测试MCP工具
    success = await test_mcp_tools(args.mcp_url, args.session_id)
    
    if success:
        logger.info("测试成功")
        sys.exit(0)
    else:
        logger.error("测试失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 