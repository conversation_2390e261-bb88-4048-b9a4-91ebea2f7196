from fastapi import APIRouter, UploadFile, HTTPException
import json
import os
from datetime import datetime
import uuid
import aiofiles
import httpx
import traceback
from pathlib import Path
import logging
from .common import (
    wait_for_multiple_images,
    upload_image,
    load_workflow,
    send_prompt,
    logger
)

router = APIRouter()
server_name="theme_ui_split_server"
def build_ui_split_workflow(ui_filename: str, timestamp: str) -> dict:
    """构建UI切分工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("ui_split.json")
        
        # 修改UI输入节点
        if "11" not in workflow:
            logger.error("工作流中找不到节点11")
            raise HTTPException(status_code=500, detail="Node 11 not found in workflow")
            
        workflow["11"]["inputs"]["image"] = ui_filename
        logger.info(f"设置输入图片: {ui_filename}")

        # 修改保存路径
        if "100" not in workflow:
            logger.error("工作流中找不到节点100")
            raise HTTPException(status_code=500, detail="Node 100 not found in workflow")
            
        save_prefix = f"UI/{timestamp}/ui"
        workflow["100"]["inputs"]["filename_prefix"] = save_prefix
        logger.info(f"设置保存路径前缀: {save_prefix}")
            
        return workflow, "100"  # 100是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

def build_ui_split_path_workflow(task_id: str) -> dict:
    """构建UI切分路径工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("ui_split path.json")
        
        # 修改UI输入节点
        if "101" not in workflow:
            logger.error("工作流中找不到节点101")
            raise HTTPException(status_code=500, detail="Node 101 not found in workflow")
            
        workflow["101"]["inputs"]["file_path"] = f"/ssd2/ComfyUI/output/changan/mainLine_{task_id}/UI"
        logger.info(f"设置输入路径: {workflow['101']['inputs']['file_path']}")

        # 修改保存路径
        if "100" not in workflow:
            logger.error("工作流中找不到节点100")
            raise HTTPException(status_code=500, detail="Node 100 not found in workflow")
            
        save_prefix = f"changan/mainLine_{task_id}/styleIcon/icon"
        workflow["100"]["inputs"]["filename_prefix"] = save_prefix
        logger.info(f"设置保存路径前缀: {save_prefix}")
            
        return workflow, "100"  # 100是保存图片的节点ID
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

@router.post("/ui-split")
async def split_ui(ui_image: UploadFile):
    try:
        logger.info("\n========== 开始处理UI切分请求 ==========")
        logger.info(f"UI图片文件名: {ui_image.filename}")

        if not ui_image:
            raise HTTPException(status_code=400, detail="请上传UI图片")

        # 生成时间戳作为任务ID
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        logger.info(f"生成任务ID: {timestamp}")

        # 上传UI图片
        ui_uploaded_filename = await upload_image(server_name, ui_image.file, "UI图片")

        # 构建工作流
        workflow, output_node_id = build_ui_split_workflow(ui_uploaded_filename, timestamp)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 构建子文件夹路径
        subfolder = f"UI/{timestamp}"
        
        # 等待图片生成
        image_urls = await wait_for_multiple_images(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URLs: {image_urls}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / timestamp
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存所有图片
        public_image_urls = []
        for idx, image_url in enumerate(image_urls):
            image_name = f"ui_{idx+1}.png"
            image_path = save_dir / image_name
            
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(image_url)
                    response.raise_for_status()
                    async with aiofiles.open(image_path, "wb") as f:
                        await f.write(response.content)
                    
                    # 构建公开访问URL
                    public_url = f"/ui_source/{timestamp}/{image_name}"
                    public_image_urls.append(public_url)
                    logger.info(f"保存图片: {public_url}")
                except Exception as e:
                    logger.error(f"保存图片失败: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")

        logger.info("========== UI切分请求处理完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "task_id": timestamp,
            "image_urls": public_image_urls
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理UI切分请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ui-split-path")
async def split_ui_path(task_id: str):
    """使用路径工作流切分UI"""
    try:
        logger.info("\n========== 开始处理UI路径切分请求 ==========")
        logger.info(f"任务ID: {task_id}")

        if not task_id:
            raise HTTPException(status_code=400, detail="请提供任务ID")

        # 构建工作流
        workflow, output_node_id = build_ui_split_path_workflow(task_id)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 构建子文件���路径
        subfolder = f"changan/mainLine_{task_id}/styleIcon"
        
        # 等待图片生成
        image_urls = await wait_for_multiple_images(server_name, data["prompt_id"], output_node_id, subfolder)
        logger.info(f"生成的图片URLs: {image_urls}")

        # 创建保存目录
        save_dir = Path("public/ui_source") / f"mainLine_{task_id}/styleIcon"
        save_dir.mkdir(parents=True, exist_ok=True)

        # 下载并保存所有图片
        public_image_urls = []
        for idx, image_url in enumerate(image_urls):
            image_name = f"icon_{idx+1}.png"
            image_path = save_dir / image_name
            
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(image_url)
                    response.raise_for_status()
                    async with aiofiles.open(image_path, "wb") as f:
                        await f.write(response.content)
                    
                    # 构建公开访问URL
                    public_url = f"/ui_source/mainLine_{task_id}/styleIcon/{image_name}"
                    public_image_urls.append(public_url)
                    logger.info(f"保存图片: {public_url}")
                except Exception as e:
                    logger.error(f"保存图片失败: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")

        logger.info("========== UI路径切分请求处理完成 ==========\n")

        return {
            "prompt_id": data["prompt_id"],
            "task_id": task_id,
            "image_urls": public_image_urls
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理UI路径切分请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))
