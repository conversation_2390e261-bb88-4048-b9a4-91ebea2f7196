/**
 * 粒子特效样式
 * 可以被导入到任何需要使用粒子效果的组件中
 */

/* 粒子容器样式 */
.particle-effect-container {
  position: relative;
  overflow: hidden;
  background-color: transparent;
}

/* 粒子区域容器 */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 粒子样式 */
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  background-color: white;
  transition: opacity 0.3s ease, transform 0.5s ease; /* 平滑过渡效果 */
  mix-blend-mode: screen;
}

/* 渐变遮罩 */
.radial-mask {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  mask-image: radial-gradient(ellipse at center, transparent 0%, black 100%);
  -webkit-mask-image: radial-gradient(
    ellipse at center,
    transparent 0%,
    black 100%
  );
  z-index: 3;
  pointer-events: none;
}

/* 星星闪烁动画 */
@keyframes starlight {
  0%, 100% {
    box-shadow: 0 0 4px white, 0 0 8px white;
    opacity: 1;
  }
  50% {
    box-shadow: 0 0 6px white, 0 0 12px white;
    opacity: 0.5;
  }
}

/* 全屏背景效果容器 */
.fullscreen-particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
} 