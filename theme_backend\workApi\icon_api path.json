{"12": {"inputs": {"seed": 173607039636087, "steps": 8, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 1, "preview_method": "none", "vae_decode": "true", "model": ["43", 0], "positive": ["28", 1], "negative": ["28", 2], "latent_image": ["28", 3], "optional_vae": ["28", 4]}, "class_type": "KSampler (Efficient)", "_meta": {"title": "K采样器(效率)"}}, "15": {"inputs": {"switch_1": "On", "controlnet_1": "MistoLine_MistoLine_V1.0.safetensors", "controlnet_strength_1": 1, "start_percent_1": 0, "end_percent_1": 1, "switch_2": "On", "controlnet_2": "controlnet-union.safetensors", "controlnet_strength_2": 1, "start_percent_2": 0, "end_percent_2": 1, "switch_3": "Off", "controlnet_3": "None", "controlnet_strength_3": 1, "start_percent_3": 0, "end_percent_3": 1, "image_1": ["21", 0], "image_2": ["75", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "ControlNet堆"}}, "21": {"inputs": {"merge_with_lineart": "lineart_standard", "resolution": 1560, "lineart_lower_bound": 0, "lineart_upper_bound": 1, "object_min_size": 36, "object_connectivity": 1, "image": ["70", 0]}, "class_type": "AnyLineArtPreprocessor_aux", "_meta": {"title": "AnyLine预处理器"}}, "28": {"inputs": {"ckpt_name": "AWPainting XL_1.0.safetensors", "vae_name": "Baked VAE", "clip_skip": -1, "lora_name": "None", "lora_model_strength": 1, "lora_clip_strength": 1, "positive": ["87", 2], "negative": "(worst quality),(low quality),(normal quality),lowres,bad anatomy,((bad hands,broken hands)),(many fingers),((grayscale)) watermark,moles,", "token_normalization": "mean", "weight_interpretation": "A1111", "empty_latent_width": ["78", 0], "empty_latent_height": ["78", 1], "batch_size": 1, "lora_stack": ["34", 0], "cnet_stack": ["15", 0]}, "class_type": "Efficient Loader", "_meta": {"title": "效率加载器"}}, "31": {"inputs": {"weight": 0.6, "weight_faceidv2": 0.6, "weight_type": "strong style transfer", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "layer_weights": "", "model": ["28", 0], "ipadapter": ["33", 1], "image": ["90", 0]}, "class_type": "IPAdapterMS", "_meta": {"title": "IPAdapter Mad Scientist"}}, "33": {"inputs": {"preset": "PLUS (high strength)", "model": ["28", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter加载器"}}, "34": {"inputs": {"switch_1": "On", "lora_name_1": "功能/add-detail-xl.safetensors", "model_weight_1": 1, "clip_weight_1": 1, "switch_2": "On", "lora_name_2": "功能/Hyper-SDXL-8steps-UNIT.safetensors", "model_weight_2": 1, "clip_weight_2": 1, "switch_3": "Off", "lora_name_3": "None", "model_weight_3": 1, "clip_weight_3": 1}, "class_type": "CR LoRA Stack", "_meta": {"title": "LoRA堆"}}, "35": {"inputs": {"seed": 777482157126900, "steps": 8, "cfg": 1, "sampler_name": "euler_cfg_pp", "scheduler": "normal", "denoise": 0.35000000000000003, "preview_method": "none", "vae_decode": "true", "model": ["12", 0], "positive": ["28", 1], "negative": ["28", 2], "latent_image": ["40", 0], "optional_vae": ["28", 4]}, "class_type": "KSampler (Efficient)", "_meta": {"title": "K采样器(效率)"}}, "37": {"inputs": {"upscale_model": ["38", 0], "image": ["12", 5]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "图像通过模型放大"}}, "38": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "放大模型加载器"}}, "39": {"inputs": {"upscale_method": "nearest-exact", "scale_by": 0.25, "image": ["37", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "图像按系数缩放"}}, "40": {"inputs": {"pixels": ["39", 0], "vae": ["12", 4]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "42": {"inputs": {"filename_prefix": "/ssd2/ComfyUI/output/changan/mainLine_111/styleIccon/styleIcon", "images": ["35", 5]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "43": {"inputs": {"weight": 0.5, "weight_faceidv2": 1, "weight_type": "style and composition", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "layer_weights": "", "model": ["31", 0], "ipadapter": ["45", 1], "image": ["88", 0], "attn_mask": ["85", 0]}, "class_type": "IPAdapterMS", "_meta": {"title": "IPAdapter Mad Scientist"}}, "45": {"inputs": {"preset": "PLUS (high strength)", "model": ["31", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter加载器"}}, "66": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["88", 0], "mask": ["89", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "遮罩裁剪"}}, "68": {"inputs": {"blend_factor": 0.5, "blend_mode": "normal", "image1": ["88", 0], "image2": ["90", 0]}, "class_type": "ImageBlend", "_meta": {"title": "图像混合"}}, "70": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 50, "y_percent": 50, "mirror": "None", "scale": 1, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "background_image": ["68", 0], "layer_image": ["66", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V2", "_meta": {"title": "LayerUtility: ImageBlendAdvance V2"}}, "75": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 512, "image": ["70", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "Aux集成预处理器"}}, "78": {"inputs": {"image": ["21", 0]}, "class_type": "ImageGenResolutionFromImage", "_meta": {"title": "图像分辨率"}}, "85": {"inputs": {"channel": "red", "image": ["89", 0]}, "class_type": "ImageToMask", "_meta": {"title": "图像到遮罩"}}, "87": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "detailed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "replace_tags eg:search1:replace1;search2:replace2", "images": ["88", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "88": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_icon", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "89": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_mask", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "90": {"inputs": {"file_path": "/ssd2/ComfyUI/output/changan/mainLine_111/releases/wallpaper", "white_bg": "disable", "sort_by": "file_name", "index_variable": 0, "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}}