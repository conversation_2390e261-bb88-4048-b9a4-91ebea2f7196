<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文生主题系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                            600: '#475569',
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            overflow: auto;
            background-color: #0f172a;
            background-image: 
                radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
                radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
                radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-attachment: fixed;
            color: white;
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        
        .preview-container {
            display: flex;
            flex-direction: column;
            gap: 3rem;
            justify-content: center;
            padding: 2rem;
            max-width: 1800px;
            margin: 0 auto;
        }
        
        .mockup-frame {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 100%;
            height: 1250px;
            margin-bottom: 1rem;
        }
        
        .mockup-frame .header {
            height: 2.75rem;
            background: rgba(20, 30, 48, 0.7);
            display: flex;
            align-items: center;
            padding: 0 1.25rem;
        }
        
        .mockup-frame .content {
            height: calc(100% - 2.75rem);
            background: rgba(15, 23, 42, 0.8);
            overflow: hidden;
        }
        
        .mockup-dot {
            height: 0.85rem;
            width: 0.85rem;
            border-radius: 50%;
            margin-right: 0.4rem;
        }

        @keyframes pulse-animation {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .animate-pulse-custom {
            animation: pulse-animation 2s infinite ease-in-out;
        }
        
        /* 新增样式 */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        .feature-card {
            transition: all 0.4s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: 1rem;
            background: linear-gradient(45deg, transparent 50%, rgba(255, 255, 255, 0.1) 60%, transparent 70%);
            z-index: -1;
            transition: all 0.6s ease;
            opacity: 0;
        }
        
        .feature-card:hover::before {
            background-position: 150% 0;
            opacity: 1;
        }
        
        .bg-gradient-tech {
            background: linear-gradient(125deg, #0ea5e9, #7c3aed, #3b82f6);
            background-size: 200% 200%;
            animation: gradientBG 15s ease infinite;
        }
        
        @keyframes gradientBG {
            0% {background-position: 0% 50%;}
            50% {background-position: 100% 50%;}
            100% {background-position: 0% 50%;}
        }
    </style>
</head>
<body class="dark">

<h1 class="text-center text-4xl font-bold pt-8 pb-4 text-white">文生主题系统 - 设计预览</h1>

<!-- 页面预览容器 -->
<div class="preview-container">

    <!-- 新增：主功能入口首页 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">系统首页</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="h-12 w-12 rounded-full bg-gradient-tech flex items-center justify-center float-animation">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h1 class="ml-4 text-3xl font-bold text-white">AI智能创作系统</h1>
                    </div>
                    <div class="flex items-center">
                        <button class="glass-card px-5 py-3 rounded-lg mr-4 hover:bg-purple-700/30 transition-all text-lg">
                            使用指南
                        </button>
                        <button class="glass-card px-5 py-3 rounded-lg hover:bg-purple-700/30 transition-all text-lg">
                            登录/注册
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <!-- 欢迎区域 -->
                        <div class="mb-10 text-center">
                            <h2 class="text-3xl font-bold text-white mb-4">欢迎使用 <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">AI智能创作系统</span></h2>
                            <p class="text-xl text-gray-300">选择您需要的创作功能，开启智能生成之旅</p>
                        </div>
                        
                        <!-- 功能卡片区域 -->
                        <div class="grid grid-cols-3 gap-8 flex-grow">
                            <!-- 文生壁纸功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生壁纸</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">通过文字描述智能生成高清壁纸，支持多种分辨率和风格定制</p>
                                <button class="mt-auto glass-card bg-blue-500/20 hover:bg-blue-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                            
                            <!-- 文生主题功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-purple-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生主题</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">一键生成完整UI主题，包含壁纸、图标、字体与配色方案</p>
                                <button class="mt-auto glass-card bg-purple-500/20 hover:bg-purple-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                            
                            <!-- 文生视频功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-green-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生视频</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">基于文字描述生成动态视频内容，支持多种场景与风格</p>
                                <button class="mt-auto glass-card bg-green-500/20 hover:bg-green-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                        </div>
                        
                        <!-- 底部扩展区域 -->
                        <div class="mt-10 pt-6 border-t border-gray-700/50 flex justify-between items-center">
                            <div>
                                <h3 class="text-xl font-semibold text-white mb-2">更多功能即将推出</h3>
                                <p class="text-gray-400 text-lg">我们正在不断拓展AI创作能力，敬请期待</p>
                            </div>
                            <button class="glass-card bg-dark-700/50 hover:bg-dark-700 text-white py-3 px-6 rounded-lg transition-all duration-300 flex items-center text-lg">
                                <span class="mr-2">加入测试计划</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主页预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">首页</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex justify-between items-center">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                        </svg>
                        <h1 class="ml-4 text-3xl font-bold text-white">AI文生主题</h1>
                    </div>
                    <div>
                        <button class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 flex items-center text-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            创建主题
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-8 text-purple-300">最近生成的主题</h2>
                        
                        <div class="grid grid-cols-3 gap-8 flex-grow">
                            <!-- 主题卡片1 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1614063742148-493c82af9a6c?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">中国风西湖主题</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-03-04</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 主题卡片2 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1561315235-448e9027f3f4?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">科技风未来城市</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-03-02</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- 主题卡片3 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1478760329108-5c3ed9d495a0?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">山水自然风光</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-02-28</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 创建主题页面预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">创建主题</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">创建新主题</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-10 text-center text-purple-200">描述您想要的主题</h2>
                        
                        <div class="mb-8">
                            <textarea 
                                class="w-full p-5 bg-dark-800/50 border border-gray-700 focus:border-purple-500 rounded-lg text-white focus:outline-none resize-none h-40 text-lg"
                                placeholder="描述您想要的主题风格，例如：'帮我生成一个中国风的西湖风光的主题'"
                            ></textarea>
                        </div>
                        
                        <div class="mb-8">
                            <p class="text-gray-300 text-lg mb-4">生成速度</p>
                            <div class="flex items-center">
                                <span class="text-base text-gray-400 mr-4">快速</span>
                                <div class="flex-grow relative">
                                    <div class="h-4 bg-dark-700 rounded-full">
                                        <div class="h-full w-1/2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                                    </div>
                                    <div class="absolute h-7 w-7 bg-white rounded-full -top-1.5 border-2 border-purple-600 left-1/2 transform -translate-x-1/2 shadow"></div>
                                </div>
                                <span class="text-base text-gray-400 ml-4">高质量</span>
                            </div>
                        </div>
                        
                        <div class="mb-10">
                            <p class="text-gray-300 text-lg mb-4">参考样式</p>
                            <div class="grid grid-cols-4 gap-6">
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1592609931095-54a2168ae893?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1666697190001-51d9ad1fa9c0?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1664478711535-fd3cc5d1a99a?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1557683311-eac922347aa1?q=80&w=200')"></div>
                            </div>
                        </div>
                        
                        <button class="glass-card bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-5 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg text-xl">
                            开始生成
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成进度页面预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">生成进度</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">中国风西湖主题 - 生成中</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <!-- 总进度 -->
                        <div class="mb-10">
                            <div class="flex justify-between mb-4">
                                <h2 class="text-2xl font-bold text-purple-200">总体进度</h2>
                                <span class="text-purple-300 font-semibold text-xl">45%</span>
                            </div>
                            <div class="h-6 bg-dark-700 rounded-full overflow-hidden">
                                <div class="h-full w-[45%] bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
                            </div>
                        </div>
                        
                        <!-- 当前步骤 -->
                        <div class="mb-10">
                            <h3 class="font-semibold text-purple-200 mb-4 text-xl">当前步骤：UI主题生成</h3>
                            <div class="flex items-center text-lg">
                                <span class="inline-block h-5 w-5 rounded-full bg-purple-500 animate-pulse-custom mr-4"></span>
                                <span class="text-gray-300">预计剩余时间：5分钟</span>
                            </div>
                        </div>
                        
                        <!-- 步骤列表 -->
                        <div class="flex-grow mb-8">
                            <h3 class="font-semibold text-purple-200 mb-6 text-xl">处理步骤</h3>
                            <div class="grid grid-cols-2 gap-x-16 gap-y-8">
                                <!-- 已完成的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-white text-lg">壁纸生成</p>
                                        <p class="text-base text-gray-400">已完成 - 用时 2分30秒</p>
                                    </div>
                                </div>
                                
                                <!-- 当前步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-purple-500 animate-pulse-custom"></div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-white text-lg">UI主题生成</p>
                                        <p class="text-base text-gray-400">进行中 - 45%</p>
                                    </div>
                                </div>
                                
                                <!-- 等待中的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                            <span class="text-base text-gray-400">3</span>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">UI资源切分</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                                
                                <!-- 等待中的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                            <span class="text-base text-gray-400">4</span>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">并行任务处理</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                                
                                <!-- 最后一个步骤 -->
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                        <span class="text-base text-gray-400">5</span>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">完成打包</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex justify-between">
                            <button class="py-4 px-7 bg-red-600/30 hover:bg-red-600/50 text-white rounded-lg transition text-lg">
                                终止任务
                            </button>
                            <button class="py-4 px-7 bg-purple-600/30 hover:bg-purple-600/50 text-white rounded-lg transition flex items-center text-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                </svg>
                                刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 壁纸预览页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">壁纸预览</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">壁纸预览</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full">
                        <div class="grid grid-cols-5 gap-8 h-full">
                            <!-- 左侧预览图 -->
                            <div class="col-span-3 flex flex-col">
                                <div class="rounded-lg overflow-hidden mb-8 flex-grow bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1614063742148-493c82af9a6c?q=80&w=600')">
                                    <div class="w-full h-full flex items-center justify-center backdrop-blur-sm bg-black/20 hover:backdrop-blur-none hover:bg-transparent transition-all duration-300">
                                        <div class="bg-black/40 px-5 py-2.5 rounded-full text-white text-base font-semibold">点击放大</div>
                                    </div>
                                </div>
                                
                                <!-- 控制按钮 -->
                                <div class="flex justify-between">
                                    <button class="py-4 px-7 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition duration-300 flex items-center text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                        </svg>
                                        重新生成
                                    </button>
                                    <button class="py-4 px-7 bg-purple-600 hover:bg-purple-500 text-white rounded-lg transition duration-300 text-lg">
                                        继续下一步
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 右侧信息和主色调 -->
                            <div class="col-span-2 flex flex-col">
                                <!-- 壁纸信息 -->
                                <div class="mb-8">
                                    <h2 class="text-2xl font-bold text-purple-200 mb-4">壁纸信息</h2>
                                    <div class="glass-card p-6 rounded-lg">
                                        <div class="grid grid-cols-2 gap-6 text-lg">
                                            <div>
                                                <p class="text-gray-400">尺寸</p>
                                                <p class="text-white">1920 x 1080</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">格式</p>
                                                <p class="text-white">JPG</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">分辨率</p>
                                                <p class="text-white">300dpi</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">生成时间</p>
                                                <p class="text-white">2:30</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 主色调展示 -->
                                <div>
                                    <h2 class="text-2xl font-bold text-purple-200 mb-4">主色调</h2>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #2c5282;"></div>
                                            <span class="text-base text-gray-300">#2c5282</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #285e61;"></div>
                                            <span class="text-base text-gray-300">#285e61</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #744210;"></div>
                                            <span class="text-base text-gray-300">#744210</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #1a202c;"></div>
                                            <span class="text-base text-gray-300">#1a202c</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题详细预览页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">主题详细预览</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="bg-dark-800/40 backdrop-blur-md py-5 px-10 flex justify-between items-center border-b border-gray-700/30">
                    <div class="flex items-center">
                        <button class="text-gray-300 hover:text-white mr-5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h1 class="text-3xl font-bold text-white">中国风西湖主题</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                            </svg>
                            编辑
                        </button>
                        <button class="bg-purple-600 hover:bg-purple-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            下载
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-6 overflow-hidden">
                    <div class="grid grid-cols-4 gap-6 h-full">
                        <!-- 左侧预览面板 -->
                        <div class="col-span-3 h-full flex flex-col space-y-6">
                            <!-- 壁纸预览 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="text-xl font-bold text-white">壁纸预览</h2>
                                    <div class="flex space-x-3">
                                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="relative rounded-lg overflow-hidden h-64">
                                    <img src="https://images.unsplash.com/photo-1614063742148-493c82af9a6c?q=80&w=1000" class="w-full h-full object-cover" />
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-dark-900/30 backdrop-blur-sm transition-all duration-300">
                                        <button class="bg-white/20 backdrop-blur-md text-white px-4 py-2 rounded-lg flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                                            </svg>
                                            全屏查看
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="flex mt-4 space-x-4">
                                    <button class="bg-purple-600/30 hover:bg-purple-600/50 text-white px-4 py-2 rounded-lg transition flex items-center text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                                        </svg>
                                        DeepSeek智能优化
                                    </button>
                                    <button class="bg-dark-700/50 hover:bg-dark-700 text-white px-4 py-2 rounded-lg transition flex items-center text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                        </svg>
                                        重新生成
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 图标预览 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="text-xl font-bold text-white">主题图标</h2>
                                    <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="grid grid-cols-5 gap-4">
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex flex-col items-center">
                                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-300 text-xs">首页</span>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex flex-col items-center">
                                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-300 text-xs">应用</span>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex flex-col items-center">
                                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-300 text-xs">时钟</span>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex flex-col items-center">
                                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                                                <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-300 text-xs">消息</span>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex flex-col items-center">
                                        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <span class="text-gray-300 text-xs">设置</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- UI元素预览 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="text-xl font-bold text-white">UI预览</h2>
                                    <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="grid grid-cols-2 gap-6">
                                    <!-- 左侧 UI 组件 -->
                                    <div class="space-y-4">
                                        <div class="bg-dark-700/30 backdrop-blur-md rounded-lg p-4 flex flex-col space-y-4">
                                            <h3 class="text-sm font-medium text-white">按钮样式</h3>
                                            <div class="flex space-x-3">
                                                <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm">主按钮</button>
                                                <button class="bg-dark-700/50 text-white px-4 py-2 rounded-lg text-sm">次要按钮</button>
                                                <button class="border border-purple-500/50 text-purple-400 px-4 py-2 rounded-lg text-sm">边框按钮</button>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-dark-700/30 backdrop-blur-md rounded-lg p-4 flex flex-col space-y-4">
                                            <h3 class="text-sm font-medium text-white">输入框样式</h3>
                                            <div class="flex flex-col space-y-3">
                                                <input type="text" class="bg-dark-800/50 border border-gray-700 focus:border-purple-500 rounded-lg p-3 text-white text-sm focus:outline-none" placeholder="请输入文本..." />
                                                <div class="relative">
                                                    <input type="text" class="bg-dark-800/50 border border-gray-700 focus:border-purple-500 rounded-lg p-3 text-white text-sm focus:outline-none w-full pr-10" placeholder="搜索..." />
                                                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 右侧 UI 组件 -->
                                        <div class="space-y-4">
                                            <div class="bg-dark-700/30 backdrop-blur-md rounded-lg p-4 flex flex-col space-y-4">
                                                <h3 class="text-sm font-medium text-white">卡片样式</h3>
                                                <div class="bg-dark-800/60 backdrop-blur-md rounded-lg p-4 border border-gray-700/30">
                                                    <h4 class="text-white text-sm font-medium mb-2">卡片标题</h4>
                                                    <p class="text-gray-300 text-xs">这是一个样式卡片的示例内容，展示了主题的卡片设计风格和文字排版效果。</p>
                                                </div>
                                            </div>
                                            
                                            <div class="bg-dark-700/30 backdrop-blur-md rounded-lg p-4 flex flex-col space-y-4">
                                                <h3 class="text-sm font-medium text-white">开关和单选框</h3>
                                                <div class="flex justify-between">
                                                    <div class="flex items-center">
                                                        <div class="w-10 h-5 bg-purple-600 rounded-full relative">
                                                            <div class="absolute right-0.5 top-0.5 bg-white rounded-full h-4 w-4"></div>
                                                        </div>
                                                        <span class="text-white text-xs ml-2">开启</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <div class="w-10 h-5 bg-dark-700 rounded-full relative">
                                                            <div class="absolute left-0.5 top-0.5 bg-gray-400 rounded-full h-4 w-4"></div>
                                                        </div>
                                                        <span class="text-gray-400 text-xs ml-2">关闭</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧信息面板 -->
                    <div class="col-span-1 h-full bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col">
                        <h2 class="text-xl font-bold text-white mb-6">主题信息</h2>
                        
                        <div class="mb-6 pb-6 border-b border-gray-700/30">
                            <h3 class="text-base text-gray-300 mb-3">基本信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">主题名称</span>
                                    <span class="text-white text-sm">中国风西湖主题</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">创建时间</span>
                                    <span class="text-white text-sm">2024-03-04</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">分辨率</span>
                                    <span class="text-white text-sm">1920 x 1080</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">主题包大小</span>
                                    <span class="text-white text-sm">24.5 MB</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 色彩方案 -->
                        <div class="mb-6 pb-6 border-b border-gray-700/30">
                            <h3 class="text-base text-gray-300 mb-3">色彩方案</h3>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="flex flex-col items-center">
                                    <div class="h-12 w-12 rounded-lg mb-1" style="background-color: #2c5282;"></div>
                                    <span class="text-xs text-gray-300">#2c5282</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="h-12 w-12 rounded-lg mb-1" style="background-color: #285e61;"></div>
                                    <span class="text-xs text-gray-300">#285e61</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="h-12 w-12 rounded-lg mb-1" style="background-color: #744210;"></div>
                                    <span class="text-xs text-gray-300">#744210</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="h-12 w-12 rounded-lg mb-1" style="background-color: #1a202c;"></div>
                                    <span class="text-xs text-gray-300">#1a202c</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- AI优化建议 -->
                        <div class="mb-6">
                            <h3 class="text-base text-gray-300 mb-3">AI优化建议</h3>
                            <div class="bg-dark-700/50 rounded-lg p-4">
                                <p class="text-sm text-gray-300 mb-3">可以尝试以下优化以提升主题效果：</p>
                                <ul class="text-xs text-gray-400 space-y-2 list-disc pl-4">
                                    <li>增加山水元素的饱和度，提升视觉冲击力</li>
                                    <li>调整图标配色，与壁纸色调保持一致</li>
                                    <li>增加水墨风格的动态过渡效果</li>
                                </ul>
                                <button class="mt-4 w-full bg-purple-600/30 hover:bg-purple-600/50 text-white py-2 rounded-lg transition flex items-center justify-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                                    </svg>
                                    应用AI建议
                                </button>
                            </div>
                        </div>
                        
                        <!-- 用户交互 -->
                        <div class="mt-auto pt-6 border-t border-gray-700/30">
                            <button class="w-full bg-dark-700/50 hover:bg-dark-700 text-white py-3 rounded-lg transition flex items-center justify-center text-base">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V7a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                删除主题
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文生视频创建页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">文生视频创建</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">创建文生视频</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-8 text-center text-green-300">描述您想要的视频内容</h2>
                        
                        <div class="grid grid-cols-5 gap-8 flex-grow">
                            <!-- 左侧表单 -->
                            <div class="col-span-3 flex flex-col space-y-8">
                                <!-- 视频描述 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">详细描述</label>
                                    <textarea 
                                        class="w-full p-5 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none resize-none h-40 text-lg"
                                        placeholder="描述您想要的视频内容和风格，例如：'一段展示山水风光的视频，有流动的江水和雾气缭绕的山脉，配以传统古筝音乐...'"
                                    ></textarea>
                                </div>
                                
                                <!-- 视频时长 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">视频时长</label>
                                    <div class="flex items-center">
                                        <div class="flex-grow relative">
                                            <div class="h-4 bg-dark-700 rounded-full">
                                                <div class="h-full w-1/3 bg-gradient-to-r from-green-500 to-green-600 rounded-full"></div>
                                            </div>
                                            <div class="absolute h-7 w-7 bg-white rounded-full -top-1.5 border-2 border-green-600 left-1/3 transform -translate-x-1/2 shadow"></div>
                                        </div>
                                        <span class="text-base text-white ml-4 min-w-[80px] text-center">15 秒</span>
                                    </div>
                                    <div class="flex justify-between mt-2 text-xs text-gray-400">
                                        <span>5秒</span>
                                        <span>15秒</span>
                                        <span>30秒</span>
                                        <span>60秒</span>
                                    </div>
                                </div>
                                
                                <!-- 视频场景 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">选择场景类型</label>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="bg-dark-800/50 border border-green-500 rounded-lg p-4 flex flex-col items-center cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                            </svg>
                                            <span class="text-white">自然景观</span>
                                        </div>
                                        <div class="bg-dark-800/50 border border-gray-700 hover:border-green-500 rounded-lg p-4 flex flex-col items-center cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                            <span class="text-gray-300">城市建筑</span>
                                        </div>
                                        <div class="bg-dark-800/50 border border-gray-700 hover:border-green-500 rounded-lg p-4 flex flex-col items-center cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
                                            </svg>
                                            <span class="text-gray-300">抽象艺术</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 视频风格 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">视频风格</label>
                                    <div class="flex flex-wrap gap-3">
                                        <span class="px-4 py-2 bg-green-600/30 text-green-300 rounded-full text-sm cursor-pointer">电影风格</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">3D写实</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">动画</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">水墨画</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">黑白</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">赛博朋克</span>
                                        <span class="px-4 py-2 bg-dark-700/50 text-gray-300 rounded-full text-sm cursor-pointer hover:bg-green-600/30 hover:text-green-300">复古</span>
                                    </div>
                                </div>
                                
                                <!-- 分辨率选择 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">分辨率</label>
                                    <div class="flex space-x-4">
                                        <div class="flex-1">
                                            <select class="w-full p-3 bg-dark-800/50 border border-gray-700 text-white rounded-lg focus:outline-none focus:border-green-500">
                                                <option>1080p (1920 x 1080)</option>
                                                <option>720p (1280 x 720)</option>
                                                <option>4K (3840 x 2160)</option>
                                                <option>竖屏 (1080 x 1920)</option>
                                            </select>
                                        </div>
                                        <div class="flex-1">
                                            <select class="w-full p-3 bg-dark-800/50 border border-gray-700 text-white rounded-lg focus:outline-none focus:border-green-500">
                                                <option>标准帧率 (24fps)</option>
                                                <option>高帧率 (60fps)</option>
                                                <option>慢动作 (120fps)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 右侧预览和参考 -->
                            <div class="col-span-2 flex flex-col space-y-8">
                                <!-- AI助手 -->
                                <div class="bg-dark-800/50 rounded-xl p-6 h-auto">
                                    <div class="flex items-start mb-4">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-bold text-white">DeepSeek助手</h3>
                                            <p class="text-gray-400 text-sm">让AI帮助你完善视频创意</p>
                                        </div>
                                    </div>
                                    <div class="bg-dark-700/50 rounded-lg p-4 mb-3">
                                        <p class="text-green-300 text-sm mb-2">🤖 AI助手</p>
                                        <p class="text-white text-sm">您好！我可以帮您优化视频创意。看起来您想创建一段自然景观视频。您希望有什么特定元素或情感表达吗？</p>
                                    </div>
                                    
                                    <div class="bg-dark-700/30 rounded-lg p-4 mb-4">
                                        <p class="text-blue-300 text-sm mb-2">👤 您</p>
                                        <p class="text-white text-sm">我想要一段山水风光的视频，有流动的江水和雾气缭绕的山脉</p>
                                    </div>
                                    
                                    <div class="flex">
                                        <input 
                                            type="text" 
                                            class="flex-grow p-3 bg-dark-800/70 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none text-sm"
                                            placeholder="询问AI助手建议..."
                                        />
                                        <button class="ml-2 p-3 bg-green-600 text-white rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 参考视频示例 -->
                                <div class="bg-dark-800/50 rounded-xl p-6 flex-grow">
                                    <h3 class="text-lg font-bold text-white mb-4">参考示例</h3>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="cursor-pointer group">
                                            <div class="h-24 rounded-lg bg-cover bg-center relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=300')">
                                                <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-300 mt-2">山谷流水</p>
                                        </div>
                                        <div class="cursor-pointer group">
                                            <div class="h-24 rounded-lg bg-cover bg-center relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?q=80&w=300')">
                                                <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-300 mt-2">雾山日出</p>
                                        </div>
                                        <div class="cursor-pointer group">
                                            <div class="h-24 rounded-lg bg-cover bg-center relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1519681393784-d120267933ba?q=80&w=300')">
                                                <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-300 mt-2">星空延时</p>
                                        </div>
                                        <div class="cursor-pointer group">
                                            <div class="h-24 rounded-lg bg-cover bg-center relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1502472584811-0a2f2feb8968?q=80&w=300')">
                                                <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-300 mt-2">森林雨景</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 底部按钮区域 -->
                        <div class="mt-8 flex justify-between">
                            <button class="py-4 px-7 bg-dark-700/50 hover:bg-dark-700 text-white rounded-lg transition text-lg">
                                保存为草稿
                            </button>
                            <button class="py-4 px-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white rounded-lg transition duration-300 text-lg font-semibold">
                                开始生成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文生视频生成进度页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">视频生成进度</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">自然景观视频 - 生成中</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <!-- 上方区域：进度和预览 -->
                        <div class="grid grid-cols-5 gap-8 mb-8">
                            <!-- 左侧进度信息 -->
                            <div class="col-span-2 flex flex-col">
                                <!-- 总进度 -->
                                <div class="mb-6">
                                    <div class="flex justify-between mb-3">
                                        <h2 class="text-2xl font-bold text-green-200">总体进度</h2>
                                        <span class="text-green-300 font-semibold text-xl">67%</span>
                                    </div>
                                    <div class="h-6 bg-dark-700 rounded-full overflow-hidden">
                                        <div class="h-full w-[67%] bg-gradient-to-r from-green-600 to-green-400 rounded-full"></div>
                                    </div>
                                </div>
                                
                                <!-- 当前步骤 -->
                                <div class="mb-6">
                                    <h3 class="font-semibold text-green-200 mb-3 text-xl">当前步骤：场景渲染</h3>
                                    <div class="flex items-center text-lg">
                                        <span class="inline-block h-5 w-5 rounded-full bg-green-500 animate-pulse-custom mr-4"></span>
                                        <span class="text-gray-300">预计剩余时间：2分钟</span>
                                    </div>
                                </div>
                                
                                <!-- 任务详情 -->
                                <div class="bg-dark-800/50 rounded-lg p-5 mb-6">
                                    <h3 class="text-lg font-semibold text-white mb-3">任务详情</h3>
                                    <div class="space-y-3">
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">创建时间</span>
                                            <span class="text-white">2024-03-10 14:32</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">分辨率</span>
                                            <span class="text-white">1920 x 1080</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">时长</span>
                                            <span class="text-white">15秒</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-400">风格</span>
                                            <span class="text-white">电影风格</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="flex space-x-4">
                                    <button class="flex-1 py-3 px-4 bg-red-600/30 hover:bg-red-600/50 text-white rounded-lg transition text-base flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        终止任务
                                    </button>
                                    <button class="flex-1 py-3 px-4 bg-green-600/30 hover:bg-green-600/50 text-white rounded-lg transition text-base flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                        </svg>
                                        刷新状态
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 右侧预览和生成状态 -->
                            <div class="col-span-3 flex flex-col">
                                <!-- 预览区域 -->
                                <div class="bg-dark-800/50 rounded-lg overflow-hidden h-60 mb-4 relative">
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="relative w-full max-w-xs">
                                            <!-- 视频帧预览 -->
                                            <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=600" class="w-full rounded" alt="视频帧预览" />
                                            
                                            <!-- 生成中遮罩 -->
                                            <div class="absolute inset-0 bg-black/30 backdrop-blur-sm flex flex-col items-center justify-center">
                                                <div class="h-16 w-16 rounded-full border-4 border-t-green-500 border-r-green-500 border-b-green-300 border-l-transparent animate-spin mb-4"></div>
                                                <p class="text-white text-lg font-medium">场景渲染中...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 帧生成进度 -->
                                <div class="grid grid-cols-12 gap-1 mb-4">
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-500 rounded"></div>
                                    <div class="h-2 bg-green-400 rounded animate-pulse"></div>
                                    <div class="h-2 bg-dark-700 rounded"></div>
                                    <div class="h-2 bg-dark-700 rounded"></div>
                                    <div class="h-2 bg-dark-700 rounded"></div>
                                </div>
                                
                                <!-- 实时信息 -->
                                <div class="bg-dark-800/50 rounded-lg p-5 flex-grow">
                                    <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                        实时进度信息
                                    </h3>
                                    <div class="h-32 bg-dark-900/50 rounded p-3 overflow-y-auto text-sm font-mono">
                                        <p class="text-gray-500">[14:32:45] 任务已开始...</p>
                                        <p class="text-gray-500">[14:33:12] 正在解析文本描述...</p>
                                        <p class="text-gray-500">[14:33:30] 开始创建关键帧...</p>
                                        <p class="text-gray-500">[14:34:15] 关键帧创建完成，共8个</p>
                                        <p class="text-gray-500">[14:34:45] 开始生成转场效果...</p>
                                        <p class="text-gray-500">[14:35:10] 转场效果生成完成</p>
                                        <p class="text-green-400">[14:35:30] 开始场景渲染，进度8/12...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 下方区域：生成步骤 -->
                        <div>
                            <h3 class="font-semibold text-green-200 mb-6 text-xl">处理步骤</h3>
                            <div class="flex justify-between relative">
                                <!-- 进度线 -->
                                <div class="absolute top-5 left-[55px] right-[55px] h-1 bg-gray-700">
                                    <div class="h-full w-[60%] bg-green-500"></div>
                                </div>
                                
                                <!-- 步骤1：文本解析 -->
                                <div class="flex flex-col items-center relative z-10 w-28">
                                    <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <p class="font-medium text-white text-sm text-center">文本解析</p>
                                    <p class="text-xs text-green-400 text-center">已完成</p>
                                </div>
                                
                                <!-- 步骤2：关键帧 -->
                                <div class="flex flex-col items-center relative z-10 w-28">
                                    <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <p class="font-medium text-white text-sm text-center">关键帧生成</p>
                                    <p class="text-xs text-green-400 text-center">已完成</p>
                                </div>
                                
                                <!-- 步骤3：场景渲染 -->
                                <div class="flex flex-col items-center relative z-10 w-28">
                                    <div class="h-10 w-10 rounded-full bg-green-500 animate-pulse-custom mb-3"></div>
                                    <p class="font-medium text-white text-sm text-center">场景渲染</p>
                                    <p class="text-xs text-green-400 text-center">67%</p>
                                </div>
                                
                                <!-- 步骤4：音频合成 -->
                                <div class="flex flex-col items-center relative z-10 w-28">
                                    <div class="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center mb-3">
                                        <span class="text-gray-400 text-sm">4</span>
                                    </div>
                                    <p class="font-medium text-gray-400 text-sm text-center">音频合成</p>
                                    <p class="text-xs text-gray-500 text-center">等待中</p>
                                </div>
                                
                                <!-- 步骤5：视频合成 -->
                                <div class="flex flex-col items-center relative z-10 w-28">
                                    <div class="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center mb-3">
                                        <span class="text-gray-400 text-sm">5</span>
                                    </div>
                                    <p class="font-medium text-gray-400 text-sm text-center">视频合成</p>
                                    <p class="text-xs text-gray-500 text-center">等待中</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文生视频预览和编辑页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">视频预览与编辑</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center justify-between">
                    <div class="flex items-center">
                        <button class="text-gray-300 hover:text-white mr-5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h1 class="text-3xl font-bold text-white">自然景观视频 - 完成</h1>
                    </div>
                    <div class="flex space-x-4">
                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                            </svg>
                            历史版本
                        </button>
                        <button class="bg-green-600 hover:bg-green-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            下载视频
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="grid grid-cols-7 gap-6 h-full">
                        <!-- 左侧视频预览区 -->
                        <div class="col-span-4 flex flex-col space-y-6">
                            <!-- 视频播放预览区 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col flex-grow">
                                <h2 class="text-xl font-bold text-white mb-4">视频预览</h2>
                                
                                <!-- 视频播放器 -->
                                <div class="relative rounded-lg overflow-hidden bg-black flex-grow flex items-center justify-center">
                                    <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=1000" class="w-full h-full object-cover" alt="视频预览" />
                                    
                                    <!-- 播放控制遮罩 -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
                                        <!-- 播放按钮 -->
                                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                            <div class="h-16 w-16 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center cursor-pointer hover:bg-white/30 transition duration-300">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                        </div>
                                        
                                        <!-- 底部进度条和控制器 -->
                                        <div class="absolute bottom-0 left-0 right-0 p-4">
                                            <div class="flex items-center mb-2">
                                                <span class="text-white text-sm mr-2">00:05</span>
                                                <div class="h-1.5 bg-gray-700 rounded-full flex-1 mx-2">
                                                    <div class="h-full w-1/3 bg-green-500 rounded-full relative">
                                                        <div class="absolute -right-2 -top-1 h-4 w-4 bg-white rounded-full"></div>
                                                    </div>
                                                </div>
                                                <span class="text-white text-sm ml-2">00:15</span>
                                            </div>
                                            
                                            <div class="flex justify-between items-center">
                                                <div class="flex space-x-4">
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                        </svg>
                                                    </button>
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414-9.9m-3.535 13.435a9 9 0 010-17.07" />
                                                        </svg>
                                                    </button>
                                                </div>
                                                
                                                <div class="flex space-x-4">
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                        </svg>
                                                    </button>
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 视频帧预览区 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="text-xl font-bold text-white">关键帧</h2>
                                    <button class="text-gray-400 hover:text-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="flex space-x-3 overflow-x-auto pb-2">
                                    <div class="flex-shrink-0 w-32 h-24 rounded-lg bg-cover bg-center border-2 border-green-500" style="background-image: url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=200')"></div>
                                    <div class="flex-shrink-0 w-32 h-24 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1542224566-6e85f2e6772f?q=80&w=200')"></div>
                                    <div class="flex-shrink-0 w-32 h-24 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1498429089284-41f8cf3ffd39?q=80&w=200')"></div>
                                    <div class="flex-shrink-0 w-32 h-24 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1458966480358-a0ac42de0a7a?q=80&w=200')"></div>
                                    <div class="flex-shrink-0 w-32 h-24 rounded-lg bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?q=80&w=200')"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧编辑和优化区 -->
                        <div class="col-span-3 flex flex-col space-y-6">
                            <!-- 基本信息 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <h2 class="text-xl font-bold text-white mb-4">视频信息</h2>
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">名称</span>
                                        <span class="text-white">自然景观视频</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">创建时间</span>
                                        <span class="text-white">2024-03-10 14:45</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">分辨率</span>
                                        <span class="text-white">1920 x 1080</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">时长</span>
                                        <span class="text-white">15秒</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">文件大小</span>
                                        <span class="text-white">54.2 MB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- DeepSeek AI 优化 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex-grow">
                                <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                                    </svg>
                                    DeepSeek AI 视频优化
                                </h2>
                                
                                <!-- 优化选项 -->
                                <div class="space-y-4 mb-6">
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-green-500 flex-shrink-0 flex items-center justify-center mt-0.5">
                                            <div class="h-2 w-2 rounded-full bg-green-500"></div>
                                        </div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">提升视频清晰度</h3>
                                            <p class="text-gray-400 text-sm">使用AI超分辨率技术，提升视频画质锐度和细节表现</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0 mt-0.5"></div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">音频增强</h3>
                                            <p class="text-gray-400 text-sm">添加或增强自然环境音效，提升听觉体验</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0 mt-0.5"></div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">视频平滑处理</h3>
                                            <p class="text-gray-400 text-sm">提升视频帧率并添加平滑转场效果</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0 mt-0.5"></div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">色彩增强</h3>
                                            <p class="text-gray-400 text-sm">智能调整色彩平衡，增强视觉冲击力</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- AI助手对话 -->
                                <div class="bg-dark-900/50 rounded-lg p-4 mb-4">
                                    <div class="flex items-start mb-3">
                                        <div class="h-8 w-8 rounded-full bg-green-600 flex items-center justify-center mr-3 flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-white">我注意到视频的某些部分可能需要提升清晰度。建议使用AI超分辨率处理，可以显著提升画面细节表现，特别是远景山脉部分。需要我帮你应用这个优化吗？</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="flex justify-between">
                                    <button class="py-3 px-5 bg-dark-700/50 hover:bg-dark-700 text-white rounded-lg transition flex items-center text-base">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                                            <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                                        </svg>
                                        自定义编辑
                                    </button>
                                    <button class="py-3 px-6 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white rounded-lg transition duration-300 text-base font-semibold">
                                        应用优化
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 分享选项 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <h2 class="text-xl font-bold text-white mb-4">分享</h2>
                                <div class="flex space-x-4">
                                    <button class="flex-1 py-2.5 bg-blue-600/20 hover:bg-blue-600/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
                                        </svg>
                                        Facebook
                                    </button>
                                    <button class="flex-1 py-2.5 bg-blue-400/20 hover:bg-blue-400/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                        </svg>
                                        Twitter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图生视频创建页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">图生视频创建</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">图生视频</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-8 text-center text-indigo-300">将静态图片转为动态视频</h2>
                        
                        <div class="grid grid-cols-7 gap-8">
                            <!-- 左侧：图片上传区域 -->
                            <div class="col-span-3 flex flex-col space-y-6">
                                <!-- 图片上传区 -->
                                <div class="bg-dark-800/50 rounded-xl p-6 flex flex-col items-center justify-center border-2 border-dashed border-gray-700 hover:border-indigo-500 transition-all cursor-pointer h-64">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-gray-400 text-lg mb-2">点击或拖拽上传图片</p>
                                    <p class="text-gray-500 text-sm">支持 PNG, JPG, WEBP (最大 20MB)</p>
                                </div>
                                
                                <!-- 或使用示例图片 -->
                                <div>
                                    <p class="text-gray-300 text-lg mb-4">或选择示例图片</p>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="rounded-lg h-24 bg-cover bg-center border-2 border-indigo-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1682685797660-3d847763208e?q=80&w=200')"></div>
                                        <div class="rounded-lg h-24 bg-cover bg-center border-2 border-transparent hover:border-indigo-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1506259091721-347e791bab0f?q=80&w=200')"></div>
                                        <div class="rounded-lg h-24 bg-cover bg-center border-2 border-transparent hover:border-indigo-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1511447333015-45b65e60f6d5?q=80&w=200')"></div>
                                    </div>
                                </div>
                                
                                <!-- 自定义文本描述 -->
                                <div>
                                    <label class="block text-gray-300 text-lg mb-4">添加描述（可选）</label>
                                    <textarea 
                                        class="w-full p-5 bg-dark-800/50 border border-gray-700 focus:border-indigo-500 rounded-lg text-white focus:outline-none resize-none h-32 text-lg"
                                        placeholder="添加额外的描述，帮助系统理解您期望的动画效果..."
                                    ></textarea>
                                </div>
                            </div>
                            
                            <!-- 中间：预览与配置 -->
                            <div class="col-span-2 flex flex-col space-y-6">
                                <!-- 图片预览 -->
                                <div class="bg-dark-800/50 rounded-xl p-5 flex flex-col">
                                    <h3 class="text-lg font-semibold text-white mb-3">预览</h3>
                                    <div class="relative rounded-lg overflow-hidden aspect-video bg-black/50 flex items-center justify-center">
                                        <img src="https://images.unsplash.com/photo-1682685797660-3d847763208e?q=80&w=500" class="w-full h-full object-contain" alt="预览图" />
                                        <div class="absolute top-2 right-2 bg-indigo-600/80 text-white text-xs py-1 px-2 rounded">
                                            原始图片
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 视频时长设置 -->
                                <div class="bg-dark-800/50 rounded-xl p-5">
                                    <h3 class="text-lg font-semibold text-white mb-3">视频时长</h3>
                                    <div class="flex items-center">
                                        <div class="flex-grow relative">
                                            <div class="h-4 bg-dark-700 rounded-full">
                                                <div class="h-full w-1/4 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
                                            </div>
                                            <div class="absolute h-7 w-7 bg-white rounded-full -top-1.5 border-2 border-indigo-500 left-1/4 transform -translate-x-1/2 shadow"></div>
                                        </div>
                                        <span class="text-base text-white ml-4 min-w-[80px] text-center">6 秒</span>
                                    </div>
                                    <div class="flex justify-between mt-2 text-xs text-gray-400">
                                        <span>3秒</span>
                                        <span>6秒</span>
                                        <span>10秒</span>
                                        <span>15秒</span>
                                    </div>
                                </div>
                                
                                <!-- 动画风格 -->
                                <div class="bg-dark-800/50 rounded-xl p-5">
                                    <h3 class="text-lg font-semibold text-white mb-3">动画风格</h3>
                                    <div class="space-y-3">
                                        <div class="bg-indigo-600/20 border border-indigo-500/50 rounded-lg p-3 flex items-center cursor-pointer">
                                            <div class="h-5 w-5 rounded-full border-2 border-indigo-500 flex-shrink-0 flex items-center justify-center">
                                                <div class="h-2 w-2 rounded-full bg-indigo-500"></div>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-white">自然流动</p>
                                            </div>
                                        </div>
                                        <div class="bg-dark-700/30 border border-gray-700 rounded-lg p-3 flex items-center cursor-pointer hover:bg-indigo-600/10 hover:border-indigo-500/30 transition-colors">
                                            <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0"></div>
                                            <div class="ml-3">
                                                <p class="text-gray-300">3D视差</p>
                                            </div>
                                        </div>
                                        <div class="bg-dark-700/30 border border-gray-700 rounded-lg p-3 flex items-center cursor-pointer hover:bg-indigo-600/10 hover:border-indigo-500/30 transition-colors">
                                            <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0"></div>
                                            <div class="ml-3">
                                                <p class="text-gray-300">梦幻变形</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 右侧：AI助手和高级选项 -->
                            <div class="col-span-2 flex flex-col space-y-6">
                                <!-- AI建议区 -->
                                <div class="bg-dark-800/50 rounded-xl p-5 flex-grow">
                                    <div class="flex items-start mb-3">
                                        <div class="h-9 w-9 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mr-3 flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-bold text-white">DeepSeek AI助手</h3>
                                            <p class="text-gray-400 text-sm">提供智能建议，优化动画效果</p>
                                        </div>
                                    </div>
                                    
                                    <!-- AI对话框 -->
                                    <div class="space-y-3 mb-4">
                                        <div class="bg-dark-700/50 rounded-lg p-3">
                                            <p class="text-indigo-300 text-sm mb-1">🤖 AI助手</p>
                                            <p class="text-white text-sm">我注意到您上传了一张海面日落图片。建议使用"自然流动"动画风格，可以让水面和云层产生逼真的流动效果。您想要水面有什么特定的动作吗？</p>
                                        </div>
                                        
                                        <div class="bg-dark-700/30 rounded-lg p-3">
                                            <p class="text-blue-300 text-sm mb-1">👤 您</p>
                                            <p class="text-white text-sm">我希望水面有轻微的波动，天空的云彩可以慢慢移动</p>
                                        </div>
                                        
                                        <div class="bg-dark-700/50 rounded-lg p-3">
                                            <p class="text-indigo-300 text-sm mb-1">🤖 AI助手</p>
                                            <p class="text-white text-sm">了解了！我会设置水面有轻微波动效果，天空云层缓慢飘动。还可以为日落添加轻微的光芒闪烁效果，增强画面生动感。需要我为您应用这些建议吗？</p>
                                        </div>
                                    </div>
                                    
                                    <!-- 对话输入框 -->
                                    <div class="flex">
                                        <input 
                                            type="text" 
                                            class="flex-grow p-3 bg-dark-800/70 border border-gray-700 focus:border-indigo-500 rounded-lg text-white focus:outline-none text-sm"
                                            placeholder="询问AI助手如何提升效果..."
                                        />
                                        <button class="ml-2 p-3 bg-indigo-600 text-white rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 高级选项 -->
                                <div class="bg-dark-800/50 rounded-xl p-5">
                                    <div class="flex justify-between items-center mb-3">
                                        <h3 class="text-lg font-semibold text-white">高级选项</h3>
                                        <button class="bg-dark-700/50 hover:bg-dark-700 text-gray-300 p-1.5 rounded transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <div class="space-y-4">
                                        <div>
                                            <label class="flex justify-between items-center text-gray-300 text-sm mb-2">
                                                <span>运动强度</span>
                                                <span>中等</span>
                                            </label>
                                            <div class="h-2 bg-dark-700 rounded-full">
                                                <div class="h-full w-1/2 bg-indigo-500 rounded-full"></div>
                                            </div>
                                        </div>
                                        
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-300 text-sm">添加音效</span>
                                            <div class="w-10 h-5 bg-indigo-600 rounded-full relative">
                                                <div class="absolute right-0.5 top-0.5 bg-white rounded-full h-4 w-4"></div>
                                            </div>
                                        </div>
                                        
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-300 text-sm">循环播放</span>
                                            <div class="w-10 h-5 bg-dark-700 rounded-full relative">
                                                <div class="absolute left-0.5 top-0.5 bg-gray-400 rounded-full h-4 w-4"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 底部按钮区域 -->
                        <div class="mt-8 flex justify-between">
                            <button class="py-4 px-7 bg-dark-700/50 hover:bg-dark-700 text-white rounded-lg transition text-lg">
                                保存为草稿
                            </button>
                            <button class="py-4 px-12 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white rounded-lg transition duration-300 text-lg font-semibold flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                开始生成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图生视频效果预览页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">图生视频效果预览</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center justify-between">
                    <div class="flex items-center">
                        <button class="text-gray-300 hover:text-white mr-5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h1 class="text-3xl font-bold text-white">动态海面日落 - 效果预览</h1>
                    </div>
                    <div class="flex space-x-4">
                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                                <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                            </svg>
                            重新编辑
                        </button>
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white py-2.5 px-5 rounded-lg transition duration-300 flex items-center text-base">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            下载视频
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="grid grid-cols-3 gap-6 h-full">
                        <!-- 左侧：视频预览和对比区 -->
                        <div class="col-span-2 flex flex-col space-y-6">
                            <!-- 视频预览播放器 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex flex-col flex-grow">
                                <div class="flex justify-between items-center mb-4">
                                    <h2 class="text-xl font-bold text-white">视频预览</h2>
                                    <div class="flex space-x-3">
                                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                        <button class="bg-dark-700/50 hover:bg-dark-700 text-white p-2 rounded-lg transition flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 视频播放区域 -->
                                <div class="relative rounded-lg overflow-hidden flex-grow bg-black">
                                    <img src="https://images.unsplash.com/photo-1682685797660-3d847763208e?q=80&w=1000" class="w-full h-full object-cover" alt="视频预览" />
                                    
                                    <!-- 播放控制遮罩 -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
                                        <!-- 播放按钮 -->
                                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                            <div class="h-16 w-16 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center cursor-pointer hover:bg-white/30 transition duration-300">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                        </div>
                                        
                                        <!-- 底部播放控制区 -->
                                        <div class="absolute bottom-0 left-0 right-0 p-4">
                                            <div class="flex items-center mb-2">
                                                <span class="text-white text-sm mr-2">00:03</span>
                                                <div class="h-1.5 bg-gray-700 rounded-full flex-1 mx-2">
                                                    <div class="h-full w-1/2 bg-indigo-500 rounded-full relative">
                                                        <div class="absolute -right-2 -top-1 h-4 w-4 bg-white rounded-full"></div>
                                                    </div>
                                                </div>
                                                <span class="text-white text-sm ml-2">00:06</span>
                                            </div>
                                            
                                            <div class="flex justify-between items-center">
                                                <!-- 左侧控制按钮 -->
                                                <div class="flex space-x-4">
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414-9.9m-3.535 13.435a9 9 0 010-17.07" />
                                                        </svg>
                                                    </button>
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.333 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z" />
                                                        </svg>
                                                    </button>
                                                </div>
                                                
                                                <!-- 中间播放/暂停按钮 -->
                                                <button class="text-white p-1 rounded-full bg-indigo-600/50 backdrop-blur-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                    </svg>
                                                </button>
                                                
                                                <!-- 右侧控制按钮 -->
                                                <div class="flex space-x-4">
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z" />
                                                        </svg>
                                                    </button>
                                                    <button class="text-white">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 对比区域 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <h2 class="text-xl font-bold text-white mb-4">原图与动效对比</h2>
                                <div class="grid grid-cols-2 gap-6">
                                    <!-- 原始图片 -->
                                    <div>
                                        <p class="text-gray-300 mb-2 text-sm">原始静态图片</p>
                                        <div class="relative rounded-lg overflow-hidden aspect-video bg-black/40">
                                            <img src="https://images.unsplash.com/photo-1682685797660-3d847763208e?q=80&w=500" class="w-full h-full object-cover" alt="原始图片" />
                                        </div>
                                    </div>
                                    
                                    <!-- 动效图片 -->
                                    <div>
                                        <p class="text-indigo-300 mb-2 text-sm">动效处理后</p>
                                        <div class="relative rounded-lg overflow-hidden aspect-video bg-black/40">
                                            <img src="https://images.unsplash.com/photo-1682685797660-3d847763208e?q=80&w=500" class="w-full h-full object-cover" alt="动效图片" />
                                            <!-- 动效指示图标 -->
                                            <div class="absolute bottom-2 right-2 bg-indigo-500/80 rounded-full p-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：视频参数和DeepSeek优化 -->
                        <div class="col-span-1 flex flex-col space-y-6">
                            <!-- 视频信息 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <h2 class="text-xl font-bold text-white mb-4">视频信息</h2>
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">名称</span>
                                        <span class="text-white">动态海面日落</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">创建时间</span>
                                        <span class="text-white">2024-03-10 15:20</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">分辨率</span>
                                        <span class="text-white">1920 x 1080</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">动画风格</span>
                                        <span class="text-white">自然流动</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">时长</span>
                                        <span class="text-white">6秒</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">文件大小</span>
                                        <span class="text-white">8.7 MB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- DeepSeek AI 优化 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6 flex-grow">
                                <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                                    </svg>
                                    DeepSeek AI 增强
                                </h2>
                                
                                <!-- AI助手建议 -->
                                <div class="bg-dark-900/50 rounded-lg p-4 mb-6">
                                    <div class="flex items-start">
                                        <div class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center mr-3 flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-white">我的分析显示此视频可通过以下方式增强效果：</p>
                                            <ul class="text-sm text-gray-300 mt-2 space-y-1 list-disc pl-4">
                                                <li>增加水面反光和波纹细节</li>
                                                <li>调整云层移动速度，使其更加自然</li>
                                                <li>为日落区域添加光芒动态效果</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 增强选项 -->
                                <div class="space-y-4 mb-6">
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-indigo-500 flex-shrink-0 flex items-center justify-center mt-0.5">
                                            <div class="h-2 w-2 rounded-full bg-indigo-500"></div>
                                        </div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">高清增强</h3>
                                            <p class="text-gray-400 text-sm">提升视频分辨率和细节表现</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0 mt-0.5"></div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">色彩优化</h3>
                                            <p class="text-gray-400 text-sm">增强色彩饱和度和对比度</p>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-dark-700/50 rounded-lg p-4 flex items-start space-x-4 cursor-pointer hover:bg-dark-700/70 transition duration-300">
                                        <div class="h-5 w-5 rounded-full border-2 border-gray-500 flex-shrink-0 mt-0.5"></div>
                                        <div>
                                            <h3 class="text-white font-medium mb-1">添加环境音效</h3>
                                            <p class="text-gray-400 text-sm">为视频添加海浪和风声音效</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <button class="w-full py-3 px-6 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white rounded-lg transition duration-300 text-base font-semibold flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                                    </svg>
                                    应用增强效果
                                </button>
                            </div>
                            
                            <!-- 分享选项 -->
                            <div class="bg-dark-800/40 backdrop-blur-md rounded-xl p-6">
                                <h2 class="text-xl font-bold text-white mb-4">分享</h2>
                                <div class="grid grid-cols-2 gap-3">
                                    <button class="py-2.5 bg-blue-600/20 hover:bg-blue-600/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path>
                                        </svg>
                                        Facebook
                                    </button>
                                    <button class="py-2.5 bg-blue-400/20 hover:bg-blue-400/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                        </svg>
                                        Twitter
                                    </button>
                                    <button class="py-2.5 bg-green-600/20 hover:bg-green-600/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                        </svg>
                                        WhatsApp
                                    </button>
                                    <button class="py-2.5 bg-red-600/20 hover:bg-red-600/40 text-white rounded-lg transition flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                                        </svg>
                                        Instagram
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
