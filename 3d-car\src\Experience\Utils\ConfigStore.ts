// 配置存储类，用于管理配置信息
export class ConfigStore {
  private static instance: ConfigStore;
  private configs: {
    themeUIUrl: string;
  };

  private constructor() {
    // 默认配置
    this.configs = {
      themeUIUrl: 'http://localhost:3000',
    };

    // 尝试从localStorage加载配置
    this.loadFromStorage();
  }

  // 单例模式获取实例
  public static getInstance(): ConfigStore {
    if (!ConfigStore.instance) {
      ConfigStore.instance = new ConfigStore();
    }
    return ConfigStore.instance;
  }

  // 获取Theme UI的URL
  public getThemeUIUrl(): string {
    return this.configs.themeUIUrl;
  }

  // 设置Theme UI的URL
  public setThemeUIUrl(url: string): void {
    this.configs.themeUIUrl = url;
    this.saveToStorage();
  }

  // 保存配置到localStorage
  private saveToStorage(): void {
    try {
      localStorage.setItem('3d-car-config', JSON.stringify(this.configs));
    } catch (e) {
      console.error('保存配置失败:', e);
    }
  }

  // 从localStorage加载配置
  private loadFromStorage(): void {
    try {
      const storedConfig = localStorage.getItem('3d-car-config');
      if (storedConfig) {
        this.configs = { ...this.configs, ...JSON.parse(storedConfig) };
      }
    } catch (e) {
      console.error('加载配置失败:', e);
    }
  }
}

// 导出简化使用的API
export const useConfigStore = {
  getThemeUIUrl: () => ConfigStore.getInstance().getThemeUIUrl(),
  setThemeUIUrl: (url: string) => ConfigStore.getInstance().setThemeUIUrl(url),
}; 