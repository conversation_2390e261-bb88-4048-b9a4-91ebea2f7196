<template>
  <div class="fullscreen-particle-background">
    <ParticleEffect width="100%" height="100%" :particleDensity="particleDensity" :primaryColor="primaryColor" :secondaryColor="secondaryColor" :particleColor="particleColor" :minSize="minSize" :maxSize="maxSize" :speed="speed" ref="particleEffectRef" />
  </div>
</template>

<script lang="ts">
// 提供一个常规脚本导出以便其他组件导入
import { defineComponent } from "vue";

export default defineComponent({
  name: "StarBackground",
});
</script>

<script setup lang="ts">
import { ref } from "vue";
import ParticleEffect from "./ParticleEffect.vue";
import "../styles/particle-effects.css";
import { ParticleComponentRef } from "./types";

// 定义组件属性
const props = defineProps({
  particleDensity: {
    type: Number,
    default: 120, // 全屏背景默认使用更高的粒子密度
  },
  primaryColor: {
    type: String,
    default: "rgba(99, 102, 241, 0.6)",
  },
  secondaryColor: {
    type: String,
    default: "rgba(59, 130, 246, 0.6)",
  },
  particleColor: {
    type: String,
    default: "rgba(255, 255, 255, 0.7)",
  },
  minSize: {
    type: Number,
    default: 0.2,
  },
  maxSize: {
    type: Number,
    default: 1.0,
  },
  speed: {
    type: Number,
    default: 0.3,
  },
  zIndex: {
    type: Number,
    default: 0, // 默认位于最底层
  },
});

// 引用粒子效果组件
const particleEffectRef = ref<ParticleComponentRef>(null);

// 暴露公共方法
defineExpose({
  // 重新初始化粒子
  reinitialize: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.reinitialize();
    }
  },
  // 暂停动画
  pause: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.pause();
    }
  },
  // 恢复动画
  resume: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.resume();
    }
  },
});
</script>

<style scoped>
.fullscreen-particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: v-bind("props.zIndex");
  pointer-events: none;
  overflow: hidden;
}
</style> 