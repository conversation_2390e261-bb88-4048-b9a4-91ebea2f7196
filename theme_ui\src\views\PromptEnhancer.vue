<template>
  <div class="tech-bg min-h-screen p-6 flex flex-col items-center justify-center text-white">
    <!-- 提示词增强助手主容器 -->
    <div class="container max-w-4xl mx-auto">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">提示词增强助手</h1>
      </div>

      <!-- 聊天界面 -->
      <div class="chat-glass-card overflow-hidden p-5 w-full min-h-[600px] flex flex-col">
        <!-- 聊天容器 -->
        <div
          ref="chatContainer"
          class="chat-container custom-scrollbar flex-1 overflow-y-auto p-4 flex flex-col space-y-3"
        >
          <!-- 问候消息 -->
          <div class="ai-message">
            <div class="ai-avatar">
              <span class="text-blue-300 text-sm">AI</span>
            </div>
            <div class="message-bubble glass-bubble">
              <p>你好，我是提示词增强助手。请告诉我你的想法，我会帮你优化提示词。</p>
            </div>
          </div>

          <!-- 消息历史记录 -->
          <div v-for="(message, index) in messages" :key="index" :class="message.type === 'user' ? 'user-message' : 'ai-message'">
            <template v-if="message.type === 'user'">
              <div class="message-bubble user-bubble">
                <p>{{ message.content }}</p>
              </div>
              <div class="user-avatar">
                <span class="text-indigo-300 text-sm">你</span>
              </div>
            </template>
            <template v-else>
              <div class="ai-avatar">
                <span class="text-blue-300 text-sm">AI</span>
              </div>
              <div class="message-bubble glass-bubble">
                <p>{{ message.content }}</p>
              </div>
            </template>
          </div>

          <!-- 思考过程 -->
          <div v-if="isProcessing && thinkingSteps.length > 0" class="ai-message">
            <div class="ai-avatar">
              <span class="text-blue-300 text-sm">AI</span>
            </div>
            <div class="thinking-container dark-bubble">
              <!-- 思考标题区域 -->
              <div class="thinking-header rounded-t-lg p-2 px-4 bg-gray-800 border-b border-gray-700 flex items-center justify-between">
                <div class="flex items-center">
                  <div class="thinking-icon-container animate-spin mr-2">
                    <svg class="thinking-svg" viewBox="0 0 24 24" width="16" height="16">
                      <circle class="thinking-circle" cx="12" cy="12" r="10" stroke="#0ea5e9" stroke-width="2" fill="none" />
                      <path class="thinking-path" d="M12 6v6l4 2" stroke="#0ea5e9" stroke-width="2" fill="none" stroke-linecap="round" />
                    </svg>
                  </div>
                  <div class="thinking-title text-sm font-medium text-blue-300">深度思考中</div>
                </div>
                <div class="chevron-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </div>
              </div>
              
              <!-- 思考步骤内容 -->
              <div class="thinking-content p-3 bg-gray-900/80 rounded-b-lg border border-gray-700 border-t-0">
                <div v-for="(step, index) in thinkingSteps" :key="index" class="thinking-step py-1 text-sm">
                  <span class="step-bullet text-blue-400 mr-1">•</span>
                  <div class="step-text text-gray-300" :class="{'step-active text-blue-200': !step.completed}">
                    {{ step.text }}
                    <span v-if="!step.completed" class="pulsing-dots text-blue-300"><span>.</span><span>.</span><span>.</span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 结果显示区域 -->
          <div v-if="enhancedResult" class="enhanced-result-container mt-4 p-4 rounded-md border border-indigo-300 bg-indigo-900/30">
            <h3 class="text-xl font-semibold mb-2 text-indigo-200">增强后的提示词:</h3>
            <div ref="resultContainer" class="whitespace-pre-wrap text-white">{{ enhancedResult }}</div>
            <div class="flex gap-2 mt-3 justify-end">
              <button @click="copyResult" class="btn-primary px-3 py-1 text-sm">
                <span class="mr-1">📋</span> 复制结果
              </button>
              <button @click="generateImage" class="btn-secondary px-3 py-1 text-sm">
                <span class="mr-1">📹</span> 生成壁纸
              </button>
              <button @click="generateVideo" class="btn-secondary px-3 py-1 text-sm">
                <span class="mr-1">📹</span> 生成视频
              </button>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="mt-5 flex flex-col gap-3">
          <!-- 示例提示词标签 -->
          <div class="flex flex-col gap-2">
            <span class="text-gray-400 text-sm">试一试：</span>
            <div class="flex gap-2">
              <button 
                v-for="(example, index) in examplePrompts" 
                :key="index"
                @click="useExamplePrompt(example)"
                class="px-3 py-1.5 text-sm bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 rounded-lg border border-gray-700 transition-all duration-200"
              >
                {{ example }}
              </button>
            </div>
          </div>
          
          <div class="flex gap-2">
            <input
              v-model="prompt"
              @keyup.enter="enhancePrompt"
              :disabled="isProcessing"
              type="text"
              placeholder="输入你的提示词..."
              class="flex-1 bg-gray-800 bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-white placeholder-gray-400"
            />
            <button
              @click="enhancePrompt"
              :disabled="isProcessing || !prompt.trim()"
              class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium shadow-glow-purple disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {{ isProcessing ? '处理中...' : '增强' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div
      v-if="notification.show"
      :class="{
        'fixed bottom-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300': true,
        'bg-green-600': notification.type === 'success',
        'bg-red-600': notification.type === 'error'
      }"
    >
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch, nextTick } from "vue";
import { useRouter } from "vue-router";

// 组件状态
interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
}

const prompt = ref("");
const enhancedResult = ref("");
const isProcessing = ref(false);
const sessionId = ref("");
const resultContainer = ref<HTMLElement | null>(null);
const chatContainer = ref<HTMLElement | null>(null);
const router = useRouter();

// WebSocket连接
let mcpWebSocket: WebSocket | null = null;
const isConnected = ref(false);

// 思考步骤
interface ThinkingStep {
  text: string;
  completed: boolean;
}
const thinkingSteps = ref<ThinkingStep[]>([]);

// 通知状态
const notification = ref({
  show: false,
  message: "",
  type: "success" as "success" | "error",
  timeout: null as ReturnType<typeof setTimeout> | null,
});

// 聊天历史记录
const messages = ref<ChatMessage[]>([]);

// 示例提示词
const examplePrompts = ref([
  "一只可爱的猫咪在花园里玩耍",
  "未来科技城市的夜景",
  "梦幻般的海底世界"
]);

// 使用示例提示词
function useExamplePrompt(promptText: string): void {
  prompt.value = promptText;
  enhancePrompt();
}

// 修改API和WebSocket配置
// const apiBaseUrl = "http://localhost:8000"; // theme_backend服务地址
// const wsBaseUrl = "ws://localhost:8000"; // 同一个服务的WebSocket地址
const apiBaseUrl = "http://***********:9077"; // theme_backend服务地址
const wsBaseUrl = "ws://***********:9077"; // 同一个服务的WebSocket地址

// 初始化WebSocket连接
function initWebSocket(): void {
  // 关闭现有连接
  closeWebSocket();

  // 确保会话ID
  if (!sessionId.value) {
    showNotification("无法建立WebSocket连接：缺少会话ID", "error");
    return;
  }

  try {
    // 创建WebSocket URL - 直接连接到theme_backend的WebSocket接口
    const wsUrl = `${wsBaseUrl}/api/ws/enhance/${sessionId.value}`;

    // 创建WebSocket连接
    mcpWebSocket = new WebSocket(wsUrl);

    // 连接打开时
    mcpWebSocket.onopen = () => {
      isConnected.value = true;
    };

    // 接收消息时
    mcpWebSocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data as string);
        handleWebSocketMessage(data);
      } catch (error) {
        showNotification("处理WebSocket消息时出错", "error");
      }
    };

    // 连接关闭时
    mcpWebSocket.onclose = (event) => {
      isConnected.value = false;

      // 如果不是正常关闭，尝试重连
      if (event.code !== 1000 && event.code !== 1001) {
        setTimeout(() => {
          initWebSocket();
        }, 3000);
      }
    };

    // 连接错误时
    mcpWebSocket.onerror = () => {
      showNotification("WebSocket连接错误", "error");
      isConnected.value = false;
    };
  } catch (error: any) {
    showNotification("创建WebSocket连接失败: " + error.message, "error");
    isConnected.value = false;
  }
}

// 关闭WebSocket连接
function closeWebSocket(): void {
  if (mcpWebSocket) {
    try {
      mcpWebSocket.close();
    } catch (error) {
      console.error("关闭WebSocket连接时出错：", error);
    }
    mcpWebSocket = null;
    isConnected.value = false;
  }
}

// 处理WebSocket消息
interface WebSocketMessage {
  event_type: string;
  data: {
    content: string;
    is_final?: boolean;
    message?: string;
  };
}

function handleWebSocketMessage(data: WebSocketMessage): void {
  const eventType = data.event_type;

  if (!eventType) {
    console.error("收到未知消息类型：", data);
    return;
  }

  // 处理不同类型的消息
  switch (eventType) {
    case "thinking":
      // 思考过程
      addThinking(data.data.content);
      break;

    case "content":
      // 内容更新
      if (data.data.is_final) {
        setFinalResult(data.data.content);
      } else {
        appendContent(data.data.content);
      }
      break;

    case "result":
      // 最终结果处理
      console.log("接收到最终结果", data.data.content);
      isProcessing.value = false; // 处理完成
      completeAllThinking(); // 完成所有思考步骤
      
      // 如果收到的是"提示词增强过程已完成"消息，不显示到界面上
      if (data.data.content !== "提示词增强过程已完成") {
        setFinalResult(data.data.content);
      }
      break;

    // 添加一个默认的思考消息处理，兼容可能的后端消息格式
    case "think":
      addThinking(data.data.content);
      break;

    case "error":
      // 错误消息
      showNotification(data.data.message || "处理过程中出错", "error");
      isProcessing.value = false;
      break;

    default:
      // 如果包含内容，尝试作为思考步骤或内容处理
      if (data.data && data.data.content) {
        if (data.data.is_final) {
          setFinalResult(data.data.content);
        } else {
          addThinking(data.data.content);
        }
      }
  }
}

// 增强提示词函数
async function enhancePrompt(): Promise<void> {
  if (!prompt.value.trim() || isProcessing.value) return;

  try {
    // 保存用户输入的提示词
    const userPrompt = prompt.value.trim();
    
    // 将用户输入添加到消息历史
    messages.value.push({
      type: 'user',
      content: userPrompt
    });
    
    // 清空输入框
    prompt.value = "";
    
    // 重置状态
    isProcessing.value = true;
    enhancedResult.value = "";
    thinkingSteps.value = [{ text: "开始思考...", completed: false }];

    // 关闭之前的WebSocket连接
    closeWebSocket();

    // 发送提示词到API服务
    const response = await fetch(`${apiBaseUrl}/api/enhance/prompt`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt: userPrompt,
      }),
    });

    if (!response.ok) {
      let errorText = await response.text();
      throw new Error(`API请求失败：${response.status} - ${errorText}`);
    }

    // 解析响应获取session_id
    const data = await response.json();

    if (!data.session_id) {
      throw new Error("响应中缺少session_id");
    }

    sessionId.value = data.session_id;

    // 使用session_id连接到WebSocket
    initWebSocket();

    // 自动滚动到用户的提问
    nextTick(() => {
      scrollToBottom();
    });
  } catch (error: any) {
    showNotification("网络请求失败: " + error.message, "error");
    isProcessing.value = false;
  }
}

// 复制结果
function copyResult(): void {
  if (!enhancedResult.value) return;

  navigator.clipboard
    .writeText(enhancedResult.value)
    .then(() => {
      showNotification("已复制到剪贴板", "success");
    })
    .catch(() => {
      showNotification("复制失败", "error");
    });
}

// 生成壁纸
function generateImage(): void {
  if (!enhancedResult.value) return;

  // 跳转到视频生成页面，并传递增强后的提示词
  router.push({
    name: "ImageGenerator",
    query: { prompt: enhancedResult.value },
  });
}

// 生成视频
function generateVideo(): void {
  if (!enhancedResult.value) return;

  // 跳转到视频生成页面，并传递增强后的提示词
  router.push({
    name: "VideoGenerator",
    query: { prompt: enhancedResult.value },
  });
}

// 显示通知
function showNotification(message: string, type: "success" | "error" = "success"): void {
  // 清除之前的定时器
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // 设置通知
  notification.value = {
    show: true,
    message,
    type,
    timeout: setTimeout(() => {
      notification.value.show = false;
    }, 3000),
  };
}

// 添加思考步骤
function addThinking(content: string): void {
  // 过滤掉WebSocket连接相关的调试信息
  if (content.includes('WebSocket连接') || content.includes('会话状态') || content.includes('会话ID')) {
    return;
  }
  
  // 添加新思考步骤
  thinkingSteps.value.push({
    text: content || "思考中...",
    completed: false,
  });

  // 将上一步标记为已完成
  if (thinkingSteps.value.length > 1) {
    thinkingSteps.value[thinkingSteps.value.length - 2].completed = true;
  }

  // 自动滚动到最新步骤
  nextTick(() => {
    scrollToBottom();
  });
}

// 完成所有思考步骤
function completeAllThinking(): void {
  thinkingSteps.value.forEach((step) => {
    step.completed = true;
  });
}

// 设置最终结果
function setFinalResult(content: string): void {
  // 确保结果不为空
  if (content && content.trim()) {
    enhancedResult.value = content.trim();
    
    // 将增强后的提示词添加到消息历史中，作为AI的回复
    messages.value.push({
      type: 'ai',
      content: content.trim()
    });
  } else {
    // 如果返回结果为空，设置一个默认值
    const defaultMessage = "提示词增强过程已完成，但未返回增强结果。";
    enhancedResult.value = defaultMessage;
    
    // 将默认消息添加到历史中
    messages.value.push({
      type: 'ai',
      content: defaultMessage
    });
  }
  
  // 将处理状态设置为完成
  isProcessing.value = false;

  // 将最后一个思考步骤标记为已完成
  if (thinkingSteps.value.length > 0) {
    thinkingSteps.value[thinkingSteps.value.length - 1].completed = true;
  }

  // 显示成功通知
  showNotification("提示词增强完成", "success");

  // 自动滚动到最新结果
  nextTick(() => {
    scrollToBottom();
  });
}

// 追加内容
function appendContent(content: string): void {
  if (!enhancedResult.value) {
    enhancedResult.value = content;
  } else {
    enhancedResult.value += content;
  }

  // 自动滚动到最新内容
  nextTick(() => {
    scrollToBottom();
  });
}

// 自动滚动到底部
function scrollToBottom(): void {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
}

// 监视聊天内容变化，自动滚动
watch([thinkingSteps, enhancedResult], () => {
  nextTick(() => {
    scrollToBottom();
  });
});

// 组件卸载时关闭WebSocket连接
onUnmounted(() => {
  closeWebSocket();

  // 清除通知定时器
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }
});
</script>

<style scoped>
/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 10px;
}

/* 聊天卡片 */
.chat-glass-card {
  background: rgba(17, 24, 39, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.1);
}

/* 消息气泡基础样式 */
.message-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  white-space: pre-wrap;
  line-height: 1.5;
}

/* 用户气泡 */
.user-bubble {
  background: rgba(99, 102, 241, 0.3);
  border: 1px solid rgba(99, 102, 241, 0.5);
  border-bottom-right-radius: 0;
  margin-left: auto;
}

/* 玻璃气泡 */
.glass-bubble {
  background: rgba(31, 41, 55, 0.7);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-bottom-left-radius: 0;
}

/* 深色气泡 */
.dark-bubble {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* 用户消息布局 */
.user-message {
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-start;
  margin-bottom: 16px;
}

/* AI消息布局 */
.ai-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

/* 头像 */
.user-avatar, .ai-avatar {
  min-width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(99, 102, 241, 0.3);
  float: right; /* 添加此行 */
}

/* 思考过程样式 */
.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.thinking-step {
  display: flex;
  align-items: flex-start;
  padding: 4px 0;
}

.step-bullet {
  margin-right: 8px;
  color: #60a5fa;
}

.step-active {
  color: #93c5fd;
  font-weight: 500;
}

/* 动画效果 */
.pulsing-dots span {
  animation: pulse 1.4s infinite;
  animation-fill-mode: both;
  display: inline-block;
  padding: 0 1px;
}

.pulsing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.pulsing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 80%, 100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}

.shadow-glow-purple {
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

/* 技术背景 */
.tech-bg {
  background: linear-gradient(135deg, #111827 0%, #1e293b 100%);
  background-size: 100% 100%;
  position: relative;
}

.tech-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.1;
}

/* 按钮样式 */
.btn-primary {
  background: rgba(79, 70, 229, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.5);
  border-radius: 6px;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: rgba(99, 102, 241, 0.9);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

.btn-secondary {
  background: rgba(55, 65, 81, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 6px;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: rgba(75, 85, 99, 0.9);
  box-shadow: 0 0 10px rgba(75, 85, 99, 0.5);
}

/* 思考区域动画 */
@keyframes thinking {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: thinking 2s linear infinite;
}

.enhanced-result-container {
  width: 100%;
  background: rgba(49, 46, 129, 0.2);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.1);
}
</style>