{"11": {"inputs": {"image": "arranged_icons_long (2).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "76": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["11", 0], "mask": ["82", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "遮罩裁剪"}}, "82": {"inputs": {"file_path": "/ssd2/changan/theme/main_line/source_mask/list", "white_bg": "disable", "sort_by": "file_name", "index_variable": ["95", 1], "watcher": "disable", "result": "", "prompt": ""}, "class_type": "LoadImagesFromPath", "_meta": {"title": "加载图像(路径)"}}, "95": {"inputs": {"total": 35}, "class_type": "easy forLoopStart", "_meta": {"title": "For Loop Start"}}, "96": {"inputs": {"flow": ["95", 0], "initial_value1": ["97", 0]}, "class_type": "easy forLoopEnd", "_meta": {"title": "For Loop End"}}, "97": {"inputs": {"any_1": ["95", 2], "any_2": ["76", 0]}, "class_type": "easy batchAnything", "_meta": {"title": "Batch Any"}}, "100": {"inputs": {"filename_prefix": "changan/mainLine_11/styleIcon", "images": ["96", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}