"""
进程管理模块，用于追踪和终止与特定任务相关的系统进程
"""

import os
import signal
import asyncio
import psutil
import logging
from typing import List, Dict, Set

logger = logging.getLogger(__name__)

# 任务进程映射表 - 记录每个任务ID关联的进程PID
# 格式: {task_id: set(pid1, pid2, ...)}
task_processes: Dict[str, Set[int]] = {}

def register_process(task_id: str, pid: int) -> None:
    """
    注册进程与任务的关联关系
    
    Args:
        task_id: 任务ID
        pid: 进程ID
    """
    if task_id not in task_processes:
        task_processes[task_id] = set()
    
    task_processes[task_id].add(pid)
    logger.info(f"已注册进程 PID={pid} 到任务 {task_id}")

def register_current_process(task_id: str) -> int:
    """
    注册当前进程到指定任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        int: 当前进程的PID
    """
    pid = os.getpid()
    register_process(task_id, pid)
    return pid

async def terminate_task_processes(task_id: str) -> int:
    """
    终止与指定任务相关的所有进程
    
    Args:
        task_id: 任务ID
        
    Returns:
        int: 成功终止的进程数量
    """
    if task_id not in task_processes:
        logger.warning(f"未找到任务 {task_id} 的关联进程")
        return 0
    
    terminated_count = 0
    pids_to_remove = set()
    
    for pid in task_processes[task_id]:
        try:
            # 检查进程是否存在
            if not psutil.pid_exists(pid):
                logger.info(f"进程 PID={pid} 已不存在")
                pids_to_remove.add(pid)
                continue
            
            # 获取进程对象
            process = psutil.Process(pid)
            
            # 尝试获取子进程，以便一并终止
            try:
                children = process.children(recursive=True)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                children = []
            
            # 首先终止子进程
            for child in children:
                try:
                    child.terminate()  # 发送SIGTERM
                    logger.info(f"已向子进程 PID={child.pid} 发送SIGTERM信号")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.warning(f"终止子进程 PID={child.pid} 失败: {str(e)}")
            
            # 等待子进程终止
            _, alive = psutil.wait_procs(children, timeout=3)
            
            # 对仍然存活的子进程发送SIGKILL
            for child in alive:
                try:
                    child.kill()  # 发送SIGKILL
                    logger.info(f"已向子进程 PID={child.pid} 发送SIGKILL信号")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.warning(f"强制终止子进程 PID={child.pid} 失败: {str(e)}")
            
            # 终止主进程
            process.terminate()  # 发送SIGTERM
            logger.info(f"已向进程 PID={pid} 发送SIGTERM信号")
            
            # 等待主进程终止
            try:
                process.wait(timeout=3)
                logger.info(f"进程 PID={pid} 已终止")
                terminated_count += 1
            except psutil.TimeoutExpired:
                # 如果进程未在超时时间内终止，则发送SIGKILL
                try:
                    process.kill()  # 发送SIGKILL
                    logger.info(f"已向进程 PID={pid} 发送SIGKILL信号")
                    process.wait(timeout=1)  # 再次等待
                    terminated_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired) as e:
                    logger.error(f"强制终止进程 PID={pid} 失败: {str(e)}")
            
            pids_to_remove.add(pid)
            
        except psutil.NoSuchProcess:
            logger.info(f"进程 PID={pid} 不存在")
            pids_to_remove.add(pid)
        except psutil.AccessDenied:
            logger.warning(f"无权限终止进程 PID={pid}")
        except Exception as e:
            logger.error(f"终止进程 PID={pid} 时发生错误: {str(e)}")
    
    # 清理已处理的PID
    task_processes[task_id] -= pids_to_remove
    
    # 如果所有进程都已终止，则移除任务记录
    if not task_processes[task_id]:
        del task_processes[task_id]
    
    return terminated_count

def get_task_processes(task_id: str) -> List[int]:
    """
    获取与任务关联的所有进程ID
    
    Args:
        task_id: 任务ID
        
    Returns:
        List[int]: 关联的进程ID列表
    """
    if task_id not in task_processes:
        return []
    
    return list(task_processes[task_id])

async def cleanup_all_processes():
    """清理所有注册的进程，通常在应用关闭时调用"""
    logger.info("开始清理所有注册的进程...")
    
    total_terminated = 0
    for task_id in list(task_processes.keys()):
        terminated = await terminate_task_processes(task_id)
        total_terminated += terminated
    
    logger.info(f"进程清理完成，共终止 {total_terminated} 个进程")
    return total_terminated 