# AI HMI 场景布局生成器测试流程说明

## 📋 概述

我已经为您创建了一个全新的 n8n 工作流程，用于基于场景描述生成 AI HMI 布局。该流程接收用户的场景描述，通过注入的知识库分析场景特点，自动选择合适的风格和组件，调用 GLM-4.1V-9B-Thinking 模型生成完整的 HMI 界面布局。

## 🔧 工作流程详情

**工作流程 ID:** `hDRbwIumsQ52bUAs`  
**工作流程名称:** AI HMI 场景布局生成器  
**Webhook 路径:** `/webhook/ai-hmi-scene-layout`  
**HTTP 方法:** POST

### 节点说明

1. **接收场景描述** (Webhook 触发器)
   - 路径: `/webhook/ai-hmi-scene-layout`
   - 方法: POST
   - 接收参数: `scene_description` (场景描述文本)

2. **构建完整提示词** (Code 节点)
   - 注入所有知识库内容:
     - 场景库 (Scene_Element_KB.json)
     - 风格库 (Style_KB.json)
     - 组件库 (Component_KB.json)
     - 布局库 (Layout_KB.json)
   - 根据用户场景描述构建完整的提示词
   - 包含场景分析、布局生成和HTML渲染的完整任务

3. **调用 GLM 模型** (HTTP Request 节点)
   - 目标: `http://************:8194/v1/chat/completions`
   - 模型: GLM-4.1V-9B-Thinking
   - 最大令牌数: 32768
   - 温度: 0.7

4. **解析响应** (Code 节点)
   - 提取场景分析结果
   - 提取布局设计说明
   - 提取生成的布局JSON
   - 提取完整的HTML代码

5. **返回结果** (Respond to Webhook 节点)
   - 返回结构化的分析和生成结果

## 📝 输入格式

### 请求体 (JSON)

```json
{
  "scene_description": "早高峰通勤，需要送孩子上学然后去公司"
}
```

### 场景描述示例

- `"早高峰通勤，需要送孩子上学然后去公司"`
- `"雨夜深夜下班回家，感觉疲惫需要放松"`
- `"在充电站等待充电，想看看视频或新闻"`
- `"周末和家人一起去公园游玩"`
- `"长途高速驾驶，需要保持专注和警惕"`

## 📤 输出格式

### 成功响应

```json
{
  "success": true,
  "userSceneDescription": "早高峰通勤，需要送孩子上学然后去公司",
  "sceneAnalysis": "场景分析文本...",
  "layoutDesign": "布局设计说明...",
  "layoutJSON": {
    "scene_id": "natural_commute",
    "style_id": "natural",
    "container": {...},
    "components": [...]
  },
  "htmlCode": "<!DOCTYPE html>...",
  "fullResponse": "完整的LLM响应...",
  "timestamp": "2025-07-29T03:14:10.481Z",
  "usage": {
    "prompt_tokens": 1234,
    "completion_tokens": 5678,
    "total_tokens": 6912
  }
}
```

## 🧪 测试方法

### 方法1: 使用测试页面

1. 打开 `test-ai-hmi-workflow.html`
2. 输入 Webhook URL (例如: `http://your-n8n-instance.com/webhook/ai-hmi-scene-layout`)
3. 输入或选择场景描述
4. 点击"🚀 生成场景布局"
5. 查看生成结果

### 方法2: 使用 curl 命令

```bash
curl -X POST "http://your-n8n-instance.com/webhook/ai-hmi-scene-layout" \
  -H "Content-Type: application/json" \
  -d '{
    "scene_description": "早高峰通勤，需要送孩子上学然后去公司"
  }'
```

### 方法3: 使用 Postman

1. 创建新的 POST 请求
2. URL: `http://your-n8n-instance.com/webhook/ai-hmi-scene-layout`
3. Headers: `Content-Type: application/json`
4. Body (raw JSON):
   ```json
   {
     "scene_description": "早高峰通勤，需要送孩子上学然后去公司"
   }
   ```

## 🔧 配置要求

### n8n 配置

1. **激活工作流**: 确保工作流已激活
2. **API 密钥**: 在 HTTP Request 节点中配置正确的 API 密钥
3. **网络访问**: 确保 n8n 可以访问 GLM API 端点

### GLM API 配置

- **端点**: `http://************:8194/v1/chat/completions`
- **模型**: GLM-4.1V-9B-Thinking
- **认证**: Bearer Token (需要配置)

## 🎯 核心特性

### 1. 智能场景分析
- 基于用户描述自动识别场景类型
- 从知识库中匹配最合适的场景ID
- 分析用户需求和使用环境

### 2. 自动风格选择
- 根据场景特点选择合适的视觉风格
- 支持自然、赛博朋克、玻璃拟物、新拟物、可爱卡通等风格

### 3. 智能组件选择
- 根据场景需求自动选择合适的UI组件
- 包含VPA交互、导航、音乐、天气等功能组件

### 4. 布局自动生成
- 基于场景和组件自动选择合适的布局容器
- 生成完整的布局JSON结构

### 5. HTML渲染
- 将布局JSON渲染为完整的HTML代码
- 包含CSS样式和交互效果

## 🚨 注意事项

1. **工作流激活**: 确保工作流已激活才能接收 Webhook 请求
2. **API 密钥**: 需要配置有效的 GLM API 密钥
3. **网络连接**: 确保 n8n 实例可以访问外部 API
4. **响应时间**: AI 生成可能需要 10-30 秒，请耐心等待
5. **错误处理**: 如果生成失败，检查 API 密钥和网络连接

## 📚 相关文件

- `test-ai-hmi-workflow.html` - 测试页面
- `aiHmi/docs/提示词/提示词.md` - 主提示词模板
- `aiHmi/docs/提示词/Scene_Element_KB.json` - 场景知识库
- `aiHmi/docs/提示词/Style_KB.json` - 风格知识库
- `aiHmi/docs/提示词/Component_KB.json` - 组件知识库
- `aiHmi/docs/提示词/Layout_KB.json` - 布局知识库
- `aiHmi/docs/场景.md` - 详细场景描述参考

## 🔄 更新日志

- **2025-07-29**: 创建基于场景描述的新工作流程
- 集成所有知识库内容
- 支持完整的场景分析到HTML生成流程
- 更新测试页面和文档
