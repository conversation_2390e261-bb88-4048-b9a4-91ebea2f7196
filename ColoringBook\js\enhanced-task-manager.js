/**
 * 增强的任务管理器 - 实现多任务并发处理优化
 * 
 * 功能特性：
 * 1. 修复并发任务创建和状态跟踪逻辑
 * 2. 实现任务队列管理，防止重复提交
 * 3. 添加任务进度的独立显示和更新
 * 4. 优化轮询策略，根据任务状态调整频率
 */

class EnhancedTaskManager {
    constructor(options = {}) {
        // 基础配置
        this.maxConcurrentTasks = options.maxConcurrentTasks || 5;
        this.maxRetries = options.maxRetries || 3;
        this.basePollingInterval = options.basePollingInterval || 2000;
        this.maxPollingAttempts = options.maxPollingAttempts || 60;

        // 任务状态管理
        this.activeTasks = new Map(); // 活跃任务映射
        this.taskQueue = new Set(); // 任务队列，防止重复提交
        this.completedTasks = new Map(); // 已完成任务缓存
        this.taskHistory = []; // 任务历史记录

        // 全局状态
        this.isGenerating = false;
        this.totalTasksStarted = 0;
        this.totalTasksCompleted = 0;
        this.totalTasksFailed = 0;

        // 性能监控
        this.performanceMetrics = {
            averageTaskDuration: 0,
            averagePollingInterval: 0,
            successRate: 0,
            lastUpdateTime: Date.now()
        };

        // 事件回调
        this.callbacks = {
            onTaskStart: null,
            onTaskProgress: null,
            onTaskComplete: null,
            onTaskFail: null,
            onAllTasksComplete: null
        };
    }

    /**
     * 设置事件回调函数
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (this.callbacks.hasOwnProperty(`on${event.charAt(0).toUpperCase() + event.slice(1)}`)) {
            this.callbacks[`on${event.charAt(0).toUpperCase() + event.slice(1)}`] = callback;
        }
    }

    /**
     * 生成任务唯一标识符（增强版）
     * @param {string} prompt - 提示词
     * @param {number} index - 任务索引
     * @param {Object} options - 额外选项
     * @returns {string} - 任务标识符
     */
    generateTaskKey(prompt, index, options = {}) {
        const promptHash = this.hashString(prompt);
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        return `task_${promptHash}_${index}_${timestamp}_${randomSuffix}`;
    }

    /**
     * 简单字符串哈希函数
     * @param {string} str - 输入字符串
     * @returns {string} - 哈希值
     */
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * 检查是否为重复任务（增强版）
     * @param {string} prompt - 提示词
     * @param {Object} options - 任务选项
     * @returns {boolean} - 是否重复
     */
    isDuplicateTask(prompt, options = {}) {
        // 检查队列中是否有相同的提示词
        for (const taskKey of this.taskQueue) {
            if (taskKey.includes(this.hashString(prompt))) {
                return true;
            }
        }

        // 检查活跃任务中是否有相同的提示词
        for (const [taskId, taskInfo] of this.activeTasks) {
            if (taskInfo.prompt === prompt && taskInfo.status === 'running') {
                return true;
            }
        }

        return false;
    }

    /**
     * 添加任务到队列（增强版）
     * @param {string} taskKey - 任务标识符
     * @param {Object} taskInfo - 任务信息
     */
    addToQueue(taskKey, taskInfo) {
        this.taskQueue.add(taskKey);
        console.log(`[TaskManager] 任务 ${taskKey.substring(taskKey.length - 8)} 添加到队列，当前队列大小: ${this.taskQueue.size}`);

        // 触发任务开始回调
        if (this.callbacks.onTaskStart) {
            this.callbacks.onTaskStart(taskKey, taskInfo);
        }
    }

    /**
     * 从队列中移除任务
     * @param {string} taskKey - 任务标识符
     */
    removeFromQueue(taskKey) {
        this.taskQueue.delete(taskKey);
        console.log(`[TaskManager] 任务 ${taskKey.substring(taskKey.length - 8)} 从队列移除，当前队列大小: ${this.taskQueue.size}`);
    }

    /**
     * 启动单个任务（增强版）
     * @param {Object} task - 任务对象
     * @param {number} index - 任务索引
     * @param {Object} options - 启动选项
     * @returns {Promise<Object|null>} - 任务结果或null
     */
    async startSingleTask(task, index, options = {}) {
        const taskKey = this.generateTaskKey(task.prompt, index, options);

        // 检查重复提交
        if (this.isDuplicateTask(task.prompt, options)) {
            console.warn(`[TaskManager] 任务 ${index + 1} 重复提交，跳过`);
            return null;
        }

        const taskInfo = {
            prompt: task.prompt,
            index: index,
            taskKey: taskKey,
            status: 'queued',
            progress: 0,
            startTime: Date.now(),
            retryCount: 0,
            options: options
        };

        this.addToQueue(taskKey, taskInfo);

        try {
            // 使用指数退避重试机制
            const result = await this.retryWithBackoff(async () => {
                const requestBody = {
                    prompt: task.prompt,
                    count: options.count || 1,
                    ratio: options.ratio || '3:4'
                };
                
                const response = await fetch('http://127.0.0.1:8100/api/v1/coloring-book/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody),
                    signal: AbortSignal.timeout(120000) // 120秒超时
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    if (response.status >= 500) {
                        throw new Error(`HTTP ${response.status}: 服务器内部错误 - ${errorText}`);
                    } else if (response.status === 429) {
                        throw new Error('请求过于频繁，请稍后再试');
                    } else if (response.status === 400) {
                        throw new Error(`请求参数错误: ${errorText}`);
                    } else {
                        throw new Error(`HTTP ${response.status}: 请求失败 - ${errorText}`);
                    }
                }

                return await response.json();
            }, this.maxRetries, 1000);

            // 更新任务信息并存储到活跃任务映射中
            taskInfo.taskId = result.task_id;
            taskInfo.status = result.image_url ? 'completed' : 'running';
            taskInfo.startTime = Date.now();

            // 如果直接返回了图片URL列表，说明任务已完成
            if (result.image_urls && result.image_urls.length > 0) {
                taskInfo.completedTime = Date.now();
                taskInfo.duration = taskInfo.completedTime - taskInfo.startTime;
                this.totalTasksCompleted++;
                this.completedTasks.set(result.task_id, taskInfo);

                console.log(`[TaskManager] 任务 ${index + 1} 直接完成，ID: ${result.task_id}，生成了 ${result.image_urls.length} 张图片`);

                // 触发任务完成回调
                if (this.callbacks.onTaskComplete) {
                    this.callbacks.onTaskComplete(result.task_id, result);
                }
            } else {
                this.activeTasks.set(result.task_id, taskInfo);
                this.totalTasksStarted++;
                console.log(`[TaskManager] 任务 ${index + 1} 启动成功，ID: ${result.task_id}`);
            }

            return result;

        } catch (error) {
            console.error(`[TaskManager] 任务 ${index + 1} 启动失败:`, error);
            taskInfo.status = 'failed';
            taskInfo.error = error.message;
            this.totalTasksFailed++;

            // 触发任务失败回调
            if (this.callbacks.onTaskFail) {
                this.callbacks.onTaskFail(taskKey, error);
            }

            return null;
        } finally {
            this.removeFromQueue(taskKey);
        }
    }

    /**
     * 批量启动任务（增强版）
     * @param {Array} tasks - 任务数组
     * @param {Object} options - 启动选项
     * @returns {Promise<Array>} - 启动成功的任务数组
     */
    async startTasks(tasks, options = {}) {
        const batchSize = Math.min(this.maxConcurrentTasks, tasks.length);
        const runningTasks = [];
        const startTime = Date.now();

        console.log(`[TaskManager] 开始批量启动 ${tasks.length} 个任务，批次大小: ${batchSize}`);

        // 分批启动任务，避免同时启动过多任务
        for (let i = 0; i < tasks.length; i += batchSize) {
            const batch = tasks.slice(i, i + batchSize);
            const batchIndex = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(tasks.length / batchSize);

            console.log(`[TaskManager] 启动批次 ${batchIndex}/${totalBatches}，包含 ${batch.length} 个任务`);

            const batchPromises = batch.map((task, batchTaskIndex) =>
                this.startSingleTask(task, i + batchTaskIndex, options)
            );

            const batchResults = await Promise.allSettled(batchPromises);

            // 处理批次结果
            batchResults.forEach((result, batchTaskIndex) => {
                if (result.status === 'fulfilled' && result.value !== null) {
                    runningTasks.push(result.value);
                } else if (result.status === 'rejected') {
                    console.error(`[TaskManager] 批次任务 ${i + batchTaskIndex + 1} 失败:`, result.reason);
                }
            });

            // 如果还有更多批次，适当延迟以避免服务器过载
            if (i + batchSize < tasks.length) {
                const delay = this.calculateBatchDelay(batchIndex, totalBatches);
                console.log(`[TaskManager] 等待 ${delay}ms 后启动下一批次...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        const totalTime = Date.now() - startTime;
        console.log(`[TaskManager] 批量启动完成，耗时 ${totalTime}ms，成功启动 ${runningTasks.length}/${tasks.length} 个任务`);

        return runningTasks;
    }

    /**
     * 计算批次间延迟时间
     * @param {number} batchIndex - 当前批次索引
     * @param {number} totalBatches - 总批次数
     * @returns {number} - 延迟时间（毫秒）
     */
    calculateBatchDelay(batchIndex, totalBatches) {
        // 基础延迟500ms，根据批次数量和服务器负载动态调整
        let delay = 500;

        // 如果批次较多，增加延迟
        if (totalBatches > 3) {
            delay += (batchIndex - 1) * 200;
        }

        // 根据当前活跃任务数量调整
        const activeTaskCount = this.activeTasks.size;
        if (activeTaskCount > 5) {
            delay += activeTaskCount * 100;
        }

        return Math.min(delay, 2000); // 最大延迟2秒
    }

    /**
     * 优化的轮询策略（增强版）
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} - 任务结果
     */
    async pollTaskWithOptimizedStrategy(taskId) {
        const taskInfo = this.activeTasks.get(taskId);
        if (!taskInfo) {
            throw new Error(`任务 ${taskId} 不存在于活跃任务列表中`);
        }

        let consecutiveErrors = 0;
        const maxConsecutiveErrors = 3;
        let lastProgress = 0;
        let stagnantCount = 0; // 进度停滞计数

        for (let pollCount = 0; pollCount < this.maxPollingAttempts; pollCount++) {
            try {
                const result = await this.retryWithBackoff(async () => {
                    const response = await fetch(`http://127.0.0.1:8100/api/v1/coloring-book/status/${taskId}`, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' },
                        signal: AbortSignal.timeout(120000) // 120秒超时
                    });

                    if (!response.ok) {
                        if (response.status >= 500) {
                            throw new Error(`HTTP ${response.status}: 服务器内部错误`);
                        } else if (response.status === 404) {
                            throw new Error(`任务 ${taskId} 不存在`);
                        } else {
                            throw new Error(`HTTP ${response.status}: 状态查询失败`);
                        }
                    }

                    return await response.json();
                }, 2, 500);

                // 重置连续错误计数
                consecutiveErrors = 0;

                // 更新任务状态和进度
                taskInfo.status = result.status;
                taskInfo.progress = result.progress || 0;
                taskInfo.lastPolledTime = Date.now();

                // 检查进度是否停滞
                if (taskInfo.progress === lastProgress) {
                    stagnantCount++;
                } else {
                    stagnantCount = 0;
                    lastProgress = taskInfo.progress;
                }

                this.activeTasks.set(taskId, taskInfo);

                // 触发进度更新回调
                if (this.callbacks.onTaskProgress) {
                    this.callbacks.onTaskProgress(taskId, taskInfo);
                }

                // 检查任务完成状态
                if (result.status === 'completed') {
                    taskInfo.completedTime = Date.now();
                    taskInfo.duration = taskInfo.completedTime - taskInfo.startTime;

                    this.totalTasksCompleted++;
                    this.completedTasks.set(taskId, taskInfo);

                    console.log(`[TaskManager] 任务 ${taskId} 完成，耗时 ${taskInfo.duration}ms`);

                    // 触发任务完成回调
                    if (this.callbacks.onTaskComplete) {
                        this.callbacks.onTaskComplete(taskId, result);
                    }

                    return result;
                } else if (result.status === 'failed') {
                    taskInfo.status = 'failed';
                    taskInfo.error = result.error || '未知错误';
                    this.totalTasksFailed++;

                    // 触发任务失败回调
                    if (this.callbacks.onTaskFail) {
                        this.callbacks.onTaskFail(taskId, new Error(taskInfo.error));
                    }

                    throw new Error(`魔法施展失败: ${taskInfo.error}`);
                }

                // 如果进度长时间停滞，调整轮询策略
                if (stagnantCount > 5) {
                    console.warn(`[TaskManager] 任务 ${taskId} 进度停滞，调整轮询策略`);
                }

                // 动态计算轮询间隔
                const interval = this.calculateDynamicPollingInterval(taskId, pollCount, stagnantCount);
                await new Promise(resolve => setTimeout(resolve, interval));

            } catch (error) {
                consecutiveErrors++;
                console.error(`[TaskManager] 轮询任务 ${taskId} 时出错 (第${consecutiveErrors}次):`, error);

                // 如果连续错误次数过多，停止轮询
                if (consecutiveErrors >= maxConsecutiveErrors) {
                    taskInfo.status = 'failed';
                    taskInfo.error = `轮询连续失败${maxConsecutiveErrors}次`;
                    this.totalTasksFailed++;

                    throw new Error(`轮询任务状态时连续失败${maxConsecutiveErrors}次，停止轮询`);
                }

                // 指数退避等待
                const backoffDelay = Math.min(2000 * Math.pow(2, consecutiveErrors - 1), 10000);
                await new Promise(resolve => setTimeout(resolve, backoffDelay));
            }
        }

        // 轮询超时
        taskInfo.status = 'timeout';
        taskInfo.error = '轮询超时';
        this.totalTasksFailed++;

        throw new Error('魔法施展超时，请稍后再试');
    }

    /**
     * 计算动态轮询间隔（增强版）
     * @param {string} taskId - 任务ID
     * @param {number} pollCount - 轮询次数
     * @param {number} stagnantCount - 进度停滞次数
     * @returns {number} - 轮询间隔（毫秒）
     */
    calculateDynamicPollingInterval(taskId, pollCount, stagnantCount = 0) {
        const taskInfo = this.activeTasks.get(taskId);
        const runningTime = taskInfo ? Date.now() - taskInfo.startTime : 0;
        const progress = taskInfo ? taskInfo.progress : 0;

        let baseInterval = this.basePollingInterval;

        // 根据运行时间调整
        if (runningTime > 300000) { // 超过5分钟
            baseInterval = 8000;
        } else if (runningTime > 120000) { // 超过2分钟
            baseInterval = 5000;
        } else if (runningTime > 60000) { // 超过1分钟
            baseInterval = 3000;
        }

        // 根据轮询次数调整（避免频繁轮询）
        if (pollCount > 30) {
            baseInterval = Math.min(baseInterval * 1.5, 10000);
        }

        // 根据进度调整
        if (progress > 80) {
            // 接近完成时，稍微增加轮询频率
            baseInterval = Math.max(baseInterval * 0.8, 1500);
        } else if (progress < 10) {
            // 刚开始时，降低轮询频率
            baseInterval = Math.min(baseInterval * 1.2, 5000);
        }

        // 根据进度停滞情况调整
        if (stagnantCount > 3) {
            baseInterval = Math.min(baseInterval * (1 + stagnantCount * 0.2), 15000);
        }

        // 根据系统负载调整
        const activeTaskCount = this.activeTasks.size;
        if (activeTaskCount > 10) {
            baseInterval = Math.min(baseInterval * 1.3, 12000);
        }

        return Math.max(baseInterval, 1000); // 最小间隔1秒
    }

    /**
     * 指数退避重试机制（增强版）
     * @param {Function} fn - 要重试的函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} baseDelay - 基础延迟时间（毫秒）
     * @returns {Promise} - 重试结果
     */
    async retryWithBackoff(fn, maxRetries = this.maxRetries, baseDelay = 1000) {
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;

                if (attempt === maxRetries) {
                    throw error;
                }

                // 指数退避：每次重试延迟时间翻倍，加上随机抖动
                const jitter = Math.random() * 500; // 随机抖动0-500ms
                const delay = baseDelay * Math.pow(2, attempt) + jitter;
                const maxDelay = 30000; // 最大延迟30秒
                const actualDelay = Math.min(delay, maxDelay);

                console.log(`[TaskManager] 重试第 ${attempt + 1} 次，${actualDelay.toFixed(0)}ms 后重试...`);
                await new Promise(resolve => setTimeout(resolve, actualDelay));
            }
        }

        throw lastError;
    }

    /**
     * 获取任务进度统计（增强版）
     * @returns {Object} - 详细的进度统计信息
     */
    getProgressStats() {
        const tasks = Array.from(this.activeTasks.values());
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(task => task.status === 'completed').length;
        const failedTasks = tasks.filter(task => task.status === 'failed').length;
        const runningTasks = tasks.filter(task => task.status === 'running').length;
        const queuedTasks = tasks.filter(task => task.status === 'queued').length;

        // 计算平均进度
        const totalProgress = tasks.reduce((sum, task) => sum + (task.progress || 0), 0);
        const averageProgress = totalTasks > 0 ? totalProgress / totalTasks : 0;

        // 计算成功率
        const totalProcessed = this.totalTasksCompleted + this.totalTasksFailed;
        const successRate = totalProcessed > 0 ? (this.totalTasksCompleted / totalProcessed) * 100 : 0;

        // 计算平均任务持续时间
        const completedTasksWithDuration = Array.from(this.completedTasks.values())
            .filter(task => task.duration);
        const averageDuration = completedTasksWithDuration.length > 0
            ? completedTasksWithDuration.reduce((sum, task) => sum + task.duration, 0) / completedTasksWithDuration.length
            : 0;

        return {
            total: totalTasks,
            completed: completedTasks,
            failed: failedTasks,
            running: runningTasks,
            queued: queuedTasks,
            progress: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
            averageProgress: averageProgress,
            successRate: successRate,
            averageDuration: averageDuration,
            totalStarted: this.totalTasksStarted,
            totalCompleted: this.totalTasksCompleted,
            totalFailed: this.totalTasksFailed,
            queueSize: this.taskQueue.size,
            isGenerating: this.isGenerating
        };
    }

    /**
     * 清理已完成的任务（增强版）
     * @param {boolean} keepHistory - 是否保留历史记录
     */
    cleanupCompletedTasks(keepHistory = true) {
        let cleanedCount = 0;
        const tasksToClean = [];

        for (const [taskId, taskInfo] of this.activeTasks.entries()) {
            if (taskInfo.status === 'completed' || taskInfo.status === 'failed') {
                tasksToClean.push([taskId, taskInfo]);
                cleanedCount++;
            }
        }

        // 移动到历史记录
        if (keepHistory) {
            tasksToClean.forEach(([taskId, taskInfo]) => {
                this.taskHistory.push({
                    ...taskInfo,
                    taskId: taskId,
                    cleanedAt: Date.now()
                });
            });

            // 限制历史记录大小
            if (this.taskHistory.length > 100) {
                this.taskHistory = this.taskHistory.slice(-50);
            }
        }

        // 从活跃任务中移除
        tasksToClean.forEach(([taskId]) => {
            this.activeTasks.delete(taskId);
        });

        if (cleanedCount > 0) {
            console.log(`[TaskManager] 清理了 ${cleanedCount} 个已完成的任务`);
        }

        return cleanedCount;
    }

    /**
     * 取消所有活跃任务（增强版）
     * @param {string} reason - 取消原因
     */
    cancelAllTasks(reason = '用户取消') {
        const taskCount = this.activeTasks.size;
        const queueCount = this.taskQueue.size;

        // 标记所有活跃任务为已取消
        for (const [taskId, taskInfo] of this.activeTasks.entries()) {
            taskInfo.status = 'cancelled';
            taskInfo.error = reason;
            taskInfo.cancelledAt = Date.now();
        }

        this.activeTasks.clear();
        this.taskQueue.clear();
        this.isGenerating = false;

        console.log(`[TaskManager] 取消了 ${taskCount} 个活跃任务和 ${queueCount} 个队列任务，原因: ${reason}`);

        return { cancelledTasks: taskCount, cancelledQueue: queueCount };
    }

    /**
     * 获取任务历史记录
     * @param {number} limit - 返回记录数量限制
     * @returns {Array} - 任务历史记录
     */
    getTaskHistory(limit = 20) {
        return this.taskHistory.slice(-limit);
    }

    /**
     * 获取性能指标
     * @returns {Object} - 性能指标
     */
    getPerformanceMetrics() {
        const stats = this.getProgressStats();

        return {
            ...this.performanceMetrics,
            currentStats: stats,
            lastUpdateTime: Date.now()
        };
    }

    /**
     * 重置任务管理器状态
     */
    reset() {
        this.cancelAllTasks('重置任务管理器');
        this.completedTasks.clear();
        this.taskHistory = [];
        this.totalTasksStarted = 0;
        this.totalTasksCompleted = 0;
        this.totalTasksFailed = 0;

        console.log('[TaskManager] 任务管理器已重置');
    }

    /**
     * 智能任务队列管理 - 防止重复提交和管理并发（增强版）
     * @param {Array} tasks - 要提交的任务数组
     * @returns {Promise<Array>} - 成功启动的任务数组
     */
    async startTasksWithQueueManagement(tasks, options = {}) {
        // 防止重复提交检查
        if (this.isGenerating) {
            console.warn('[TaskManager] 任务队列正在处理中，跳过重复提交');
            return [];
        }

        this.isGenerating = true;
        const startTime = Date.now();

        try {
            // 增强的重复任务检查
            const uniqueTasks = this.filterDuplicateTasks(tasks);

            if (uniqueTasks.length === 0) {
                console.warn('[TaskManager] 所有任务都是重复的，跳过提交');
                return [];
            }

            console.log(`[TaskManager] 开始处理 ${uniqueTasks.length} 个唯一任务（原始: ${tasks.length}）`);

            // 使用增强的并发管理启动任务
            const results = await this.manageConcurrentTasksEnhanced(uniqueTasks, options);

            const successCount = results.filter(r => r && !r.error).length;
            const failCount = results.filter(r => r && r.error).length;
            const totalTime = Date.now() - startTime;

            console.log(`[TaskManager] 任务处理完成: ${successCount} 成功, ${failCount} 失败, 耗时 ${totalTime}ms`);

            // 更新性能指标
            this.updatePerformanceMetrics(successCount, failCount, totalTime);

            return results.filter(r => r && !r.error);

        } catch (error) {
            console.error('[TaskManager] 任务队列处理失败:', error);
            throw error;
        } finally {
            this.isGenerating = false;
            this.cleanupCompletedTasks();
        }
    }

    /**
     * 增强的重复任务过滤
     * @param {Array} tasks - 原始任务数组
     * @returns {Array} - 过滤后的唯一任务数组
     */
    filterDuplicateTasks(tasks) {
        const uniqueTasks = [];
        const seenPrompts = new Set();
        const currentTime = Date.now();

        for (let i = 0; i < tasks.length; i++) {
            const task = tasks[i];
            const promptHash = this.hashString(task.prompt);

            // 检查是否为重复提示词
            if (seenPrompts.has(promptHash)) {
                console.warn(`[TaskManager] 任务 ${i + 1} 与之前的任务重复，跳过`);
                continue;
            }

            // 检查是否与当前活跃任务重复
            let isDuplicateActive = false;
            for (const [taskId, taskInfo] of this.activeTasks) {
                if (taskInfo.prompt === task.prompt &&
                    (taskInfo.status === 'running' || taskInfo.status === 'queued')) {
                    // 如果是最近5分钟内的任务，认为是重复
                    if (currentTime - taskInfo.startTime < 300000) {
                        isDuplicateActive = true;
                        break;
                    }
                }
            }

            if (isDuplicateActive) {
                console.warn(`[TaskManager] 任务 ${i + 1} 与活跃任务重复，跳过`);
                continue;
            }

            // 检查队列中是否有重复
            let isDuplicateQueue = false;
            for (const taskKey of this.taskQueue) {
                if (taskKey.includes(promptHash)) {
                    isDuplicateQueue = true;
                    break;
                }
            }

            if (isDuplicateQueue) {
                console.warn(`[TaskManager] 任务 ${i + 1} 在队列中重复，跳过`);
                continue;
            }

            seenPrompts.add(promptHash);
            uniqueTasks.push(task);
        }

        return uniqueTasks;
    }

    /**
     * 增强的并发任务管理 - 改进的并发控制和错误处理
     * @param {Array} tasks - 任务数组
     * @returns {Promise<Array>} - 处理结果
     */
    async manageConcurrentTasksEnhanced(tasks, options = {}) {
        const results = [];
        const runningTasks = new Map(); // 使用Map来更好地跟踪任务
        let taskIndex = 0;
        let completedCount = 0;
        const totalTasks = tasks.length;

        console.log(`[TaskManager] 开始增强并发处理 ${totalTasks} 个任务，最大并发: ${this.maxConcurrentTasks}`);

        // 处理任务队列
        while (taskIndex < tasks.length || runningTasks.size > 0) {
            // 启动新任务直到达到并发限制
            while (runningTasks.size < this.maxConcurrentTasks && taskIndex < tasks.length) {
                const task = tasks[taskIndex];
                const currentIndex = taskIndex;

                console.log(`[TaskManager] 启动任务 ${currentIndex + 1}/${totalTasks}: ${task.prompt.substring(0, 30)}...`);

                const taskPromise = this.processTaskWithPollingEnhanced(task, currentIndex, options)
                    .then(result => ({
                        success: true,
                        index: currentIndex,
                        result,
                        completedAt: Date.now()
                    }))
                    .catch(error => ({
                        success: false,
                        index: currentIndex,
                        error: error.message,
                        completedAt: Date.now()
                    }));

                runningTasks.set(currentIndex, taskPromise);
                taskIndex++;

                // 添加小延迟避免同时启动过多请求
                if (runningTasks.size >= this.maxConcurrentTasks) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // 等待至少一个任务完成
            if (runningTasks.size > 0) {
                const completedTask = await Promise.race(Array.from(runningTasks.values()));

                // 从运行列表中移除已完成的任务
                runningTasks.delete(completedTask.index);
                results.push(completedTask);
                completedCount++;

                const progressPercent = ((completedCount / totalTasks) * 100).toFixed(1);
                console.log(`[TaskManager] 任务 ${completedTask.index + 1} 完成 (${completedTask.success ? '成功' : '失败'}), 总进度: ${progressPercent}%`);

                // 如果有失败的任务，记录详细信息
                if (!completedTask.success) {
                    console.error(`[TaskManager] 任务 ${completedTask.index + 1} 失败: ${completedTask.error}`);
                }
            }
        }

        // 按索引排序结果
        results.sort((a, b) => a.index - b.index);

        const successResults = results.filter(r => r.success).map(r => r.result);
        const failedResults = results.filter(r => !r.success);

        console.log(`[TaskManager] 增强并发处理完成: ${successResults.length} 成功, ${failedResults.length} 失败`);

        return results;
    }

    /**
     * 增强的任务处理包括轮询
     * @param {Object} task - 任务对象
     * @param {number} index - 任务索引
     * @returns {Promise<Object>} - 任务结果
     */
    async processTaskWithPollingEnhanced(task, index, options = {}) {
        const startTime = Date.now();

        try {
            // 启动任务
            const startResult = await this.startSingleTask(task, index, options);
            if (!startResult) {
                throw new Error(`任务 ${index + 1} 启动失败`);
            }

            // 检查任务是否已经完成（同步模式）
            if (startResult.image_urls && startResult.image_urls.length > 0) {
                console.log(`[TaskManager] 任务 ${index + 1} 已同步完成，无需轮询`);
                return startResult;
            }

            console.log(`[TaskManager] 任务 ${index + 1} 启动成功，开始轮询状态...`);

            // 轮询任务状态直到完成
            const finalResult = await this.pollTaskWithOptimizedStrategy(startResult.task_id);

            const duration = Date.now() - startTime;
            console.log(`[TaskManager] 任务 ${index + 1} 处理完成，总耗时: ${duration}ms`);

            return finalResult;

        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`[TaskManager] 任务 ${index + 1} 处理失败，耗时: ${duration}ms，错误: ${error.message}`);
            throw error;
        }
    }

    /**
     * 更新性能指标
     * @param {number} successCount - 成功任务数
     * @param {number} failCount - 失败任务数
     * @param {number} totalTime - 总耗时
     */
    updatePerformanceMetrics(successCount, failCount, totalTime) {
        const totalTasks = successCount + failCount;
        if (totalTasks === 0) return;

        // 更新成功率
        this.performanceMetrics.successRate = (successCount / totalTasks) * 100;

        // 更新平均任务持续时间
        if (successCount > 0) {
            const avgDuration = totalTime / successCount;
            this.performanceMetrics.averageTaskDuration =
                (this.performanceMetrics.averageTaskDuration + avgDuration) / 2;
        }

        // 更新最后更新时间
        this.performanceMetrics.lastUpdateTime = Date.now();

        console.log(`[TaskManager] 性能指标更新: 成功率 ${this.performanceMetrics.successRate.toFixed(1)}%, 平均耗时 ${this.performanceMetrics.averageTaskDuration.toFixed(0)}ms`);
    }

    /**
     * 并发任务管理 - 限制同时运行的任务数量
     * @param {Array} tasks - 任务数组
     * @returns {Promise<Array>} - 处理结果
     */
    async manageConcurrentTasks(tasks) {
        const results = [];
        const runningTasks = [];
        let taskIndex = 0;

        // 处理任务队列
        while (taskIndex < tasks.length || runningTasks.length > 0) {
            // 启动新任务直到达到并发限制
            while (runningTasks.length < this.maxConcurrentTasks && taskIndex < tasks.length) {
                const task = tasks[taskIndex];
                const currentIndex = taskIndex;

                const taskPromise = this.processTaskWithPolling(task, currentIndex)
                    .then(result => ({ success: true, index: currentIndex, result }))
                    .catch(error => ({ success: false, index: currentIndex, error: error.message }));

                runningTasks.push(taskPromise);
                taskIndex++;
            }

            // 等待至少一个任务完成
            if (runningTasks.length > 0) {
                const completedTask = await Promise.race(runningTasks);

                // 从运行列表中移除已完成的任务
                const completedIndex = runningTasks.findIndex(promise => promise === completedTask);
                if (completedIndex !== -1) {
                    runningTasks.splice(completedIndex, 1);
                }

                results.push(completedTask);
            }
        }

        return results.sort((a, b) => a.index - b.index).map(r => r.success ? r.result : null);
    }

    /**
     * 处理单个任务包括轮询
     * @param {Object} task - 任务对象
     * @param {number} index - 任务索引
     * @returns {Promise<Object>} - 任务结果
     */
    async processTaskWithPolling(task, index) {
        // 启动任务
        const startResult = await this.startSingleTask(task, index);
        if (!startResult) {
            throw new Error(`任务 ${index + 1} 启动失败`);
        }

        // 轮询任务状态直到完成
        const finalResult = await this.pollTaskWithOptimizedStrategy(startResult.task_id);
        return finalResult;
    }

    /**
     * 增强的任务状态跟踪
     * @param {string} taskId - 任务ID
     * @param {Object} statusUpdate - 状态更新
     */
    updateTaskStatus(taskId, statusUpdate) {
        if (this.activeTasks.has(taskId)) {
            const taskInfo = this.activeTasks.get(taskId);
            Object.assign(taskInfo, statusUpdate);
            taskInfo.lastUpdated = Date.now();
            this.activeTasks.set(taskId, taskInfo);

            // 触发进度回调
            if (this.callbacks.onTaskProgress) {
                this.callbacks.onTaskProgress(taskId, taskInfo);
            }
        }
    }

    /**
     * 获取实时任务统计信息
     * @returns {Object} - 实时统计
     */
    getRealTimeStats() {
        const stats = this.getProgressStats();
        const now = Date.now();

        // 计算任务处理速度
        const recentTasks = Array.from(this.activeTasks.values())
            .filter(task => task.lastUpdated && (now - task.lastUpdated) < 30000); // 最近30秒

        const processingSpeed = recentTasks.length > 0
            ? recentTasks.reduce((sum, task) => sum + (task.progress || 0), 0) / recentTasks.length
            : 0;

        return {
            ...stats,
            processingSpeed: processingSpeed,
            activePolling: recentTasks.length,
            timestamp: now
        };
    }
}

// 导出任务管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedTaskManager;
} else if (typeof window !== 'undefined') {
    window.EnhancedTaskManager = EnhancedTaskManager;
}