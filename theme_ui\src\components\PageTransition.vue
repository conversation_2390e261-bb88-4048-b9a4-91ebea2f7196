<template>
  <div class="page-transition-container">
    <transition :name="transitionName" :mode="mode" @before-enter="beforeEnter" @after-enter="afterEnter">
      <slot></slot>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useRoute } from "vue-router";

const props = defineProps({
  // 过渡名称：fade, slide, zoom, flip
  name: {
    type: String,
    default: "fade",
  },
  // 过渡模式：out-in, in-out, default
  mode: {
    type: String,
    default: "out-in",
  },
  // 是否根据路由自动选择过渡效果
  autoTransition: {
    type: Boolean,
    default: true,
  },
});

// 当前使用的过渡效果名称
const transitionName = ref(props.name);
const route = useRoute();

// 当路由变更时，根据路由路径选择不同的过渡效果
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!props.autoTransition) return;

    // 从首页进入功能页面
    if (oldPath === "/" || oldPath === "") {
      // 各功能使用不同过渡效果
      if (newPath.includes("image")) {
        transitionName.value = "zoom";
      } else if (newPath.includes("video")) {
        transitionName.value = "slide-left";
      } else if (newPath.includes("voice")) {
        transitionName.value = "slide-up";
      } else if (newPath.includes("theme")) {
        transitionName.value = "flip";
      } else {
        transitionName.value = "fade";
      }
    }
    // 返回首页
    else if (newPath === "/") {
      transitionName.value = "slide-right";
    }
    // 其他页面切换
    else {
      transitionName.value = "fade";
    }
  },
  { immediate: true }
);

// 过渡开始前
const beforeEnter = (el: HTMLElement) => {
  document.body.style.overflow = "hidden"; // 防止过渡过程中滚动
  el.style.transformOrigin = "center"; // 设置变换原点
};

// 过渡结束后
const afterEnter = () => {
  document.body.style.overflow = "";
};

onMounted(() => {
  // 添加初始类名，以便首次加载也有动画效果
  document.body.classList.add("page-transition-ready");
});
</script>

<style>
/* 基础过渡样式 */
.page-transition-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 淡入淡出过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 从左向右滑入 */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.5, 1), opacity 0.4s;
}
.slide-left-enter-from {
  transform: translateX(60px);
  opacity: 0;
}
.slide-left-leave-to {
  transform: translateX(-60px);
  opacity: 0;
}

/* 从右向左滑入 */
.slide-right-enter-from {
  transform: translateX(-60px);
  opacity: 0;
}
.slide-right-leave-to {
  transform: translateX(60px);
  opacity: 0;
}

/* 从下向上滑入 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.5, 1), opacity 0.4s;
}
.slide-up-enter-from {
  transform: translateY(60px);
  opacity: 0;
}
.slide-up-leave-to {
  transform: translateY(-60px);
  opacity: 0;
}

/* 缩放过渡 */
.zoom-enter-active,
.zoom-leave-active {
  transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.5s;
}
.zoom-enter-from {
  transform: scale(0.95);
  opacity: 0;
}
.zoom-leave-to {
  transform: scale(1.05);
  opacity: 0;
}

/* 翻转过渡 */
.flip-enter-active,
.flip-leave-active {
  transition: transform 0.6s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.5s;
  backface-visibility: hidden;
}
.flip-enter-from {
  transform: perspective(1000px) rotateY(-10deg);
  opacity: 0;
}
.flip-leave-to {
  transform: perspective(1000px) rotateY(10deg);
  opacity: 0;
}

/* 页面加载初始动画 */
.page-transition-ready {
  animation: pageLoad 0.8s ease-out;
}

@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 