<template>
  <div :class="wrapperClasses">
    <label v-if="label" :for="inputId" class="input-label">
      <BaseText variant="caption" color="secondary">
        {{ label }}
        <span v-if="required" class="required-indicator">*</span>
      </BaseText>
    </label>
    
    <div class="input-container">
      <BaseIcon 
        v-if="prefixIcon" 
        :name="prefixIcon" 
        :size="16"
        class="input-icon input-icon--prefix"
      />
      
      <input
        :id="inputId"
        ref="inputRef"
        :class="inputClasses"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :autocomplete="autocomplete"
        :maxlength="maxlength"
        :minlength="minlength"
        :min="min"
        :max="max"
        :step="step"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <BaseIcon 
        v-if="suffixIcon" 
        :name="suffixIcon" 
        :size="16"
        class="input-icon input-icon--suffix"
        @click="handleSuffixClick"
      />
      
      <button
        v-if="clearable && modelValue && !disabled && !readonly"
        type="button"
        class="input-clear"
        @click="handleClear"
      >
        <BaseIcon name="close" :size="14" />
      </button>
    </div>
    
    <div v-if="helperText || errorMessage" class="input-helper">
      <BaseText 
        variant="caption" 
        :color="hasError ? 'error' : 'tertiary'"
      >
        {{ errorMessage || helperText }}
      </BaseText>
    </div>
  </div>
</template>

<script>
import { computed, ref, nextTick } from 'vue'
import BaseIcon from './BaseIcon.vue'
import BaseText from './BaseText.vue'

export default {
  name: 'BaseInput',
  components: {
    BaseIcon,
    BaseText
  },
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'text',
      validator: (value) => [
        'text', 'password', 'email', 'number', 'tel', 'url', 'search'
      ].includes(value)
    },
    label: {
      type: String,
      default: null
    },
    placeholder: {
      type: String,
      default: null
    },
    helperText: {
      type: String,
      default: null
    },
    errorMessage: {
      type: String,
      default: null
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    variant: {
      type: String,
      default: 'glass',
      validator: (value) => ['glass', 'outlined', 'filled'].includes(value)
    },
    prefixIcon: {
      type: String,
      default: null
    },
    suffixIcon: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    autocomplete: {
      type: String,
      default: 'off'
    },
    maxlength: {
      type: [String, Number],
      default: null
    },
    minlength: {
      type: [String, Number],
      default: null
    },
    min: {
      type: [String, Number],
      default: null
    },
    max: {
      type: [String, Number],
      default: null
    },
    step: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:modelValue', 'focus', 'blur', 'clear', 'suffix-click', 'keydown'],
  setup(props, { emit }) {
    const inputRef = ref(null)
    const isFocused = ref(false)
    
    const inputId = computed(() => `input-${Math.random().toString(36).substr(2, 9)}`)
    
    const hasError = computed(() => !!props.errorMessage)
    
    const wrapperClasses = computed(() => [
      'base-input',
      `base-input--${props.size}`,
      `base-input--${props.variant}`,
      {
        'base-input--focused': isFocused.value,
        'base-input--disabled': props.disabled,
        'base-input--readonly': props.readonly,
        'base-input--error': hasError.value,
        'base-input--with-prefix': props.prefixIcon,
        'base-input--with-suffix': props.suffixIcon || props.clearable
      }
    ])
    
    const inputClasses = computed(() => [
      'input-field'
    ])
    
    const handleInput = (event) => {
      emit('update:modelValue', event.target.value)
    }
    
    const handleFocus = (event) => {
      isFocused.value = true
      emit('focus', event)
    }
    
    const handleBlur = (event) => {
      isFocused.value = false
      emit('blur', event)
    }
    
    const handleClear = () => {
      emit('update:modelValue', '')
      emit('clear')
      nextTick(() => {
        inputRef.value?.focus()
      })
    }
    
    const handleSuffixClick = () => {
      emit('suffix-click')
    }
    
    const handleKeydown = (event) => {
      emit('keydown', event)
    }
    
    return {
      inputRef,
      inputId,
      hasError,
      wrapperClasses,
      inputClasses,
      handleInput,
      handleFocus,
      handleBlur,
      handleClear,
      handleSuffixClick,
      handleKeydown
    }
  }
}
</script>

<style scoped>
.base-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-label {
  display: block;
}

.required-indicator {
  color: var(--color-error);
  margin-left: 2px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-field {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background: transparent;
  transition: all var(--transition-normal);
  outline: none;
}

/* === 尺寸变体 === */
.base-input--small .input-field {
  height: 32px;
  padding: 0 var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.base-input--medium .input-field {
  height: 44px;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-md);
}

.base-input--large .input-field {
  height: 56px;
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-lg);
}

/* === 样式变体 === */
.base-input--glass .input-field {
  background: rgba(255, 255, 255, var(--transparency-medium));
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium));
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    var(--shadow-small),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.base-input--outlined .input-field {
  background: transparent;
  border: 1.5px solid var(--border-color);
}

.base-input--filled .input-field {
  background: var(--bg-secondary);
  border: 1px solid transparent;
}

/* === 状态样式 === */
.base-input--focused .input-field {
  border-color: var(--color-primary);
  box-shadow: 
    var(--shadow-medium),
    0 0 0 3px rgba(var(--color-primary), 0.1);
}

.base-input--error .input-field {
  border-color: var(--color-error);
}

.base-input--error.base-input--focused .input-field {
  box-shadow: 
    var(--shadow-medium),
    0 0 0 3px rgba(var(--color-error), 0.1);
}

.base-input--disabled .input-field {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

.base-input--readonly .input-field {
  cursor: default;
  background: var(--bg-tertiary);
}

/* === 图标样式 === */
.input-icon {
  position: absolute;
  color: var(--text-tertiary);
  pointer-events: none;
  z-index: 1;
}

.input-icon--prefix {
  left: var(--spacing-sm);
}

.input-icon--suffix {
  right: var(--spacing-sm);
}

.base-input--with-prefix .input-field {
  padding-left: calc(var(--spacing-sm) * 2 + 16px);
}

.base-input--with-suffix .input-field {
  padding-right: calc(var(--spacing-sm) * 2 + 16px);
}

/* === 清除按钮 === */
.input-clear {
  position: absolute;
  right: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 2;
}

.input-clear:hover {
  background: rgba(0, 0, 0, 0.2);
  color: var(--text-secondary);
}

/* === 占位符样式 === */
.input-field::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  .base-input--glass .input-field {
    background: rgba(28, 28, 30, var(--transparency-medium));
  }
  
  .input-clear {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .input-clear:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* === 自动填充样式 === */
.input-field:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset;
  -webkit-text-fill-color: var(--text-primary);
}
</style>
