<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">图像识别</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧上传区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6 flex flex-col">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">上传图片</h2>

            <form @submit.prevent="uploadImage">
              <div class="w-1/2 p-4">
                <label for="file" class="block text-sm font-medium text-purple-200 mb-2">
                  选择图片 （5MB以内）<span class="text-red-400">*</span>
                </label>
                <input type="file" id="file" @change="handleFileChange" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" accept="image/*" required />
                <p class="text-xs text-gray-400 mt-2">
                  上传一张图片，AI将为您生成描述文本。
                </p>
                <div v-if="previewImage" class="mt-4">
                  <img :src="previewImage" class="max-w-full h-auto rounded-lg" alt="预览图片" />
                </div>
              </div>

              <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isProcessing">
                  <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isProcessing ? '处理中...' : '开始生成' }}
                </button>
              </div>
            </form>
          </GlassPanel>
        </div>

        <!-- 右侧文本展示区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6 h-full flex flex-col">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">生成文本</h2>

            <!-- 文本结果展示 -->
            <div class="w-full bg-dark-800/60 rounded-lg p-4 flex-grow overflow-y-auto" :class="{'h-full': !isProcessing}">
              <h3 class="text-lg font-medium text-purple-200 mb-2">生成的文本</h3>
              <div ref="resultContainer" class="w-full text-gray-300 text-sm whitespace-pre-wrap overflow-hidden result-text" :class="{'pulsing-text': isProcessing && generatedText.length === 0}">
                <template v-if="generatedText">{{ generatedText }}</template>
                <template v-else-if="isProcessing">正在生成文本...</template>
                <template v-else>请在左侧上传图片并点击"开始生成"按钮</template>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end mt-4" v-if="generatedText">
              <button @click="copyResult" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium mr-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002-2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                </svg>
                复制
              </button>
            </div>
          </GlassPanel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
    () => import("../components/ProgressBar.vue")
);

const file = ref<File | null>(null);
const isProcessing = ref<boolean>(false);
const generatedText = ref<string>("");
const taskId = ref<string>("");
const previewImage = ref<string>("");
// 生成任务ID
const generateTaskId = (): string => {
  // 生成16位UUID + 时间戳
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};

// 处理文件变化
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    file.value = target.files[0];
    previewImage.value = URL.createObjectURL(file.value); // 创建图片的临时URL以便预览
  }
};

// 上传图片
const uploadImage = async () => {
  if (!file.value) {
    alert("请选择图片文件");
    return;
  }

  try {
    isProcessing.value = true;
    generatedText.value = "";

    // 生成任务ID
    taskId.value = generateTaskId();

    // 创建FormData对象
    const formData = new FormData();
    formData.append("file", file.value);
    formData.append("task_id", taskId.value);

    // 调用API
    const response = await themeApi.imageToText(formData);

    // 设置生成的文本
    generatedText.value = response.text;
  } catch (error) {
    console.error("上传图片失败", error);
    alert("上传图片失败，请重试");
  } finally {
    isProcessing.value = false;
  }
};

// 复制结果
const copyResult = () => {
  navigator.clipboard.writeText(generatedText.value);
  alert("文本已复制到剪贴板");
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

.pulsing-text::after {
  content: '';
  display: block;
  width: 100%;
  height: 3px;
  background-color: #7c3aed;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
</style>
