from fastapi import APIRouter, HTTPException, Request, UploadFile, File, Form
from pydantic import BaseModel
import time
import json
import os
import base64
import uuid
import asyncio
from typing import Optional, Dict, Any, List, Union
from .common import upload_image, load_workflow, send_prompt, get_history
import httpx
from app.core.redis_client import RedisClient
import logging
from PIL import Image
import sys
from ..core.config import settings

# 尝试多种方式导入SD提示词增强Agent
try:
    # 直接从agent目录导入
    from agent.sd_prompt_agent import sd_prompt_enhancer
except ImportError:
    try:
        # 使用绝对路径导入
        sys.path.insert(0, os.path.abspath("/app"))
        from agent.sd_prompt_agent import sd_prompt_enhancer
    except ImportError:
        # 如果仍然失败，记录警告并设置为None
        logger = logging.getLogger(__name__)
        logger.warning("无法导入SD提示词增强Agent，将使用默认提示词")
        sd_prompt_enhancer = None

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/doodle", tags=["doodle"])

# Redis连接设置
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_TTL = 60 * 60 * 24  # 24小时过期时间

# ComfyUI工作流路径
TUYA_WORKFLOW_PATH = "tuya.json"

SAVE_NODE_ID = "45" # 工作流中SaveImage节点的ID，需要确认
server_name="doodle_server"
# 请求模型
class DoodleStartRequest(BaseModel):
    sketch_image: str  # Base64编码的图像数据
    task_id: str
    prompt: str  # 用户输入的提示词
    initial_context: Optional[str] = None

class UpdatePromptRequest(BaseModel):
    prompt: str

# 响应模型
class DoodleResponse(BaseModel):
    task_id: str
    enhanced_prompt: str

class StatusResponse(BaseModel):
    status: str
    progress: Optional[float] = None
    result_image_url: Optional[str] = None
    result_image_base64: Optional[str] = None
    error: Optional[str] = None

class MessageResponse(BaseModel):
    message: str

# Redis缓存键生成函数
def get_enhanced_prompt_key(task_id: str) -> str:
    """生成存储增强提示词的Redis键"""
    return f"doodle_enhanced_prompt:{task_id}"

def get_original_prompt_key(task_id: str) -> str:
    """生成存储原始提示词的Redis键"""
    return f"doodle_original_prompt:{task_id}"

def get_prompt_id_key(task_id: str) -> str:
    """生成存储ComfyUI prompt_id的Redis键"""
    return f"doodle_prompt_id:{task_id}"

def get_status_key(task_id: str) -> str:
    """生成存储任务状态的Redis键"""
    return f"doodle_status:{task_id}"

# 提示词增强函数
async def enhance_prompt_with_cache(task_id: str, prompt: str) -> str:
    """
    增强提示词并缓存结果。
    
    1. 检查Redis缓存中是否有对应task_id的原始提示词
    2. 如果有且与当前提示词一致，则使用缓存的增强提示词
    3. 如果没有或不一致，调用Agent增强并更新缓存
    
    Args:
        task_id: 任务ID
        prompt: 原始提示词
    
    Returns:
        增强后的英文SD提示词字符串，失败时返回原始提示词
    """
    # 获取Redis连接
    r = RedisClient.get_redis()
    
    # 获取缓存的原始提示词和增强提示词
    cached_original_prompt = r.get(get_original_prompt_key(task_id))
    cached_enhanced_prompt = r.get(get_enhanced_prompt_key(task_id))
    
    # 如果缓存的原始提示词与当前提示词一致，使用缓存的增强提示词
    if cached_original_prompt and cached_original_prompt == prompt and cached_enhanced_prompt:
        logger.info(f"命中缓存: 任务ID={task_id}, 原始提示词='{prompt[:30]}...'")
        return cached_enhanced_prompt
    
    # 缓存未命中或提示词已更改，使用Agent增强
    logger.info(f"需要增强提示词: 任务ID={task_id}, 原始提示词='{prompt[:30]}...'")
    if sd_prompt_enhancer:
        try:
            enhanced_prompt = await sd_prompt_enhancer.enhance_prompt(prompt)
            if enhanced_prompt:
                # 更新Redis缓存 (修正缩进)
                r.set(get_original_prompt_key(task_id), prompt, ex=REDIS_TTL)
                r.set(get_enhanced_prompt_key(task_id), enhanced_prompt, ex=REDIS_TTL)
                logger.info(f"提示词增强成功: '{enhanced_prompt[:50]}...'")
                return enhanced_prompt
            else:
                logger.warning(f"提示词增强失败，使用原始提示词: '{prompt}'")
                return prompt
        except Exception as e:
            logger.error(f"增强提示词时发生错误: {e}")
            return prompt
    else:
        logger.warning("SD提示词增强Agent未初始化，使用原始提示词")
        return prompt

@router.post("/start", response_model=DoodleResponse)
async def start_doodle_task(
    sketch_image: UploadFile = File(...),
    task_id: str = Form(...),
    prompt: str = Form(...),
    initial_context: Optional[str] = Form(None),
    num_samples: int = Form(1)
):
    """
    启动涂鸦生成任务
    
    1. 接收前端上传的图像文件和表单数据
    2. 增强提示词（英文SD提示词）
    3. 上传图像文件到ComfyUI
    4. 存储提示词到Redis
    5. 异步启动ComfyUI工作流
    6. 返回任务ID和提示词
    """
    logger.info(f"后端日志：接收到 /start 请求，任务ID: {task_id}")
    try:
        # 记录接收到的文件信息
        logger.info(f"后端日志：接收到文件 filename={sketch_image.filename}, content_type={sketch_image.content_type}, size={sketch_image.size if hasattr(sketch_image, 'size') else '未知'}")
        logger.info(f"后端日志：文件类型: {type(sketch_image.file)}")
        
        if not hasattr(sketch_image.file, 'read'):
            logger.error("后端日志：文件对象无效，没有read方法")
            raise HTTPException(status_code=400, detail="无效的文件对象")
        
        logger.info("后端日志：文件对象有效，具有read方法")
        
        # 增强提示词
        logger.info(f"后端日志：用户提供的原始提示词: {prompt}")
        enhanced_prompt = await enhance_prompt_with_cache(task_id, prompt)
        
        # 直接上传图像到ComfyUI
        logger.info("后端日志：准备上传图像到 ComfyUI")
        upload_result = await upload_image(server_name, sketch_image.file, image_type='input')
        logger.info(f"后端日志：上传图像结果: {upload_result}")
        
        # 验证上传结果是否有效
        if not upload_result or upload_result.startswith("None"):
            logger.error(f"后端日志：上传图像失败，返回文件名无效: {upload_result}")
            raise HTTPException(status_code=500, detail="上传图像失败，请检查图像格式")
            
        uploaded_filename = upload_result
        logger.info(f"后端日志：图像已上传到 ComfyUI: {uploaded_filename}")
        
        # 将两种提示词都保存到Redis
        logger.info("后端日志：准备保存提示词到 Redis")
        r = RedisClient.get_redis()
        # 保存原始提示词
        r.set(f"doodle_prompt:{task_id}", prompt, ex=REDIS_TTL)
        # 保存增强后的提示词
        r.set(get_enhanced_prompt_key(task_id), enhanced_prompt, ex=REDIS_TTL)
        # 保存原始提示词（用于缓存比对）
        r.set(get_original_prompt_key(task_id), prompt, ex=REDIS_TTL)
        logger.info("后端日志：提示词已保存到 Redis")
        
        # 加载工作流模板
        logger.info(f"后端日志：准备加载工作流: {TUYA_WORKFLOW_PATH}")
        workflow_dict = load_workflow(TUYA_WORKFLOW_PATH)
        logger.info("后端日志：工作流已加载")
        
        # 修改工作流参数 - 使用增强后的提示词
        logger.info("后端日志：准备修改工作流参数")
        workflow_dict["44"]["inputs"]["image"] = uploaded_filename  # 更新LoadImage节点
        workflow_dict["41"]["inputs"]["String"] = enhanced_prompt  # 更新正面提示词节点，使用增强后的英文提示词
        workflow_dict["45"]["inputs"]["filename_prefix"] = f"output_{task_id}"  # 更新SaveImage节点
        # 如果工作流支持设置生成样本数量
        if "50" in workflow_dict and "batch_size" in workflow_dict["50"]["inputs"]:
            workflow_dict["50"]["inputs"]["batch_size"] = num_samples
        logger.info("后端日志：工作流参数已修改")
        
        # 异步发送工作流到ComfyUI
        logger.info("后端日志：准备异步发送工作流到 ComfyUI")
        asyncio.create_task(send_workflow_to_comfyui(workflow_dict, task_id))
        logger.info("后端日志：已创建发送工作流的异步任务")
        
        # 返回任务ID和增强后的提示词
        response_data = DoodleResponse(
            task_id=task_id,
            enhanced_prompt=enhanced_prompt  # 返回增强后的提示词
        )
        logger.info(f"后端日志：成功处理 /start 请求，任务ID: {task_id}")
        return response_data
    
    except HTTPException as http_exc:
        logger.error(f"后端日志：启动涂鸦任务时发生 HTTP 错误: {http_exc.detail}", exc_info=True)
        raise http_exc # 重新抛出HTTPException
    except Exception as e:
        logger.error(f"后端日志：启动涂鸦任务时发生意外错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动涂鸦任务失败: {str(e)}")

@router.get("/status/{task_id}", response_model=StatusResponse)
async def get_doodle_status(task_id: str):
    """
    查询涂鸦任务状态
    
    1. 从Redis获取ComfyUI的prompt_id
    2. 调用ComfyUI /history API查询状态
    3. 解析状态，如果完成则获取结果URL
    4. 返回状态信息
    """
    logger.info(f"查询任务状态: {task_id}")
    r = RedisClient.get_redis()
    prompt_id = r.get(get_prompt_id_key(task_id))

    if not prompt_id:
        logger.warning(f"任务ID {task_id} 没有找到对应的 prompt_id")
        # 检查是否有旧的状态信息
        cached_status = r.get(get_status_key(task_id))
        if cached_status == "failed":
             error_msg = r.get(f"doodle_error:{task_id}") or "任务启动失败或prompt_id丢失"
             return StatusResponse(status="failed", error=error_msg)
        return StatusResponse(status="failed", error="任务不存在或未成功启动")

    try:
        history_data = await get_history(server_name, prompt_id)
        
        if not history_data or prompt_id not in history_data:
            # 可能任务仍在队列中或刚刚开始，历史记录尚未生成
            logger.info(f"任务 {task_id} (prompt_id={prompt_id}) 历史记录尚未就绪，假设为运行中")
            return StatusResponse(status="running", progress=0) # 或 "queued"

        prompt_history = history_data[prompt_id]
        
        # 检查状态字段 (ComfyUI API可能变化，需要确认实际路径)
        status_info = prompt_history.get("status", {})
        current_status_str = status_info.get("status_str", "unknown").lower()
        completed = status_info.get("completed", False)
        
        # 尝试获取执行进度（如果ComfyUI提供）
        # 这部分可能不可靠或不存在，取决于ComfyUI版本和配置
        exec_info = status_info.get("exec_info", {})
        queue_remaining = exec_info.get("queue_remaining", None)
        # 简单的进度模拟：如果队列>0，进度为0；否则认为在执行，进度50%
        progress = 0.0 if queue_remaining is not None and queue_remaining > 0 else 50.0
        
        if completed and current_status_str == "success":
            logger.info(f"任务 {task_id} (prompt_id={prompt_id}) 已成功完成")
            outputs = prompt_history.get("outputs", {})
            if SAVE_NODE_ID in outputs:
                output_node = outputs[SAVE_NODE_ID]
                images = output_node.get("images", [])
                if images:
                    # 通常只关心第一张图
                    image_info = images[0]
                    filename = image_info.get("filename")
                    subfolder = image_info.get("subfolder", "")
                    image_type = image_info.get("type", "output") # 通常是 'output' 或 'temp'
                    
                    if filename:
                        # 构建图像URL
                        # 格式: http://host:port/view?filename=...&type=...&subfolder=...
                        result_image_url = f"{settings.API_URLS['doodle_server']}/view?filename={filename}&type={image_type}&subfolder={subfolder}"
                        logger.info(f"任务 {task_id} 找到结果图像URL: {result_image_url}")
                        # 更新Redis状态
                        r.set(get_status_key(task_id), "completed", ex=REDIS_TTL)
                        return StatusResponse(
                            status="completed",
                            progress=100,
                            result_image_url=result_image_url
                        )
                    else:
                        logger.error(f"任务 {task_id} (prompt_id={prompt_id}) 输出节点 {SAVE_NODE_ID} 中缺少图像文件名")
                        error_msg = "生成成功但无法找到输出文件名"
                else:
                    logger.error(f"任务 {task_id} (prompt_id={prompt_id}) 输出节点 {SAVE_NODE_ID} 中没有图像数据")
                    error_msg = "生成成功但输出节点无图像数据"
            else:
                logger.error(f"任务 {task_id} (prompt_id={prompt_id}) 历史记录输出中未找到指定的保存节点 {SAVE_NODE_ID}")
                error_msg = f"生成成功但找不到保存节点 {SAVE_NODE_ID}"
                
            # 如果成功完成但无法提取URL
            r.set(get_status_key(task_id), "failed", ex=REDIS_TTL)
            r.set(f"doodle_error:{task_id}", error_msg, ex=REDIS_TTL)
            return StatusResponse(status="failed", error=error_msg)
            
        elif current_status_str == "running" or current_status_str == "queued" or not completed:
            logger.info(f"任务 {task_id} (prompt_id={prompt_id}) 仍在运行中 (状态: {current_status_str})")
            # 更新Redis状态
            r.set(get_status_key(task_id), "running", ex=REDIS_TTL)
            return StatusResponse(status="running", progress=progress) 
            
        else: # 包含 error, unknown 等状态
            error_msg = f"ComfyUI任务失败或状态未知 ({current_status_str})"
            logger.error(f"任务 {task_id} (prompt_id={prompt_id}) 失败或状态未知: {current_status_str}")
            # 尝试获取更详细的错误信息
            node_errors = prompt_history.get("node_errors", {})
            if node_errors:
                 error_msg += f": {json.dumps(node_errors)}"
                 logger.error(f"节点错误详情: {json.dumps(node_errors)}")
            
            # 更新Redis状态
            r.set(get_status_key(task_id), "failed", ex=REDIS_TTL)
            r.set(f"doodle_error:{task_id}", error_msg, ex=REDIS_TTL)
            return StatusResponse(status="failed", error=error_msg)

    except Exception as e:
        logger.error(f"查询任务状态时发生异常: {task_id}, 错误: {str(e)}", exc_info=True)
        # 保留上次已知的状态或标记为查询失败
        cached_status = r.get(get_status_key(task_id)) or "failed"
        return StatusResponse(status=cached_status, error=f"查询状态时出错: {str(e)}")

@router.put("/prompt/{task_id}", response_model=MessageResponse)
async def update_prompt_and_regenerate(task_id: str, request: UpdatePromptRequest):
    """
    更新提示词并重新生成
    
    1. 接收新的提示词
    2. 增强提示词（英文SD提示词）
    3. 更新Redis中的提示词
    4. 重新触发ComfyUI工作流
    5. 返回确认消息
    """
    try:
        # 获取新的原始提示词
        new_prompt = request.prompt
        logger.info(f"接收到更新提示词请求: 任务ID={task_id}, 新提示词='{new_prompt[:30]}...'")
        
        # 增强提示词
        enhanced_prompt = await enhance_prompt_with_cache(task_id, new_prompt)
        
        # 更新Redis中的提示词
        r = RedisClient.get_redis()
        # 更新原始提示词
        r.set(f"doodle_prompt:{task_id}", new_prompt, ex=REDIS_TTL)
        # 更新增强后的提示词
        r.set(get_enhanced_prompt_key(task_id), enhanced_prompt, ex=REDIS_TTL)
        # 更新原始提示词（用于缓存比对）
        r.set(get_original_prompt_key(task_id), new_prompt, ex=REDIS_TTL)
        
        # 重置任务开始时间
        r.set(f"doodle_start_time:{task_id}", time.time(), ex=REDIS_TTL)
        
        # 获取原始图像文件名
        uploaded_filename = r.get(f"doodle_image:{task_id}")
        if not uploaded_filename:
            # 如果没有找到图像文件名，尝试从工作流中获取
            logger.warning(f"找不到任务ID={task_id}的图像文件名，尝试从工作流获取")
            # 这里可以添加备用逻辑
        
        # 重新加载工作流并更新参数
        workflow_dict = load_workflow(TUYA_WORKFLOW_PATH)
        
        # 更新工作流参数
        workflow_dict["44"]["inputs"]["image"] = uploaded_filename  # 更新LoadImage节点
        workflow_dict["41"]["inputs"]["String"] = enhanced_prompt  # 更新正面提示词节点，使用增强后的英文提示词
        workflow_dict["45"]["inputs"]["filename_prefix"] = f"output_{task_id}"  # 更新SaveImage节点
        
        # 异步发送工作流到ComfyUI
        asyncio.create_task(send_workflow_to_comfyui(workflow_dict, task_id))
        
        return MessageResponse(message="提示词已更新，重新生成已启动")
    
    except Exception as e:
        logger.error(f"更新提示词失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新提示词失败: {str(e)}")

async def send_workflow_to_comfyui(workflow_dict: Dict, task_id: str):
    """异步发送工作流到ComfyUI并处理结果"""
    r = RedisClient.get_redis()
    try:
        # 记录任务开始时间 (如果需要精确计时)
        # r.set(f"doodle_start_time:{task_id}", time.time(), ex=REDIS_TTL)
        
        # 保存当前使用的图像文件名，以便于更新提示词时重用
        if SAVE_NODE_ID in workflow_dict and "image" in workflow_dict[SAVE_NODE_ID]["inputs"]:
            image_filename = workflow_dict[SAVE_NODE_ID]["inputs"]["image"]
            r.set(f"doodle_image:{task_id}", image_filename, ex=REDIS_TTL)
        
        # 发送工作流到ComfyUI
        logger.info(f"发送工作流到ComfyUI, task_id={task_id}")
        response = await send_prompt(server_name, workflow_dict)
        logger.info(f"工作流发送响应: {response}")
        
        # 提取并保存 prompt_id
        prompt_id = response.get("prompt_id")
        if prompt_id:
            r.set(get_prompt_id_key(task_id), prompt_id, ex=REDIS_TTL)
            r.set(get_status_key(task_id), "queued", ex=REDIS_TTL) # 设置初始状态为 queued
            logger.info(f"任务 {task_id} 已提交到ComfyUI, prompt_id: {prompt_id}")
        else:
            # 如果没有prompt_id，说明提交失败
            logger.error(f"任务 {task_id} 提交到ComfyUI失败，未收到prompt_id。响应: {response}")
            r.set(get_status_key(task_id), "failed", ex=REDIS_TTL)
            error_msg = response.get("error", "提交失败，未知错误")
            node_errors = response.get("node_errors")
            if node_errors:
                error_msg += f": {json.dumps(node_errors)}"
            r.set(f"doodle_error:{task_id}", error_msg, ex=REDIS_TTL)
            
        # 这里不再等待结果，由 /status 接口负责
        
    except Exception as e:
        logger.error(f"发送工作流或处理响应时出错: task_id={task_id}, 错误={str(e)}", exc_info=True)
        # 更新任务状态为失败
        r.set(get_status_key(task_id), "failed", ex=REDIS_TTL)
        r.set(f"doodle_error:{task_id}", f"发送工作流失败: {str(e)}", ex=REDIS_TTL) 