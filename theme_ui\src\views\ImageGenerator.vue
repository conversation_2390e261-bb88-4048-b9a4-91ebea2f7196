<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">文本生成壁纸</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧表单区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">创建壁纸</h2>

            <form @submit.prevent="generateImage">
              <div class="mb-6">
                <label for="prompt" class="block text-sm font-medium text-purple-200 mb-2">
                  壁纸描述 <span class="text-red-400">*</span>
                </label>
                <textarea id="prompt" v-model="prompt" rows="6" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="请描述您想生成的壁纸内容..." required style="background-color: rgba(15, 23, 42, 0.7);"></textarea>
                <p class="text-xs text-gray-400 mt-2">
                  输入清晰的描述，详细说明壁纸中应该出现的场景、物体、风格等。
                </p>
              </div>

              <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isGenerating">
                  <svg v-if="isGenerating" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isGenerating ? '生成中...' : '开始生成' }}
                </button>
              </div>

              <!-- 试一试标签区域 -->
              <div class="mt-8">
                <h3 class="text-lg font-medium text-purple-200 mb-4">试一试</h3>
                <div class="flex flex-wrap gap-3">
                  <button 
                    v-for="(tag, index) in exampleTags" 
                    :key="index"
                    @click="useExampleTag(tag)"
                    class="px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 rounded-lg transition-all duration-300"
                  >
                    {{ tag }}
                  </button>
                </div>
              </div>
            </form>
          </GlassPanel>
        </div>

        <!-- 右侧预览区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">壁纸预览</h2>

            <div v-if="isGenerating" class="flex flex-col items-center justify-center my-6">
              <ProgressBar :progress="generationProgress" :completed-steps="1" :total-steps="1" class="w-full mb-4" />
              <p class="text-purple-200">生成中，请耐心等待... {{ Math.round(generationProgress) }}%</p>
            </div>

            <div v-if="imageUrl" class="bg-dark-800/60 rounded-lg overflow-hidden" @click="openImageViewer()">
              <img :src="imageUrl" class="preview-img" />
            </div>

            <div v-else-if="!isGenerating" class="bg-dark-800/60 rounded-lg p-8 flex flex-col items-center justify-center min-h-[300px]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-purple-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M4 4h16v16H4V4z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" />
                <path d="M8 8h8" />
                <path d="M8 12h8" />
                <path d="M8 16h8" />
              </svg>

              <p class="text-purple-200 text-center mb-2">尚未生成壁纸</p>
              <p class="text-gray-400 text-sm text-center">填写左侧表单并点击"开始生成"按钮</p>
            </div>

            <!-- 视频生成信息 -->
<!--            <div v-if="videoInfo" class="mt-6 bg-dark-800/30 p-4 rounded-lg">
              <h3 class="text-sm font-semibold text-purple-200 mb-2">视频信息</h3>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div class="text-gray-400">视频分辨率：</div>
                <div class="text-white">{{ videoInfo.width }}x{{ videoInfo.height }}</div>
                <div class="text-gray-400">帧数：</div>
                <div class="text-white">{{ videoInfo.num_frames }}</div>
                <div class="text-gray-400">随机种子：</div>
                <div class="text-white">{{ videoInfo.seed }}</div>
                <div class="text-gray-400">任务ID：</div>
                <div class="text-white">{{ taskId }}</div>
              </div>
            </div>-->
          </GlassPanel>
        </div>
      </div>
    </div>
    <!-- 图片查看器 -->
    <ImageViewer v-model="showImageViewer" :images="viewerImages" :initialIndex="currentImageIndex" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { useRoute } from "vue-router";
import ImageViewer from "../components/ImageViewer.vue";

const GlassPanel = defineAsyncComponent(
  () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
  () => import("../components/ProgressBar.vue")
);


const route = useRoute();
const prompt = ref<string>(route.query.prompt ? route.query.prompt.toString() : "");
const isGenerating = ref<boolean>(false);
const generationProgress = ref<number>(0);
const imageUrl = ref<string | null>(null);
const videoInfo = ref<any>(null);
const taskId = ref<string>("");
// 图片查看器状态
const showImageViewer = ref(false);
const viewerImages = ref<string[]>([]);
const currentImageIndex = ref(0);

// 打开图片查看器
const openImageViewer = () => {
  viewerImages.value = [imageUrl.value];
  currentImageIndex.value = 0;
  showImageViewer.value = true;
};
const exampleTags = ref<string[]>([
  "梦幻星空，银河璀璨，紫色星云",
  "赛博朋克城市，霓虹灯光，未来科技",
  "宁静山水，水墨风格，中国风"
]);

const useExampleTag = (tag: string) => {
  prompt.value = tag;
  generateImage();
};

// 生成任务ID
const generateTaskId = (): string => {
  // 生成16位UUID + 时间戳
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};

// 生成视频
const generateImage = async () => {
  console.log("开始生成壁纸");
  if (!prompt.value.trim()) {
    alert("请输入壁纸描述");
    return;
  }

  try {
    isGenerating.value = true;
    generationProgress.value = 10; // 初始进度
    imageUrl.value = null;
    videoInfo.value = null;

    // 生成任务ID
    taskId.value = generateTaskId();

    // 调用API
    const response = await themeApi.textToImage(prompt.value, taskId.value);

    // 模拟进度
    const updateProgress = () => {
      if (generationProgress.value < 90) {
        generationProgress.value += 5;
        setTimeout(updateProgress, 3000);
      }
    };
    updateProgress();

    // 设置视频URL和信息 - 修复URL路径问题
    if (response.image_url) {
      // 添加服务器地址前缀
      imageUrl.value = `${response.image_url}`;
    }

    videoInfo.value = {
      width: response.width,
      height: response.height,
      num_frames: response.num_frames,
      seed: response.seed,
    };
    generationProgress.value = 100;
  } catch (error) {
    console.error("生成壁纸失败", error);
    alert("生成壁纸失败，请重试");
  } finally {
    isGenerating.value = false;
  }
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
    radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
    radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}
</style>
