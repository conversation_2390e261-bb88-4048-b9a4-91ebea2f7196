<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能车机界面</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'dark-800': 'rgba(15, 23, 42, 0.8)',
            'dark-700': 'rgba(30, 41, 59, 0.7)',
            'dark-600': 'rgba(30, 41, 59, 0.6)',
            'dark-500': 'rgba(30, 41, 59, 0.5)',
            'dark-400': 'rgba(30, 41, 59, 0.4)',
            'glasswhite': 'rgba(255, 255, 255, 0.1)',
          },
          boxShadow: {
            'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
          }
        }
      }
    }
  </script>
  <style>
    body {
      overflow: hidden;
      background-color: #000;
      font-family: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
    }
    
    .bg-noise {
      background-image: url('https://images.unsplash.com/photo-1536152470836-b943b246224c?q=80&w=2938&auto=format&fit=crop');
      background-size: cover;
      background-position: center;
    }
    
    .glass-card {
      background: rgba(30, 41, 59, 0.4);
      backdrop-filter: blur(12px) saturate(180%);
      -webkit-backdrop-filter: blur(12px) saturate(180%);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    /* 防止滚动条出现 */
    ::-webkit-scrollbar {
      display: none;
    }
  </style>
</head>

<body class="h-screen w-screen text-white bg-noise">
  <!-- 主容器 -->
  <div class="relative h-full w-full p-4 flex flex-col">
    
    <!-- 顶部状态栏 -->
    <div class="flex justify-between items-center mb-5">
      <!-- 左侧速度显示 -->
      <div class="flex items-end">
        <div class="text-7xl font-bold">120</div>
        <div class="flex flex-col ml-2 mb-1.5">
          <span class="text-sm text-gray-300">km/h</span>
          <div class="flex items-center mt-1">
            <span class="bg-green-500 px-2 text-xs font-medium rounded-sm">READY</span>
            <div class="flex items-center ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </div>
          </div>
          <div class="flex items-center mt-1">
            <div class="h-1.5 w-20 bg-gray-700 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-green-500 to-green-400 w-1/2"></div>
            </div>
            <span class="ml-1 text-xs text-gray-300">100%</span>
          </div>
          <div class="text-xs text-gray-400 mt-1">
            <span>3,460 km</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧状态图标 -->
      <div class="flex items-center space-x-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        <div class="text-xl font-medium">11:36</div>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="glass-card rounded-xl mb-5 py-3 px-4">
      <div class="flex justify-between">
        <div class="flex space-x-10">
          <div class="flex flex-col items-center opacity-100">
            <span class="text-white text-sm font-medium">应用中心</span>
            <div class="h-1 w-full bg-white rounded-full mt-2"></div>
          </div>
          <div class="flex flex-col items-center opacity-60">
            <span class="text-white text-sm">小程序</span>
            <div class="h-1 w-full bg-transparent rounded-full mt-2"></div>
          </div>
          <div class="flex flex-col items-center opacity-60">
            <span class="text-white text-sm">HUAWEI Hicar</span>
            <div class="h-1 w-full bg-transparent rounded-full mt-2"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主内容区 - 左右分栏 -->
    <div class="flex flex-1 gap-5">
      <!-- 左侧应用图标区域 -->
      <div class="w-1/2 flex flex-col">
        <div class="glass-card p-5 rounded-xl flex-1">
          <!-- 应用图标网格 -->
          <div class="grid grid-cols-3 gap-4">
            <!-- 娱乐中心 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-purple-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">娱乐中心</span>
            </div>
            
            <!-- 蓝牙电话 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-teal-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-teal-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">蓝牙电话</span>
            </div>
            
            <!-- 车辆中心 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">车辆中心</span>
            </div>
            
            <!-- 应用商店 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-orange-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">应用商店</span>
            </div>
            
            <!-- 手势 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">手势</span>
            </div>
            
            <!-- 服务中心 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-purple-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">服务中心</span>
            </div>
            
            <!-- 空调 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-teal-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-teal-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">空调</span>
            </div>
            
            <!-- 相册 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">相册</span>
            </div>
            
            <!-- 驾驶员中心 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">驾驶员中心</span>
            </div>
            
            <!-- 文件管理 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">文件管理</span>
            </div>
            
            <!-- 行车记录仪 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-blue-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">行车记录仪</span>
            </div>
            
            <!-- 驾驶手册 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-orange-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">驾驶手册</span>
            </div>
            
            <!-- 游戏中心 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-orange-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">游戏中心</span>
            </div>
            
            <!-- 照相机 -->
            <div class="flex flex-col items-center">
              <div class="w-16 h-16 bg-teal-500/30 rounded-lg flex items-center justify-center mb-2 backdrop-blur-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-teal-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="text-xs text-gray-200">相册</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容区域 - 移除毛玻璃效果 -->
      <div class="w-1/2 flex flex-col gap-5">
        <!-- 上部分 - 主推荐区 -->
        <div class="bg-dark-800/40 p-4 rounded-xl h-1/3">
          <!-- 推荐内容 - 卡片网格 -->
          <div class="flex gap-4 h-full">
            <!-- 主推荐卡片 -->
            <div class="w-7/12 h-full overflow-hidden rounded-lg relative group">
              <img src="https://images.unsplash.com/photo-1665419411877-defbd98b2d8b?q=80&w=2574&auto=format&fit=crop" alt="沙丘2" class="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105">
              <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-black/10 flex flex-col justify-end p-4">
                <div class="flex items-center mb-2">
                  <span class="text-xs bg-purple-500/60 backdrop-blur-sm px-2 py-0.5 rounded-sm">首映</span>
                  <span class="text-xs bg-gray-700/60 backdrop-blur-sm px-2 py-0.5 rounded-sm ml-2">科幻</span>
                </div>
                <h3 class="text-lg font-bold">沙丘2</h3>
                <p class="text-xs text-gray-300">传奇 · 力量 · 史诗大片</p>
                <div class="flex items-center mt-2">
                  <div class="flex items-center bg-gray-800/40 backdrop-blur-sm rounded-full px-3 py-1">
                    <span class="text-xs text-gray-300">立即播放</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 次要推荐卡片 -->
            <div class="w-5/12 flex flex-col gap-4">
              <!-- 上部卡片 -->
              <div class="h-1/2 overflow-hidden rounded-lg relative group">
                <img src="https://images.unsplash.com/photo-1567206983724-406d27365e3e?q=80&w=2565&auto=format&fit=crop" alt="操作指引" class="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105">
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-black/10 flex flex-col justify-end p-3">
                  <h3 class="text-sm font-bold">操作指引</h3>
                  <p class="text-[10px] text-gray-300">快速了解系统功能</p>
                </div>
              </div>
              
              <!-- 下部卡片 -->
              <div class="h-1/2 overflow-hidden rounded-lg relative group">
                <img src="https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?q=80&w=2670&auto=format&fit=crop" alt="越野车型" class="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105">
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-black/10 flex flex-col justify-end p-3">
                  <h3 class="text-sm font-bold">越野车型</h3>
                  <p class="text-[10px] text-gray-300">全能驾驶模式推荐</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 中部分 - 搜索和功能区 -->
        <div class="bg-dark-800/40 p-4 rounded-xl h-1/6">
          <div class="flex justify-between items-center h-full">
            <!-- 搜索栏 -->
            <div class="flex-1 mr-8">
              <div class="flex items-center bg-dark-700/60 rounded-full px-4 py-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input type="text" placeholder="搜索目的地" class="bg-transparent border-none outline-none text-white ml-3 w-full placeholder-gray-400 text-sm">
              </div>
            </div>
            
            <!-- 功能按钮组 -->
            <div class="flex space-x-4">
              <div class="flex flex-col items-center">
                <button class="w-10 h-10 bg-dark-600/70 rounded-full flex items-center justify-center hover:bg-blue-500/30 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </button>
                <span class="text-[10px] text-gray-300 mt-1">统计</span>
              </div>
              
              <div class="flex flex-col items-center">
                <button class="w-10 h-10 bg-dark-600/70 rounded-full flex items-center justify-center hover:bg-blue-500/30 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </button>
                <span class="text-[10px] text-gray-300 mt-1">日历</span>
              </div>
              
              <div class="flex flex-col items-center">
                <button class="w-10 h-10 bg-dark-600/70 rounded-full flex items-center justify-center hover:bg-blue-500/30 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </button>
                <span class="text-[10px] text-gray-300 mt-1">安全</span>
              </div>
              
              <div class="flex flex-col items-center">
                <button class="w-10 h-10 bg-dark-600/70 rounded-full flex items-center justify-center hover:bg-blue-500/30 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </button>
                <span class="text-[10px] text-gray-300 mt-1">时间</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 下部分 - 应用快捷方式 -->
        <div class="bg-dark-800/40 p-4 rounded-xl flex-1">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-sm font-medium text-gray-200">快捷应用</h3>
            <button class="text-xs text-gray-400 hover:text-white">查看全部</button>
          </div>
          
          <!-- 应用图标大小调整 -->
          <div class="grid grid-cols-6 gap-5">
            <!-- AI空间 -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-blue-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">AI空间</span>
            </div>
            
            <!-- KTV -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-pink-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-pink-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">KTV</span>
            </div>
            
            <!-- 聚合服务 -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-orange-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-orange-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">聚合服务</span>
            </div>
            
            <!-- 驾驶手册 -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-yellow-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-yellow-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">驾驶手册</span>
            </div>
            
            <!-- 场景模式 -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-purple-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-purple-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">场景模式</span>
            </div>
            
            <!-- 游戏空间 -->
            <div class="flex flex-col items-center">
              <div class="w-14 h-14 bg-red-600/40 rounded-lg flex items-center justify-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-red-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                </svg>
              </div>
              <span class="text-[10px] text-gray-300">游戏空间</span>
            </div>
          </div>
          
          <!-- 最近使用的应用 - 调整大小 -->
          <div class="mt-6">
            <h3 class="text-sm font-medium text-gray-200 mb-4">最近使用</h3>
            <div class="grid grid-cols-6 gap-5">
              <!-- 地图导航 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-green-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-green-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">地图导航</span>
              </div>
              
              <!-- 音乐 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-blue-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">音乐</span>
              </div>
              
              <!-- 电话 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-blue-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">电话</span>
              </div>
              
              <!-- 天气 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-yellow-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-yellow-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">天气</span>
              </div>
              
              <!-- 车辆诊断 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-indigo-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-indigo-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">车辆诊断</span>
              </div>
              
              <!-- 空调控制 -->
              <div class="flex flex-col items-center">
                <div class="w-14 h-14 bg-teal-600/40 rounded-lg flex items-center justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-teal-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <span class="text-[10px] text-gray-300">空调控制</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部控制栏 -->
    <div class="mt-5 glass-card rounded-xl py-2 px-4 flex justify-between items-center">
      <div class="flex space-x-6">
        <!-- 左侧底部控制按钮 -->
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </button>
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </button>
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <div class="flex items-center">
        <!-- 温度显示 -->
        <span class="text-lg font-medium">28.5°</span>
      </div>
      
      <div class="flex space-x-6">
        <!-- 右侧底部控制按钮 -->
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12H3m9-9v18" />
          </svg>
        </button>
        <button class="p-2 opacity-70 hover:opacity-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
    
  </div>
</body>
</html>
