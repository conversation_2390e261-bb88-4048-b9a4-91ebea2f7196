<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">用车助手</h1>
      </div>

      <!-- 搜索框 -->
      <form @submit.prevent="generateAdvice" class="flex items-center mb-6">
        <label for="prompt" class="block text-sm font-medium text-purple-200 mr-2">
          请求描述 <span class="text-red-400">*</span>
        </label>
        <input id="prompt" v-model="prompt" type="text" class="flex-1 px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="请输入您的用车相关请求..." required>
        <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center ml-2" :disabled="isGenerating">
          <svg v-if="isGenerating" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isGenerating ? '处理中...' : '开始处理' }}
        </button>
      </form>

      <!-- 试一试标签区域 -->
      <div class="mb-6">
        <p class="text-purple-200 text-sm mb-2">试一试：</p>
        <div class="flex flex-wrap gap-2">
          <button 
            v-for="(tag, index) in suggestionTags" 
            :key="index"
            @click="handleTagClick(tag)"
            class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors duration-200"
          >
            {{ tag }}
          </button>
        </div>
      </div>

      <!-- 大段文本区域展示助手回复 -->
      <div v-if="assistantResponse" class="bg-dark-800/60 rounded-lg p-8 overflow-y-auto mb-6 markdown-content" style="min-height: 200px;">
        <div v-html="assistantResponse" class="text-white"></div>
      </div>

      <!-- Tooltip 组件 -->
      <Tooltip
        :show="showTooltip"
        :content="tooltipData.content"
        :document-name="tooltipData.documentName"
        :image-id="tooltipData.imageId"
        :position="tooltipData.position"
        @close="handleCloseTooltip"
      />

      <!-- 操作按钮 -->
      <div class="flex justify-end mt-4" v-if="assistantResponse">
        <button @click="copyResult" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium mr-3 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002-2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
          </svg>
          复制
        </button>
      </div>
      <div v-else-if="!isGenerating" class="bg-dark-800/60 rounded-lg p-8 flex flex-col items-center justify-center min-h-[200px] mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-purple-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path d="M4 4h16v16H4V4z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" />
          <path d="M8 8h8" />
          <path d="M8 12h8" />
          <path d="M8 16h8" />
        </svg>
        <p class="text-purple-200 text-center mb-2">尚未收到回复</p>
        <p class="text-gray-400 text-sm text-center">填写请求并点击"开始处理"按钮</p>
      </div>

      <!-- doc_aggs 列表展示 -->
      <!-- <div v-if="docAggs.length" class="mb-6">
        <h2 class="text-2xl font-bold text-purple-200 mb-4">文档聚合</h2>
        <div v-for="(agg, index) in docAggs" :key="index" class="p-4 bg-dark-800/60 rounded-lg mb-2 flex items-center justify-between">
          <p class="text-white">{{ agg.doc_name }}</p>
          <p class="text-white">{{ agg.count }}</p>
        </div>
      </div> -->

      <!-- chunks 列表展示 -->
      <!-- <div v-if="chunks.length" class="mb-6">
        <h2 class="text-2xl font-bold text-purple-200 mb-4">详细内容</h2>
        <div v-for="(chunk, index) in chunks" :key="index" class="p-4 bg-dark-800/60 rounded-lg mb-2 flex items-center justify-between">
          <div class="flex flex-col">
            <p class="text-white" v-html="chunk.highlight"></p>
            <p class="text-gray-400">{{ chunk.document_keyword }}</p>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted } from "vue";
import { RAG_API_ASK_URL, RAG_API_KEY } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import MarkdownIt from 'markdown-it';
import Tooltip from "../components/Tooltip.vue";

const prompt = ref("");
const isGenerating = ref(false);
const assistantResponse = ref(null);
const chunks = ref([]);
const showTooltip = ref(false);
const tooltipData = ref({
  content: '',
  documentName: '',
  imageId: '',
  position: { x: 0, y: 0 }
});

const suggestionTags = ref([
  "如何正确使用自动驻车功能？",
  "车辆保养周期是多久？",
  "氛围灯设置"
]);

const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true
});

// 处理特殊标记
const processSpecialMarkers = (text: string) => {
  return text.replace(/##(\d+)\$\$/g, (match, number) => {
    return `<span class="special-marker" data-number="${number}">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block" viewBox="0 0 20 20" fill="white">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
      </svg>
    </span>`;
  });
};

// 处理标记悬停事件
const handleMarkerHover = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const marker = target.closest('.special-marker');
  if (marker) {
    const number = marker.getAttribute('data-number');
    if (number) {
      const index = parseInt(number);
      const chunk = chunks.value[index];
      if (chunk) {
        tooltipData.value = {
          content: chunk.content,
          documentName: chunk.document_name,
          imageId: chunk.image_id,
          position: {
            x: event.clientX,
            y: event.clientY
          }
        };
        showTooltip.value = true;
      }
    }
  }
};

// 处理关闭提示框
const handleCloseTooltip = () => {
  showTooltip.value = false;
};

// 处理点击外部区域关闭提示框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const isMarker = target.closest('.special-marker');
  const isTooltip = target.closest('.tooltip-container');
  
  if (!isMarker && !isTooltip) {
    showTooltip.value = false;
  }
};

// 生成场景
const generateAdvice = async () => {
  if (!prompt.value.trim()) {
    alert("请输入用车请求描述");
    return;
  }

  try {
    isGenerating.value = true;
    assistantResponse.value = null;
    chunks.value = [];

    const askResponse = await fetch(`${RAG_API_ASK_URL}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAG_API_KEY}`
      },
      body: JSON.stringify({
        question: prompt.value,
        stream: true,
        session_id: "3106593ab34d494f892b1e890b10b57d"
      })
    });

    if (askResponse.ok && askResponse.body) {
      const reader = askResponse.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let done = false;
      let accumulatedAnswer = '';

      while (!done) {
        const { done: readerDone, value } = await reader.read();
        done = readerDone;
        const chunk = decoder.decode(value, { stream: !done });
        accumulatedAnswer += chunk;

        const lines = accumulatedAnswer.replace("/\\n+/", "\\n").split(/\n+/);
        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.substring(5);
            try {
              const parsedData = JSON.parse(data);
              if (parsedData.data && parsedData.data.answer) {
                const processedText = parsedData.data.answer;
                const renderedMarkdown = md.render(processedText);
                assistantResponse.value = processSpecialMarkers(renderedMarkdown);
                
                if (parsedData.data.reference && parsedData.data.reference.chunks) {
                  chunks.value = parsedData.data.reference.chunks;
                }
                
                nextTick(() => {
                  const markers = document.querySelectorAll('.special-marker');
                  markers.forEach(marker => {
                    marker.addEventListener('mouseenter', handleMarkerHover);
                  });
                });
              }
            } catch (error) {
              // 处理错误
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    isGenerating.value = false;
  }
};

// 复制结果
const copyResult = () => {
  if (assistantResponse.value) {
    navigator.clipboard.writeText(assistantResponse.value);
  }
};

// 处理标签点击
const handleTagClick = (tag: string) => {
  prompt.value = tag;
  generateAdvice();
};

// 在组件挂载后添加事件监听
onMounted(() => {
  nextTick(() => {
    document.addEventListener('mouseover', handleMarkerHover);
    document.addEventListener('click', handleClickOutside);
  });
});

// 在组件卸载前移除事件监听
onUnmounted(() => {
  document.removeEventListener('mouseover', handleMarkerHover);
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
:deep(em) {
  color: #ff6347;
}

:deep(.markdown-content) {
  line-height: 1.6;
}

:deep(.markdown-content h1) {
  font-size: 2em;
  margin-bottom: 0.5em;
  color: #e9d8fd;
}

:deep(.markdown-content h2) {
  font-size: 1.5em;
  margin-bottom: 0.5em;
  color: #e9d8fd;
}

:deep(.markdown-content h3) {
  font-size: 1.25em;
  margin-bottom: 0.5em;
  color: #e9d8fd;
}

:deep(.markdown-content p) {
  margin-bottom: 1em;
}

:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

:deep(.markdown-content li) {
  margin-bottom: 0.5em;
}

:deep(.markdown-content code) {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: monospace;
}

:deep(.markdown-content pre) {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
}

:deep(.markdown-content blockquote) {
  border-left: 4px solid #7c3aed;
  padding-left: 1em;
  margin-left: 0;
  color: #a78bfa;
}

:deep(.markdown-content a) {
  color: #93c5fd;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

:deep(.special-marker) {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: #7c3aed;
  transition: color 0.2s ease;
}

:deep(.special-marker:hover) {
  color: #a78bfa;
}

:deep(.special-marker svg) {
  margin: 0 2px;
  vertical-align: middle;
}
</style>
