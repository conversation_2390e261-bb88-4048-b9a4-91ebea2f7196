# 主题生成服务 API 文档

## 基础信息

- 基础路径: `/api/v1`
- 响应格式: JSON
- 编码方式: UTF-8

## 接口列表

### 1. 启动主题生成任务

启动一个新的主题生成任务，包括壁纸生成、UI生成、切分、图标细化、内容页面生成等一系列流程。

#### 请求信息

- 接口路径：`POST /api/v1/pipeline`
- Content-Type: `application/json`

#### 请求参数

| 参数名  | 类型   | 必填 | 说明         | 示例                                 |
| ------- | ------ | ---- | ------------ | ------------------------------------ |
| prompt  | string | 是   | 主题描述文本 | "帮我生成一个中国风的西湖风光的主题" |
| task_id | string | 是   | 任务ID       | "task_20240318_001"                  |

#### 请求示例

```json
{
    "prompt": "帮我生成一个中国风的西湖风光的主题",
    "task_id": "task_20240318_001"
}
```

#### 响应参数

| 参数名  | 类型   | 说明             | 示例                |
| ------- | ------ | ---------------- | ------------------- |
| task_id | string | 传入的任务ID     | "task_20240318_001" |
| message | string | 任务启动状态描述 | "Pipeline started"  |
| status  | string | 任务初始状态     | "pending"           |

#### 响应示例

```json
{
    "task_id": "task_20240318_001",
    "message": "Pipeline started",
    "status": "pending"
}
```

### 2. 查询任务状态

查询主题生成任务的当前执行状态和进度。

#### 请求信息

- 接口路径：`GET /api/v1/pipeline/status/{task_id}`
- 路径参数：task_id - 服务端生成的任务ID

#### 响应参数

| 参数名       | 类型         | 说明                   | 示例             |
| ------------ | ------------ | ---------------------- | ---------------- |
| status       | string       | 任务状态               | "running"        |
| progress     | number       | 总体进度(0-100)        | 45.5             |
| current_step | string       | 当前执行的步骤         | "parallel_tasks" |
| error        | string\|null | 错误信息，成功时为null | null             |

#### 状态说明

- pending: 等待开始
- running: 正在执行
- completed: 任务执行完成（中间状态）
- success: 任务成功并打包完成
- failed: 执行失败
- terminated: 任务被手动终止
- interrupted: 任务执行被中断（针对子任务响应）

#### 步骤说明（中文对照）

| 步骤英文名           | 中文描述     |
| -------------------- | ------------ |
| wallpaper_generation | 壁纸生成     |
| ui_generation        | UI界面生成   |
| ui_split             | UI元素切分   |
| parallel_tasks       | 并行处理任务 |
| color_extraction     | 主题色提取   |
| convert_to_jpg       | 格式转换     |
| create_archive       | 打包发布     |

#### 响应示例

成功状态：

```json
{
    "status": "running",
    "progress": 45.5,
    "current_step": "parallel_tasks",
    "error": null
}
```

失败状态：

```json
{
    "status": "failed",
    "progress": 35.0,
    "current_step": "color_extraction",
    "error": "Failed to extract colors: Image not found"
}
```

中断状态：

```json
{
    "status": "terminated",
    "progress": 35.0,
    "current_step": "ui_generation",
    "error": "Task manually terminated"
}
```

### 3. 终止任务

手动终止正在执行的任务。

#### 请求信息

- 接口路径：`POST /api/terminate-task/{task_id}`
- 路径参数：task_id - 需要终止的任务ID

#### 响应参数

| 参数名  | 类型   | 说明         | 示例                           |
| ------- | ------ | ------------ | ------------------------------ |
| status  | string | 操作状态     | "success"                      |
| task_id | string | 任务ID       | "task_20240318_001"            |
| message | string | 操作结果描述 | "Task terminated successfully" |

#### 响应示例

```json
{
    "status": "success",
    "task_id": "task_20240318_001",
    "message": "Task terminated successfully"
}
```

如果任务已经处于终止状态：

```json
{
    "status": "warning",
    "task_id": "task_20240318_001",
    "message": "Task already in final state: terminated"
}
```

### 4. 下载生成结果

下载已完成任务的压缩包文件。

#### 请求信息

- 接口路径：`GET /api/download-archive/{task_id}`
- 路径参数：task_id - 任务ID

#### 响应说明

- Content-Type: `application/zip`
- 文件名格式：`mainLine_{task_id}_release.zip`

#### 压缩包内容

```
mainLine_{task_id}_release.zip
├── wallpaper/          # 壁纸图片
├── icons/             # 细化后的图标
└── colors.json        # 主题配色方案
```

#### 错误响应

当压缩包不存在时：

```json
{
    "detail": "Archive not found"
}
```

#### 使用示例

```bash
# 使用curl下载
curl -O -J "http://your-server/api/download-archive/a1b2c3d4"

# 或者直接在浏览器访问
http://your-server/api/download-archive/a1b2c3d4
```

### 5. SU7车衣生成接口

根据用户输入的提示词，生成车辆纹理效果图。

#### 请求信息

- 接口路径：`POST /api/v1/car-texture`
- Content-Type: `application/json`

#### 请求参数

| 参数名  | 类型   | 必填 | 说明           | 示例                |
| ------- | ------ | ---- | -------------- | ------------------- |
| prompt  | string | 是   | 车衣风格提示词 | "超级赛亚人"        |
| task_id | string | 是   | 任务ID         | "task_20240328_001" |

#### 请求示例

```json
{
    "prompt": "超级赛亚人",
    "task_id": "task_20240328_001"
}
```

#### 响应参数

| 参数名    | 类型   | 说明                | 示例                                                                         |
| --------- | ------ | ------------------- | ---------------------------------------------------------------------------- |
| prompt_id | string | 生成任务的唯一标识  | "05d9b361-b45f-471d-aae5-a5a3c4c409c9"                                       |
| image_url | string | 生成的车衣纹理图URL | "/ui_source/mainLine_task_20240328_001/releases/car_texture/car_texture.png" |

#### 响应示例

```json
{
    "prompt_id": "05d9b361-b45f-471d-aae5-a5a3c4c409c9",
    "image_url": "/ui_source/mainLine_task_20240328_001/releases/car_texture/car_texture.png"
}
```

#### 调用示例

```bash
curl -X POST http://***********:18632/api/v1/car-texture \
  -H "Content-Type: application/json" \
  -d '{"prompt": "超级赛亚人", "task_id": "task_20240328_001"}' | jq
```

## 注意事项

1. 任务执行时间较长，建议采用轮询方式查询任务状态
2. 轮询间隔建议不小于1秒
3. 单个任务总执行时间可能达到15-20分钟
4. 任务完成后，生成的文件将打包在 `public/archives/mainLine_{task_id}_release.zip` 中
5. 如需终止任务，可以调用终止接口，系统会自动清理ComfyUI队列并中断当前任务
6. 任务终止后，相关的图像生成请求也会自动停止，不会继续等待结果

## 接口调用示例

### 服务地址
- 测试环境：http://***********:18632
- 生产环境：待定

### 完整调用流程

1. 启动主题生成任务：
```bash
curl -X POST http://***********:18632/api/v1/pipeline \
  -H "Content-Type: application/json" \
  -d '{"prompt": "生成一个中国风的西湖风光主题", "task_id": "test001"}' | jq
```

预期响应：
```json
{
    "task_id": "test001",
    "message": "Pipeline started",
    "status": "pending"
}
```

2. 查询任务状态：
```bash
curl -X GET http://***********:18632/api/v1/pipeline/status/test001 | jq
```

3. 终止任务（如果需要）：
```bash
curl -X POST http://***********:18632/api/terminate-task/test001 | jq
```

4. 下载生成结果（任务成功完成后）：
```bash
curl -O -J "http://***********:18632/api/download-archive/test001"
```