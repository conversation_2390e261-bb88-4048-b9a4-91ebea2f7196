<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">语宙回声</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧上传区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">上传音频</h2>
            <form @submit.prevent="processAudio">
              <div class="mb-6">
                <label for="audio-file" class="block text-sm font-medium text-purple-200 mb-2">
                  选择音频文件 <span class="text-red-400">*</span>
                </label>
                <input type="file" id="audio-file" @change="handleFileUpload" accept="audio/*" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" />
                <p class="text-xs text-gray-400 mt-2" v-if="audioFile">
                  选择的文件：{{ audioFile.name }}
                </p>
              </div>
              <div class="mb-6">
                <label class="block text-sm font-medium text-purple-200 mb-2">
                  或选择音频文件 <span class="text-red-400">*</span>
                </label>
                <select v-model="selectedAudio" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                  <option v-for="option in audioOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                </select>
              </div>
              <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="(!audioFile && !selectedAudio) || isProcessing || isProcessingFile">
                  <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isProcessing ? '处理中...' : '开始处理' }}
                </button>
              </div>
            </form>
          </GlassPanel>
        </div>

        <!-- 右侧文本区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">输入文本</h2>
            <div class="mb-6">
              <label for="text-input" class="block text-sm font-medium text-purple-200 mb-2">
                输入文本 <span class="text-red-400">*</span>
              </label>
              <textarea id="textInput" v-model="textInput" @input="validateTextInput" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" rows="4" placeholder="请输入您想要克隆的声音文本..."></textarea>
              <p class="text-xs text-gray-400 mt-2">
                字符数: {{ textInput.length }} / 200
              </p>
              <p class="text-xs text-red-400 mt-2" v-if="!isValidTextInput">
                文本长度应在10到200个字符之间
              </p>
            </div>
            <div class="mb-6">
              <label class="block text-sm font-medium text-purple-200 mb-2">
                或选择文本
              </label>
              <select v-model="selectedText" @change="updateTextInput" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                <option v-for="option in textOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
              </select>

            </div>
          </GlassPanel>
        </div>
      </div>

      <!-- 底部播放区域 -->
      <div v-if="generatedAudioUrl" class="mt-8">
        <GlassPanel class="p-6">
          <h2 class="text-2xl font-bold text-purple-200 mb-6">回声播放</h2>
          <div class="bg-dark-800/60 rounded-lg p-8 overflow-y-auto flex items-center justify-center" style="min-height: 300px;">
            <audio :src="generatedAudioUrl" controls class="w-full max-w-lg">
              您的浏览器不支持 audio 元素。
            </audio>
          </div>
        </GlassPanel>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, onMounted } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { Howl } from 'howler';

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
    () => import("../components/ProgressBar.vue")
);

const audioFile = ref<File | null>(null);
const isProcessing = ref<boolean>(false);
const processingProgress = ref<number>(0);
const generatedAudioUrl = ref<string | null>(null);
const taskId = ref<string>("");
const textInput = ref<string>("");
const textOptions = ref([
  { label: "语宙回声", value: "语宙回声：声纹重构革命下的数字分身时代。在人工智能与生物声学交汇的坐标点上，声音克隆技术正开启声纹数字化重构的新纪元。通过深度神经网络对声波图谱的解构分析，系统可精准捕捉基频、共振峰等143项声纹特征参数，实现音色指纹的量子级解析。当这项技术遇见多模态模型，便催生出声纹迁移的魔法——只需10秒钟语音样本，即可在文字宇宙中投射出具有情感温度的声音数字分身。" },
  { label: "智能座舱", value: "超维智能座舱数字全息系统，生成式AI引擎，AI原子能力，自由编织，会呼吸的移动数字空间。这些关键词逐一展开，生成式AI引擎指的是利用生成式AI技术来动态生成内容，比如全息图像或交互界面。AI原子能力指模块化的AI功能，可以灵活组合。自由编织意味着这些模块可以按需组合，创造不同的体验。会呼吸的空间指动态变化、有生命感的数字环境。" }
]);
const selectedText = ref<string>(textOptions.value[0].value);
const audioOptions = ref([
  { label: "女声", value: "三月七十秒.MP3" },
  { label: "男声", value: "春.MP3" }
]);
const selectedAudio = ref<string>(audioOptions.value[0].value);
const isValidTextInput = ref<boolean>(true);
const isProcessingFile = ref(false);

// 初始化时将默认文本显示在输入框中
onMounted(() => {
  textInput.value = selectedText.value;
});

// 验证音频时长
const isValidDuration = (duration: number): boolean => {
  return duration >= 3 && duration < 11;
};

// 处理音频文件上传
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length) {
    const file = target.files[0];
    const fileName = file.name;
    const fileExtension = fileName.split('.').pop()?.toLowerCase(); // 获取文件扩展名
    isProcessingFile.value = true; // 开始处理时禁用按钮
    const audio = new Howl({
      src: [URL.createObjectURL(file)],
      format: [fileExtension] // 根据你的音频文件格式进行
    });
    audio.once('load', () => {
      audioFile.value = file;
      if (!isValidDuration(audio.duration())) {
        alert("音频时长应在3秒到10秒内");
        target.value = ''; // 清空文件上传输入框的内容
      }
      isProcessingFile.value = false; // 处理完成后启用按钮
    });
  }
};

// 验证文本输入
const validateTextInput = () => {
  isValidTextInput.value = textInput.value.length >= 10 && textInput.value.length <= 200;
};

// 更新文本输入框的值
const updateTextInput = () => {
  textInput.value = selectedText.value;
  validateTextInput();
};

// 处理音频
const processAudio = async () => {
  if (isProcessingFile.value) return; // 如果正在处理，不执行任何操作
  if (!audioFile.value && !selectedAudio.value) {
    alert("请上传音频文件或选择音频");
    return;
  }
  if (!textInput.value) {
    alert("请输入文本");
    return;
  }

  try {
    isProcessing.value = true;
    processingProgress.value = 10; // 初始进度
    generatedAudioUrl.value = null;

    taskId.value = generateTaskId();

    // 调用API
    const formData = new FormData();
    if (audioFile.value) {
      formData.append("audio", audioFile.value);
    } else {
      formData.append("audio_name", selectedAudio.value);
    }
    formData.append("text", textInput.value);
    formData.append("task_id", taskId.value);

    // 模拟API调用
    const response = await themeApi.processAudio(formData);
    if (response.audio_url) {
      generatedAudioUrl.value = response.audio_url;
    }

    processingProgress.value = 100;
  } catch (error) {
    console.error("处理音频请求失败", error);
    alert("处理音频请求失败，请重试");
  } finally {
    isProcessing.value = false;
  }
};

// 生成任务ID
const generateTaskId = (): string => {
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}
</style>
