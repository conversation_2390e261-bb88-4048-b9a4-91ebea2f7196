<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态界面内容渲染器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    },
                    animation: {
                        'spin-slow': 'spin 3s linear infinite',
                    }
                }
            }
        }
    </script>
    <style type="text/css">
        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1);
                opacity: 0.4;
            }
            100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
        }
        .pulse-ring {
            animation: pulse-ring 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <div class="container mx-auto px-4 py-8 flex-grow">
        <header class="mb-12 text-center">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">动态HTML内容渲染</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-300">从API获取并渲染HTML内容</p>
        </header>

        <main>
            <!-- 渲染器容器 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">内容预览</h2>
                        <button id="refreshBtn" class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors duration-200 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            刷新内容
                        </button>
                    </div>
                    
                    <!-- 内容渲染区域 -->
                    <div id="contentContainer" class="relative min-h-[400px] bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                        <!-- 占位符内容 -->
                        <div id="placeholder" class="absolute inset-0 flex flex-col items-center justify-center p-8 text-center">
                            <img src="https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                                 alt="空白文档" class="w-32 h-32 object-cover rounded-lg mb-4 opacity-80">
                            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200 mb-2">暂无内容</h3>
                            <p class="text-gray-500 dark:text-gray-400 max-w-md">
                                点击上方的"刷新内容"按钮从API获取动态HTML内容并在此处渲染。
                            </p>
                        </div>
                        
                        <!-- 加载动画 (初始隐藏) -->
                        <div id="loader" class="absolute inset-0 flex items-center justify-center hidden">
                            <div class="relative">
                                <div class="w-20 h-20 border-4 border-primary-200 border-t-primary-500 rounded-full animate-spin"></div>
                                <div class="w-20 h-20 border-4 border-primary-500 border-opacity-20 rounded-full absolute top-0 left-0 pulse-ring"></div>
                            </div>
                        </div>
                        
                        <!-- 错误提示 (初始隐藏) -->
                        <div id="errorMessage" class="absolute inset-0 flex flex-col items-center justify-center p-8 text-center hidden">
                            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-red-600 dark:text-red-400 mb-2">加载失败</h3>
                            <p class="text-gray-500 dark:text-gray-400 max-w-md">
                                无法从API获取HTML内容，请检查网络连接或稍后再试。
                            </p>
                        </div>
                        
                        <!-- 动态内容将在这里渲染 -->
                        <div id="dynamicContent" class="p-6 hidden"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer class="mt-auto py-6 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div class="container mx-auto px-4 text-center text-gray-600 dark:text-gray-400">
            <p>© 2023 动态内容渲染器 | 设计：寂寞的熊猫</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contentContainer = document.getElementById('contentContainer');
            const dynamicContent = document.getElementById('dynamicContent');
            const placeholder = document.getElementById('placeholder');
            const loader = document.getElementById('loader');
            const errorMessage = document.getElementById('errorMessage');
            const refreshBtn = document.getElementById('refreshBtn');
            
            // 显示加载状态
            function showLoading() {
                placeholder.classList.add('hidden');
                errorMessage.classList.add('hidden');
                dynamicContent.classList.add('hidden');
                loader.classList.remove('hidden');
            }
            
            // 显示错误信息
            function showError() {
                loader.classList.add('hidden');
                placeholder.classList.add('hidden');
                dynamicContent.classList.add('hidden');
                errorMessage.classList.remove('hidden');
            }
            
            // 显示动态内容
            function showContent(htmlContent) {
                loader.classList.add('hidden');
                placeholder.classList.add('hidden');
                errorMessage.classList.add('hidden');
                
                // 安全地渲染HTML内容
                dynamicContent.innerHTML = htmlContent;
                dynamicContent.classList.remove('hidden');
            }
            
            // 重置为占位符状态
            function showPlaceholder() {
                loader.classList.add('hidden');
                errorMessage.classList.add('hidden');
                dynamicContent.classList.add('hidden');
                placeholder.classList.remove('hidden');
            }
            
            // 从API获取HTML内容
            async function fetchHtmlContent() {
                showLoading();
                
                try {
                    // 这里模拟API调用，实际项目中替换为真实API
                    const response = await fetch('/api/get-dynamic-html');
                    
                    if (!response.ok) {
                        throw new Error('API请求失败');
                    }
                    
                    const htmlContent = await response.text();
                    
                    // 如果返回的内容为空，显示占位符
                    if (!htmlContent.trim()) {
                        showPlaceholder();
                        return;
                    }
                    
                    showContent(htmlContent);
                } catch (error) {
                    console.error('获取HTML内容失败:', error);
                    showError();
                }
            }
            
            // 绑定刷新按钮事件
            refreshBtn.addEventListener('click', fetchHtmlContent);
            
            // 添加模拟数据功能（仅用于演示）
            let demoMode = true;
            
            if (demoMode) {
                // 重写fetchHtmlContent函数用于演示
                fetchHtmlContent = function() {
                    showLoading();
                    
                    // 模拟API延迟
                    setTimeout(() => {
                        // 随机决定是否显示错误状态（演示用）
                        const showErrorDemo = Math.random() < 0.3;
                        
                        if (showErrorDemo) {
                            showError();
                            return;
                        }
                        
                        // 示例HTML内容
                        const demoHtml = `
                            <div class="space-y-6">
                                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">欢迎使用动态HTML渲染器</h2>
                                <p class="text-gray-600 dark:text-gray-300">这是从API获取的动态HTML内容示例。</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                                        <h3 class="font-medium text-blue-700 dark:text-blue-300">功能一</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">这是动态渲染的功能描述。</p>
                                    </div>
                                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                        <h3 class="font-medium text-green-700 dark:text-green-300">功能二</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">这是另一个动态渲染的功能描述。</p>
                                    </div>
                                </div>
                                <img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-1.2.1&auto=format&fit=crop&w=1600&q=80" 
                                     alt="示例图片" class="w-full h-48 object-cover rounded-lg">
                            </div>
                        `;
                        
                        showContent(demoHtml);
                    }, 1500);
                };
            }
        });
    </script>
</body>
</html>
