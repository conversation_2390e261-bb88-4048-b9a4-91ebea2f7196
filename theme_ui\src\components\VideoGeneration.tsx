import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

// 本地存储键
const BACKEND_URL_KEY = 'video_backend_url';
const DEFAULT_BACKEND_URL = 'http://127.0.0.1:8001';

interface VideoGenerationFormData {
  positive_prompt: string;
  negative_prompt: string;
  width: number;
  height: number;
  num_frames: number;
  frame_rate: number;
  steps: number;
  cfg: number;
  seed?: number | null;
}

interface TaskStatus {
  task_id: string;
  status: string;
  progress: number;
  message: string;
  result_url?: string;
  start_time?: string;
  estimated_time_remaining?: string;
}

const VideoGeneration: React.FC = () => {
  const navigate = useNavigate();
  
  // 获取后端URL
  const getBackendUrl = () => {
    return localStorage.getItem(BACKEND_URL_KEY) || DEFAULT_BACKEND_URL;
  };
  
  // 状态管理
  const [backendUrl, setBackendUrl] = useState<string>(getBackendUrl());
  const [formData, setFormData] = useState<VideoGenerationFormData>({
    positive_prompt: '一位未来战士，身着融合了中国传统盔甲元素的机甲，站在未来的城市废墟中，缓步走来。中景，镜头缓缓拉近，展现机甲的细节。周身环绕着粒子特效，充满科技感、史诗感和力量感。',
    negative_prompt: '色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走',
    width: 832,
    height: 480,
    num_frames: 200,
    frame_rate: 16,
    steps: 20,
    cfg: 6.0,
    seed: null,
  });
  
  const [isConfiguring, setIsConfiguring] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [currentTask, setCurrentTask] = useState<TaskStatus | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const statusCheckInterval = useRef<number | null>(null);
  
  // 生成视频
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSubmitting(true);
      
      const response = await axios.post<TaskStatus>(
        `${backendUrl}/api/videos/generate`,
        formData,
        { headers: { 'Content-Type': 'application/json' } }
      );
      
      setCurrentTask(response.data);
      toast.success('视频生成任务已提交！');
      
      // 开始轮询状态
      if (statusCheckInterval.current) {
        window.clearInterval(statusCheckInterval.current);
      }
      
      // @ts-ignore (setTimeout vs setInterval type issue)
      statusCheckInterval.current = window.setInterval(() => {
        checkTaskStatus(response.data.task_id);
      }, 3000);
      
    } catch (error) {
      console.error('提交视频生成任务失败', error);
      toast.error('提交视频生成任务失败，请检查后端连接设置');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 检查任务状态
  const checkTaskStatus = async (taskId: string) => {
    try {
      const response = await axios.get<TaskStatus>(
        `${backendUrl}/api/videos/${taskId}/status`
      );
      
      setCurrentTask(response.data);
      
      // 任务完成或失败时停止轮询
      if (['completed', 'failed'].includes(response.data.status)) {
        if (statusCheckInterval.current) {
          window.clearInterval(statusCheckInterval.current);
          statusCheckInterval.current = null;
        }
        
        if (response.data.status === 'completed' && response.data.result_url) {
          toast.success('视频生成成功！');
          // 设置视频URL
          setVideoUrl(`${backendUrl}${response.data.result_url}`);
        } else if (response.data.status === 'failed') {
          toast.error(`视频生成失败: ${response.data.message}`);
        }
      }
    } catch (error) {
      console.error('检查任务状态失败', error);
      // 出错时不停止轮询，可能是暂时性网络问题
    }
  };
  
  // 保存后端URL设置
  const saveBackendUrl = () => {
    localStorage.setItem(BACKEND_URL_KEY, backendUrl);
    setIsConfiguring(false);
    toast.success('后端URL已保存');
  };
  
  // 取消任务
  const cancelTask = () => {
    if (statusCheckInterval.current) {
      window.clearInterval(statusCheckInterval.current);
      statusCheckInterval.current = null;
    }
    
    setCurrentTask(null);
    setVideoUrl(null);
    toast.success('任务已取消');
  };
  
  // 回到主页
  const goBackToHome = () => {
    if (statusCheckInterval.current) {
      window.clearInterval(statusCheckInterval.current);
    }
    navigate('/');
  };
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (statusCheckInterval.current) {
        window.clearInterval(statusCheckInterval.current);
      }
    };
  }, []);
  
  return (
    <div className="min-h-screen bg-dark-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* 顶部导航 */}
        <div className="glass-card py-5 px-10 flex items-center mb-8">
          <button 
            className="text-gray-300 hover:text-white mr-5"
            onClick={goBackToHome}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-3xl font-bold text-white">文生视频</h1>
          <div className="ml-auto">
            <button
              onClick={() => setIsConfiguring(!isConfiguring)}
              className="bg-dark-700/50 hover:bg-dark-700 text-white py-2 px-4 rounded-lg transition"
            >
              设置
            </button>
          </div>
        </div>
        
        {/* 后端配置区域 */}
        {isConfiguring && (
          <div className="glass-card rounded-xl p-6 mb-8">
            <h2 className="text-xl font-bold mb-4">后端设置</h2>
            <div className="mb-4">
              <label htmlFor="backendUrl" className="block text-gray-300 mb-2">
                后端URL
              </label>
              <input
                id="backendUrl"
                type="text"
                value={backendUrl}
                onChange={(e) => setBackendUrl(e.target.value)}
                className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                placeholder="例如: http://127.0.0.1:8001"
              />
              <p className="text-gray-400 text-sm mt-2">
                默认URL: {DEFAULT_BACKEND_URL}
              </p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={() => setIsConfiguring(false)}
                className="py-2 px-4 bg-dark-700/50 hover:bg-dark-700 text-white rounded-lg transition mr-3"
              >
                取消
              </button>
              <button
                onClick={saveBackendUrl}
                className="py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-lg transition"
              >
                保存设置
              </button>
            </div>
          </div>
        )}
        
        {/* 主要内容 */}
        <div className="glass-card rounded-xl p-8">
          {!currentTask ? (
            <>
              <h2 className="text-3xl font-bold mb-8 text-center text-green-300">
                创建文生视频
              </h2>
              
              {/* 表单 */}
              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* 左侧：提示词输入 */}
                  <div className="space-y-6">
                    <div>
                      <label htmlFor="positive_prompt" className="block text-gray-300 text-lg mb-2">
                        正向提示词
                      </label>
                      <textarea
                        id="positive_prompt"
                        value={formData.positive_prompt}
                        onChange={(e) => setFormData({...formData, positive_prompt: e.target.value})}
                        className="w-full p-4 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none resize-none h-52 text-lg"
                        placeholder="描述您想要的视频内容和风格..."
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="negative_prompt" className="block text-gray-300 text-lg mb-2">
                        负向提示词
                      </label>
                      <textarea
                        id="negative_prompt"
                        value={formData.negative_prompt}
                        onChange={(e) => setFormData({...formData, negative_prompt: e.target.value})}
                        className="w-full p-4 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none resize-none h-24 text-lg"
                        placeholder="描述您希望避免出现的内容和风格..."
                      />
                    </div>
                  </div>
                  
                  {/* 右侧：参数设置 */}
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="width" className="block text-gray-300 mb-2">
                          宽度
                        </label>
                        <input
                          id="width"
                          type="number"
                          value={formData.width}
                          onChange={(e) => setFormData({...formData, width: parseInt(e.target.value)})}
                          className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                          min="64"
                          max="1280"
                          step="64"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="height" className="block text-gray-300 mb-2">
                          高度
                        </label>
                        <input
                          id="height"
                          type="number"
                          value={formData.height}
                          onChange={(e) => setFormData({...formData, height: parseInt(e.target.value)})}
                          className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                          min="64"
                          max="1280"
                          step="64"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="num_frames" className="block text-gray-300 mb-2">
                          总帧数
                        </label>
                        <input
                          id="num_frames"
                          type="number"
                          value={formData.num_frames}
                          onChange={(e) => setFormData({...formData, num_frames: parseInt(e.target.value)})}
                          className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                          min="50"
                          max="500"
                        />
                        <p className="text-gray-400 text-sm mt-1">
                          决定视频长度，200帧约为12秒
                        </p>
                      </div>
                      
                      <div>
                        <label htmlFor="frame_rate" className="block text-gray-300 mb-2">
                          帧率
                        </label>
                        <select
                          id="frame_rate"
                          value={formData.frame_rate}
                          onChange={(e) => setFormData({...formData, frame_rate: parseInt(e.target.value)})}
                          className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                        >
                          <option value="12">12fps</option>
                          <option value="16">16fps</option>
                          <option value="24">24fps</option>
                          <option value="30">30fps</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="steps" className="block text-gray-300 mb-2">
                          采样步数
                        </label>
                        <input
                          id="steps"
                          type="range"
                          min="10"
                          max="30"
                          step="1"
                          value={formData.steps}
                          onChange={(e) => setFormData({...formData, steps: parseInt(e.target.value)})}
                          className="w-full"
                        />
                        <div className="flex justify-between text-gray-400 text-sm">
                          <span>10</span>
                          <span>{formData.steps}</span>
                          <span>30</span>
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="cfg" className="block text-gray-300 mb-2">
                          CFG值
                        </label>
                        <input
                          id="cfg"
                          type="range"
                          min="1"
                          max="15"
                          step="0.5"
                          value={formData.cfg}
                          onChange={(e) => setFormData({...formData, cfg: parseFloat(e.target.value)})}
                          className="w-full"
                        />
                        <div className="flex justify-between text-gray-400 text-sm">
                          <span>1</span>
                          <span>{formData.cfg}</span>
                          <span>15</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="seed" className="block text-gray-300 mb-2">
                        随机种子 (可选)
                      </label>
                      <input
                        id="seed"
                        type="number"
                        value={formData.seed === null ? '' : formData.seed}
                        onChange={(e) => {
                          const val = e.target.value === '' ? null : parseInt(e.target.value);
                          setFormData({...formData, seed: val});
                        }}
                        className="w-full p-3 bg-dark-800/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none"
                        placeholder="留空使用随机种子"
                      />
                    </div>
                    
                    <div className="bg-yellow-900/20 border border-yellow-700/30 rounded-lg p-4 text-yellow-200 text-sm">
                      <p className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>
                          视频生成可能需要较长时间（约15-30分钟），请耐心等待。视频长度约为{Math.round(formData.num_frames / formData.frame_rate)}秒。
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* 提交按钮 */}
                <div className="mt-10 flex justify-center">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="py-4 px-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white rounded-lg transition duration-300 text-lg font-semibold flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        提交中...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        开始生成视频
                      </>
                    )}
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div>
              <h2 className="text-3xl font-bold mb-8 text-center text-green-300">
                视频生成进度
              </h2>
              
              {/* 进度信息 */}
              <div className="max-w-3xl mx-auto mb-8">
                <div className="flex justify-between mb-3">
                  <h3 className="text-2xl font-bold text-green-200">当前进度</h3>
                  <span className="text-green-300 font-semibold text-xl">{Math.round(currentTask.progress)}%</span>
                </div>
                
                <div className="h-6 bg-dark-700 rounded-full overflow-hidden mb-4">
                  <div 
                    className="h-full bg-gradient-to-r from-green-600 to-green-400 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${currentTask.progress}%` }}
                  ></div>
                </div>
                
                <div className="flex items-center mb-6">
                  <span className="inline-block h-5 w-5 rounded-full bg-green-500 animate-pulse mr-4"></span>
                  <span className="text-gray-300 text-lg">{currentTask.message}</span>
                </div>
                
                {currentTask.estimated_time_remaining && (
                  <div className="bg-dark-800/50 rounded-lg p-5 mb-6">
                    <p className="text-gray-300">
                      <span className="text-green-300 font-semibold">预计剩余时间：</span> {currentTask.estimated_time_remaining}
                    </p>
                  </div>
                )}
                
                {/* 视频预览 */}
                {videoUrl && (
                  <div className="mt-8">
                    <h3 className="text-xl font-bold text-white mb-4">生成结果</h3>
                    <div className="aspect-video bg-black rounded-lg overflow-hidden">
                      <video 
                        controls 
                        className="w-full h-full" 
                        src={videoUrl}
                      >
                        您的浏览器不支持视频标签
                      </video>
                    </div>
                    <div className="mt-4 flex justify-center">
                      <a 
                        href={videoUrl} 
                        download
                        className="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg transition flex items-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        下载视频
                      </a>
                    </div>
                  </div>
                )}
                
                {/* 操作按钮 */}
                <div className="mt-8 flex justify-center space-x-4">
                  <button
                    onClick={cancelTask}
                    className="py-3 px-7 bg-dark-700/50 hover:bg-dark-700 text-white rounded-lg transition flex items-center text-lg"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    {videoUrl ? '创建新视频' : '取消任务'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoGeneration; 