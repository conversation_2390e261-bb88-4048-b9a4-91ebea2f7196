# 提示词增强器界面调用模式

本文档详细说明前端界面如何与提示词增强系统进行交互，包括API服务和MCP服务的串联方式、数据流向和示例代码。

## API服务与MCP服务的区别

### MCP服务
- **功能定位**：专门处理流式输出数据，是一个通用的流式数据传输框架
- **通信方式**：主要通过WebSocket提供实时数据流
- **主要职责**：
  - 接收智能体的工具调用（thinking、stream_output等）
  - 处理并转换为事件数据
  - 向前端推送流式数据（思考过程、生成内容）
- **默认端口**：19220（注意：之前使用的是8000端口）

### API服务
- **功能定位**：提示词增强智能体的专用服务接口
- **通信方式**：提供HTTP API和WebSocket入口
- **主要职责**：
  - 接收前端用户请求
  - 调用提示词增强智能体
  - 管理会话和状态
- **默认端口**：8100

## 服务串联架构图

```
+----------------+                      +----------------+                    +-------------------+
|                |  1.发送提示词请求     |                |  2.调用             |                   |
|   前端界面     +--------------------->+  API 服务      +-------------------->+  提示词增强智能体  |
|                |  POST /api/enhance   |  (端口:8100)   |  传递用户请求        |                   |
+------^---------+                      +----------------+                    +---------+---------+
       |                                                                                |
       |                                                                                |
       |                                                                     3.MCP工具调用|
       |                                                                                |
       |                                                                                |
       |                                                                                v
       |                               +----------------+                     +---------+---------+
       |                               |                |                     |                   |
       +------------------------------>+  MCP 服务      +<--------------------+                   |
         4.建立WebSocket连接           |  (端口:19220)  |    发送思考过程和结果 |                   |
         ws://host:19220/api/v1/connect|                |                     |                   |
         5.接收流式输出                +----------------+                     +-------------------+
```

## MCP连接方式说明

### 后端MCP连接方式选择

在提示词增强智能体中，我们使用`SseServerParams`方式与MCP服务建立连接，这是AutoGen框架提供的标准连接方式之一。选择此方式的主要原因包括：

#### 支持的连接方式比较

| 连接方式          | 优点                                                                     | 缺点                                   | 适用场景              |
| ----------------- | ------------------------------------------------------------------------ | -------------------------------------- | --------------------- |
| SseServerParams   | • 标准化接口<br>• 自动工具格式转换<br>• 完善的错误处理<br>• 支持远程服务 | • 依赖HTTP服务质量<br>• 可能有轻微延迟 | 连接独立运行的MCP服务 |
| StdioServerParams | • 低延迟<br>• 进程间直接通信<br>• 适合本地开发                           | • 仅适用于本地服务<br>• 需要管理子进程 | 自动启动本地MCP服务   |
| 直接HTTP调用      | • 实现简单<br>• 无依赖                                                   | • 需自行处理格式转换<br>• 无会话管理   | 简单集成或特殊需求    |

#### 为什么选择SseServerParams

1. **与AutoGen标准集成** - 使用官方支持的方式，确保兼容性和稳定性
2. **自动工具适配** - 自动将MCP工具转换为AutoGen工具对象，避免"Unsupported tool type"错误
3. **会话管理** - 内置会话管理机制，简化开发复杂度
4. **适合生产环境** - 支持连接独立部署的MCP服务，适合生产环境架构
5. **错误处理** - 提供更完善的错误处理和异常恢复机制

### 前端与MCP连接的最佳实践

前端应用在与MCP服务交互时，应遵循以下最佳实践：

1. **健康检查先行**
   - 在建立WebSocket连接前，先通过API服务确认MCP服务可用
   - 使用`/health`或`/healthz`端点验证服务状态

2. **正确的连接URL格式**
   - 使用正确的WebSocket路径：`ws://host:19220/api/v1/connect`
   - 不要混淆旧版的`/stream`路径

3. **会话初始化**
   - 连接成功后立即发送`create_session`命令
   - 包含必要的元数据（session_id, session_name等）

4. **错误处理与重连**
   - 实现重连机制，使用指数退避策略
   - 处理连接失败的情况，提供用户友好的错误信息
   - 在WebSocket连接断开时保存状态，以便恢复

5. **资源管理**
   - 在不需要时关闭WebSocket连接，释放资源
   - 避免同时创建多个到同一MCP服务的连接

#### 连接代码示例：

```javascript
class MCPConnection {
  constructor(mcpUrl = 'ws://localhost:19220/api/v1/connect') {
    this.mcpUrl = mcpUrl;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // 初始重连延迟(毫秒)
    this.eventHandlers = {
      'thinking': [],
      'content': [],
      'code': []
    };
  }
  
  // 连接到MCP服务
  connect(sessionId) {
    return new Promise((resolve, reject) => {
      try {
        // 关闭现有连接
        this.close();
        
        // 创建新的WebSocket连接
        this.ws = new WebSocket(this.mcpUrl);
        
        // 连接打开时
        this.ws.onopen = () => {
          console.log('已连接到MCP WebSocket');
          this.reconnectAttempts = 0; // 重置重连计数
          
          // 发送创建会话命令
          this.sendCreateSession(sessionId);
          resolve(this.ws);
        };
        
        // 连接错误时
        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          reject(error);
        };
        
        // 连接关闭时
        this.ws.onclose = (event) => {
          console.log(`WebSocket连接已关闭: ${event.code} ${event.reason}`);
          this._attemptReconnect(sessionId);
        };
        
        // 接收消息时
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this._handleMessage(data);
          } catch (error) {
            console.error('处理消息时出错:', error);
          }
        };
      } catch (error) {
        console.error('创建WebSocket连接时出错:', error);
        reject(error);
      }
    });
  }
  
  // 发送创建会话命令
  sendCreateSession(sessionId) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法发送创建会话命令');
      return false;
    }
    
    this.ws.send(JSON.stringify({
      cmd: "call_tool",
      tool_name: "create_session",
      params: {
        session_id: sessionId,
        session_name: `prompt-enhancer-${Date.now()}`,
        meta_data: {
          agent_name: "PromptEnhancer",
          agent_type: "assistant"
        }
      }
    }));
    
    return true;
  }
  
  // 注册事件处理器
  on(eventType, callback) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].push(callback);
    }
    return this;
  }
  
  // 关闭连接
  close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
  
  // 处理接收到的消息
  _handleMessage(data) {
    const eventType = data.event_type;
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data.data));
    }
  }
  
  // 尝试重新连接
  _attemptReconnect(sessionId) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('达到最大重连次数，停止重连');
      return;
    }
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    console.log(`将在${delay}毫秒后尝试重连 (尝试 ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.reconnectAttempts++;
      this.connect(sessionId).catch(error => {
        console.error('重连失败:', error);
      });
    }, delay);
  }
}
```

### MCP连接参数详解

前端连接到MCP服务时，可以使用以下可选参数:

- **sessionId**: 会话标识符，用于关联同一用户的多个请求
- **userId**: 用户标识符，用于区分不同用户
- **clientId**: 客户端标识符，区分不同的客户端应用
- **token**: 认证令牌，用于安全验证（如果MCP服务配置了认证）

WebSocket连接URL示例：
```
ws://localhost:19220/api/v1/connect?clientId=web-frontend&userId=user123
```

## 详细调用流程

1. **前端发送请求**：
   - 用户在输入框中输入原始提示词并点击"开始增强"按钮
   - 前端通过HTTP POST请求将提示词发送到API服务
   - API服务返回包含session_id的响应

2. **API服务处理请求**：
   - API服务验证MCP服务可用性（尝试访问`/health`、`/healthz`或`/`端点）
   - API服务实例化提示词增强智能体
   - 将用户提示词传递给智能体处理
   - 智能体开始分析和增强提示词

3. **智能体调用MCP工具**：
   - 智能体在处理过程中使用thinking、stream_output等MCP工具
   - 这些工具调用被转发到MCP服务
   - MCP服务将数据组织为事件流

4. **前端连接MCP WebSocket**：
   - 前端使用从API响应中获取的session_id连接MCP服务的WebSocket
   - WebSocket连接地址为`ws://host:19220/api/v1/connect`
   - 连接成功后发送create_session命令以启动会话
   - 接收并处理来自MCP服务的实时事件流
   - 根据事件类型在界面上显示思考过程、生成内容等

5. **显示最终结果**：
   - 当增强过程完成后，前端显示最终的增强提示词
   - 此时用户可以复制结果或继续修改

## 服务健康检查

API服务在启动时会尝试多种方式检查MCP服务是否可用：

1. 首先尝试访问MCP服务的`/health`端点
2. 如果失败，会继续尝试`/healthz`和根路径`/`
3. 如果所有尝试都失败，API服务会记录警告日志但仍会继续启动
4. 前端应实现断线重连和错误处理逻辑，提高系统稳定性

## 前端实现示例

### HTML界面结构

```html
<div class="prompt-enhancer">
  <div class="input-section">
    <h2>创建增强提示词</h2>
    <textarea id="promptInput" placeholder="请输入您想要增强的提示词，例如: '一个未来城市的夜景'..."></textarea>
    <button id="enhanceButton">开始增强</button>
  </div>
  
  <div class="output-section">
    <h2>增强结果</h2>
    <div id="thinkingProcess" class="thinking-process"></div>
    <div id="enhancedPrompt" class="enhanced-prompt"></div>
  </div>
</div>
```

### JavaScript实现

```javascript
// 提示词增强功能
class PromptEnhancer {
  constructor(apiUrl = 'http://localhost:8100', mcpUrl = 'http://localhost:19220') {
    this.apiUrl = apiUrl;
    this.mcpUrl = mcpUrl.replace('http://', 'ws://');
    this.ws = null;
    this.sessionId = null;
    
    // DOM元素
    this.promptInput = document.getElementById('promptInput');
    this.enhanceButton = document.getElementById('enhanceButton');
    this.thinkingProcess = document.getElementById('thinkingProcess');
    this.enhancedPrompt = document.getElementById('enhancedPrompt');
    
    // 绑定事件
    this.enhanceButton.addEventListener('click', () => this.startEnhancement());
  }
  
  // 开始增强流程
  async startEnhancement() {
    const prompt = this.promptInput.value.trim();
    if (!prompt) {
      alert('请输入要增强的提示词');
      return;
    }
    
    try {
      // 清空上次结果
      this.thinkingProcess.innerHTML = '';
      this.enhancedPrompt.innerHTML = '';
      this.enhanceButton.disabled = true;
      this.enhanceButton.textContent = '增强中...';
      
      // 1. 发送HTTP请求到API服务
      const response = await fetch(`${this.apiUrl}/api/enhance`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }
      
      const data = await response.json();
      this.sessionId = data.session_id;
      
      // 2. 连接到MCP WebSocket
      this.connectToMcpWebSocket();
      
    } catch (error) {
      console.error('增强过程出错:', error);
      this.enhancedPrompt.innerHTML = `<div class="error">处理失败: ${error.message}</div>`;
      this.enhanceButton.disabled = false;
      this.enhanceButton.textContent = '开始增强';
    }
  }
  
  // 连接到MCP WebSocket
  connectToMcpWebSocket() {
    // 关闭之前的连接
    if (this.ws) {
      this.ws.close();
    }
    
    // 创建新的WebSocket连接
    const wsUrl = `${this.mcpUrl}/api/v1/connect`;
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onopen = () => {
      console.log('已连接到MCP WebSocket');
      
      // 连接后发送创建会话命令
      this.ws.send(
        JSON.stringify({
          cmd: "call_tool",
          tool_name: "create_session",
          params: {
            session_id: this.sessionId,
            session_name: `prompt-enhancer-${Date.now()}`,
            meta_data: {
              agent_name: "PromptEnhancer",
              agent_type: "assistant",
            },
          },
        })
      );
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMcpEvent(data);
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.enhancedPrompt.innerHTML += `<div class="error">WebSocket错误</div>`;
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      // 恢复按钮状态
      this.enhanceButton.disabled = false;
      this.enhanceButton.textContent = '开始增强';
    };
  }
  
  // 处理MCP事件
  handleMcpEvent(data) {
    const eventType = data.event_type;
    const eventData = data.data;
    
    switch (eventType) {
      case 'thinking':
        // 显示思考过程
        const thinkingElement = document.createElement('div');
        thinkingElement.className = 'thinking-bubble';
        thinkingElement.innerHTML = `🧠 ${eventData.content}`;
        this.thinkingProcess.appendChild(thinkingElement);
        break;
        
      case 'content':
        // 显示生成内容
        if (eventData.is_final) {
          // 最终结果
          this.enhancedPrompt.innerHTML = eventData.content;
        } else {
          // 流式内容
          if (!this.enhancedPrompt.innerHTML) {
            this.enhancedPrompt.innerHTML = eventData.content;
          } else {
            this.enhancedPrompt.innerHTML += eventData.content;
          }
        }
        break;
        
      case 'code':
        // 显示代码块
        const codeElement = document.createElement('pre');
        codeElement.className = `language-${eventData.language || 'text'}`;
        codeElement.innerHTML = `<code>${eventData.code}</code>`;
        this.enhancedPrompt.appendChild(codeElement);
        // 如果有代码高亮库，这里可以调用代码高亮
        break;
        
      default:
        console.log('未处理的事件类型:', eventType, eventData);
    }
    
    // 自动滚动到底部
    this.thinkingProcess.scrollTop = this.thinkingProcess.scrollHeight;
  }
}

// 初始化提示词增强器
document.addEventListener('DOMContentLoaded', () => {
  const enhancer = new PromptEnhancer();
});
```

## 点击"开始增强"按钮的流程图

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│ 用户点击按钮  │      │  前端界面      │      │  API服务      │
└───────┬───────┘      └───────┬───────┘      └───────┬───────┘
        │                      │                      │
        │  点击"开始增强"      │                      │
        │─────────────────────>│                      │
        │                      │                      │
        │                      │  POST /api/enhance   │
        │                      │─────────────────────>│
        │                      │                      │
        │                      │                      │─┐
        │                      │                      │ │ 调用智能体
        │                      │                      │<┘
        │                      │                      │
        │                      │   返回session_id     │
        │                      │<─────────────────────│
        │                      │                      │
┌───────┴───────┐      ┌───────┴───────┐      ┌───────┴───────┐
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
        │                      │                      │
        │                      │                      │
        │                      │                      │
        │                      │                      │
┌───────┴───────┐      ┌───────┴───────┐      ┌───────┴───────┐
│  MCP服务      │      │  前端界面      │      │  智能体       │
└───────┬───────┘      └───────┬───────┘      └───────┬───────┘
        │                      │                      │
        │   连接WebSocket      │                      │
        │<─────────────────────│                      │
        │                      │                      │
        │   发送create_session  │                      │
        │<─────────────────────│                      │
        │                      │                      │
        │                      │                      │─┐
        │                      │                      │ │ 分析提示词
        │                      │                      │<┘
        │                      │                      │
        │                      │                      │─┐
        │<─────────────────────┼──────────────────────│ │ 调用MCP工具
        │                      │                      │<┘
        │                      │                      │
        │   推送思考过程        │                      │
        │─────────────────────>│                      │
        │                      │─┐                    │
        │                      │ │ 更新界面           │
        │                      │<┘                    │
        │                      │                      │
        │   推送生成内容        │                      │
        │─────────────────────>│                      │
        │                      │─┐                    │
        │                      │ │ 更新界面           │
        │                      │<┘                    │
        │                      │                      │
┌───────┴───────┐      ┌───────┴───────┐      ┌───────┴───────┐
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

## 最佳实践

1. **错误处理**：
   - 添加超时机制，防止长时间等待
   - 实现WebSocket断线重连（多次尝试连接，指数退避策略）
   - 为API请求添加详细的错误捕获和处理
   - 使用try-catch包装所有异步操作

2. **用户体验**：
   - 在增强过程中禁用按钮，防止重复提交
   - 提供视觉反馈，如加载动画和进度指示
   - 思考过程增加图标区分不同的思考类型
   - 在连接失败时显示友好的错误信息和重试选项

3. **性能优化**：
   - 限制思考过程显示的条目数量，避免DOM过大
   - 考虑使用虚拟滚动处理大量输出
   - 实现节流机制，减少频繁DOM更新
   - 使用requestAnimationFrame来处理频繁UI更新

4. **部署注意事项**：
   - 确保CORS配置正确，允许前端访问API和WebSocket
   - 在生产环境中使用HTTPS和WSS协议
   - 对MCP服务实施健康检查和自动恢复机制
   - 考虑添加身份验证机制，保护API和WebSocket端点

## 常见问题排查

1. **连接拒绝错误(ERR_CONNECTION_REFUSED)**：
   - 确认MCP服务是否正在运行（而非API服务自动启动的默认端口服务）
   - 验证端口号是否正确（19220而非旧的8000端口）
   - 检查主机名是否正确（本地开发使用localhost）

2. **健康检查错误**：
   - MCP服务提供了多个健康检查端点：`/health`、`/healthz`和`/`
   - API服务会尝试所有这些端点，但需要至少一个返回200状态码
   - 查看API服务和MCP服务的日志以确定具体原因

3. **WebSocket连接问题**：
   - WebSocket连接路径已更新为`/api/v1/connect`
   - 确保在连接后发送正确的`create_session`命令
   - 检查浏览器控制台中的WebSocket错误信息

## 开发调试

开发过程中可使用以下命令启动服务：

```bash
# 先启动MCP服务（确保使用正确的端口）
python agents/tools/stream/run_server.py --port 19220

# 然后启动API服务（指向MCP服务的正确URL）
cd agents/agent/prompt_enhancer
.\venv\Scripts\Activate.ps1
python run_server.py --port 8100 --mcp-url http://localhost:19220
```

开发调试建议：
1. 使用浏览器开发者工具的Network面板监控API请求和WebSocket连接
2. 启用详细日志记录，便于排查问题
3. 使用专用的WebSocket调试工具（如Postman或WebSocket King）测试连接
4. 确保所有服务都使用独立的虚拟环境，避免依赖冲突
