/**
 * UI工具函数 - 增强的用户界面状态管理
 * 支持多任务并发处理的UI状态更新
 */

/**
 * 设置生成状态
 * @param {boolean} isGenerating - 是否正在生成
 */
function setGeneratingState(isGenerating) {
    const generateBtn = document.getElementById('generate-btn');
    const promptInput = document.getElementById('prompt-input');
    const countInput = document.getElementById('count-input');
    
    if (generateBtn) {
        generateBtn.disabled = isGenerating;
        if (isGenerating) {
            generateBtn.classList.add('loading');
            generateBtn.textContent = '施法中...';
        } else {
            generateBtn.classList.remove('loading');
            generateBtn.textContent = '施展魔法';
        }
    }
    
    if (promptInput) {
        promptInput.disabled = isGenerating;
    }
    
    if (countInput) {
        countInput.disabled = isGenerating;
    }
}

/**
 * 设置加载状态
 * @param {boolean} isLoading - 是否加载中
 * @param {string} message - 加载消息
 * @param {boolean} showProgress - 是否显示进度条
 */
function setLoading(isLoading, message = '加载中...', showProgress = false) {
    const loadingIndicator = document.getElementById('loading-indicator');
    const loadingText = document.getElementById('loading-text');
    const progressContainer = document.getElementById('progress-container');
    
    if (!loadingIndicator) return;
    
    if (isLoading) {
        loadingIndicator.classList.remove('hidden');
        if (loadingText) {
            loadingText.textContent = message;
        }
        if (progressContainer) {
            if (showProgress) {
                progressContainer.classList.remove('hidden');
            } else {
                progressContainer.classList.add('hidden');
            }
        }
    } else {
        loadingIndicator.classList.add('hidden');
        if (progressContainer) {
            progressContainer.classList.add('hidden');
        }
    }
}

/**
 * 更新任务进度
 * @param {number} completed - 已完成任务数
 * @param {number} total - 总任务数
 * @param {string} message - 进度消息
 */
function updateTaskProgress(completed, total, message = '') {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    
    if (progressBar) {
        const percentage = total > 0 ? (completed / total) * 100 : 0;
        progressBar.style.width = `${percentage}%`;
    }
    
    if (progressText) {
        if (message) {
            progressText.textContent = message;
        } else {
            progressText.textContent = `${completed}/${total} 任务完成`;
        }
    }
}

/**
 * 添加图片到画廊
 * @param {string} imageUrl - 图片URL
 * @param {string} taskId - 任务ID（可选）
 */
function addImageToGallery(imageUrl, taskId = null) {
    const galleryGrid = document.getElementById('gallery-grid');
    if (!galleryGrid) return;

    // 创建画框元素
    const frameDiv = document.createElement('div');
    frameDiv.className = 'gallery-frame';
    frameDiv.dataset.taskId = taskId || '';
    
    frameDiv.innerHTML = `
        <div class="image-container">
            <img src="${imageUrl}" alt="生成的图片" class="w-full h-full object-cover rounded-sm" loading="lazy">
        </div>
        <div class="flex justify-end gap-2 mt-2">
            <button class="rune-btn text-xs p-2 color-btn" title="在线涂色">🎨</button>
            <button class="rune-btn text-xs p-2 download-btn" title="下载图片">💾</button>
            <button class="rune-btn text-xs p-2 delete-btn" style="background-color: #C75D5D; color: white;" title="删除图片">🗑️</button>
        </div>
    `;

    // 添加事件监听器
    const colorBtn = frameDiv.querySelector('.color-btn');
    const downloadBtn = frameDiv.querySelector('.download-btn');
    const deleteBtn = frameDiv.querySelector('.delete-btn');
    const img = frameDiv.querySelector('img');

    // 涂色按钮事件
    if (colorBtn) {
        colorBtn.addEventListener('click', () => {
            openColoringInterface(imageUrl);
        });
    }

    // 下载按钮事件
    if (downloadBtn) {
        downloadBtn.addEventListener('click', () => {
            downloadImage(imageUrl);
        });
    }

    // 删除按钮事件
    if (deleteBtn) {
        deleteBtn.addEventListener('click', () => {
            if (confirm('确定要删除这张图片吗？')) {
                frameDiv.remove();
            }
        });
    }

    // 图片点击选择事件
    if (img) {
        img.addEventListener('click', () => {
            const selectModeToggle = document.getElementById('select-mode-toggle');
            if (selectModeToggle && selectModeToggle.checked) {
                frameDiv.classList.toggle('selected');
                updatePrintButtonState();
            }
        });
    }

    // 添加到画廊
    galleryGrid.appendChild(frameDiv);

    // 添加入场动画
    setTimeout(() => {
        frameDiv.style.opacity = '0';
        frameDiv.style.transform = 'scale(0.8)';
        frameDiv.style.transition = 'all 0.3s ease';
        
        requestAnimationFrame(() => {
            frameDiv.style.opacity = '1';
            frameDiv.style.transform = 'scale(1)';
        });
    }, 100);

    console.log('图片已添加到画廊:', imageUrl);
}

/**
 * 打开涂色界面
 * @param {string} imageUrl - 图片URL
 */
function openColoringInterface(imageUrl) {
    // 这里可以实现打开涂色界面的逻辑
    console.log('打开涂色界面:', imageUrl);
    // 可以打开新窗口或模态框来进行在线涂色
    window.open(`coloring.html?image=${encodeURIComponent(imageUrl)}`, '_blank');
}

/**
 * 下载图片
 * @param {string} imageUrl - 图片URL
 */
function downloadImage(imageUrl) {
    try {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `coloring-book-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showSuccessMessage('图片下载已开始');
    } catch (error) {
        console.error('下载图片失败:', error);
        showErrorMessage('图片下载失败，请稍后重试', ErrorTypes.UNKNOWN_ERROR);
    }
}

/**
 * 更新打印按钮状态
 */
function updatePrintButtonState() {
    const printBtn = document.getElementById('print-btn');
    const selectedFrames = document.querySelectorAll('.gallery-frame.selected');
    
    if (printBtn) {
        if (selectedFrames.length > 0) {
            printBtn.disabled = false;
            printBtn.textContent = `🖨️ 打印 (${selectedFrames.length})`;
        } else {
            printBtn.disabled = true;
            printBtn.textContent = '🖨️ 打印';
        }
    }
}

/**
 * 显示任务统计信息
 * @param {Object} stats - 统计信息
 */
function showTaskStats(stats) {
    const { total, completed, failed, running } = stats;
    
    if (total > 0) {
        console.log(`任务统计: 总计${total}, 完成${completed}, 失败${failed}, 运行中${running}`);
        
        // 可以在UI中显示统计信息
        const statsElement = document.getElementById('task-stats');
        if (statsElement) {
            statsElement.textContent = `任务进度: ${completed + failed}/${total}`;
        }
    }
}

/**
 * 清理UI状态
 */
function cleanupUIState() {
    setGeneratingState(false);
    setLoading(false);
    
    // 清理任务统计显示
    const statsElement = document.getElementById('task-stats');
    if (statsElement) {
        statsElement.textContent = '';
    }
}

/**
 * 初始化UI事件监听器
 */
function initializeUIEventListeners() {
    // 打印按钮事件
    const printBtn = document.getElementById('print-btn');
    if (printBtn) {
        printBtn.addEventListener('click', () => {
            const selectedFrames = document.querySelectorAll('.gallery-frame.selected');
            if (selectedFrames.length > 0) {
                printSelectedImages(selectedFrames);
            } else {
                showErrorMessage('请先选择要打印的图片', ErrorTypes.VALIDATION_ERROR);
            }
        });
    }
}

/**
 * 打印选中的图片
 * @param {NodeList} selectedFrames - 选中的画框元素
 */
function printSelectedImages(selectedFrames) {
    const printWindow = window.open('', '_blank');
    let printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>魔法画册 - 打印</title>
            <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .print-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
                .print-item { page-break-inside: avoid; text-align: center; }
                .print-item img { max-width: 100%; height: auto; border: 2px solid #333; }
                @media print {
                    body { margin: 0; }
                    .print-grid { gap: 10px; }
                }
            </style>
        </head>
        <body>
            <h1>魔法画册</h1>
            <div class="print-grid">
    `;

    selectedFrames.forEach((frame, index) => {
        const img = frame.querySelector('img');
        if (img) {
            printContent += `
                <div class="print-item">
                    <img src="${img.src}" alt="涂色图 ${index + 1}">
                    <p>涂色图 ${index + 1}</p>
                </div>
            `;
        }
    });

    printContent += `
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    
    // 等待图片加载完成后打印
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        setGeneratingState,
        setLoading,
        updateTaskProgress,
        addImageToGallery,
        updatePrintButtonState,
        showTaskStats,
        cleanupUIState,
        initializeUIEventListeners
    };
}