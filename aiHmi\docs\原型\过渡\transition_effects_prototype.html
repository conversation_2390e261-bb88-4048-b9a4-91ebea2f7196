<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI 过渡特效原型 - Transition Effects Prototype</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-panel {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-panel h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
        }

        .effect-button {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .effect-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .effect-button:last-child {
            margin-bottom: 0;
        }

        /* 场景页面样式 */
        .scene {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .scene-natural {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
        }

        .scene-cyberpunk {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 50%, #00cec9 100%);
        }

        .scene-glassmorphism {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .scene-neumorphism {
            background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
        }

        .scene-kawaii {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 50%, #00b894 100%);
        }

        .scene-title {
            font-size: 48px;
            font-weight: 700;
            color: white;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        .scene-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            max-width: 600px;
        }

        /* 过渡遮罩层 */
        .transition-mask {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
            pointer-events: none;
        }

        /* 特效专用样式 */
        .sliding-panel {
            clip-path: polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%);
        }

        .angled-panel {
            clip-path: polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%);
        }

        .circle-expand {
            clip-path: circle(0% at 50% 50%);
        }

        .curtain-open {
            clip-path: polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%);
        }

        /* 粒子效果 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
        }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>过渡特效演示</h3>
            <button class="effect-button" onclick="transitionTo('natural', 'sliding-panel')">自然风格 - 滑动面板</button>
            <button class="effect-button" onclick="transitionTo('cyberpunk', 'angled-panel')">赛博朋克 - 斜切面板</button>
            <button class="effect-button" onclick="transitionTo('glassmorphism', 'circle-expand')">玻璃拟态 - 圆形扩展</button>
            <button class="effect-button" onclick="transitionTo('neumorphism', 'curtain-open')">新拟态 - 幕布开启</button>
            <button class="effect-button" onclick="transitionTo('kawaii', 'circle-expand')">可爱风格 - 圆形扩展</button>
            <hr style="margin: 15px 0; border: 1px solid rgba(255,255,255,0.2);">
            <button class="effect-button" onclick="showOriginalPages()">查看原型页面</button>
        </div>

        <!-- 状态指示器 -->
        <div class="status-indicator" id="statusIndicator">
            当前场景: 玻璃拟态
        </div>

        <!-- 场景容器 -->
        <div class="scene scene-glassmorphism" id="currentScene">
            <div class="particles" id="particles"></div>
            <h1 class="scene-title">玻璃拟态主题</h1>
            <p class="scene-subtitle">精致优雅的毛玻璃效果，营造未来科技感的视觉体验</p>
        </div>

        <!-- 过渡遮罩层 -->
        <div class="transition-mask" id="transitionMask"></div>
    </div>

    <script>
        // 场景配置
        const scenes = {
            natural: {
                title: '自然风格主题',
                subtitle: '亲近自然的设计语言，营造宁静舒适的驾驶环境',
                class: 'scene-natural'
            },
            cyberpunk: {
                title: '赛博朋克主题',
                subtitle: '未来科技感的霓虹美学，打造炫酷的数字化体验',
                class: 'scene-cyberpunk'
            },
            glassmorphism: {
                title: '玻璃拟态主题',
                subtitle: '精致优雅的毛玻璃效果，营造未来科技感的视觉体验',
                class: 'scene-glassmorphism'
            },
            neumorphism: {
                title: '新拟态主题',
                subtitle: '柔和的阴影与高光，创造温暖舒适的触感体验',
                class: 'scene-neumorphism'
            },
            kawaii: {
                title: '可爱风格主题',
                subtitle: '充满活力的色彩搭配，带来愉悦轻松的使用体验',
                class: 'scene-kawaii'
            }
        };

        let currentSceneId = 'glassmorphism';
        let isTransitioning = false;

        // 初始化粒子效果
        function initParticles() {
            const particlesContainer = document.getElementById('particles');
            particlesContainer.innerHTML = '';
            
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 2 + 's';
                particlesContainer.appendChild(particle);
                
                // 粒子动画
                gsap.to(particle, {
                    y: -100,
                    opacity: 0,
                    duration: 3 + Math.random() * 2,
                    repeat: -1,
                    ease: "power2.out"
                });
            }
        }

        // 过渡特效函数
        const transitionEffects = {
            'sliding-panel': function(newScene, onComplete) {
                // 滑动面板效果
                gsap.set(newScene, { clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' });
                gsap.to(newScene, {
                    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                    duration: 1.2,
                    ease: 'power2.inOut',
                    onComplete: onComplete
                });
            },
            
            'angled-panel': function(newScene, onComplete) {
                // 斜切面板效果
                gsap.set(newScene, { clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' });
                gsap.to(newScene, {
                    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
                    duration: 1.0,
                    ease: 'power3.inOut',
                    onComplete: function() {
                        gsap.to(newScene, {
                            clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                            duration: 0.3,
                            ease: 'power2.out',
                            onComplete: onComplete
                        });
                    }
                });
            },
            
            'circle-expand': function(newScene, onComplete) {
                // 圆形扩展效果
                gsap.set(newScene, { clipPath: 'circle(0% at 50% 50%)' });
                gsap.to(newScene, {
                    clipPath: 'circle(150% at 50% 50%)',
                    duration: 1.5,
                    ease: 'power2.inOut',
                    onComplete: onComplete
                });
            },
            
            'curtain-open': function(newScene, onComplete) {
                // 幕布开启效果
                gsap.set(newScene, { clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)' });
                gsap.to(newScene, {
                    clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                    duration: 1.8,
                    ease: 'power2.inOut',
                    onComplete: onComplete
                });
            }
        };

        // 场景切换函数
        function transitionTo(sceneId, effectType) {
            if (isTransitioning || sceneId === currentSceneId) return;
            
            isTransitioning = true;
            updateStatus(`正在切换到: ${scenes[sceneId].title}`);
            
            const currentScene = document.getElementById('currentScene');
            const container = document.querySelector('.container');
            
            // 创建新场景
            const newScene = document.createElement('div');
            newScene.className = `scene ${scenes[sceneId].class}`;
            newScene.innerHTML = `
                <div class="particles"></div>
                <h1 class="scene-title">${scenes[sceneId].title}</h1>
                <p class="scene-subtitle">${scenes[sceneId].subtitle}</p>
            `;
            
            // 将新场景插入到当前场景下方
            newScene.style.zIndex = '1';
            currentScene.style.zIndex = '2';
            container.appendChild(newScene);
            
            // 执行过渡效果
            const effectFunction = transitionEffects[effectType] || transitionEffects['circle-expand'];
            effectFunction(newScene, function() {
                // 过渡完成后的清理工作
                container.removeChild(currentScene);
                newScene.id = 'currentScene';
                newScene.style.zIndex = 'auto';
                newScene.style.clipPath = 'none';
                
                currentSceneId = sceneId;
                isTransitioning = false;
                updateStatus(`当前场景: ${scenes[sceneId].title}`);
                
                // 重新初始化粒子效果
                initParticles();
            });
        }

        // 更新状态指示器
        function updateStatus(text) {
            document.getElementById('statusIndicator').textContent = text;
        }

        // 显示原型页面
        function showOriginalPages() {
            const pages = [
                '1_natural_commute.html',
                '2_cyberpunk_drive.html', 
                '3_glassmorphism_wait.html',
                '4_neumorphism_rainy.html',
                '5_kawaii_family_trip.html'
            ];
            
            const randomPage = pages[Math.floor(Math.random() * pages.length)];
            const basePath = window.location.pathname.replace('/过渡/transition_effects_prototype.html', '');
            window.open(basePath + '/' + randomPage, '_blank');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (isTransitioning) return;
            
            switch(e.key) {
                case '1':
                    transitionTo('natural', 'sliding-panel');
                    break;
                case '2':
                    transitionTo('cyberpunk', 'angled-panel');
                    break;
                case '3':
                    transitionTo('glassmorphism', 'circle-expand');
                    break;
                case '4':
                    transitionTo('neumorphism', 'curtain-open');
                    break;
                case '5':
                    transitionTo('kawaii', 'circle-expand');
                    break;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initParticles();
            updateStatus(`当前场景: ${scenes[currentSceneId].title}`);
            
            // 添加提示信息
            setTimeout(() => {
                updateStatus('使用数字键1-5快速切换场景');
                setTimeout(() => {
                    updateStatus(`当前场景: ${scenes[currentSceneId].title}`);
                }, 3000);
            }, 2000);
        });
    </script>
</body>
</html>