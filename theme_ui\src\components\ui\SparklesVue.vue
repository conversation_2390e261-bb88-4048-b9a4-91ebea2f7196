<template>
  <div 
    ref="canvasContainer" 
    class="sparkles-container" 
    :class="className"
  >
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  background: {
    type: String,
    default: 'transparent'
  },
  minSize: {
    type: Number,
    default: 0.4
  },
  maxSize: {
    type: Number,
    default: 1
  },
  particleDensity: {
    type: Number,
    default: 80
  },
  particleColor: {
    type: String,
    default: '#FFF'
  },
  className: {
    type: String,
    default: ''
  },
  particleSpeed: {
    type: Number,
    default: 0.5
  }
});

const canvasContainer = ref(null);
const canvas = ref(null);
let context = null;
let particles = [];
let animationFrame = null;
let dimension = { width: 0, height: 0 };

const resizeCanvas = () => {
  if (canvasContainer.value && canvas.value) {
    const { width, height } = canvasContainer.value.getBoundingClientRect();
    dimension = { width, height };
    canvas.value.width = width;
    canvas.value.height = height;
  }
};

const initiateParticles = () => {
  particles = [];
  const { width, height } = dimension;
  const particleCount = Math.floor((width * height) / 10000) * props.particleDensity;

  for (let i = 0; i < particleCount; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height;
    const size = Math.random() * (props.maxSize - props.minSize) + props.minSize;
    const speed = Math.random() * props.particleSpeed;
    const opacity = Math.random();

    particles.push({
      x,
      y,
      size,
      speed,
      opacity,
      factor: 1
    });
  }
};

const animate = () => {
  if (particles.length === 0) initiateParticles();

  if (context && canvas.value) {
    context.clearRect(0, 0, dimension.width, dimension.height);
    context.fillStyle = props.background;
    context.fillRect(0, 0, dimension.width, dimension.height);

    particles.forEach((particle) => {
      const { x, y, size, opacity } = particle;

      context.globalAlpha = opacity;
      context.fillStyle = props.particleColor;
      context.beginPath();
      context.arc(x, y, size, 0, 2 * Math.PI);
      context.fill();

      // 更新粒子位置
      particle.y -= particle.speed;
      if (particle.y < -size) {
        particle.y = dimension.height + size;
        particle.x = Math.random() * dimension.width;
      }

      // 添加闪烁效果
      particle.opacity = Math.abs(Math.sin(Date.now() * 0.001 * particle.speed));
    });
  }

  animationFrame = requestAnimationFrame(animate);
};

onMounted(() => {
  if (canvas.value) {
    context = canvas.value.getContext('2d');
  }
  
  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);
  animate();
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas);
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }
});

// 监听属性变化重新初始化粒子
watch([
  () => props.particleDensity,
  () => props.minSize,
  () => props.maxSize,
  () => props.particleSpeed
], () => {
  particles = [];
  initiateParticles();
});
</script>

<style scoped>
.sparkles-container {
  position: relative;
  width: 100%;
  height: 100%;
}

canvas {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}
</style> 