import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue')
    },
    {
        path: '/theme-generator',
        name: 'ThemeGenerator',
        component: () => import('../views/ThemeGenerator.vue')
    },
    {
        path: '/theme-preview/:taskId',
        name: 'ThemePreview',
        component: () => import('../views/ThemePreview.vue'),
        props: true
    },
    {
        path: '/theme-dashboard/:taskId',
        name: 'ThemePreviewDashboard',
        component: () => import('../views/ThemePreviewDashboard.vue'),
        props: true
    },
    {
        path: '/video-generator',
        name: 'VideoGenerator',
        component: () => import('../views/VideoGenerator.vue')
    },
    {
        path: '/theme-list',
        name: 'ThemeList',
        component: () => import('../views/ThemeList.vue')
    },
    {
        path: '/prompt-enhancer',
        name: 'PromptEnhancer',
        component: () => import('../views/PromptEnhancer.vue')
    },
    {
        path: '/image-generator',
        name: 'ImageGenerator',
        component: () => import('../views/ImageGenerator.vue')
    },
    {
        path: '/image-to-text-generator',
        name: 'ImageToTextGenerator',
        component: () => import('../views/ImageToTextGenerator.vue')
    },
    {
        path: '/scene-generator',
        name: 'SceneGenerator',
        component: () => import('../views/SceneGenerator.vue')
    },
    {
        path: '/car-assistant',
        name: 'CarAssistant',
        component: () => import('../views/CarAssistant.vue')
    },
    {
        path: '/voice-clone',
        name: 'VoiceClone',
        component: () => import('../views/VoiceClone.vue')
    },
    {
        path: '/llm-helper',
        name: 'LlmHelper',
        component: () => import('../views/LlmHelper.vue')
    },
    {
        path: '/music-assistant',
        name: 'MusicAssistant',
        component: () => import('../views/MusicAssistant.vue')
    },
    {
        path: '/trip-assistant',
        name: 'TripAssistant',
        component: () => import('../views/TripAssistant.vue')
    },
    {
        path: '/chat-assistant',
        name: 'ChatAssistant',
        component: () => import('../views/ChatAssistant.vue')
    },
    {
        path: '/magic-camera',
        name: 'MagicCamera',
        component: () => import('../views/MagicCamera.vue')
    },
    {
        path: '/nebula-armor',
        name: 'NebulaArmor',
        component: () => import('../views/NebulaArmor.vue')
    },
    {
        path: '/image-to-video',
        name: 'ImageToVideo',
        component: () => import('../views/ImageToVideo.vue')
    },
    {
        path: '/poster-creator',
        name: 'poster-creator',
        component: () => import('../views/PosterCreator.vue')
    },
    {
        path: '/ui-create',
        name: 'UiCreate',
        component: () => import('../views/UiCreate.vue')
    },
    {
        path: '/doodle',
        name: 'Doodle',
        component: () => import('../views/Doodle.vue')
    },
    {
        path: '/demo-videos',
        name: 'DemoVideos',
        component: () => import('../views/DemoVideos.vue')
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

export default router 