<template>
  <div class="car-armor-panel">
    <div class="glass-card p-5 h-full flex flex-col">
      <h3 class="text-xl font-bold text-purple-200 mb-4">车衣控制面板</h3>

      <div class="mb-4">
        <label class="block text-sm font-medium text-purple-200 mb-2">车衣风格名称</label>
        <input type="text" v-model="armorName" class="w-full bg-dark-800/70 border border-gray-700 rounded-lg p-2 text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500" placeholder="输入车衣名称" />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-purple-200 mb-2">车衣颜色</label>
        <div class="flex flex-wrap gap-2">
          <div v-for="(color, index) in availableColors" :key="index" :class="[
              'h-8 w-8 rounded-lg cursor-pointer ring-2 transition-all hover:scale-110',
              selectedColor === color.value ? 'ring-white' : 'ring-transparent hover:ring-gray-400'
            ]" :style="{ backgroundColor: color.value }" @click="selectedColor = color.value"></div>

          <!-- 自定义颜色选择器 -->
          <div class="h-8 w-8 rounded-lg cursor-pointer ring-2 ring-transparent hover:ring-gray-400 bg-gradient-to-r from-purple-500 to-pink-500 relative flex items-center justify-center">
            <input type="color" v-model="customColor" @change="selectedColor = customColor" class="opacity-0 absolute inset-0 w-full h-full cursor-pointer" />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-purple-200 mb-2">材质类型</label>
        <div class="flex flex-wrap gap-2">
          <div v-for="(material, index) in availableMaterials" :key="index" :class="[
              'px-3 py-1.5 rounded-lg cursor-pointer text-center text-sm transition-colors',
              selectedMaterial === material.value
                ? 'bg-purple-600/50 text-white ring-2 ring-purple-500'
                : 'bg-dark-700/70 text-gray-300 hover:bg-purple-600/30 hover:text-purple-200'
            ]" @click="selectedMaterial = material.value">
            {{ material.label }}
          </div>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-purple-200 mb-2">自动旋转速度</label>
        <div class="flex items-center">
          <input type="range" v-model="rotationSpeed" min="0" max="10" step="1" class="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer accent-purple-600" />
          <span class="ml-2 text-white min-w-[30px] text-center">{{ rotationSpeed }}</span>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-purple-200 mb-2">粒子效果</label>
        <div class="flex items-center mb-2">
          <div class="relative w-12 h-6 rounded-full bg-dark-700 cursor-pointer transition-colors" :class="{'bg-purple-600': enableParticles}" @click="enableParticles = !enableParticles">
            <div class="absolute top-1 left-1 w-4 h-4 rounded-full bg-white transform transition-transform" :class="{'translate-x-6': enableParticles}"></div>
          </div>
          <span class="ml-2 text-white">{{ enableParticles ? '开启' : '关闭' }}</span>
        </div>

        <div v-if="enableParticles" class="mt-2">
          <label class="block text-xs font-medium text-purple-200 mb-1">粒子数量</label>
          <div class="flex items-center">
            <input type="range" v-model="particleCount" min="100" max="3000" step="100" class="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer accent-purple-600" />
            <span class="ml-2 text-white min-w-[40px] text-center">{{ particleCount }}</span>
          </div>
        </div>
      </div>

      <div class="mt-auto pt-4 grid grid-cols-2 gap-3">
        <button class="btn-primary flex items-center justify-center py-2.5 px-4 rounded-lg text-white font-medium transition-colors" @click="applyChanges">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          应用车衣
        </button>

        <button class="btn-secondary flex items-center justify-center py-2.5 px-4 rounded-lg text-white font-medium transition-colors" @click="resetChanges">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          重置设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, watch } from "vue";

const emit = defineEmits(["update:carSettings", "apply", "reset"]);

// 控制面板数据
const armorName = ref("星云幻甲 - 璀璨银河");
const selectedColor = ref("#8b5cf6"); // 默认紫色
const customColor = ref("#8b5cf6");
const selectedMaterial = ref("glossy");
const rotationSpeed = ref(0);
const enableParticles = ref(true);
const particleCount = ref(1500);

// 预设颜色
const availableColors = [
  { label: "紫色", value: "#8b5cf6" },
  { label: "蓝色", value: "#3b82f6" },
  { label: "绿色", value: "#10b981" },
  { label: "红色", value: "#ef4444" },
  { label: "黄色", value: "#f59e0b" },
  { label: "粉色", value: "#ec4899" },
  { label: "青色", value: "#06b6d4" },
  { label: "银色", value: "#94a3b8" },
];

// 预设材质
const availableMaterials = [
  { label: "哑光", value: "matte" },
  { label: "高光", value: "glossy" },
  { label: "金属", value: "metallic" },
];

// 当设置变更时，向父组件发送更新
watch(
  [
    selectedColor,
    selectedMaterial,
    rotationSpeed,
    enableParticles,
    particleCount,
  ],
  () => {
    emit("update:carSettings", getSettings());
  },
  { deep: true }
);

// 获取当前设置
const getSettings = () => {
  return {
    name: armorName.value,
    color: selectedColor.value,
    material: selectedMaterial.value,
    rotationSpeed: rotationSpeed.value,
    enableParticles: enableParticles.value,
    particleCount: particleCount.value,
  };
};

// 应用更改
const applyChanges = () => {
  emit("apply", getSettings());
};

// 重置更改
const resetChanges = () => {
  // 重置为默认值
  armorName.value = "星云幻甲 - 璀璨银河";
  selectedColor.value = "#8b5cf6";
  customColor.value = "#8b5cf6";
  selectedMaterial.value = "glossy";
  rotationSpeed.value = 0;
  enableParticles.value = true;
  particleCount.value = 1500;

  emit("reset", getSettings());
};

// 暴露方法给父组件
defineExpose({
  getSettings,
  resetChanges,
});
</script>

<style scoped>
.car-armor-panel {
  height: 100%;
}

.btn-primary {
  background: linear-gradient(to right, #8b5cf6, #6366f1);
  transition: all 0.3s;
}

.btn-primary:hover {
  background: linear-gradient(to right, #7c3aed, #4f46e5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
  background: rgba(75, 85, 99, 0.6);
  transition: all 0.3s;
}

.btn-secondary:hover {
  background: rgba(75, 85, 99, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 自定义滑块样式 */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  background: #1e293b;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid #f3f4f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid #f3f4f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
</style> 