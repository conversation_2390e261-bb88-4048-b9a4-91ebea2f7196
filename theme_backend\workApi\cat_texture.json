{"1": {"inputs": {"invert_mask": true, "blend_mode": "multiply", "opacity": 100, "x_percent": 35, "y_percent": 21, "mirror": "None", "scale": 0.5, "aspect_ratio": 1, "rotate": 0, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "layer_image": ["53", 0], "background_image": ["19", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V3", "_meta": {"title": "LayerUtility: ImageBlendAdvance V3"}}, "5": {"inputs": {"size": "custom", "custom_width": 1024, "custom_height": 1024, "angle": 0, "start_color": ["54", 1], "end_color": ["6", 1]}, "class_type": "LayerUtility: GradientImage V2", "_meta": {"title": "LayerUtility: GradientImage V2"}}, "6": {"inputs": {"mode": "average", "color_of": "mask", "remove_bkgd_method": "none", "invert_mask": false, "mask_grow": 0, "image": ["53", 0]}, "class_type": "LayerUtility: GetColorToneV2", "_meta": {"title": "LayerUtility: GetColorTone V2(Advance)"}}, "12": {"inputs": {"shift_x": 128, "shift_y": 128, "cyclic": true, "background_color": "#000000", "border_mask_width": 20, "border_mask_blur": 12, "image": ["1", 0]}, "class_type": "LayerUtility: ImageShift", "_meta": {"title": "LayerUtility: ImageShift"}}, "19": {"inputs": {"image": "car_mask.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "20": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["1", 0], "mask": ["19", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "Cut By Mask"}}, "22": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["20", 0], "mask": ["23", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "Cut By Mask"}}, "23": {"inputs": {"image": "car_mask_left.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "25": {"inputs": {"force_resize_width": 0, "force_resize_height": 0, "image": ["20", 0], "mask": ["26", 0]}, "class_type": "Cut By Mask", "_meta": {"title": "Cut By Mask"}}, "26": {"inputs": {"image": "car_mask_right.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "28": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 71, "y_percent": 79.10000000000001, "mirror": "None", "scale": 0.9, "aspect_ratio": 1, "rotate": 270, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "layer_image": ["22", 0], "background_image": ["5", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V3", "_meta": {"title": "LayerUtility: ImageBlendAdvance V3"}}, "30": {"inputs": {"invert_mask": true, "blend_mode": "normal", "opacity": 100, "x_percent": 13.5, "y_percent": 47.5, "mirror": "None", "scale": 0.9, "aspect_ratio": 1, "rotate": 270, "transform_method": "lanc<PERSON>s", "anti_aliasing": 0, "layer_image": ["25", 0], "background_image": ["28", 0]}, "class_type": "LayerUtility: ImageBlendAdvance V3", "_meta": {"title": "LayerUtility: ImageBlendAdvance V3"}}, "32": {"inputs": {"filename_prefix": "car_texture/car_texture", "images": ["30", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "39": {"inputs": {"images": ["53", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "40": {"inputs": {"text": ["52", 0], "clip": ["50", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "41": {"inputs": {"width": 1536, "height": 576, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent图像"}}, "42": {"inputs": {"noise_seed": 402409675735784}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "43": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "44": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["49", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "基本调度器"}}, "45": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "46": {"inputs": {"noise": ["42", 0], "guider": ["47", 0], "sampler": ["43", 0], "sigmas": ["44", 0], "latent_image": ["41", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "自定义采样器（高级）"}}, "47": {"inputs": {"model": ["49", 0], "conditioning": ["48", 0]}, "class_type": "BasicGuider", "_meta": {"title": "基本引导器"}}, "48": {"inputs": {"guidance": 3.5, "conditioning": ["40", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "49": {"inputs": {"unet_name": "shuttle-3-diffusion.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNet加载器"}}, "50": {"inputs": {"clip_name1": "t5xxl_fp8_e4m3fn.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "51": {"inputs": {"prompt": "超级赛亚人", "model_name": "Qwen2.5-32B-Instruct-GPTQ-Int4", "base_url": "http://*************:7868/v1", "api_key": "", "is_enable": true, "seed": 511}, "class_type": "mini_flux_prompt", "_meta": {"title": "☁️Mini FLUX Prompt Generator"}}, "52": {"inputs": {"text_1": ["51", 0]}, "class_type": "LayerUtility: Text<PERSON>oin", "_meta": {"title": "LayerUtility: Text<PERSON>oin"}}, "53": {"inputs": {"samples": ["46", 0], "vae": ["45", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "54": {"inputs": {"mode": "main_color", "color_of": "mask", "remove_bkgd_method": "none", "invert_mask": false, "mask_grow": 0, "image": ["53", 0]}, "class_type": "LayerUtility: GetColorToneV2", "_meta": {"title": "LayerUtility: GetColorTone V2(Advance)"}}}