<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI HMI 集成过渡演示 - Integrated Transition Demo</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="./transition_manager.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
            border: 1px solid transparent;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            padding-top: 100px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 60px;
            max-width: 800px;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            padding: 0 20px;
            margin-bottom: 60px;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .demo-card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .demo-card-title {
            font-size: 20px;
            font-weight: 600;
            color: white;
            margin-bottom: 10px;
        }

        .demo-card-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .demo-card-effect {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 10px;
            display: inline-block;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 100%;
            margin: 0 20px;
        }

        .controls-title {
            font-size: 24px;
            font-weight: 600;
            color: white;
            margin-bottom: 20px;
            text-align: center;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            display: block;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            text-decoration: none;
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .keyboard-shortcuts {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .shortcut-key {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }

        .shortcut-desc {
            color: rgba(255, 255, 255, 0.7);
        }

        .status-bar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 36px;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">AI HMI</a>
            <nav class="nav-links">
                <a href="../transition_effects_prototype.html" class="nav-link">特效演示</a>
                <a href="../1_natural_commute.html" class="nav-link" data-transition="sliding_panel">自然风格</a>
                <a href="../2_cyberpunk_drive.html" class="nav-link" data-transition="angled_panel">赛博朋克</a>
                <a href="../3_glassmorphism_wait.html" class="nav-link" data-transition="circle_expand">玻璃拟态</a>
            </nav>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <h1 class="hero-title">AI HMI 过渡特效系统</h1>
            <p class="hero-subtitle">
                基于GSAP和CSS clip-path技术实现的高性能蒙版切换效果，
                为车载人机交互界面提供流畅优雅的场景过渡体验。
                点击下方卡片体验不同的过渡效果。
            </p>
        </section>

        <!-- 演示卡片网格 -->
        <section class="demo-grid">
            <a href="../1_natural_commute.html" class="demo-card" data-transition="sliding_panel">
                <div class="demo-card-icon">🌿</div>
                <h3 class="demo-card-title">自然风格主题</h3>
                <p class="demo-card-description">
                    亲近自然的设计语言，营造宁静舒适的驾驶环境，
                    采用有机形状和自然色彩。
                </p>
                <span class="demo-card-effect">滑动面板过渡</span>
            </a>

            <a href="../2_cyberpunk_drive.html" class="demo-card" data-transition="angled_panel">
                <div class="demo-card-icon">⚡</div>
                <h3 class="demo-card-title">赛博朋克主题</h3>
                <p class="demo-card-description">
                    未来科技感的霓虹美学，打造炫酷的数字化体验，
                    充满电子感的视觉效果。
                </p>
                <span class="demo-card-effect">斜切面板过渡</span>
            </a>

            <a href="../3_glassmorphism_wait.html" class="demo-card" data-transition="circle_expand">
                <div class="demo-card-icon">💎</div>
                <h3 class="demo-card-title">玻璃拟态主题</h3>
                <p class="demo-card-description">
                    精致优雅的毛玻璃效果，营造未来科技感的视觉体验，
                    透明度与模糊的完美结合。
                </p>
                <span class="demo-card-effect">圆形扩展过渡</span>
            </a>

            <a href="../4_neumorphism_rainy.html" class="demo-card" data-transition="curtain_open">
                <div class="demo-card-icon">🎨</div>
                <h3 class="demo-card-title">新拟态主题</h3>
                <p class="demo-card-description">
                    柔和的阴影与高光，创造温暖舒适的触感体验，
                    模拟真实物理材质的质感。
                </p>
                <span class="demo-card-effect">幕布开启过渡</span>
            </a>

            <a href="../5_kawaii_family_trip.html" class="demo-card" data-transition="circle_expand">
                <div class="demo-card-icon">🌸</div>
                <h3 class="demo-card-title">可爱风格主题</h3>
                <p class="demo-card-description">
                    充满活力的色彩搭配，带来愉悦轻松的使用体验，
                    日式可爱文化的设计灵感。
                </p>
                <span class="demo-card-effect">圆形扩展过渡</span>
            </a>

            <a href="./transition_effects_prototype.html" class="demo-card" data-transition="fade">
                <div class="demo-card-icon">🎭</div>
                <h3 class="demo-card-title">特效原型演示</h3>
                <p class="demo-card-description">
                    查看所有过渡特效的独立演示，
                    了解每种效果的技术实现和视觉表现。
                </p>
                <span class="demo-card-effect">淡入淡出过渡</span>
            </a>
        </section>

        <!-- 控制面板 -->
        <section class="controls-section">
            <h2 class="controls-title">过渡控制面板</h2>
            
            <div class="control-group">
                <label class="control-label">快速导航:</label>
                <div class="control-buttons">
                    <button class="control-button" onclick="quickTransition('1_natural_commute.html', 'sliding_panel')">自然</button>
                    <button class="control-button" onclick="quickTransition('2_cyberpunk_drive.html', 'angled_panel')">赛博</button>
                    <button class="control-button" onclick="quickTransition('3_glassmorphism_wait.html', 'circle_expand')">玻璃</button>
                    <button class="control-button" onclick="quickTransition('4_neumorphism_rainy.html', 'curtain_open')">新拟态</button>
                    <button class="control-button" onclick="quickTransition('5_kawaii_family_trip.html', 'circle_expand')">可爱</button>
                </div>
            </div>

            <div class="control-group">
                <label class="control-label">测试特效:</label>
                <div class="control-buttons">
                    <button class="control-button" onclick="testEffect('sliding_panel')">滑动面板</button>
                    <button class="control-button" onclick="testEffect('angled_panel')">斜切面板</button>
                    <button class="control-button" onclick="testEffect('circle_expand')">圆形扩展</button>
                    <button class="control-button" onclick="testEffect('curtain_open')">幕布开启</button>
                </div>
            </div>

            <div class="keyboard-shortcuts">
                <h3 style="color: white; margin-bottom: 15px; font-size: 16px;">键盘快捷键</h3>
                <div class="shortcut-item">
                    <span class="shortcut-key">Ctrl + 1</span>
                    <span class="shortcut-desc">自然风格 (滑动面板)</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">Ctrl + 2</span>
                    <span class="shortcut-desc">赛博朋克 (斜切面板)</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">Ctrl + 3</span>
                    <span class="shortcut-desc">玻璃拟态 (圆形扩展)</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">Ctrl + 4</span>
                    <span class="shortcut-desc">新拟态 (幕布开启)</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">Ctrl + 5</span>
                    <span class="shortcut-desc">可爱风格 (圆形扩展)</span>
                </div>
            </div>
        </section>
    </main>

    <!-- 状态栏 -->
    <div class="status-bar" id="statusBar">
        就绪 - 点击任意卡片开始体验
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div>正在执行过渡效果...</div>
        </div>
    </div>

    <script>
        // 状态管理
        let currentStatus = '就绪';
        
        function updateStatus(message) {
            document.getElementById('statusBar').textContent = message;
            currentStatus = message;
        }

        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('active');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('active');
        }

        // 快速过渡函数
        function quickTransition(page, effect) {
            if (window.transitionManager && !window.transitionManager.isTransitioning) {
                updateStatus(`正在切换到: ${page}`);
                showLoading();
                
                const basePath = window.location.pathname.replace('/过渡/integrated_demo.html', '');
                const targetUrl = basePath + '/' + page;
                
                window.transitionManager.transitionToPage(targetUrl, effect)
                    .finally(() => {
                        hideLoading();
                    });
            }
        }

        // 测试特效函数
        function testEffect(effectType) {
            updateStatus(`测试特效: ${effectType}`);
            
            // 创建测试元素
            const testElement = document.createElement('div');
            testElement.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                z-index: 9998;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
            `;
            testElement.textContent = `${effectType} 特效测试`;
            
            document.body.appendChild(testElement);
            
            // 执行特效
            const effects = {
                sliding_panel: () => {
                    gsap.set(testElement, { clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' });
                    gsap.to(testElement, {
                        clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                        duration: 1.2,
                        ease: 'power2.inOut',
                        onComplete: () => removeTestElement(testElement)
                    });
                },
                angled_panel: () => {
                    gsap.set(testElement, { clipPath: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)' });
                    gsap.to(testElement, {
                        clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
                        duration: 1.0,
                        ease: 'power3.inOut',
                        onComplete: () => removeTestElement(testElement)
                    });
                },
                circle_expand: () => {
                    gsap.set(testElement, { clipPath: 'circle(0% at 50% 50%)' });
                    gsap.to(testElement, {
                        clipPath: 'circle(150% at 50% 50%)',
                        duration: 1.5,
                        ease: 'power2.inOut',
                        onComplete: () => removeTestElement(testElement)
                    });
                },
                curtain_open: () => {
                    gsap.set(testElement, { clipPath: 'polygon(50% 0%, 50% 0%, 50% 100%, 50% 100%)' });
                    gsap.to(testElement, {
                        clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                        duration: 1.8,
                        ease: 'power2.inOut',
                        onComplete: () => removeTestElement(testElement)
                    });
                }
            };
            
            const effect = effects[effectType];
            if (effect) {
                effect();
            } else {
                removeTestElement(testElement);
            }
        }

        function removeTestElement(element) {
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
                updateStatus('就绪 - 点击任意卡片开始体验');
            }, 1000);
        }

        // 监听过渡管理器状态
        document.addEventListener('DOMContentLoaded', function() {
            // 检查过渡管理器是否可用
            if (window.transitionManager) {
                updateStatus('过渡系统已就绪');
            } else {
                updateStatus('过渡系统加载中...');
                
                // 等待过渡管理器加载
                const checkManager = setInterval(() => {
                    if (window.transitionManager) {
                        clearInterval(checkManager);
                        updateStatus('过渡系统已就绪');
                    }
                }, 100);
            }
            
            // 添加页面入场动画
            gsap.from('.hero-section', {
                opacity: 0,
                y: 50,
                duration: 1,
                ease: 'power2.out'
            });
            
            gsap.from('.demo-card', {
                opacity: 0,
                y: 30,
                duration: 0.8,
                stagger: 0.1,
                delay: 0.3,
                ease: 'power2.out'
            });
            
            gsap.from('.controls-section', {
                opacity: 0,
                y: 30,
                duration: 0.8,
                delay: 0.8,
                ease: 'power2.out'
            });
        });

        // 监听过渡事件
        document.addEventListener('click', function(e) {
            const card = e.target.closest('.demo-card[data-transition]');
            if (card) {
                const effect = card.dataset.transition;
                const href = card.href;
                updateStatus(`准备执行 ${effect} 过渡效果`);
            }
        });
    </script>
</body>
</html>