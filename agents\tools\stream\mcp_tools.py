"""
MCP工具定义和实现
基于流式输出工具，提供标准化的MCP工具接口
"""

import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Callable
from functools import wraps

import stream_manager
from stream_manager import StreamManager
import stream_types
from stream_types import StreamEventType as EventType, StreamEvent as MessageData, create_token_event as EventFactory
import mongo_client
from mongo_client import MongoClient

logger = logging.getLogger(__name__)

class StreamingTools:
    """流式输出工具MCP封装"""
    
    def __init__(self):
        """初始化流式工具"""
        self.stream_manager = StreamManager()
        self.mongo_client = MongoClient()
        self.subscribers = {}  # 存储会话订阅者
        logger.info("🔧 StreamingTools 初始化完成")
    
    async def create_session(self, 
                           session_name: str, 
                           meta_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建新的会话
        
        Args:
            session_name: 会话名称
            meta_data: 会话元数据
            
        Returns:
            包含会话ID的响应
        """
        try:
            logger.info(f"🔄 创建新会话调用开始: session_name={session_name}, meta_data={meta_data}")
            
            # 检查StreamManager实例
            logger.info(f"🔍 检查StreamManager实例: {self.stream_manager}")
            
            # 检查create_session方法是否存在
            logger.info(f"🔍 检查create_session方法: {getattr(self.stream_manager, 'create_session', None)}")
            
            # 记录参数
            logger.info(f"📝 准备传递参数: user_id={session_name}, metadata={meta_data or {}}")
            
            # 调用StreamManager.create_session - 注意这不是异步方法，所以移除await
            logger.info(f"📞 调用StreamManager.create_session方法")
            session_id = self.stream_manager.create_session(
                user_id=session_name,
                metadata=meta_data or {}
            )
            
            # 记录返回结果
            logger.info(f"🔙 StreamManager.create_session返回: {session_id}")
            
            if session_id is None:
                logger.error("❌ StreamManager.create_session返回了None")
                return {
                    "success": False,
                    "error": "Stream Manager returned None for session ID"
                }
            
            logger.info(f"✅ 会话创建成功: {session_id}")
            return {
                "success": True,
                "session_id": session_id
            }
        except Exception as e:
            logger.error(f"❌ 创建会话失败: {str(e)}")
            logger.exception("详细异常信息:")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close_session(self, session_id: str) -> Dict[str, Any]:
        """
        关闭会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            关闭状态
        """
        try:
            logger.info(f"🔄 尝试关闭会话: {session_id}")
            await self.stream_manager.close_session(session_id)
            
            # 清理会话的所有订阅
            if session_id in self.subscribers:
                del self.subscribers[session_id]
                logger.info(f"🧹 已清理会话的所有订阅: {session_id}")
            
            logger.info(f"✅ 会话关闭成功: {session_id}")
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"❌ 关闭会话失败: {session_id}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stream_thinking(self, 
                            session_id: str, 
                            agent_name: str, 
                            content: str) -> Dict[str, Any]:
        """
        流式输出思考步骤
        
        Args:
            session_id: 会话ID
            agent_name: 代理名称
            content: 思考内容
            
        Returns:
            发布状态
        """
        try:
            event = EventFactory.create_thinking_event(
                agent_name=agent_name,
                content=content
            )
            
            await self.stream_manager.publish_event(
                session_id=session_id,
                event_type=EventType.THINKING,
                data=event
            )
            
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"发布思考步骤失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def stream_tokens(self, 
                          session_id: str, 
                          agent_name: str, 
                          content: str, 
                          is_final: bool = False) -> Dict[str, Any]:
        """
        流式输出文本内容
        
        Args:
            session_id: 会话ID
            agent_name: 代理名称
            content: 输出内容
            is_final: 是否为最后的内容块
            
        Returns:
            发布状态
        """
        try:
            event = EventFactory.create_content_event(
                agent_name=agent_name,
                content=content,
                is_final=is_final
            )
            
            await self.stream_manager.publish_event(
                session_id=session_id,
                event_type=EventType.CONTENT,
                data=event
            )
            
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"流式输出内容失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def publish_code_block(self, 
                               session_id: str, 
                               agent_name: str, 
                               code: str, 
                               language: str = "python") -> Dict[str, Any]:
        """
        发布代码块
        
        Args:
            session_id: 会话ID
            agent_name: 代理名称
            code: 代码内容
            language: 代码语言
            
        Returns:
            发布状态
        """
        try:
            event = EventFactory.create_code_event(
                agent_name=agent_name,
                code=code,
                language=language
            )
            
            await self.stream_manager.publish_event(
                session_id=session_id,
                event_type=EventType.CODE,
                data=event
            )
            
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"发布代码块失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def publish_tool_call(self, 
                              session_id: str, 
                              agent_name: str, 
                              tool_name: str, 
                              tool_input: Dict[str, Any], 
                              tool_output: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发布工具调用事件
        
        Args:
            session_id: 会话ID
            agent_name: 代理名称
            tool_name: 工具名称
            tool_input: 工具输入参数
            tool_output: 工具输出结果（可选）
            
        Returns:
            发布状态
        """
        try:
            event = EventFactory.create_tool_call_event(
                agent_name=agent_name,
                tool_name=tool_name,
                tool_input=tool_input,
                tool_output=tool_output
            )
            
            await self.stream_manager.publish_event(
                session_id=session_id,
                event_type=EventType.TOOL_CALL,
                data=event
            )
            
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"发布工具调用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def save_context(self, 
                         session_id: str, 
                         key: str, 
                         value: Any, 
                         ttl: Optional[int] = None) -> Dict[str, Any]:
        """
        保存上下文数据
        
        Args:
            session_id: 会话ID
            key: 上下文键
            value: 上下文值
            ttl: 过期时间（秒）
            
        Returns:
            保存状态
        """
        try:
            await self.stream_manager.save_context(
                session_id=session_id,
                key=key,
                value=value,
                ttl=ttl
            )
            
            return {
                "success": True
            }
        except Exception as e:
            logger.error(f"保存上下文失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_context(self, 
                        session_id: str, 
                        key: str) -> Dict[str, Any]:
        """
        获取上下文数据
        
        Args:
            session_id: 会话ID
            key: 上下文键
            
        Returns:
            包含上下文值的响应
        """
        try:
            value = await self.stream_manager.get_context(
                session_id=session_id,
                key=key
            )
            
            return {
                "success": True,
                "value": value
            }
        except Exception as e:
            logger.error(f"获取上下文失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_session_messages(self, 
                                 session_id: str, 
                                 limit: int = 100) -> Dict[str, Any]:
        """
        获取会话消息历史
        
        Args:
            session_id: 会话ID
            limit: 消息数量限制
            
        Returns:
            包含会话消息的响应
        """
        try:
            messages = await self.mongo_client.get_session_messages(
                session_id=session_id,
                limit=limit
            )
            
            return {
                "success": True,
                "messages": messages
            }
        except Exception as e:
            logger.error(f"获取会话消息失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """
        获取所有工具定义
        
        Returns:
            工具定义列表
        """
        try:
            logger.info("🔍 获取所有工具定义开始...")
            tools = [
                {
                    "name": "create_session",
                    "description": "创建新的会话，返回会话ID",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_name": {
                                "type": "string",
                                "description": "会话名称"
                            },
                            "meta_data": {
                                "type": "object",
                                "description": "会话元数据（可选）"
                            }
                        },
                        "required": ["session_name"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "close_session",
                    "description": "关闭现有会话",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            }
                        },
                        "required": ["session_id"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "stream_thinking",
                    "description": "流式输出思考步骤",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "agent_name": {
                                "type": "string",
                                "description": "代理名称"
                            },
                            "content": {
                                "type": "string",
                                "description": "思考内容"
                            }
                        },
                        "required": ["session_id", "agent_name", "content"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "stream_tokens",
                    "description": "流式输出文本内容",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "agent_name": {
                                "type": "string",
                                "description": "代理名称"
                            },
                            "content": {
                                "type": "string",
                                "description": "输出内容"
                            },
                            "is_final": {
                                "type": "boolean",
                                "description": "是否为最后的内容块"
                            }
                        },
                        "required": ["session_id", "agent_name", "content"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "publish_code_block",
                    "description": "发布代码块",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "agent_name": {
                                "type": "string",
                                "description": "代理名称"
                            },
                            "code": {
                                "type": "string",
                                "description": "代码内容"
                            },
                            "language": {
                                "type": "string",
                                "description": "代码语言"
                            }
                        },
                        "required": ["session_id", "agent_name", "code"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "publish_tool_call",
                    "description": "发布工具调用事件",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "agent_name": {
                                "type": "string",
                                "description": "代理名称"
                            },
                            "tool_name": {
                                "type": "string",
                                "description": "工具名称"
                            },
                            "tool_input": {
                                "type": "object",
                                "description": "工具输入参数"
                            },
                            "tool_output": {
                                "type": "object",
                                "description": "工具输出结果（可选）"
                            }
                        },
                        "required": ["session_id", "agent_name", "tool_name", "tool_input"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "save_context",
                    "description": "保存上下文数据",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "key": {
                                "type": "string",
                                "description": "上下文键"
                            },
                            "value": {
                                "description": "上下文值（任意类型）"
                            },
                            "ttl": {
                                "type": "integer",
                                "description": "过期时间（秒）"
                            }
                        },
                        "required": ["session_id", "key", "value"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "get_context",
                    "description": "获取上下文数据",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "key": {
                                "type": "string",
                                "description": "上下文键"
                            }
                        },
                        "required": ["session_id", "key"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "value": {
                                "description": "上下文值（可能是任意类型）"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                },
                {
                    "name": "get_session_messages",
                    "description": "获取会话消息历史",
                    "input_schema": {
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "会话ID"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "消息数量限制"
                            }
                        },
                        "required": ["session_id"]
                    },
                    "output_schema": {
                        "type": "object",
                        "properties": {
                            "success": {
                                "type": "boolean",
                                "description": "是否成功"
                            },
                            "messages": {
                                "type": "array",
                                "description": "会话消息列表"
                            },
                            "error": {
                                "type": "string",
                                "description": "错误信息（如果失败）"
                            }
                        }
                    }
                }
            ]
            
            logger.info(f"✅ 成功获取工具定义，共 {len(tools)} 个工具")
            
            # 记录每个工具
            for i, tool in enumerate(tools):
                logger.info(f"🛠️ 工具定义 {i+1}: {tool.get('name', '未命名')} - {tool.get('description', '无描述')}")
            
            return tools
        except Exception as e:
            logger.error(f"❌ 获取工具定义失败: {str(e)}")
            logger.exception("详细异常信息:")
            # 返回空列表，但不中断程序流程
            return []
    
    def get_tool_implementations(self) -> Dict[str, Callable]:
        """
        获取所有工具实现
        
        Returns:
            工具名称到工具实现的映射
        """
        try:
            logger.info("🔍 获取所有工具实现开始...")
            implementations = {
                "create_session": self.create_session,
                "close_session": self.close_session,
                "stream_thinking": self.stream_thinking,
                "stream_tokens": self.stream_tokens,
                "publish_code_block": self.publish_code_block,
                "publish_tool_call": self.publish_tool_call,
                "save_context": self.save_context,
                "get_context": self.get_context,
                "get_session_messages": self.get_session_messages
            }
            
            logger.info(f"✅ 成功获取工具实现，共 {len(implementations)} 个实现")
            
            # 记录每个工具实现
            for i, (name, impl) in enumerate(implementations.items()):
                logger.info(f"🛠️ 工具实现 {i+1}: {name}")
            
            return implementations
        except Exception as e:
            logger.error(f"❌ 获取工具实现失败: {str(e)}")
            logger.exception("详细异常信息:")
            # 返回空字典，但不中断程序流程
            return {}
    
    async def subscribe_to_session(self, session_id: str, callback: Callable) -> bool:
        """
        订阅会话事件
        
        Args:
            session_id: 会话ID
            callback: 回调函数，接收事件数据
            
        Returns:
            订阅是否成功
        """
        logger.info(f"🔄 尝试订阅会话: {session_id}")
        try:
            if session_id not in self.subscribers:
                self.subscribers[session_id] = []
            
            self.subscribers[session_id].append(callback)
            
            # 尝试使用StreamManager订阅
            try:
                if hasattr(self.stream_manager, 'subscribe_to_session'):
                    await self.stream_manager.subscribe_to_session(session_id, callback)
                    logger.info(f"✅ 通过StreamManager订阅会话成功: {session_id}")
            except Exception as e:
                logger.warning(f"⚠️ StreamManager订阅尝试失败: {str(e)}")
            
            logger.info(f"✅ 会话订阅成功: {session_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 订阅会话失败: {session_id}, 错误: {str(e)}")
            return False
    
    async def unsubscribe_from_session(self, session_id: str, callback: Callable) -> bool:
        """
        取消订阅会话事件
        
        Args:
            session_id: 会话ID
            callback: 之前注册的回调函数
            
        Returns:
            取消订阅是否成功
        """
        logger.info(f"🔄 尝试取消订阅会话: {session_id}")
        try:
            if session_id in self.subscribers and callback in self.subscribers[session_id]:
                self.subscribers[session_id].remove(callback)
                
                # 如果没有更多订阅者，删除会话键
                if not self.subscribers[session_id]:
                    del self.subscribers[session_id]
                
                # 尝试使用StreamManager取消订阅
                try:
                    if hasattr(self.stream_manager, 'unsubscribe_from_session'):
                        await self.stream_manager.unsubscribe_from_session(session_id, callback)
                        logger.info(f"✅ 通过StreamManager取消订阅成功: {session_id}")
                except Exception as e:
                    logger.warning(f"⚠️ StreamManager取消订阅尝试失败: {str(e)}")
                
                logger.info(f"✅ 会话取消订阅成功: {session_id}")
                return True
            
            logger.warning(f"⚠️ 未找到会话订阅: {session_id}")
            return False
        except Exception as e:
            logger.error(f"❌ 取消订阅会话失败: {session_id}, 错误: {str(e)}")
            return False
    
    def has_session(self, session_id: str) -> bool:
        """
        检查会话是否存在
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话是否存在
        """
        logger.info(f"🔍 检查会话是否存在: {session_id}")
        try:
            # 首先检查本地订阅者列表
            if session_id in self.subscribers:
                logger.info(f"✅ 会话存在于订阅者列表中: {session_id}")
                return True
            
            # 尝试使用StreamManager检查
            try:
                if hasattr(self.stream_manager, 'has_session'):
                    result = self.stream_manager.has_session(session_id)
                    logger.info(f"✅ StreamManager会话检查结果: {result}, 会话ID: {session_id}")
                    return result
            except Exception as e:
                logger.warning(f"⚠️ StreamManager会话检查尝试失败: {str(e)}")
            
            logger.info(f"❓ 未找到会话: {session_id}")
            return False
        except Exception as e:
            logger.error(f"❌ 会话检查失败: {session_id}, 错误: {str(e)}")
            return False 