<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">场景生成助手</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧表单区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">输入请求</h2>

            <form @submit.prevent="generateScene">
              <div class="mb-6">
                <label for="prompt" class="block text-sm font-medium text-purple-200 mb-2">
                  场景描述 <span class="text-red-400">*</span>
                </label>
                <textarea id="prompt" v-model="prompt" rows="6" class="w-full px-4 py-3 bg-gray-900 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="请描述您希望生成的驾驶场景..." required style="background-color: rgba(15, 23, 42, 0.7);"></textarea>
                <p class="text-xs text-gray-400 mt-2">
                  输入清晰的描述，详细说明场景中应包含的环境、天气、执行动作等。
                </p>
              </div>

              <!-- 示例标签区域 -->
              <div class="mb-6">
                <p class="text-sm font-medium text-purple-200 mb-3">试一试</p>
                <div class="flex flex-wrap gap-2">
                  <button 
                    v-for="(example, index) in examples" 
                    :key="index"
                    @click="useExample(example)"
                    class="px-3 py-1.5 bg-purple-500/20 text-purple-200 rounded-lg hover:bg-purple-500/30 transition-colors duration-200 text-sm"
                  >
                    {{ example }}
                  </button>
                </div>
              </div>

              <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isGenerating">
                  <svg v-if="isGenerating" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isGenerating ? '生成中...' : '开始生成' }}
                </button>
              </div>
            </form>
          </GlassPanel>
        </div>

        <!-- 右侧预览区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">场景输出</h2>

            <div v-if="isGenerating" class="flex flex-col items-center justify-center my-6">
              <ProgressBar :progress="generationProgress" :completed-steps="1" :total-steps="1" class="w-full mb-4" />
              <p class="text-purple-200">生成中，请耐心等待... {{ Math.round(generationProgress) }}%</p>
            </div>

            <div v-if="generatedScene" class="bg-dark-800/60 rounded-lg p-8 overflow-y-auto" style="min-height: 300px;">
              <p class="text-white" style="white-space: pre-wrap;">{{ generatedScene }}</p>
            </div>

            <div v-else-if="!isGenerating" class="bg-dark-800/60 rounded-lg p-8 flex flex-col items-center justify-center min-h-[300px]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-purple-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M4 4h16v16H4V4z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" />
                <path d="M8 8h8" />
                <path d="M8 12h8" />
                <path d="M8 16h8" />
              </svg>

              <p class="text-purple-200 text-center mb-2">尚未生成场景</p>
              <p class="text-gray-400 text-sm text-center">填写左侧表单并点击"开始生成"按钮</p>
            </div>
          </GlassPanel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { themeApi } from "../api/themeApi";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

const GlassPanel = defineAsyncComponent(
    () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
    () => import("../components/ProgressBar.vue")
);

const prompt = ref<string>("");
const isGenerating = ref<boolean>(false);
const generationProgress = ref<number>(0);
const generatedScene = ref<string | null>(null);
const taskId = ref<string>("");

// 示例场景列表
const examples = [
  "雨天高速行驶场景",
  "城市拥堵路段场景",
  "夜间山路驾驶场景"
];

// 使用示例场景
const useExample = (example: string) => {
  prompt.value = example;
  generateScene();
};

// 生成任务ID
const generateTaskId = (): string => {
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `${uuid}_${timestamp}`;
};

// 生成场景
const generateScene = async () => {
  if (!prompt.value.trim()) {
    alert("请输入场景描述");
    return;
  }

  try {
    isGenerating.value = true;
    generationProgress.value = 10; // 初始进度
    generatedScene.value = null;

    taskId.value = generateTaskId();

    // 调用API
    const response = await themeApi.textToScene({
      "model": "scene-qwen2.5-7b",
      "messages": [
        {
          "role": "user",
          "content": `请根据用户要求和车辆当前状态，生成合适的场景方案。\n\n用户要求如下：${prompt.value.trim()}\n车辆当前状态：（播放的歌名：无\n音乐的歌手：无\n请求人位置：主驾\n当前车内温度：23\n当前车外温度：23\n当前车辆档位：P\n当前车辆速度：0\n当前主驾空调温度：\n当前副驾空调温度：\n当前空调风量：0)`
        }
      ],
      "max_tokens": 512,
      "temperature": 0
    });

    // 模拟进度
    const updateProgress = () => {
      if (generationProgress.value < 90) {
        generationProgress.value += 5;
        setTimeout(updateProgress, 3000);
      }
    };
    updateProgress();

    // 设置生成的场景文本
    if (response.choices && response.choices[0].message && response.choices[0].message.content) {
      const sceneContent = response.choices[0].message.content;
      // 解析 content 中的场景文本
      generatedScene.value = sceneContent;
    }

    generationProgress.value = 100;
  } catch (error) {
    console.error("生成场景失败", error);
    alert("生成场景失败，请重试");
  } finally {
    isGenerating.value = false;
  }
};
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}
</style>
