<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">语义大模型</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in">
        <!-- 音乐DJ大模型卡片 -->
        <GlassCard title="音乐DJ大模型" description="基于生成式AI引擎，为用户提供音乐相关服务，包括歌手推荐、歌曲创作背景问答、意境搜索和歌词搜索"
                   buttonText="立即体验" color="primary" @click="$router.push('/music-assistant')">
          <template #icon>
            <svg class="icon-svg w-10 h-10 text-info" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="icon-border" d="M3 19C3 14.5885 4.5885 3 12 3C19.4115 3 21 14.5885 21 19C21 22.5829 19.1839 25.3969 16.2 26C15.4122 25.9752 14.6219 25.5302 13.92 25H9.88C9.17814 25.5302 8.38786 25.9752 7.6 26C4.6161 25.3969 3 22.5829 3 19Z" stroke="currentColor" stroke-width="2" />
              <path class="icon-path" d="M12 19C12 21.2091 10.2091 23 8 23C5.79086 23 4 21.2091 4 19C4 16.7909 5.79086 15 8 15C10.2091 15 12 16.7909 12 19Z" stroke="currentColor" stroke-width="2" />
              <path class="icon-path-delay1" d="M12 19C12 21.2091 13.7909 23 16 23C18.2091 23 20 21.2091 20 19C20 16.7909 18.2091 15 16 15C13.7909 15 12 16.7909 12 19Z" stroke="currentColor" stroke-width="2" />
            </svg>
          </template>
        </GlassCard>

        <!-- 出行大咖卡片 -->
        <GlassCard title="出行大咖" description="为您的旅行提供个性化推荐，包括景点推荐、路书规划和生活方式建议"
                   buttonText="立即体验" color="secondary" @click="$router.push('/trip-assistant')">
          <template #icon>
            <svg class="icon-svg w-10 h-10 text-info" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path class="icon-border" d="M17 21H7C5.34315 21 4 19.6569 4 18V7C4 5.34315 5.34315 4 7 4H17C18.6569 4 20 5.34315 20 7V18C20 19.6569 18.6569 21 17 21Z" stroke="currentColor" stroke-width="2" />
              <path class="icon-path" d="M12 16V8" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              <path class="icon-path-delay1" d="M10 10H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
            </svg>
          </template>
        </GlassCard>

        <!-- 闲聊大模型卡片 -->
        <GlassCard
            title="闲聊大模型"
            description="与您进行自然流畅的对话，提供娱乐和交流服务"
            buttonText="立即体验"
            color="info"
            @click="$router.push('/chat-assistant')"
        >
          <template #icon>
            <svg class="icon-svg w-10 h-10 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 3h18v18H3V3z" stroke="currentColor" stroke-width="2" fill="none"/>
              <path d="M8 6h8v2H8V6zm0 4h8v2H8v-2zm0 4h8v2H8v-2zm8-8h2v2h-2v-2zm0 4h2v2h-2v-2zm0 4h2v2h-2v-2z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </template>
        </GlassCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from "vue";

const GlassCard = defineAsyncComponent(
    () => import("../components/GlassCard.vue")
);
</script>

<style scoped>
.tech-bg {
  background-color: #0f172a;
  background-image: radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
  radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
  radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  border-radius: 0.75rem;
}

.icon-svg {
  transition: transform 0.3s ease;
}

.glass-card:hover .icon-svg {
  transform: scale(1.1);
}
</style>
