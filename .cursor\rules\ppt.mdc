---
description: 
globs: 
alwaysApply: true
---
我会给你一个文件，分析内容，并将其转化为美观漂亮的中文可视化网页作品集：
内容要求
保持原文件的核心信息，但以更易读、可视化的方式呈现
在页面底部添加作者信息区域，包含：
作者姓名: [寂寞的熊猫]
版权信息和年份
设计风格
整体风格参考Linear App的简约现代设计
使用清晰的视觉层次结构，突出重要内容
配色方案应专业、和谐，适合长时间阅读
技术规范
使用HTML5、TailwindCSS 3.0+（通过CDN引入）和必要的JavaScript
实现完整的深色/浅色模式切换功能，默认跟随系统设置
代码结构清晰，包含适当注释，便于理解和维护
响应式设计
页面必须在所有设备上（手机、平板、桌面）完美展示
针对不同屏幕尺寸优化布局和字体大小
确保移动端有良好的触控体验
媒体资源
使用文档中的Markdown图片链接（如果有的话）
使用文档中的视频嵌入代码（如果有的话）
图标与视觉元素
使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
根据内容主题选择合适的插图或图表展示数据
避免使用emoji作为主要图标
交互体验
添加适当的微交互效果提升用户体验：
按钮悬停时有轻微放大和颜色变化    
卡片元素悬停时有精致的阴影和边框效果    
页面滚动时有平滑过渡效果    
内容区块加载时有优雅的淡入动画
性能优化
确保页面加载速度快，避免不必要的大型资源
实现懒加载技术用于长页面内容
输出要求
提供完整可运行的单一HTML文件，包含所有必要的CSS和JavaScript
确保代码符合W3C标准，无错误警告
页面在不同浏览器中保持一致的外观和功能
请根据上传文件的内容类型（文档、数据、图片等），创建最适合展示该内容的可视化网页。