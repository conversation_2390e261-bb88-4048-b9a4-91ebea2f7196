<template>
  <div class="glowing-line-container" :style="containerStyle">
    <!-- 使用重构后的 ParticleEffect 组件 -->
    <ParticleEffect :width="width" :height="height" :primaryColor="primaryColor" :secondaryColor="secondaryColor" :particleColor="particleColor" :particleDensity="particleDensity" :minSize="minSize" :maxSize="maxSize" :speed="speed" @initialized="handleInitialized" ref="particleEffectRef" />
  </div>
</template>

<script lang="ts">
// 提供一个常规脚本导出以便其他组件导入
import { defineComponent } from "vue";

export default defineComponent({
  name: "GlowingLine",
});
</script>

<script setup lang="ts">
import { ref, computed } from "vue";
import ParticleEffect from "./ParticleEffect.vue";
import { ParticleComponentRef } from "./types";

// 组件属性
const props = defineProps({
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "10rem",
  },
  primaryColor: {
    type: String,
    default: "rgba(99, 102, 241, 0.7)", // 降低默认不透明度
  },
  secondaryColor: {
    type: String,
    default: "rgba(59, 130, 246, 0.7)", // 降低默认不透明度
  },
  particleColor: {
    type: String,
    default: "rgba(255, 255, 255, 0.6)", // 降低默认不透明度
  },
  particleDensity: {
    type: Number,
    default: 50, // 降低默认密度
  },
  minSize: {
    type: Number,
    default: 0.2, // 降低最小尺寸
  },
  maxSize: {
    type: Number,
    default: 1.0, // 降低最大尺寸
  },
  speed: {
    type: Number,
    default: 0.3, // 降低默认速度
  },
});

// 容器样式
const containerStyle = computed(() => ({
  width: props.width,
  height: props.height,
}));

// 引用粒子效果组件
const particleEffectRef = ref<ParticleComponentRef>(null);

// 处理初始化完成事件
const handleInitialized = () => {
  console.log("ParticleEffect 初始化完成");
};

// 暴露公共方法，兼容原始 API
defineExpose({
  // 重新初始化粒子
  reinitialize: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.reinitialize();
    }
  },
  // 暂停动画
  pause: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.pause();
    }
  },
  // 恢复动画
  resume: () => {
    if (particleEffectRef.value) {
      particleEffectRef.value.resume();
    }
  },
});
</script>

<style scoped>
.glowing-line-container {
  position: relative;
  overflow: hidden;
  background-color: transparent;
}
</style> 