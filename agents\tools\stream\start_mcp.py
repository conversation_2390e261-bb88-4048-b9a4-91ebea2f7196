#!/usr/bin/env python
"""
MCP服务器启动脚本
直接修改Python路径启动MCP服务
"""

import os
import sys
import logging
import asyncio
import argparse
from dotenv import load_dotenv

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到Python路径
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 现在可以导入当前目录下的模块
import mcp_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

async def main(host: str, port: int, name: str, description: str):
    """
    启动MCP服务器
    
    Args:
        host: 主机地址
        port: 端口号
        name: 服务器名称
        description: 服务器描述
    """
    # 加载环境变量
    load_dotenv()
    
    logger.info(f"正在启动MCP服务器 {name} 在 {host}:{port}...")
    
    # 创建服务器实例
    server = mcp_server.create_mcp_server(
        name=name,
        description=description,
        version="1.0.0"
    )
    
    try:
        # 启动服务器
        await server.start(host=host, port=port)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        await server.stop()
    except Exception as e:
        logger.error(f"服务器运行错误: {str(e)}")
        await server.stop()
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="启动流式输出MCP服务器")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="主机地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="端口号 (默认: 8000)")
    parser.add_argument("--name", type=str, default="streaming-mcp", help="服务器名称")
    parser.add_argument("--description", type=str, default="流式输出MCP服务器", help="服务器描述")
    
    args = parser.parse_args()
    
    # 运行主函数
    asyncio.run(main(
        host=args.host,
        port=args.port,
        name=args.name,
        description=args.description
    )) 