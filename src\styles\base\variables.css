/* AI HMI 基础CSS变量定义 - 支持壁纸整合和动态主题 */

:root {
  /* === 透明度层次系统 === */
  --transparency-none: 0;           /* VPA数字人完全透明背景 */
  --transparency-high: 0.2;         /* 临时交互组件 */
  --transparency-medium: 0.6;       /* 常驻卡片组件 */
  --transparency-low: 0.9;          /* 重要信息组件 */

  /* === 毛玻璃效果参数 === */
  --blur-light: 8px;               /* 轻度模糊 */
  --blur-medium: 12px;             /* 中度模糊 */
  --blur-heavy: 20px;              /* 重度模糊 */
  
  --saturate-light: 1.2;           /* 轻度饱和度增强 */
  --saturate-medium: 1.5;          /* 中度饱和度增强 */
  --saturate-heavy: 1.8;           /* 重度饱和度增强 */
  
  --brightness-light: 1.1;         /* 轻度亮度调节 */
  --brightness-medium: 1.2;        /* 中度亮度调节 */
  --brightness-heavy: 1.3;         /* 重度亮度调节 */

  /* === 动态壁纸适配变量 === */
  --wallpaper-brightness: 0.5;     /* 壁纸亮度 (0-1) */
  --wallpaper-dominant-color: #ffffff; /* 壁纸主色调 */
  --wallpaper-contrast-color: #000000; /* 壁纸对比色 */

  /* === 基础色彩系统 === */
  --color-primary: #007aff;        /* 主色调 */
  --color-secondary: #5856d6;      /* 辅助色 */
  --color-accent: #ff9500;         /* 强调色 */
  --color-success: #34c759;        /* 成功色 */
  --color-warning: #ff9500;        /* 警告色 */
  --color-error: #ff3b30;          /* 错误色 */

  /* === 文字颜色 (动态适配) === */
  --text-primary: rgba(0, 0, 0, 0.9);
  --text-secondary: rgba(0, 0, 0, 0.6);
  --text-tertiary: rgba(0, 0, 0, 0.4);
  --text-inverse: rgba(255, 255, 255, 0.9);

  /* === 背景颜色 === */
  --bg-primary: rgba(255, 255, 255, var(--transparency-medium));
  --bg-secondary: rgba(248, 248, 248, var(--transparency-medium));
  --bg-tertiary: rgba(242, 242, 242, var(--transparency-medium));
  --bg-overlay: rgba(0, 0, 0, 0.3);

  /* === 边框和阴影 === */
  --border-color: rgba(255, 255, 255, 0.2);
  --border-radius-small: 8px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xl: 24px;

  /* === 阴影系统 === */
  --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(0, 0, 0, 0.1);

  /* === 间距系统 === */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* === 字体系统 === */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* === 网格系统 === */
  --grid-columns: 8;               /* 8列网格 */
  --grid-rows: 4;                  /* 4行网格 */
  --grid-gap: var(--spacing-md);   /* 网格间距 */

  /* === 动画参数 === */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* === Z-index层级 === */
  --z-background: -1;
  --z-base: 0;
  --z-cards: 10;
  --z-navigation: 20;
  --z-overlay: 30;
  --z-modal: 40;
  --z-tooltip: 50;
  --z-vpa: 100;                    /* VPA数字人最高层级 */
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --text-tertiary: rgba(255, 255, 255, 0.4);
    --text-inverse: rgba(0, 0, 0, 0.9);

    --bg-primary: rgba(28, 28, 30, var(--transparency-medium));
    --bg-secondary: rgba(44, 44, 46, var(--transparency-medium));
    --bg-tertiary: rgba(58, 58, 60, var(--transparency-medium));
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  :root {
    --transparency-medium: 0.8;
    --transparency-low: 0.95;
    --blur-light: 4px;
    --blur-medium: 6px;
    --blur-heavy: 8px;
  }
}

/* === 减少动画模式 === */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
  }
}
