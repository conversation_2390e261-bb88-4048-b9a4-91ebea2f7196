<template>
  <div class="sparkle-container" ref="container">
    <slot></slot>
    <div v-for="sparkle in sparkles" :key="sparkle.id" class="sparkle" :style="{
           top: `${sparkle.top}%`,
           left: `${sparkle.left}%`,
           width: `${sparkle.size}px`,
           height: `${sparkle.size}px`,
           animationDelay: `${sparkle.delay}ms`,
           animationDuration: `${sparkle.duration}ms`
         }">
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";

interface Sparkle {
  id: number;
  top: number;
  left: number;
  size: number;
  delay: number;
  duration: number;
}

const container = ref<HTMLElement | null>(null);
const sparkles = ref<Sparkle[]>([]);
const sparkleCount = 15; // 闪光数量
let intervalId: number | null = null;

// 生成随机闪光
const createSparkle = (id: number): Sparkle => {
  return {
    id,
    top: Math.random() * 100,
    left: Math.random() * 100,
    size: 8 + Math.random() * 10, // 8px 到 18px 的随机大小
    delay: Math.random() * 1500, // 0 到 1500ms 的随机延迟
    duration: 500 + Math.random() * 1000, // 500ms 到 1500ms 的随机持续时间
  };
};

// 初始化闪光
const initSparkles = () => {
  sparkles.value = Array.from({ length: sparkleCount }, (_, i) =>
    createSparkle(i)
  );
};

// 刷新闪光位置
const refreshSparkles = () => {
  const randomIndex = Math.floor(Math.random() * sparkleCount);
  sparkles.value[randomIndex] = createSparkle(randomIndex);
};

onMounted(() => {
  initSparkles();
  // 每隔一段时间刷新一些闪光点
  intervalId = window.setInterval(() => {
    refreshSparkles();
  }, 300);
});

onUnmounted(() => {
  if (intervalId !== null) {
    clearInterval(intervalId);
  }
});
</script>

<style scoped>
.sparkle-container {
  position: relative;
  display: inline-block;
}

.sparkle {
  position: absolute;
  pointer-events: none;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 0C10.5523 0 11 4.47715 11 10C11 15.5228 10.5523 20 10 20C9.44772 20 9 15.5228 9 10C9 4.47715 9.44772 0 10 0Z' fill='white'/%3E%3Cpath d='M20 10C20 10.5523 15.5228 11 10 11C4.47715 11 0 10.5523 0 10C0 9.44772 4.47715 9 10 9C15.5228 9 20 9.44772 20 10Z' fill='white'/%3E%3Cpath d='M17.0711 2.92893C17.4616 3.31945 14.2512 8.06066 10 12.3219C5.74878 16.5831 0.998582 19.7935 0.608058 19.403C0.217533 19.0125 3.42788 14.2712 7.67911 10.01C11.9303 5.74878 16.6805 2.5384 17.0711 2.92893Z' fill='white'/%3E%3Cpath d='M2.92893 2.92893C3.31946 2.5384 8.06067 5.74878 12.3219 10C16.5831 14.2512 19.7935 19.0124 19.403 19.403C19.0125 19.7935 14.2713 16.5831 10.0101 12.3219C5.74879 8.06066 2.53841 3.31945 2.92893 2.92893Z' fill='white'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0;
  will-change: transform, opacity;
  animation: sparkle-appear-disappear 1000ms linear forwards;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 1));
}

@keyframes sparkle-appear-disappear {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

/* 增强闪光颜色和亮度 */
.sparkle:nth-child(3n + 1) {
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1))
    drop-shadow(0 0 4px rgba(99, 102, 241, 1)) hue-rotate(0deg);
}

.sparkle:nth-child(3n + 2) {
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1))
    drop-shadow(0 0 4px rgba(139, 92, 246, 1)) hue-rotate(120deg);
}

.sparkle:nth-child(3n) {
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1))
    drop-shadow(0 0 4px rgba(59, 130, 246, 1)) hue-rotate(240deg);
}
</style> 