/* AI HMI 重置样式 - 为车载系统优化 */

/* === 基础重置 === */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background: transparent; /* 让壁纸透过 */
  overflow: hidden; /* 车载系统不需要滚动 */
  user-select: none; /* 防止意外选择文本 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* === 移除默认样式 === */
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
}

ul, ol {
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  background: none;
  border: none;
  font: inherit;
  cursor: pointer;
  outline: none;
}

input, textarea, select {
  font: inherit;
  border: none;
  outline: none;
  background: transparent;
}

img, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* === 车载系统专用样式 === */
#app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: transparent;
}

/* === 触摸优化 === */
button, 
[role="button"],
.clickable {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  min-height: 44px; /* 车载系统最小触摸目标 */
  min-width: 44px;
}

/* === 焦点样式 === */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--border-radius-small);
}

/* === 滚动条样式 (如果需要) === */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  backdrop-filter: blur(10px);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 选择样式 === */
::selection {
  background: rgba(var(--color-primary), 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(var(--color-primary), 0.3);
  color: var(--text-primary);
}

/* === 禁用样式 === */
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* === 隐藏样式 === */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === 动画基础 === */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, backdrop-filter;
  transition-duration: var(--transition-fast);
  transition-timing-function: var(--animation-smooth);
}

/* === 减少动画模式 === */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  :focus-visible {
    outline-width: 3px;
  }
}

/* === 打印样式 === */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
