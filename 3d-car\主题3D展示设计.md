# 超维3D汽车展示模块 - 开发指导文档

## 项目概述

超维3D汽车展示系统是一个将小米SU7的3D实时渲染技术与现代化UI融为一体的沉浸式交互展示平台。本项目将su7-replica项目中的3D渲染技术进行组件化改造，将其无缝集成到theme_ui项目中，打造科技感十足的用户体验。

## 系统架构设计

### 组件架构

系统采用模块化组件设计，以下是需要开发的主要组件：

1. **核心组件**：
   - `CarShowcaseView.vue`: 3D汽车展示主视图
   - `CarControlPanel.vue`: 交互控制面板
   - `TransitionPortal.vue`: 页面转场特效组件
   - `InfoPointSystem.vue`: 车辆信息点展示系统
   - `FloatingActionButton.vue`: 悬浮交互按钮

2. **功能组件**：
   - `EnvironmentController.vue`: 环境控制器
   - `CarColorPicker.vue`: 车身颜色选择器
   - `CameraController.js`: 相机控制器
   - `EffectsManager.js`: 特效管理器

3. **公共组件**：
   - `LoadingScreen.vue`: 加载界面
   - `InfoCard.vue`: 信息卡片
   - `GlassButton.vue`: 玻璃态按钮

### 页面架构

- `WelcomeScreen.vue`: 欢迎页/启动页
- `CarShowcasePage.vue`: 3D汽车展示页面
- 使用现有的`Home.vue`: 主页

## 技术实现规范

### 开发技术栈

- **前端框架**: Vue 3 + TypeScript
- **3D渲染**: Three.js + kokomi.js
- **动画库**: GSAP
- **状态管理**: Pinia (用于复杂状态管理)
- **样式方案**: Tailwind CSS + 自定义SCSS

### 代码组织规范

1. **目录结构**:
```
src/
├── components/
│   ├── car3d/              // 3D展示相关组件
│   │   ├── CarShowcaseView.vue
│   │   ├── CarControlPanel.vue
│   │   ├── InfoPointSystem.vue
│   │   └── ...
│   └── ...
├── composables/           // 可复用的组合式函数
│   ├── useCarInteraction.ts
│   ├── useTransition.ts
│   └── ...
├── utils/
│   ├── three/             // Three.js相关工具
│   │   ├── CameraController.js
│   │   ├── EffectsManager.js
│   │   └── ...
│   └── ...
├── views/
│   ├── CarShowcasePage.vue
│   └── ...
└── ...
```

2. **命名规范**:
   - 组件使用PascalCase命名
   - 文件名与组件名保持一致
   - composables使用camelCase，以use开头
   - 工具类使用PascalCase

3. **注释规范**:
   - 每个组件顶部添加组件说明
   - 复杂方法需添加方法说明
   - Three.js相关代码需详细注释

## 交互流程实现指南

### 1. 欢迎页到3D展示页转场

**设计要求**：
- 简洁的欢迎页面，显示品牌标识和"进入体验"按钮
- 点击按钮后，展示加载动画，然后平滑过渡到3D展示页面

**实现步骤**：
1. 在`WelcomeScreen.vue`中创建欢迎页面UI
2. 使用`TransitionPortal.vue`组件处理页面转场
3. 加载完成后使用GSAP控制相机从远处平滑推进到预设位置

**代码实现关键点**：
- 使用Vue的动态组件和Transition组件实现页面切换
- 资源预加载显示进度条
- 使用GSAP的timeline处理动画序列

### 2. 3D展示页交互控制

**设计要求**：
- 支持鼠标拖拽旋转查看车辆不同角度
- 滚轮缩放查看细节
- 左侧垂直排列的功能按钮组
- 车身上可交互的信息点

**实现步骤**：
1. 基于kokomi.js的控制器实现基础交互
2. 开发`CarControlPanel.vue`实现功能按钮组
3. 实现`InfoPointSystem.vue`处理信息点交互

**代码实现关键点**：
- 使用Raycaster处理鼠标与3D物体的交互
- 信息点使用CSS3D渲染，确保UI清晰
- 使用节流函数优化拖拽性能

### 3. 从3D展示页返回主页

**设计要求**：
- 右上角或左下角设置"返回主页"按钮
- 点击按钮触发转场动画：3D场景淡出，UI界面淡入

**实现步骤**：
1. 添加`FloatingActionButton.vue`作为返回按钮
2. 点击按钮触发`TransitionPortal.vue`的转场动画
3. 完成转场后路由跳转到Home页面

**代码实现关键点**：
- 转场过程中暂停3D渲染，释放资源
- 使用router的beforeEach守卫确保转场动画完成后再跳转
- 保存用户当前查看状态，以便再次进入时恢复

### 4. 从主页进入3D展示页

**设计要求**：
- 在Home.vue的卡片网格中添加"超维3D展示"卡片
- 点击卡片触发平滑转场到3D展示页

**实现步骤**：
1. 在Home.vue中添加新的GlassCard组件
2. 设计专门的3D展示入口卡片样式
3. 点击时触发转场动画并跳转

**代码实现关键点**：
- 使用与现有卡片一致的动画和交互效果
- 预加载关键3D资源
- 实现点击波纹效果增强交互反馈

## 视觉设计规范

### 1. 配色方案

- **主背景色**: 深空黑 `#0A0E17`
- **强调色**: 科技蓝 `#3B82F6`
- **辅助色**: 流光银 `#E2E8F0`
- **交互高亮色**: 亮蓝 `#60A5FA`
- **警示色**: 珊瑚红 `#F87171`

### 2. 布局与尺寸

- **大屏模式** (>1440px):
  - 控制按钮尺寸: 64px × 64px
  - 内边距: 32px
  - 文字基础大小: 18px

- **桌面模式** (1024px-1440px):
  - 控制按钮尺寸: 56px × 56px
  - 内边距: 24px
  - 文字基础大小: 16px

- **平板/移动模式** (<1024px):
  - 控制按钮尺寸: 48px × 48px
  - 内边距: 16px
  - 文字基础大小: 14px

### 3. 动画与过渡效果

- **按钮交互**:
  - 悬停: 轻微放大(scale: 1.05)和发光效果
  - 点击: 波纹扩散效果，短暂缩小(scale: 0.95)
  - 过渡时间: 0.3s

- **页面转场**:
  - 淡入淡出持续时间: 0.8s
  - 位移效果: 使用cubic-bezier(0.4, 0, 0.2, 1)缓动函数
  - 3D转场: 透视效果和深度变化

- **信息卡片**:
  - 展开动画: 弹性曲线，从中心向外扩展
  - 关闭动画: 淡出并缩小

## 性能优化指南

### 1. 3D渲染优化

- 实现动态LOD(细节层次)系统
- 仅在视锥体内的物体进行渲染
- 使用实例化渲染(Instancing)优化相似物体
- 合理设置渲染分辨率，保持60fps

### 2. 资源加载优化

- 模型使用draco压缩减小体积
- 纹理使用WebP格式并进行预加载
- 使用资源优先级队列，优先加载关键资源
- 实现渐进式加载策略

### 3. 内存管理

- 正确处理组件卸载时的资源释放
- 使用对象池管理频繁创建销毁的对象
- 监控内存使用，避免内存泄漏

## 特效实现指南

### 1. 光流交互效果

**设计说明**:
当用户进入3D展示页或与车辆交互时，光线粒子从四周流向汽车模型，强调车身流线型设计。

**实现方法**:
- 使用Three.js的Points系统创建粒子
- 设计路径算法使粒子沿车身流线运动
- 通过shader实现粒子的颜色和亮度变化

### 2. 焦点交互效果

**设计说明**:
用户悬停或点击车辆特定部位时，该区域被强调显示，周围区域轻微模糊和变暗。

**实现方法**:
- 使用后期处理效果实现选择性模糊
- 创建自定义着色器处理高亮区域
- 使用GSAP控制相机平滑移动至最佳观察角度

### 3. 全息投影效果

**设计说明**:
展示车辆关键技术时，通过全息效果将内部构造投影呈现。

**实现方法**:
- 使用shader实现半透明描边效果
- 创建动态装配/分解动画
- 添加浮动的信息标签和连接线

## 组件API规范

### 1. CarShowcaseView

**Props**:
- `modelPath`: String (必填) - 3D模型路径
- `initialPosition`: Object - 初始相机位置
- `controlsEnabled`: Boolean - 是否启用用户控制

**事件**:
- `@model-loaded`: 模型加载完成
- `@info-point-clicked`: 信息点被点击

**插槽**:
- `#loading`: 自定义加载界面
- `#controls`: 自定义控制界面

### 2. CarControlPanel

**Props**:
- `position`: String - 面板位置 ('left', 'right', 'bottom')
- `controls`: Array - 控制选项配置

**事件**:
- `@control-activated`: 控制选项被激活
- `@panel-toggled`: 面板显示状态切换

**方法**:
- `activateControl(id)`: 激活特定控制
- `togglePanel()`: 切换面板显示状态

### 3. InfoPointSystem

**Props**:
- `points`: Array - 信息点配置
- `visible`: Boolean - 是否显示信息点

**事件**:
- `@point-hovered`: 信息点被悬停
- `@point-clicked`: 信息点被点击
- `@info-closed`: 信息卡片被关闭

**方法**:
- `showPoint(id)`: 显示特定信息点
- `hideAllPoints()`: 隐藏所有信息点

## 主要功能模块实现详解

### 1. 车辆模型交互系统

**功能说明**:
实现用户与3D车辆模型的交互，包括旋转、缩放、点击特定部位等。

**关键代码模块**:
- `useCarInteraction.ts`: 处理基础交互逻辑
- `CameraController.js`: 管理相机行为
- `RaycasterService.js`: 处理点击检测

**交互事件流**:
1. 用户输入 → 事件捕获
2. 射线检测 → 确定交互对象
3. 交互逻辑处理 → 更新模型/相机状态
4. 状态变更 → 触发UI更新

### 2. 环境控制系统

**功能说明**:
管理场景的环境光照、背景、氛围效果等。

**关键代码模块**:
- `EnvironmentController.vue`: 环境控制器组件
- `LightingSetup.js`: 光照设置
- `EnvironmentMapService.js`: 环境贴图管理

**预设环境场景**:
- 展厅环境: 聚光灯照射，强调车身线条
- 户外环境: 自然光照，展示真实使用场景
- 夜景环境: 霓虹灯效，展示车灯效果

### 3. 信息展示系统

**功能说明**:
在3D模型上展示交互信息点，点击后显示详细信息。

**关键代码模块**:
- `InfoPointSystem.vue`: 信息点系统组件
- `InfoCard.vue`: 信息卡片组件
- `InfoPointData.js`: 信息点数据定义

**信息类型支持**:
- 文本信息
- 图片展示
- 参数数据
- 动态图表
- 短视频展示

## 开发注意事项

1. **性能监控**
   - 使用Three.js的Stats模块监控FPS
   - 开发模式下启用性能监控面板
   - 定期进行性能测试，特别是在添加新特效后

2. **兼容性测试**
   - 确保在主流浏览器中正常工作(Chrome, Firefox, Safari, Edge)
   - 适配不同分辨率和设备像素比
   - 针对触控设备优化交互体验

3. **代码质量控制**
   - 遵循ESLint规则确保代码风格一致
   - 复杂逻辑添加单元测试
   - 使用TypeScript类型定义增强代码健壮性

4. **资源管理**
   - 模型和纹理存放于public/models和public/textures目录
   - 使用版本控制管理大型二进制文件
   - 考虑使用CDN分发大型资源

## 开发路线图

### 第一阶段：基础框架搭建
- [ ] 创建必要的组件目录结构
- [ ] 从su7-replica提取核心3D渲染逻辑
- [ ] 实现基础的模型加载和交互系统
- [ ] **特效复用**: 直接使用su7-replica demo中的现有特效，包括环境映射、地面反射、相机动画等效果
- [ ] **直接迁移**: 优先将su7-replica中的Experience、World、Car等核心模块封装为Vue组件，保留原有特效和交互体验
- [ ] **最小可行产品**: 第一阶段以快速实现基本功能为目标，在保证现有demo特效正常运行的基础上进行组件化改造

### 第二阶段：组件开发
- [ ] 开发CarShowcaseView核心组件
- [ ] 实现控制面板和信息点系统
- [ ] 添加转场动画和页面切换效果

### 第三阶段：UI完善与整合
- [ ] 设计并实现玻璃态UI组件
- [ ] 在Home.vue中添加3D展示入口
- [ ] 完善移动端适配

### 第四阶段：特效强化与优化
- [ ] 添加高级视觉特效
- [ ] 性能优化和内存管理
- [ ] 兼容性测试和问题修复

## 参考资源

- Three.js官方文档: [https://threejs.org/docs/](https://threejs.org/docs/)
- kokomi.js文档: [https://github.com/alphardex/kokomi.js](https://github.com/alphardex/kokomi.js)
- GSAP动画库: [https://greensock.com/docs/](https://greensock.com/docs/)
- 原项目su7-replica: [本地项目路径]/theme_ui/demo/su7-replica

## 附录

### A. 推荐的VSCode插件
- Three.js Snippets
- GLSL Linter
- Shader languages support
- Vue Language Features (Volar)

### B. 调试技巧
- 使用Three.js Inspector Chrome插件辅助调试
- 添加debug参数开启调试面板: `?debug=true`
- 通过控制台访问全局对象: `window.carShowcase`
