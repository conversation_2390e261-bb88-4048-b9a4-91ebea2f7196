import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

export class ApiService {
    private static instance: ApiService;
    private baseUrl: string;

    private constructor() {
        // 这里可以根据环境配置不同的baseUrl
        this.baseUrl = 'http://***********:9077/api/v1';
        // this.baseUrl = 'http://localhost:8000/api/v1';
    }

    public static getInstance(): ApiService {
        if (!ApiService.instance) {
            ApiService.instance = new ApiService();
        }
        return ApiService.instance;
    }

    private generateTaskId(): string {
        const timestamp = dayjs().format("YYYYMMDDHHmmss");
        const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
        return `${uuid}_${timestamp}`;
    }

    public async generateCarSkin(prompt: string): Promise<{ success: boolean; data?: any; error?: string }> {
        try {
            const taskId = this.generateTaskId();
            const response = await fetch(`${this.baseUrl}/car-texture`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    prompt,
                    task_id: taskId
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { success: true, data };
        } catch (error) {
            console.error('生成车衣失败:', error);
            return { 
                success: false, 
                error: error instanceof Error ? error.message : '生成车衣失败' 
            };
        }
    }
} 