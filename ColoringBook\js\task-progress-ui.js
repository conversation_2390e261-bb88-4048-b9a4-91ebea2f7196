/**
 * 任务进度UI组件 - 独立的任务进度显示和更新
 * 
 * 功能特性：
 * 1. 实时显示任务进度
 * 2. 独立的进度条和状态指示器
 * 3. 任务详情展示
 * 4. 性能监控面板
 */

class TaskProgressUI {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            throw new Error(`容器元素 ${containerId} 不存在`);
        }
        
        this.options = {
            showDetailedProgress: options.showDetailedProgress !== false,
            showPerformanceMetrics: options.showPerformanceMetrics || false,
            updateInterval: options.updateInterval || 1000,
            maxVisibleTasks: options.maxVisibleTasks || 10,
            ...options
        };
        
        this.taskManager = null;
        this.updateTimer = null;
        this.isVisible = false;
        
        this.init();
    }

    /**
     * 初始化UI组件
     */
    init() {
        this.createProgressUI();
        this.bindEvents();
    }

    /**
     * 设置任务管理器引用
     * @param {EnhancedTaskManager} taskManager - 任务管理器实例
     */
    setTaskManager(taskManager) {
        this.taskManager = taskManager;
        
        // 绑定任务管理器事件
        if (taskManager) {
            taskManager.on('taskStart', (taskKey, taskInfo) => {
                this.onTaskStart(taskKey, taskInfo);
            });
            
            taskManager.on('taskProgress', (taskId, taskInfo) => {
                this.onTaskProgress(taskId, taskInfo);
            });
            
            taskManager.on('taskComplete', (taskId, result) => {
                this.onTaskComplete(taskId, result);
            });
            
            taskManager.on('taskFail', (taskId, error) => {
                this.onTaskFail(taskId, error);
            });
        }
    }

    /**
     * 创建进度UI界面
     */
    createProgressUI() {
        this.container.innerHTML = `
            <div class="task-progress-panel hidden" id="taskProgressPanel">
                <!-- 主进度条 -->
                <div class="main-progress-section mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-lg font-semibold text-gray-800">任务进度</h3>
                        <div class="flex items-center space-x-2">
                            <button id="toggleDetails" class="text-sm text-blue-600 hover:text-blue-800">
                                <span id="toggleDetailsText">显示详情</span>
                            </button>
                            <button id="closeProgress" class="text-gray-500 hover:text-gray-700">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 总体进度条 -->
                    <div class="mb-3">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span id="overallProgressText">准备中...</span>
                            <span id="overallProgressPercent">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div id="overallProgressBar" class="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="grid grid-cols-4 gap-3 text-center">
                        <div class="bg-blue-50 p-2 rounded">
                            <div class="text-lg font-bold text-blue-600" id="totalTasksCount">0</div>
                            <div class="text-xs text-gray-600">总任务</div>
                        </div>
                        <div class="bg-green-50 p-2 rounded">
                            <div class="text-lg font-bold text-green-600" id="completedTasksCount">0</div>
                            <div class="text-xs text-gray-600">已完成</div>
                        </div>
                        <div class="bg-yellow-50 p-2 rounded">
                            <div class="text-lg font-bold text-yellow-600" id="runningTasksCount">0</div>
                            <div class="text-xs text-gray-600">运行中</div>
                        </div>
                        <div class="bg-red-50 p-2 rounded">
                            <div class="text-lg font-bold text-red-600" id="failedTasksCount">0</div>
                            <div class="text-xs text-gray-600">失败</div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细进度区域 -->
                <div class="detailed-progress-section hidden" id="detailedProgressSection">
                    <!-- 个别任务进度 -->
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-700 mb-2">任务详情</h4>
                        <div id="individualTasksList" class="space-y-2 max-h-64 overflow-y-auto">
                            <!-- 个别任务进度将在这里显示 -->
                        </div>
                    </div>
                    
                    <!-- 性能指标 -->
                    <div class="performance-metrics hidden" id="performanceMetrics">
                        <h4 class="text-md font-medium text-gray-700 mb-2">性能指标</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="bg-gray-50 p-2 rounded">
                                <div class="text-gray-600">平均耗时</div>
                                <div class="font-semibold" id="avgDuration">-</div>
                            </div>
                            <div class="bg-gray-50 p-2 rounded">
                                <div class="text-gray-600">成功率</div>
                                <div class="font-semibold" id="successRate">-</div>
                            </div>
                            <div class="bg-gray-50 p-2 rounded">
                                <div class="text-gray-600">队列大小</div>
                                <div class="font-semibold" id="queueSize">-</div>
                            </div>
                            <div class="bg-gray-50 p-2 rounded">
                                <div class="text-gray-600">平均进度</div>
                                <div class="font-semibold" id="avgProgress">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        const toggleDetailsBtn = document.getElementById('toggleDetails');
        const closeProgressBtn = document.getElementById('closeProgress');
        
        if (toggleDetailsBtn) {
            toggleDetailsBtn.addEventListener('click', () => {
                this.toggleDetailedView();
            });
        }
        
        if (closeProgressBtn) {
            closeProgressBtn.addEventListener('click', () => {
                this.hide();
            });
        }
    }

    /**
     * 显示进度面板
     */
    show() {
        const panel = document.getElementById('taskProgressPanel');
        if (panel) {
            panel.classList.remove('hidden');
            this.isVisible = true;
            this.startUpdateTimer();
        }
    }

    /**
     * 隐藏进度面板
     */
    hide() {
        const panel = document.getElementById('taskProgressPanel');
        if (panel) {
            panel.classList.add('hidden');
            this.isVisible = false;
            this.stopUpdateTimer();
        }
    }

    /**
     * 切换详细视图
     */
    toggleDetailedView() {
        const detailedSection = document.getElementById('detailedProgressSection');
        const toggleText = document.getElementById('toggleDetailsText');
        
        if (detailedSection && toggleText) {
            const isHidden = detailedSection.classList.contains('hidden');
            
            if (isHidden) {
                detailedSection.classList.remove('hidden');
                toggleText.textContent = '隐藏详情';
                
                // 如果启用了性能指标，也显示它
                if (this.options.showPerformanceMetrics) {
                    const performanceMetrics = document.getElementById('performanceMetrics');
                    if (performanceMetrics) {
                        performanceMetrics.classList.remove('hidden');
                    }
                }
            } else {
                detailedSection.classList.add('hidden');
                toggleText.textContent = '显示详情';
            }
        }
    }

    /**
     * 开始更新定时器
     */
    startUpdateTimer() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        this.updateTimer = setInterval(() => {
            if (this.taskManager && this.isVisible) {
                this.updateProgress();
            }
        }, this.options.updateInterval);
    }

    /**
     * 停止更新定时器
     */
    stopUpdateTimer() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * 更新进度显示
     */
    updateProgress() {
        if (!this.taskManager) return;
        
        const stats = this.taskManager.getProgressStats();
        this.updateOverallProgress(stats);
        this.updateStatistics(stats);
        
        if (!document.getElementById('detailedProgressSection').classList.contains('hidden')) {
            this.updateIndividualTasks();
            
            if (this.options.showPerformanceMetrics) {
                this.updatePerformanceMetrics();
            }
        }
    }

    /**
     * 更新总体进度
     * @param {Object} stats - 统计信息
     */
    updateOverallProgress(stats) {
        const progressBar = document.getElementById('overallProgressBar');
        const progressText = document.getElementById('overallProgressText');
        const progressPercent = document.getElementById('overallProgressPercent');
        
        if (progressBar && progressText && progressPercent) {
            const progress = stats.progress || 0;
            
            progressBar.style.width = `${progress}%`;
            progressPercent.textContent = `${progress.toFixed(1)}%`;
            
            if (stats.total === 0) {
                progressText.textContent = '等待任务开始...';
            } else if (stats.completed === stats.total) {
                progressText.textContent = '所有任务已完成';
            } else if (stats.running > 0) {
                progressText.textContent = `正在处理 ${stats.running} 个任务...`;
            } else {
                progressText.textContent = `处理中... (${stats.completed}/${stats.total})`;
            }
        }
    }

    /**
     * 更新统计信息
     * @param {Object} stats - 统计信息
     */
    updateStatistics(stats) {
        const elements = {
            totalTasksCount: stats.total,
            completedTasksCount: stats.completed,
            runningTasksCount: stats.running,
            failedTasksCount: stats.failed
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value.toString();
            }
        });
    }

    /**
     * 更新个别任务进度
     */
    updateIndividualTasks() {
        const tasksList = document.getElementById('individualTasksList');
        if (!tasksList || !this.taskManager) return;
        
        const activeTasks = Array.from(this.taskManager.activeTasks.entries())
            .slice(0, this.options.maxVisibleTasks);
        
        if (activeTasks.length === 0) {
            tasksList.innerHTML = '<div class="text-gray-500 text-center py-4">暂无活跃任务</div>';
            return;
        }
        
        const tasksHTML = activeTasks.map(([taskId, taskInfo]) => {
            const progress = taskInfo.progress || 0;
            const statusColor = this.getStatusColor(taskInfo.status);
            const runningTime = Date.now() - taskInfo.startTime;
            const runningTimeText = this.formatDuration(runningTime);
            
            return `
                <div class="task-item bg-white border rounded-lg p-3 shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full ${statusColor}"></div>
                            <span class="text-sm font-medium">任务 ${taskInfo.index + 1}</span>
                            <span class="text-xs text-gray-500">${taskId.substring(taskId.length - 8)}</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            ${runningTimeText}
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <div class="flex justify-between text-xs text-gray-600 mb-1">
                            <span>${taskInfo.status}</span>
                            <span>${progress.toFixed(1)}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full transition-all duration-300 ${this.getProgressBarColor(taskInfo.status)}" 
                                 style="width: ${progress}%"></div>
                        </div>
                    </div>
                    
                    <div class="text-xs text-gray-500 truncate">
                        ${taskInfo.prompt.substring(0, 50)}${taskInfo.prompt.length > 50 ? '...' : ''}
                    </div>
                </div>
            `;
        }).join('');
        
        tasksList.innerHTML = tasksHTML;
    }

    /**
     * 更新性能指标
     */
    updatePerformanceMetrics() {
        if (!this.taskManager) return;
        
        const stats = this.taskManager.getProgressStats();
        const metrics = this.taskManager.getPerformanceMetrics();
        
        const elements = {
            avgDuration: this.formatDuration(stats.averageDuration),
            successRate: `${stats.successRate.toFixed(1)}%`,
            queueSize: stats.queueSize.toString(),
            avgProgress: `${stats.averageProgress.toFixed(1)}%`
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * 获取状态颜色类
     * @param {string} status - 任务状态
     * @returns {string} - CSS类名
     */
    getStatusColor(status) {
        const colors = {
            'queued': 'bg-gray-400',
            'running': 'bg-blue-500',
            'completed': 'bg-green-500',
            'failed': 'bg-red-500',
            'cancelled': 'bg-yellow-500',
            'timeout': 'bg-orange-500'
        };
        return colors[status] || 'bg-gray-400';
    }

    /**
     * 获取进度条颜色类
     * @param {string} status - 任务状态
     * @returns {string} - CSS类名
     */
    getProgressBarColor(status) {
        const colors = {
            'queued': 'bg-gray-400',
            'running': 'bg-blue-500',
            'completed': 'bg-green-500',
            'failed': 'bg-red-500',
            'cancelled': 'bg-yellow-500',
            'timeout': 'bg-orange-500'
        };
        return colors[status] || 'bg-blue-500';
    }

    /**
     * 格式化持续时间
     * @param {number} duration - 持续时间（毫秒）
     * @returns {string} - 格式化的时间字符串
     */
    formatDuration(duration) {
        if (!duration || duration < 0) return '-';
        
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * 任务开始事件处理器
     * @param {string} taskKey - 任务键
     * @param {Object} taskInfo - 任务信息
     */
    onTaskStart(taskKey, taskInfo) {
        if (!this.isVisible) {
            this.show();
        }
        console.log(`[TaskProgressUI] 任务开始: ${taskKey}`);
    }

    /**
     * 任务进度事件处理器
     * @param {string} taskId - 任务ID
     * @param {Object} taskInfo - 任务信息
     */
    onTaskProgress(taskId, taskInfo) {
        // 进度更新会通过定时器自动处理
        console.log(`[TaskProgressUI] 任务进度更新: ${taskId} - ${taskInfo.progress}%`);
    }

    /**
     * 任务完成事件处理器
     * @param {string} taskId - 任务ID
     * @param {Object} result - 任务结果
     */
    onTaskComplete(taskId, result) {
        console.log(`[TaskProgressUI] 任务完成: ${taskId}`);
        
        // 如果所有任务都完成了，延迟隐藏进度面板
        setTimeout(() => {
            if (this.taskManager) {
                const stats = this.taskManager.getProgressStats();
                if (stats.running === 0 && stats.queued === 0) {
                    setTimeout(() => this.hide(), 3000); // 3秒后自动隐藏
                }
            }
        }, 1000);
    }

    /**
     * 任务失败事件处理器
     * @param {string} taskId - 任务ID
     * @param {Error} error - 错误信息
     */
    onTaskFail(taskId, error) {
        console.log(`[TaskProgressUI] 任务失败: ${taskId} - ${error.message}`);
    }

    /**
     * 销毁UI组件
     */
    destroy() {
        this.stopUpdateTimer();
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// 导出任务进度UI类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskProgressUI;
} else if (typeof window !== 'undefined') {
    window.TaskProgressUI = TaskProgressUI;
}