<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的涂色本</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=ZCOOL+KuaiLe&display=swap" rel="stylesheet">
    <style>
        /* 吉卜力风格主题 - 修正版 */
        body {
            background-color: #f4f1e9; /* 更温暖的画纸颜色 */
            background-image: url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23d4c2ad" fill-opacity="0.08"%3E%3Cpath d="M0 0h40v40H0zM40 40h40v40H40z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
            font-family: 'Nunito', sans-serif;
            color: #5c544b;
        }

        .font-handwritten {
            font-family: 'ZCOOL KuaiLe', cursive;
        }

        .title-ghibli {
            font-family: 'ZCOOL KuaiLe', cursive;
            font-size: 3.5rem;
            color: #5B8E7D; /* 龙猫绿 */
            text-shadow: 1px 1px 2px #d4c2ad;
        }

        .control-panel-ghibli {
            background-color: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(212, 194, 173, 0.4); /* 大地色边框 */
            border-radius: 1.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        /* 纯CSS实现的卷轴效果 */
        .prompt-scroll-container {
            background: linear-gradient(to right, #d4c2ad, #e0d6c7, #d4c2ad);
            border-radius: 10px;
            padding: 8px;
            box-shadow: 2px 2px 10px rgba(0,0,0,0.15), inset 0 0 5px rgba(0,0,0,0.1);
        }
        .prompt-scroll {
            background-color: #f4f1e9;
            border: 2px solid #c8b8a2;
            border-radius: 5px;
            color: #5c544b;
            font-size: 1.1rem;
            width: 100%;
            padding: 1rem;
            resize: none;
            transition: all 0.3s ease;
        }
        .prompt-scroll:focus {
            outline: none;
            border-color: #5B8E7D;
            box-shadow: 0 0 10px rgba(91, 142, 125, 0.5);
        }

        .rune-btn {
            background-color: #e0d6c7; /* 更柔和的大地色 */
            border: 1px solid #c8b8a2;
            color: #5c544b;
            font-weight: 600;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transform: rotate(-1deg); /* 轻微旋转 */
        }
        .rune-btn:hover {
            transform: translateY(-2px) rotate(2deg);
            box-shadow: 0 6px 10px rgba(0,0,0,0.1);
            border-color: #A3B899;
        }
        .rune-btn.active {
            background-color: #5A7A9E; /* 哈尔蓝 */
            color: white;
            border-color: #5A7A9E;
            transform: rotate(1deg) scale(1.05);
        }

        .magic-amulet-btn {
            background: linear-gradient(145deg, #6cb09e, #5B8E7D);
            color: white;
            font-family: 'ZCOOL KuaiLe', cursive;
            font-size: 1.5rem;
            padding: 0.8rem 2rem;
            border-radius: 50px;
            border: 2px solid rgba(255,255,255,0.5);
            box-shadow: 0 4px 15px rgba(91, 142, 125, 0.4);
            transition: all 0.3s ease-out;
        }
        .magic-amulet-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(91, 142, 125, 0.6);
        }

        /* 纯CSS实现的画框效果 */
        .gallery-frame {
            background-color: #d4c2ad; /* 木头底色 */
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1), inset 0 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            transform: rotate(1deg);
        }
        .gallery-frame:nth-child(even) {
            transform: rotate(-1deg);
        }
        .gallery-frame:hover {
            transform: scale(1.05) rotate(0deg);
            z-index: 10;
        }
        .gallery-frame .image-container {
            background-color: white;
            border: 1px solid #f4f1e9;
            border-radius: 4px;
            padding: 4px;
        }
        .gallery-frame.selected {
            box-shadow: 0 0 0 4px #5A7A9E, 0 4px 6px rgba(0,0,0,0.1); /* 哈尔蓝光晕 */
        }

        .firefly-switch-label {
            cursor: pointer;
        }
        .firefly-switch-checkbox:checked + .firefly-switch-label .text-gray-400 {
            color: #FFD700; /* 金色萤火虫 */
            text-shadow: 0 0 10px #FFD700;
        }

        /* 加载动画和进度指示器 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #d4c2ad;
            border-top: 2px solid #5B8E7D;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .magic-amulet-btn:disabled {
            background: linear-gradient(145deg, #a0a0a0, #888888);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .magic-amulet-btn:disabled:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* 错误和成功通知样式 */
        .error-notification, .success-notification, .retry-notification, .connection-check-notification, .task-failure-notification {
            backdrop-filter: blur(8px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .retry-btn {
            border: 1px solid currentColor;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .retry-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* 增强的加载状态视觉反馈 */
        .magic-amulet-btn.loading {
            position: relative;
            overflow: hidden;
        }

        .magic-amulet-btn.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 通知动画增强 */
        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

    </style>
</head>
<body class="p-4 md:p-8">

    <div class="max-w-4xl mx-auto">
        <header class="text-center mb-8">
            <h1 class="title-ghibli">我的涂色本</h1>
            <p class="text-lg md:text-xl text-gray-500 mt-2                                       │
 │           font-handwritten">输入任何你喜欢的东西，把它变成可以涂色的画！</p>
        </header>

        <main>
            <div class="control-panel-ghibli p-6 md:p-8 mb-12">
                <div class="prompt-scroll-container mb-6">
                    <textarea id="prompt-input" class="prompt-scroll h-24" placeholder="在此卷轴上写下你的想象..."></textarea>
                </div>

                <div class="flex flex-col sm:flex-row justify-between items-center gap-6 mb-6">
                    <div id="ratio-buttons" class="flex flex-wrap gap-3">
                        <button class="rune-btn active" data-ratio="1:1">1:1</button>
                        <button class="rune-btn" data-ratio="4:3">4:3</button>
                        <button class="rune-btn" data-ratio="3:4">3:4</button>
                        <button class="rune-btn" data-ratio="16:9">16:9</button>
                    </div>
                    <input type="number" id="count-input" value="1" min="1" max="10" class="w-20 p-2 text-center rune-btn">
                </div>

                <div class="text-center">
                    <button id="generate-btn" class="magic-amulet-btn">施展魔法</button>
                    <div id="loading-indicator" class="hidden mt-4">
                        <div class="flex items-center justify-center space-x-2">
                            <div class="loading-spinner"></div>
                            <span id="loading-text" class="text-sm font-handwritten text-gray-600">施法中...</span>
                        </div>
                        <div id="progress-container" class="mt-2 hidden">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="progress-text" class="text-xs text-center mt-1 font-handwritten text-gray-500">准备中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-2">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="select-mode-toggle" class="hidden firefly-switch-checkbox">
                        <label for="select-mode-toggle" class="firefly-switch-label flex items-center gap-2 text-lg font-handwritten cursor-pointer">
                            <span class="text-gray-400 transition-all duration-500">✨</span>
                            <span>选择画作</span>
                        </label>
                    </div>
                    <button id="print-btn" class="rune-btn">🖨️ 打印</button>
                </div>

                <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 示例卡片 -->
                    <div class="gallery-frame hidden">
                        <div class="image-container">
                            <img src="https://via.placeholder.com/512" alt="生成的图片" class="w-full h-full object-cover rounded-sm">
                        </div>
                        <div class="flex justify-end gap-2 mt-2">
                            <button class="rune-btn text-xs p-2">🎨</button>
                            <button class="rune-btn text-xs p-2 delete-btn" style="background-color: #C75D5D; color: white;">🗑️</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 任务进度面板容器 -->
    <div id="taskProgressContainer" class="fixed top-4 left-4 z-50 max-w-md"></div>

    <!-- 脚本文件 -->
    <script src="js/storage-manager.js"></script>
    <script src="js/enhanced-task-manager.js"></script>
    <script src="js/task-progress-ui.js"></script>
    <script src="js/enhanced-gallery.js"></script>
    <script src="js/integrated-task-system.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
