# 提示词增强智能体

文生图提示词增强智能体，基于AutoGen框架实现，使用标准的MCP服务集成。

## 功能简介

该智能体能够接收用户简单的描述，将其转化为详细、结构化的文生图提示词，包括：

- 主体元素描述（人物、物体、场景等）
- 风格指导（艺术风格、色彩方案、渲染类型等）
- 技术细节（分辨率、视角、构图等）
- 情感氛围（情绪基调、整体感觉等）
- 正面和负面提示词（需要包含和避免的元素）

## 系统架构

整个系统采用以下组件：

1. **前端界面** - 用户交互界面
2. **API服务** - 提供HTTP API接口，接收前端请求
3. **提示词增强智能体** - 核心智能体，处理提示词增强逻辑
4. **MCP服务** - 流式数据传输服务，采用双协议架构

### 通信流程

```
+----------------+                      +----------------+                    +-------------------+
|                |  1.发送提示词请求     |                |  2.调用             |                   |
|   前端界面     +--------------------->+  API 服务      +-------------------->+  提示词增强智能体  |
|                |  POST /api/enhance   |  (端口:8100)   |  传递用户请求        |                   |
+------^---------+                      +----------------+                    +---------+---------+
       |                                                                                |
       |                                                                                |
       |                                                                     3.MCP工具调用|
       |                                                                                |
       |                                                                                |
       |                                                                                v
       |                               +----------------+                     +---------+---------+
       |                               |                |                     |                   |
       +------------------------------>+  MCP 服务      +<--------------------+                   |
         4.建立WebSocket连接           |  (端口:19220)  |    发送思考过程和结果 |                   |
         ws://host:19220/api/v1/connect|                |                     |                   |
         5.接收流式输出                +----------------+                     +-------------------+
```

### 双协议架构

系统采用双协议架构：

- **前端与MCP服务** - 使用WebSocket协议（`ws://host:19220/api/v1/connect`）
- **智能体与MCP服务** - 使用SSE协议（`http://host:19220/api/v1/sse`）

## 技术实现

### 使用的技术

- **AutoGen框架** - 用于构建智能体
- **FastAPI** - 用于构建API服务
- **MCP SDK** - 用于连接MCP服务

### MCP集成方式

智能体使用AutoGen标准的`SseServerParams`方式连接MCP服务：

```python
from autogen_ext.tools.mcp import SseServerParams, mcp_server_tools

# 确保URL以/api/v1/sse结尾
sse_url = "http://localhost:19220/api/v1/sse"  

# 创建SSE服务参数
server_params = SseServerParams(
    url=sse_url,
    headers={"Content-Type": "application/json"}
)

# 获取MCP工具
tools = await mcp_server_tools(server_params)
```

## 安装和使用

### 环境要求

- Python 3.9+
- 依赖库（见requirements.txt）

### 安装步骤

1. 克隆代码仓库
2. 进入项目目录并创建虚拟环境

```bash
cd agents/agent/prompt_enhancer
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 启动服务

启动服务需要两个步骤：

1. 首先启动MCP服务（必须在API服务之前启动）：

```bash
cd agents/tools/stream_sdk
python run_server.py --port 19220
```

2. 然后启动API服务：

```bash
cd agents/agent/prompt_enhancer
python run_server.py --port 8100 --mcp-url http://localhost:19220
```

### 命令行参数

API服务支持以下命令行参数：

| 参数         | 说明               | 默认值                 |
| ------------ | ------------------ | ---------------------- |
| --host       | API服务监听地址    | 0.0.0.0                |
| --port       | API服务监听端口    | 8100                   |
| --mcp-url    | MCP服务URL         | http://localhost:19220 |
| --model-name | 使用的大语言模型   | gpt-3.5-turbo          |
| --api-base   | API基础URL（可选） | -                      |
| --api-key    | API密钥（可选）    | -                      |

## API接口说明

### HTTP API

#### 增强提示词

```
POST /api/enhance
```

请求体：
```json
{
  "prompt": "一个未来城市的夜景",
  "session_id": "可选-自定义会话ID"
}
```

响应：
```json
{
  "session_id": "会话ID",
  "status": "processing",
  "message": "提示词增强请求已接收，请通过WebSocket连接获取实时结果"
}
```

#### 获取MCP信息

```
GET /api/mcp-info
```

响应：
```json
{
  "mcp_url": "http://localhost:19220",
  "mcp_ws_url": "ws://localhost:19220/api/v1/connect",
  "connected": true
}
```

### WebSocket连接

前端应通过WebSocket连接到MCP服务以接收实时更新：

```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:19220/api/v1/connect');

// 连接打开时，发送创建会话命令
ws.onopen = () => {
  ws.send(JSON.stringify({
    cmd: "call_tool",
    tool_name: "create_session",
    params: {
      session_id: "会话ID",  // 使用API响应中的会话ID
      session_name: "提示词增强会话",
      agent_name: "prompt_enhancer"
    }
  }));
};

// 处理接收的消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  // 根据事件类型处理不同的事件
  switch(data.event_type) {
    case "thinking":
      console.log("思考过程:", data.data.content);
      break;
    case "content":
      console.log("内容:", data.data.content);
      console.log("是否最终结果:", data.data.is_final);
      break;
    case "code":
      console.log("代码:", data.data.code);
      console.log("语言:", data.data.language);
      break;
  }
};
```

## 无状态设计

最新版本的MCP服务采用无状态设计，会话ID完全由前端/调用方传入，不再内部生成。主要优点包括：

1. **解耦合** - 服务之间的依赖降低
2. **可扩展性** - 更容易水平扩展
3. **可靠性** - 会话状态由调用方管理，避免丢失
4. **灵活性** - 允许自定义会话ID的生成规则

## 常见问题

### MCP服务连接问题

如果无法连接到MCP服务，请检查：

1. MCP服务是否已启动（必须在API服务之前启动）
2. 端口号是否正确（默认19220）
3. MCP服务是否使用了正确的主机地址

### WebSocket连接问题

如果WebSocket连接失败，请检查：

1. 是否使用了正确的WebSocket URL（必须使用`/api/v1/connect`路径）
2. 是否在连接后正确发送了`create_session`命令
3. 会话ID是否与API响应中的一致

## 环境安装

### 使用虚拟环境 (推荐)

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 详细安装步骤

如果遇到导入错误，请确保安装以下关键依赖：

```bash
# FastAPI相关
pip install fastapi uvicorn httpx

# AutoGen相关
pip install git+https://github.com/microsoft/autogen.git
# 或者安装稳定版
pip install pyautogen

# 其他必要依赖
pip install aiohttp websockets python-multipart
```

### 可能遇到的问题

1. **ModuleNotFoundError: No module named 'autogen_core'**
   
   这是因为代码使用了新版AutoGen的导入路径，而您安装的可能是旧版。有两种解决方案:
   
   a. 安装新版AutoGen:
   ```bash
   pip install git+https://github.com/microsoft/autogen.git
   ```
   
   b. 修改代码使用旧版导入路径:
   编辑`prompt_enhancer_agent.py`文件，将以下部分:
   ```python
   try:
       # 尝试使用新包路径导入
       from autogen_core import CancellationToken  
       from autogen_agentchat.agents import AssistantAgent
       from autogen_agentchat.messages import TextMessage
       from autogen_ext.models.openai import OpenAIChatCompletionClient
   ```
   
   修改为:
   ```python
   try:
       # 使用旧版AutoGen导入路径
       from autogen import CancellationToken
       from autogen.agents import AssistantAgent
       from autogen.messages import TextMessage
       from autogen.models.openai import OpenAIChatCompletionClient
   ```

2. **NameError: name 'logger' is not defined**
   
   已修复：确保在模块顶部已正确定义logger。

3. **uvicorn相关错误**
   
   服务使用uvicorn作为ASGI服务器运行FastAPI应用。确保安装了uvicorn:
   ```bash
   pip install uvicorn
   ```
   
   如果在`api.py`中发现以下错误：
   ```
   ImportError: cannot import name 'ServerErrorMiddleware' from 'starlette.middleware.errors'
   ```
   
   这可能是uvicorn与starlette版本不兼容，尝试安装兼容版本：
   ```bash
   pip uninstall starlette uvicorn
   pip install starlette==0.36.3 uvicorn==0.27.1
   ```

## 运行服务

```bash
python run_server.py
```

默认情况下，服务将在`0.0.0.0:8100`上运行，MCP服务URL为`http://localhost:19220`。

## 配置

可以通过环境变量或`.env`文件修改配置:

- `API_HOST`: API服务主机地址
- `API_PORT`: API服务端口
- `MCP_URL`: MCP服务URL
- `MODEL_NAME`: 使用的模型名称

## MCP服务

提示词增强服务需要连接到MCP服务。确保MCP服务正在运行：

```bash
cd agents/tools/stream_sdk
python run_server.py --port 19220
``` 