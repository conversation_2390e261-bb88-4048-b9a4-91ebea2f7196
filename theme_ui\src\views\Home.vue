<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <!-- 使用基于Canvas的星空背景组件提高性能 -->
    <div class="fullscreen-background">
      <StarCanvasBackground :particleDensity="80" primaryColor="rgba(99, 102, 241, 0.5)" secondaryColor="rgba(59, 130, 246, 0.5)" particleColor="rgba(255, 255, 255, 0.6)" :minSize="0.1" :maxSize="0.7" :speed="0.2" />
    </div>

    <div class="container mx-auto px-4">
      <div class="text-center mb-10 relative entrance-animation">
        <h1 class="title-container mb-0">
          <SparkleEffect>
            <span class="text-6xl font-bold text-white title-text advanced-entrance">超维智能座舱数字全息系统</span>
          </SparkleEffect>
        </h1>

        <p class="text-xl text-gray-300 mt-16 fade-in-sequence">
          <span class="inline-block fade-in-item">基于生成式AI引擎，</span>
          <span class="inline-block fade-in-item">通过AI原子能力的自由编织，</span>
          <span class="inline-block fade-in-item">打造会呼吸的移动数字空间，</span>
          <span class="inline-block fade-in-item">开启智能座舱新体验</span>
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 文生壁纸卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.1s' }">
          <GlassCard title="文生壁纸" description="通过文字描述智能生成高清壁纸，支持多种分辨率和风格定制" buttonText="立即创建" color="primary" @click="navigateTo('/image-generator')" buttonText_list="壁纸库" @click_list="navigateTo({ path: '/theme-list', query: { taskType: 'image' } })">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-path" d="M4 16L8.586 11.414C9.367 10.633 10.632 10.633 11.414 11.414L16 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path class="icon-path-delay1" d="M14 14L15.586 12.414C16.367 11.633 17.632 11.633 18.414 12.414L20 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <circle class="icon-path-delay2" cx="8.5" cy="8.5" r="1.5" fill="currentColor" />
                <path class="icon-border" d="M3 7.8C3 6.11984 3 5.27976 3.32698 4.63803C3.6146 4.07354 4.07354 3.6146 4.63803 3.32698C5.27976 3 6.11984 3 7.8 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11984 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V7.8Z" stroke="currentColor" stroke-width="2" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 提示词增强助手卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.2s' }">
          <GlassCard title="提示词增强助手" description="AI智能分析并增强简单提示词，帮您创建详细的多维度提示词" buttonText="立即体验" color="info" @click="navigateTo('/prompt-enhancer')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-tertiary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-path" d="M7 8H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M7 12H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay2" d="M14 16H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M7 16H10" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-border" d="M3 12C3 4.5885 4.5885 3 12 3C19.4115 3 21 4.5885 21 12C21 19.4115 19.4115 21 12 21C4.5885 21 3 19.4115 3 12Z" stroke="currentColor" stroke-width="2" />
                <circle class="icon-path-delay2" cx="15" cy="12" r="1" fill="currentColor" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 文生主题卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.3s' }">
          <GlassCard title="文生主题" description="一键生成完整UI主题，包含壁纸、图标、字体与配色方案" buttonText="立即创建" color="secondary" @click="navigateTo('/theme-generator')" buttonText_list="主题库" @click_list="navigateTo({ path: '/theme-list', query: { taskType: 'theme' } })">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect class="icon-path" x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" stroke-width="2" />
                <rect class="icon-path-delay1" x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" stroke-width="2" />
                <rect class="icon-path-delay2" x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" stroke-width="2" />
                <circle class="icon-border" cx="17.5" cy="17.5" r="3.5" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay1" d="M17.5 15.5V17.5H19.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 文生视频卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.4s' }">
          <GlassCard title="文生视频" description="基于文字描述生成动态视频内容，支持多种场景与风格" buttonText="立即创建" color="primary" @click="navigateTo('/video-generator')" buttonText_list="视频库" @click_list="navigateTo({ path: '/theme-list', query: { taskType: 'video' } })">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-info" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect class="icon-border" x="2" y="4" width="16" height="12" rx="2" stroke="currentColor" stroke-width="2" />
                <path class="icon-path" d="M22 7L18 10L22 13V7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path class="icon-path-delay1" d="M6 20H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay2" d="M10 16V20" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 图像识别卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.5s' }">
          <GlassCard title="图像识别" description="将图像内容转换为文字描述，从图片中提取关键信息" buttonText="立即体验" color="info" @click="navigateTo('/image-to-text-generator')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-info" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M3 3h18v18H3V3z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M12 3v18M3 12h18" stroke="currentColor" stroke-width="2" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 场景生成卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.6s' }">
          <GlassCard title="场景生成" description="根据输入生成相应的场景，辅助您的驾驶过程" buttonText="立即体验" color="primary" @click="navigateTo('/scene-generator')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M3 3h18v18H3V3z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M2 12h20M12 2v20" stroke="currentColor" stroke-width="2" />
                <circle class="icon-path-delay1" cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2" fill="none" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 用车助手卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.7s' }">
          <GlassCard title="用车助手" description="为您提供用车相关的智能助手服务，方便您的出行" buttonText="立即体验" color="secondary" @click="navigateTo('/car-assistant')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M12 4C6.48 4 2 8.48 2 14c0 1.22 0.21 2.39 0.6 3.47C3.14 18.87 4.46 20 6 20h12c1.54 0 2.86-1.13 3.4-2.53C21.79 16.39 22 15.22 22 14c0-5.52-4.48-10-10-10z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M7 14h10v2H7v-2z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay1" d="M9 10h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay2" d="M17 9c0.55 0 1 0.45 1 1s-0.45 1-1 1-1-0.45-1-1 0.45-1 1-1z" fill="currentColor" />
                <path class="icon-path-delay2" d="M7 9c0.55 0 1 0.45 1 1s-0.45 1-1 1-1-0.45-1-1 0.45-1 1-1z" fill="currentColor" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 语宙回声卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.8s' }">
          <GlassCard title="语宙回声" description="声音克隆，在文字宇宙投射声波，音色迁移构建声音数字分身" buttonText="立即体验" color="primary" @click="navigateTo('/voice-clone')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M3 3h18v18H3V3z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M12 4v16" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay1" d="M8 8h8" stroke="currentColor" stroke-width="2" />
                <circle class="icon-path-delay2" cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2" fill="none" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 语义大模型卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '0.9s' }">
          <GlassCard title="语义大模型" description="使用先进的语义大模型，为您提供深入的语义理解和分析服务" buttonText="立即体验" color="info" @click="navigateTo('/llm-helper')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-info" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M21 8c0 2.76-1.34 5.2-3.4 6.71C16.12 15.88 14.15 16.5 12 16.5c-2.15 0-4.12-0.62-5.6-1.79C4.34 13.2 3 10.76 3 8c0-4.42 4.03-8 9-8s9 3.58 9 8z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M14.5 8.5c0 0.83-0.67 1.5-1.5 1.5s-1.5-0.67-1.5-1.5 0.67-1.5 1.5-1.5 1.5 0.67 1.5 1.5z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay1" d="M9.5 8.5c0 0.83-0.67 1.5-1.5 1.5S6.5 9.33 6.5 8.5 7.17 7 8 7s1.5 0.67 1.5 1.5z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay2" d="M10.5 13h3" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M9 20l1.5-3.5M15 20l-1.5-3.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 幻影画匠卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '1.0s' }">
          <GlassCard title="幻影画匠" description="魔法相机，现实照片秒变风格，折射你的多维艺术人格" buttonText="立即试用" color="primary" @click="navigateTo('/magic-camera')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M4 6h16c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M4 8h16v6H4V8z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path-delay1" d="M16 16l-4-4-4 4" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay2" d="M8 10h8" stroke="currentColor" stroke-width="2" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 星云幻甲卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '1.1s' }">
          <GlassCard title="星云幻甲" description="如星云般变幻莫测，为你的爱车披上个性十足的战甲！" buttonText="立即试用" color="secondary" @click="navigateToUrl('http://*************:17099')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M12 2L22 22H2L12 2z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path-delay1" d="M12 4C17.5228 4 21 7.47715 21 12C21 16.5228 17.5228 20 12 20C6.47715 20 3 16.5228 3 12C3 7.47715 6.47715 4 12 4z" stroke="currentColor" stroke-width="2" fill="none" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 图生视频卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '1.2s' }">
          <GlassCard title="图生视频" description="从图像生成视频，为你的每一帧画面添加独特的特效动作！" buttonText="立即试用" color="primary" @click="navigateTo('/image-to-video')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-tertiary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M12 6V18M12 6L16 12M12 18L16 12" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path-effect" d="M8 12L16 12M8 12L12 15M8 12L12 9" stroke="currentColor" stroke-width="2" fill="none" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 海报创作卡片 -->
        <!-- <div class="card-entrance-animation" :style="{ animationDelay: '1.3s' }">
          <GlassCard title="海报创作" description="一句话生成精美海报，让创意瞬间绽放，打造独特的视觉艺术" buttonText="立即创作" color="info" @click="navigateTo('/poster-creator')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M3 3h18v18H3V3z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M7 7h10v2H7V7z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay1" d="M7 11h10v2H7v-2z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-delay2" d="M7 15h6v2H7v-2z" stroke="currentColor" stroke-width="2" />
                <path class="icon-path-effect" d="M15 15l4 4M19 15l-4 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </template>
          </GlassCard>
        </div> -->

        <!-- 界面生成卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '1.3s' }">
          <GlassCard title="界面生成" description="AI智能创建界面，输入需求获取精美HTML代码，所见即所得交互设计" buttonText="立即创建" color="info" @click="navigateTo('/ui-create')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-tertiary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M3 7C3 4.79086 4.79086 3 7 3H17C19.2091 3 21 4.79086 21 7V17C21 19.2091 19.2091 21 17 21H7C4.79086 21 3 19.2091 3 17V7Z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M9 8H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M8 12H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay2" d="M11 16H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M3 9L21 9" stroke="currentColor" stroke-width="2" />
                <circle class="icon-path-delay2" cx="5" cy="6" r="1" fill="currentColor" />
              </svg>
            </template>
          </GlassCard>
        </div>

        <!-- 涂鸦绘画卡片 -->
        <div class="card-entrance-animation" :style="{ animationDelay: '1.4s' }">
          <GlassCard title="涂鸦绘画" description="在画布上涂鸦，AI智能理解你的创意，生成精美图像" buttonText="立即创作" color="primary" @click="navigateTo('/doodle')">
            <template #icon>
              <svg class="icon-svg w-10 h-10 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path class="icon-border" d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z" stroke="currentColor" stroke-width="2" fill="none" />
                <path class="icon-path" d="M16 8C16 8 14.5 10 12 10C9.5 10 8 8 8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M8 16C8 16 9.5 14 12 14C14.5 14 16 16 16 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay2" d="M8 12H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                <path class="icon-path-delay1" d="M12 8V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </template>
          </GlassCard>
        </div>

      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

const navigateToUrl = (url: string) => {
  window.location.href = url;
};
// 带过渡效果的导航方法
const navigateTo = (path: string, query?: Record<string, any>) => {
  // 添加点击的视觉反馈
  const clickEffect = document.createElement("div");
  clickEffect.className = "click-ripple";
  document.body.appendChild(clickEffect);

  // 设置点击波纹的位置
  const mouseX = event ? (event as MouseEvent).clientX : window.innerWidth / 2;
  const mouseY = event ? (event as MouseEvent).clientY : window.innerHeight / 2;
  clickEffect.style.top = `${mouseY}px`;
  clickEffect.style.left = `${mouseX}px`;

  // 触发点击动画
  clickEffect.classList.add("active");

  // 动画结束后移除元素
  setTimeout(() => {
    router.push(query ? { path, query } : path);

    setTimeout(() => {
      document.body.removeChild(clickEffect);
    }, 600);
  }, 300);
};

const GlassCard = defineAsyncComponent(
  () => import("../components/GlassCard.vue")
);
const GlassPanel = defineAsyncComponent(
  () => import("../components/GlassPanel.vue")
);
const SparkleEffect = defineAsyncComponent(
  () => import("../components/SparkleEffect.vue")
);
const StarCanvasBackground = defineAsyncComponent(
  () => import("../components/StarCanvasBackground.vue")
);

onMounted(() => {
  // 添加页面加载完成的标记
  document.body.classList.add("page-loaded");
});
</script>

<style scoped>
.title-container {
  position: relative;
  margin-bottom: 1rem;
  animation: float 5s ease-in-out infinite;
}

.title-text {
  position: relative;
  z-index: 2;
  color: white;
  font-weight: 800;
  letter-spacing: 1px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.5));
}

/* 发光效果 - 使用多层伪元素 */
.title-text::before {
  content: "超维智能座舱数字全息系统";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: #6366f1;
  filter: blur(6px);
  opacity: 0.5;
  animation: pulse 3s ease-in-out infinite;
}

.title-text::after {
  content: "超维智能座舱数字全息系统";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  color: #8b5cf6;
  filter: blur(12px);
  opacity: 0.3;
  animation: pulse 4s ease-in-out infinite reverse;
}

/* 增强版标题进入动画 */
.advanced-entrance {
  display: inline-block;
  opacity: 0;
  transform: perspective(1000px) translateY(-50px) translateZ(-100px)
    rotateX(30deg) scale(0.9);
  filter: blur(8px);
  animation: advanced-entrance 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

@keyframes advanced-entrance {
  0% {
    opacity: 0;
    transform: perspective(1000px) translateY(-50px) translateZ(-100px)
      rotateX(30deg) scale(0.9);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: perspective(1000px) translateY(0) translateZ(0) rotateX(0)
      scale(1);
    filter: blur(0);
  }
}

/* 文本分段淡入效果 */
.fade-in-sequence {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.2rem;
  opacity: 0;
  animation: fade-in 0.5s ease-out 0.8s forwards;
}

.fade-in-item {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-item:nth-child(1) {
  animation: fade-in-slide-up 0.6s ease-out 1s forwards;
}

.fade-in-item:nth-child(2) {
  animation: fade-in-slide-up 0.6s ease-out 1.2s forwards;
}

.fade-in-item:nth-child(3) {
  animation: fade-in-slide-up 0.6s ease-out 1.4s forwards;
}

.fade-in-item:nth-child(4) {
  animation: fade-in-slide-up 0.6s ease-out 1.6s forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-in-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题入场动画 */
.entrance-animation {
  opacity: 0;
  transform: translateY(-30px);
  animation: fadeInDown 1s ease forwards;
  animation-delay: 0.2s;
}

/* 卡片入场动画 */
.card-entrance-animation {
  opacity: 0;
  transform: scale(0.8) translateY(50px);
  animation: cardExpand 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardExpand {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    filter: blur(6px);
  }
  50% {
    opacity: 0.8;
    filter: blur(10px);
  }
}

/* 卡片基础样式 */
.glass {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 卡片悬浮效果 */
.glass:hover {
  background: rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
  transform: translateY(-5px);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 玻璃反光效果 - 伪元素 */
.glass::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0s;
  z-index: 1;
}

/* 鼠标悬停时的反光动画 */
.glass:hover::before {
  left: 150%;
  transition: all 0.7s ease;
}

/* 增强毛玻璃效果 */
.glass-darker {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* SVG图标动画效果 */
.icon-svg {
  transition: transform 0.3s ease;
}

.glass:hover .icon-svg {
  transform: scale(1.1);
}

.icon-path,
.icon-path-delay1,
.icon-path-delay2,
.icon-border {
  stroke-dasharray: 100;
  stroke-dashoffset: 0;
  transition: all 0.5s ease;
}

.glass:hover .icon-path {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease forwards;
}

.glass:hover .icon-path-delay1 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.2s forwards;
}

.glass:hover .icon-path-delay2 {
  stroke-dashoffset: 100;
  animation: dash 1.5s ease 0.4s forwards;
}

.glass:hover .icon-border {
  stroke-dashoffset: 100;
  animation: dash 2s ease 0.1s forwards;
}

@keyframes dash {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* 调整描述文字位置，避免被粒子效果遮挡 */
.animate-slide-in {
  position: relative;
  z-index: 10;
}

/* 全屏背景效果容器 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

/* 确保内容在背景上方 */
.container {
  position: relative;
  z-index: 1;
}

/* 标题容器调整 */
.title-container {
  position: relative;
  z-index: 10; /* 确保标题在发光线上方 */
}

/* 点击波纹效果 */
.click-ripple {
  position: fixed;
  z-index: 9999;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: scale(0);
  opacity: 1;
  pointer-events: none;
}

.click-ripple.active {
  animation: ripple 0.8s cubic-bezier(0, 0.5, 0.5, 1) forwards;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(100);
    opacity: 0;
  }
}
</style> 