<template>
  <div class="tech-bg min-h-screen py-8 relative">
    <div class="container mx-auto px-4">
      <!-- 顶部导航 -->
      <div class="glass-card py-5 px-10 flex items-center mb-8">
        <button class="text-gray-300 hover:text-white mr-5" @click="$router.back()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-3xl font-bold text-white">涂鸦绘画</h1>
      </div>

      <!-- 主内容区域 -->
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 左侧区域（根据画布状态显示不同内容） -->
        <div class="w-full md:w-1/2">
          <!-- 未创建画布状态 -->
          <GlassPanel v-if="!isCanvasActive" class="p-6">
            <div class="flex flex-col items-center justify-center min-h-[300px] text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-purple-300 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 16l2.879-2.879a2 2 0 012.828 0L16 16m-1.414-1.414l.707-.707a2 2 0 012.828 0L20 16" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h2 class="text-2xl font-bold text-purple-200 mb-4">准备开始涂鸦</h2>
              <p class="text-gray-300 mb-8">请在右侧输入提示词，然后点击"创建画布"按钮开始绘图</p>
            </div>
          </GlassPanel>

          <!-- 已创建画布状态 -->
          <GlassPanel v-if="isCanvasActive" class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">
              <span>绘图说明：</span>
              <span class="text-lg font-normal">{{ drawingInstruction }}</span>
            </h2>
            
            <!-- 当前选择的画图类型 -->
            <div class="mb-4 p-3 bg-purple-900/20 rounded-lg border border-purple-500/30">
              <p class="text-purple-200">
                <span class="font-medium">当前选择的画图类型：</span>
                <span class="text-white">{{ selectedPromptType }}</span>
              </p>
            </div>

            <!-- 画布工具栏 -->
            <div class="flex items-center justify-between mb-4 bg-dark-800/30 p-3 rounded-lg">
              <!-- 颜色选择器 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-300">颜色：</span>
                <input type="color" v-model="currentColor" class="h-8 w-10 rounded cursor-pointer" title="选择画笔颜色">
                <!-- 预设颜色 -->
                <div class="flex space-x-1">
                  <div v-for="color in presetColors" :key="color" :style="{ backgroundColor: color }" @click="currentColor = color" class="w-6 h-6 rounded-full cursor-pointer hover:scale-110 transition-transform" :class="{'ring-2 ring-white': currentColor === color}">
                  </div>
                </div>
              </div>

              <!-- 笔刷大小选择器 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-300">大小：</span>
                <input type="range" v-model="currentBrushSize" min="1" max="30" class="w-24 h-2 cursor-pointer" title="调整画笔大小">
                <span class="text-sm text-gray-300 w-5">{{ currentBrushSize }}</span>
              </div>

              <!-- 画笔/橡皮切换按钮 -->
              <div class="flex items-center space-x-2">
                <button @click="currentTool = 'pen'" class="p-2 rounded-lg transition-colors" :class="{'bg-purple-500/50': currentTool === 'pen', 'bg-gray-700/50': currentTool !== 'pen'}" title="使用画笔">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </button>
                <button @click="currentTool = 'eraser'" class="p-2 rounded-lg transition-colors" :class="{'bg-purple-500/50': currentTool === 'eraser', 'bg-gray-700/50': currentTool !== 'eraser'}" title="使用橡皮擦">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- 画布容器 -->
            <div class="relative w-full overflow-hidden rounded-lg bg-white mb-6">
              <canvas ref="drawingCanvas" width="512" height="512" @mousedown="startDrawing" @mousemove="draw" @mouseup="stopDrawing" @mouseout="stopDrawing" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="stopDrawing" class="w-full border border-gray-300 cursor-crosshair">
              </canvas>
            </div>

            <!-- 控制按钮 -->
            <div class="flex justify-between">
              <button @click="clearCanvas" class="px-6 py-3 bg-gray-700 text-white rounded-lg font-medium hover:bg-gray-600 transition-all duration-300" :disabled="isSubmitting">
                清除画布
              </button>
              <button @click="submitDrawing" class="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center" :disabled="isSubmitting">
                <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isTaskCreated ? '重新生成' : '生成图像' }}
              </button>
            </div>
          </GlassPanel>
        </div>

        <!-- 右侧结果区域 -->
        <div class="w-full md:w-1/2">
          <GlassPanel class="p-6">
            <h2 class="text-2xl font-bold text-purple-200 mb-6">提示词与预览</h2>

            <!-- 提示词输入框 -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-purple-200 mb-2">画图类型</label>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                <div v-for="(promptValue, promptKey) in promptOptions" :key="promptKey" 
                     class="px-4 py-3 bg-gray-900/70 border rounded-lg cursor-pointer transition-all duration-200"
                     :class="selectedPromptType === promptKey ? 'border-purple-500 bg-purple-900/40' : 'border-purple-500/30 hover:border-purple-500/60'"
                     @click="selectPromptType(promptKey)">
                  <div class="flex items-center">
                    <div class="w-6 h-6 flex items-center justify-center mr-2 text-purple-300">
                      <svg v-if="selectedPromptType === promptKey" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" opacity="0.4">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <span class="text-white">{{ promptKey }}</span>
                  </div>
                </div>
              </div>
              <p class="text-xs text-gray-400 mt-2">
                选择画图类型，AI将根据您的涂鸦和选择的类型生成图像
              </p>
            </div>

            <!-- 创建画布按钮（仅在未创建画布时显示） -->
            <div v-if="!isCanvasActive" class="mb-6">
              <button @click="createCanvas" class="w-full px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg font-medium hover:opacity-90 transition-all duration-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                创建画布
              </button>
            </div>

            <!-- 重新生成画布按钮（仅在已创建画布时显示） -->
            <div v-if="isCanvasActive && !isSubmitting" class="mb-6">
              <button @click="regenerateCanvas" class="w-full px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-all duration-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                创建新画布
              </button>
            </div>

            <!-- 任务ID显示 -->
            <!-- 注释掉任务ID显示部分 -->
            <!--
            <div v-if="taskId" class="mb-4">
              <label class="block text-sm font-medium text-purple-200 mb-2">任务ID</label>
              <input type="text" v-model="taskId" readonly class="w-full px-4 py-2 bg-gray-900/70 border border-purple-500/30 text-white rounded-lg opacity-70 cursor-not-allowed" />
            </div>
            -->

            <!-- 使用的提示词显示 -->
            <!-- 注释掉使用的提示词显示部分 -->
            <!--
            <div v-if="isTaskCreated" class="mb-6">
              <label class="block text-sm font-medium text-purple-200 mb-2">
                使用的提示词 <span class="text-xs text-gray-400">(可编辑后重新生成)</span>
              </label>
              <textarea v-model="currentPrompt" rows="3" class="w-full px-4 py-3 bg-gray-900/70 border border-purple-500/30 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" :disabled="isSubmitting">
              </textarea>
            </div>
            -->

            <!-- 加载状态 -->
            <div v-if="isLoading" class="flex flex-col items-center justify-center my-6">
              <ProgressBar :progress="generationProgress" :completed-steps="1" :total-steps="1" class="w-full mb-4" />
              <p class="text-purple-200">生成中，请耐心等待... {{ Math.round(generationProgress) }}%</p>
            </div>

            <!-- 结果图像 -->
            <div v-if="resultImageUrl" class="bg-dark-800/60 rounded-lg overflow-hidden mb-4">
              <img :src="resultImageUrl" class="w-full h-auto" alt="生成结果" @click="openImageViewer()" />
            </div>

            <!-- 空状态 -->
            <div v-else-if="!isLoading" class="bg-dark-800/60 rounded-lg p-8 flex flex-col items-center justify-center min-h-[300px]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-purple-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14" />
                <circle cx="8" cy="8" r="1.5" fill="currentColor" />
              </svg>
              <p class="text-purple-200 text-center mb-2">{{ isCanvasActive ? (statusMessage || '尚未生成图像') : '请先创建画布' }}</p>
              <p class="text-gray-400 text-sm text-center">{{ isCanvasActive ? (statusDescription || '在左侧画布上绘制并点击"生成图像"按钮') : '点击上方的"创建画布"按钮开始' }}</p>
            </div>

          </GlassPanel>
        </div>
      </div>
    </div>

    <!-- 图片查看器 -->
    <ImageViewer v-model="showImageViewer" :images="viewerImages" :initialIndex="currentImageIndex" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, nextTick } from "vue";
import { themeApi } from "../api/themeApi";
import { API_CONFIG } from "../config/api";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import ImageViewer from "../components/ImageViewer.vue";
// 移除 element-plus 导入
// import { ElMessage } from "element-plus";

// 组件导入
const GlassPanel = defineAsyncComponent(
  () => import("../components/GlassPanel.vue")
);
const ProgressBar = defineAsyncComponent(
  () => import("../components/ProgressBar.vue")
);

// 绘图说明
const drawingInstruction = ref<string>(
  "请在画布上涂鸦，选择画图类型后AI将生成相应风格的图像"
);

// 画布激活状态
const isCanvasActive = ref<boolean>(false);

// 画布状态
const drawingCanvas = ref<HTMLCanvasElement | null>(null);
const isDrawing = ref<boolean>(false);
const currentColor = ref<string>("#000000");
const currentBrushSize = ref<number>(5);
const currentTool = ref<"pen" | "eraser">("pen");
const lastX = ref<number>(0);
const lastY = ref<number>(0);

// 预设颜色
const presetColors = ref<string[]>([
  "#000000",
  "#FF0000",
  "#00FF00",
  "#0000FF",
  "#FFFF00",
  "#FF00FF",
  "#00FFFF",
  "#FFFFFF",
]);

// 用户输入的提示词
const userPrompt = ref<string>("");
// 当前使用的提示词（可能会在任务创建后被修改）
const currentPrompt = ref<string>("");

// 任务状态
const isSubmitting = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const isTaskCreated = ref<boolean>(false);
const generationProgress = ref<number>(0);
const taskId = ref<string>("");
const resultImageUrl = ref<string | null>(null);
const statusMessage = ref<string>("");
const statusDescription = ref<string>("");

// 轮询相关
let pollingInterval: number | null = null;

// 图片查看器
const showImageViewer = ref<boolean>(false);
const viewerImages = ref<string[]>([]);
const currentImageIndex = ref<number>(0);

// 提示词选项
const promptOptions = ref<Record<string, string>>({
  "人像": "masterpiece, best quality, high resolution, highly detailed, intricate details, professional photography, portrait of a person, dressed in elegant attire, perfect composition, soft lighting, warm tone",
  "动物": "masterpiece, best quality, high resolution, highly detailed, intricate details, professional photography, adorable animal, natural habitat, wildlife photography, perfect composition, warm lighting",
  "山水": "masterpiece, best quality, high resolution, highly detailed, intricate details, traditional Chinese landscape painting, mountains and rivers, fog, poetic atmosphere, flowing water, ancient style",
  "花卉": "masterpiece, best quality, high resolution, highly detailed, intricate details, professional photography, beautiful flowers, vibrant colors, perfect composition, soft lighting, botanical garden",
  "建筑": "masterpiece, best quality, high resolution, highly detailed, intricate details, professional photography, magnificent architecture, structural beauty, perfect composition, dramatic lighting",
  "未来城市": "masterpiece, best quality, high resolution, highly detailed, intricate details, futuristic cityscape, advanced technology, flying vehicles, neon lights, skyscrapers, sci-fi atmosphere",
  "自然风景": "masterpiece, best quality, high resolution, highly detailed, intricate details, professional photography, breathtaking landscape, natural scenery, perfect composition, golden hour lighting",
  "现代艺术": "masterpiece, best quality, high resolution, highly detailed, intricate details, modern art style, abstract elements, vibrant colors, artistic composition, gallery quality",
});

const selectedPromptType = ref<string>("");

// 初始化
onMounted(() => {
  // 设置默认的画图类型为"动物"
  selectedPromptType.value = "动物";
});

// 创建画布
const createCanvas = async () => {
  if (isCanvasActive.value) return;

  isCanvasActive.value = true;

  // 等待DOM更新完成
  await nextTick();

  if (drawingCanvas.value) {
    const ctx = drawingCanvas.value.getContext("2d");
    if (ctx) {
      // 设置画布背景为白色
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(0, 0, drawingCanvas.value.width, drawingCanvas.value.height);
    }
  }

  // 重置相关状态
  resultImageUrl.value = null;
  taskId.value = "";
  statusMessage.value = "";
  // 修复字符串中的引号问题
  statusDescription.value = `请在画布上涂鸦并点击"生成图像"按钮`;
  generationProgress.value = 0;
  isTaskCreated.value = false;
  isSubmitting.value = false;
  isLoading.value = false;
  // 用户已选择的类型保留
};

// 重新生成画布
const regenerateCanvas = async () => {
  if (!isCanvasActive.value) return;

  // 确认操作
  if (!confirm("确定要重新生成画布吗？这将清除当前画布和生成结果。")) return;

  // 清除画布内容
  if (drawingCanvas.value) {
    const ctx = drawingCanvas.value.getContext("2d");
    if (ctx) {
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(0, 0, drawingCanvas.value.width, drawingCanvas.value.height);
    }
  }

  // 重置所有相关状态
  resultImageUrl.value = null;
  taskId.value = "";
  statusMessage.value = "";
  // 修复字符串中的引号问题
  statusDescription.value = '请在画布上涂鸦并点击"生成图像"按钮';
  generationProgress.value = 0;
  isTaskCreated.value = false;
  isSubmitting.value = false;
  isLoading.value = false;
  // 用户提示词保留，避免用户需要重新输入
};

// 生成任务ID
const generateTaskId = (): string => {
  // 生成16位UUID + 时间戳
  const timestamp = dayjs().format("YYYYMMDDHHmmss");
  const uuid = uuidv4().replace(/-/g, "").substr(0, 16);
  return `doodle_${uuid}_${timestamp}`;
};

// 开始绘制
const startDrawing = (e: MouseEvent) => {
  if (!drawingCanvas.value || !isCanvasActive.value) return;

  isDrawing.value = true;
  const rect = drawingCanvas.value.getBoundingClientRect();
  lastX.value = e.clientX - rect.left;
  lastY.value = e.clientY - rect.top;

  // 调整坐标以适应实际画布尺寸
  lastX.value = (lastX.value / rect.width) * drawingCanvas.value.width;
  lastY.value = (lastY.value / rect.height) * drawingCanvas.value.height;
};

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  if (!drawingCanvas.value || !isCanvasActive.value || e.touches.length === 0)
    return;

  e.preventDefault(); // 防止页面滚动

  isDrawing.value = true;
  const rect = drawingCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];

  lastX.value = touch.clientX - rect.left;
  lastY.value = touch.clientY - rect.top;

  // 调整坐标以适应实际画布尺寸
  lastX.value = (lastX.value / rect.width) * drawingCanvas.value.width;
  lastY.value = (lastY.value / rect.height) * drawingCanvas.value.height;
};

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (
    !isDrawing.value ||
    !drawingCanvas.value ||
    !isCanvasActive.value ||
    e.touches.length === 0
  )
    return;

  e.preventDefault(); // 防止页面滚动

  const rect = drawingCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];

  const x = touch.clientX - rect.left;
  const y = touch.clientY - rect.top;

  // 调整坐标以适应实际画布尺寸
  const canvasX = (x / rect.width) * drawingCanvas.value.width;
  const canvasY = (y / rect.height) * drawingCanvas.value.height;

  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 绘制
const draw = (e: MouseEvent) => {
  if (!isDrawing.value || !drawingCanvas.value || !isCanvasActive.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // 调整坐标以适应实际画布尺寸
  const canvasX = (x / rect.width) * drawingCanvas.value.width;
  const canvasY = (y / rect.height) * drawingCanvas.value.height;

  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 绘制线条
const drawLine = (fromX: number, fromY: number, toX: number, toY: number) => {
  if (!drawingCanvas.value || !isCanvasActive.value) return;

  const ctx = drawingCanvas.value.getContext("2d");
  if (!ctx) return;

  ctx.beginPath();
  ctx.moveTo(fromX, fromY);
  ctx.lineTo(toX, toY);

  // 使用用户选择的颜色或白色（如果是橡皮擦）
  ctx.strokeStyle =
    currentTool.value === "eraser" ? "#FFFFFF" : currentColor.value;

  // 确保线条足够粗
  ctx.lineWidth = Math.max(currentBrushSize.value, 3); // 线条最小宽度为3像素

  // 圆滑的线条端点和连接点
  ctx.lineJoin = "round";
  ctx.lineCap = "round";

  // 如果是黑色画笔，增加线条的对比度
  if (currentColor.value === "#000000" && currentTool.value !== "eraser") {
    ctx.globalAlpha = 1.0; // 确保不透明度最大
  }

  ctx.stroke();
  ctx.globalAlpha = 1.0; // 重置透明度
};

// 停止绘制
const stopDrawing = () => {
  isDrawing.value = false;
};

// 清除画布
const clearCanvas = () => {
  if (!drawingCanvas.value || !isCanvasActive.value) return;

  if (!confirm("确定要清除画布吗？")) return;

  const ctx = drawingCanvas.value.getContext("2d");
  if (ctx) {
    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, drawingCanvas.value.width, drawingCanvas.value.height);
  }

  // 不再重置结果状态，清除画布不影响结果预览
};

// 提交绘制的内容
const submitDrawing = async () => {
  if (!isCanvasActive.value) {
    // 替换 ElMessage.error 为 alert
    alert("请先创建画布");
    return;
  }

  const canvas = drawingCanvas.value;
  if (!canvas) return;

  // 检查画布是否为空
  if (isCanvasEmpty(canvas)) {
    // 替换 ElMessage.error 为 alert
    alert("请先在画布上绘制内容");
    isLoading.value = false;
    isSubmitting.value = false;
    return;
  }

  if (!selectedPromptType.value) {
    // 替换 ElMessage.error 为 alert
    alert("请选择画图类型");
    return;
  }

  // 获取选定类型对应的固定提示词
  const actualPrompt = promptOptions.value[selectedPromptType.value];
  if (!actualPrompt) {
    alert("所选画图类型无效");
    return;
  }

  isLoading.value = true;
  isSubmitting.value = true;
  statusMessage.value = "提交中...";
  statusDescription.value = "正在上传您的涂鸦作品";
  generationProgress.value = 10;
  // 结果图像在提交时暂时清除，等待新结果
  resultImageUrl.value = null;

  try {
    // 将画布内容转换为JPG格式并确保背景为白色
    const ctx = canvas.getContext("2d");
    if (ctx) {
      // 确保背景为白色(再次设置一次白色背景)
      ctx.globalCompositeOperation = "destination-over";
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.globalCompositeOperation = "source-over";
    }

    // 获取图像数据
    const dataUrl = canvas.toDataURL("image/jpeg", 0.95); // 提高质量到0.95

    // 从dataUrl中提取base64数据部分（去掉前缀）
    const base64Data = dataUrl.split(",")[1];

    // *** 修改任务ID生成逻辑 ***
    let currentTaskId: string;
    if (!isTaskCreated.value) {
      // 首次生成图像，创建新的 Task ID
      const newTaskId = generateTaskId();
      taskId.value = newTaskId; // 存储新的ID
      currentTaskId = newTaskId;
    } else {
      // 更新提示词，复用旧的 Task ID
      if (!taskId.value) {
        // 添加错误处理，防止 Task ID 丢失
        alert("发生错误：任务ID丢失，无法更新。请尝试重新生成画布。");
        isLoading.value = false;
        isSubmitting.value = false;
        return;
      }
      currentTaskId = taskId.value;
    }

    // 手动创建文件对象
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: "image/jpeg" });

    // 提交新任务
    const formData = new FormData();
    formData.append("sketch_image", blob);
    // 使用 currentTaskId 
    formData.append("task_id", currentTaskId);
    // 使用选定类型对应的固定提示词
    formData.append("prompt", actualPrompt);
    formData.append("num_samples", "1");

    const result = await themeApi.doodleStart(formData);

    // 确认API返回的任务ID与前端使用的一致，并更新状态
    taskId.value = result.task_id;
    if (taskId.value !== currentTaskId) {
      // 如果不一致，以API返回为准可能更安全，具体取决于后端实现
    }

    // 保存增强后的提示词和当前选择的类型
    currentPrompt.value = result.enhanced_prompt || actualPrompt;
    isTaskCreated.value = true; // 标记任务已创建或更新
    generationProgress.value = 20;
    statusMessage.value = "任务已提交";
    statusDescription.value = "等待服务器处理中...";
    pollTaskStatus();
  } catch (error: any) {
    isLoading.value = false;
    isSubmitting.value = false;
    generationProgress.value = 0;
    statusMessage.value = "提交失败";
    statusDescription.value = error.message || "未知错误";
  }
};

// 开始轮询任务状态
const pollTaskStatus = () => {
  // 清除之前的轮询
  if (pollingInterval) clearInterval(pollingInterval);

  isLoading.value = true; // 确保开始轮询时设置加载状态
  isSubmitting.value = true; // 确保开始轮询时禁用按钮

  pollingInterval = window.setInterval(async () => {
    if (!taskId.value) {
      // console.warn("Task ID 为空，停止轮询");
      stopPolling();
      isLoading.value = false;
      isSubmitting.value = false;
      return;
    }
    try {
      const response = await themeApi.doodleStatus(taskId.value);

      // 处理不同状态
      if (response.status === "completed") {
        // 任务完成
        stopPolling();
        isLoading.value = false;
        isSubmitting.value = false; // <--- 启用按钮
        resetProgress();
        generationProgress.value = 100;

        // 设置结果图像URL
        if (response.result_image_url) {
          resultImageUrl.value = response.result_image_url;
          statusMessage.value = "生成成功";
          statusDescription.value = "您可以修改提示词并重新生成";
        } else if (response.result_image_base64) {
          // 如果后端返回base64
          resultImageUrl.value = response.result_image_base64;
          statusMessage.value = "生成成功";
          statusDescription.value = "您可以修改提示词并重新生成";
        } else {
          // 如果完成但没有图像信息
          statusMessage.value = "生成完成但未找到图像";
          statusDescription.value = response.error || "无法获取结果图像";
        }
      } else if (response.status === "failed") {
        // 任务失败
        stopPolling();
        isLoading.value = false;
        isSubmitting.value = false; // <--- 启用按钮
        resetProgress();
        statusMessage.value = "生成失败";
        statusDescription.value = response.error || "未知错误";
      } else {
        // 任务进行中
        statusMessage.value = "任务进行中...";
        // 确保 isSubmitting 和 isLoading 保持 true
        isSubmitting.value = true;
        isLoading.value = true;
        if (response.progress) {
          generationProgress.value = response.progress;
        }
      }
    } catch (error: any) {
      // console.error("查询任务状态失败:", error);
      // 查询失败也应该停止轮询并启用按钮，避免无限循环
      stopPolling();
      isLoading.value = false;
      isSubmitting.value = false;
      statusMessage.value = "查询状态失败";
      statusDescription.value = "无法连接服务器或任务已失效";
    }
  }, 500); // 每0.5秒轮询一次
};

// 停止轮询
const stopPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }
};

// 重置进度
const resetProgress = () => {
  generationProgress.value = 0;
};

// 打开图片查看器
const openImageViewer = () => {
  if (resultImageUrl.value) {
    viewerImages.value = [resultImageUrl.value];
    currentImageIndex.value = 0;
    showImageViewer.value = true;
  }
};

// 组件卸载时清理
onMounted(() => {
  return () => {
    stopPolling();
  };
});

// 检查画布是否为空
const isCanvasEmpty = (canvas: HTMLCanvasElement): boolean => {
  const ctx = canvas.getContext("2d", { willReadFrequently: true }); // 启用 willReadFrequently 以优化性能
  if (!ctx) return true; // 如果无法获取上下文，视为无效或空

  // 获取画布的像素数据
  // 检查所有像素是否都是白色（或接近白色，允许一点点误差）
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  const whiteThreshold = 250; // 允许一点点非白色像素 (0-255)

  for (let i = 0; i < data.length; i += 4) {
    // 检查 RGB 值，忽略 Alpha 通道 (data[i+3])
    if (
      data[i] < whiteThreshold ||
      data[i + 1] < whiteThreshold ||
      data[i + 2] < whiteThreshold
    ) {
      // 发现非白色像素，画布不为空
      return false;
    }
  }

  // 如果所有检查的像素都是白色，则画布为空
  return true;
};

// 选择提示词类型
const selectPromptType = (type: string) => {
  selectedPromptType.value = type;
};
</script>

<style scoped>
/* 毛玻璃卡片样式 */
.glass-card {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

/* 画布与光标样式 */
canvas {
  touch-action: none; /* 防止移动设备上的默认触摸行为 */
}

/* 预览图像样式 */
.preview-img {
  width: 100%;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.preview-img:hover {
  transform: scale(1.02);
}
</style> 