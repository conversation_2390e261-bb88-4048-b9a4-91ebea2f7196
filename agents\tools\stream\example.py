"""
示例文件，演示如何使用LLM流式输出工具
"""

import asyncio
import logging
import time
import json
import sys
import os
from typing import AsyncGenerator, Dict, List, Any

# 添加父目录到路径，以便导入stream包
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from agents.tools.stream import (
    StreamManager,
    WebSocketServer,
    LLMStreamAdapter,
    run_websocket_server
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 模拟LLM响应生成器
async def mock_llm_response() -> AsyncGenerator[str, None]:
    """模拟LLM流式响应"""
    chunks = [
        "🧠 思考问题中...\n",
        "我需要考虑几个方面的信息：\n",
        "1. 现有的架构设计\n",
        "2. 用户需求\n",
        "3. 技术可行性\n\n",
        "让我们看一下代码示例：\n",
        "```python\n",
        "def process_stream(data):\n",
        "    # 处理流式数据\n",
        "    result = analyze_data(data)\n",
        "    return format_output(result)\n",
        "\n",
        "def analyze_data(data):\n",
        "    # 分析数据\n",
        "    return data.transform()\n",
        "\n",
        "def format_output(result):\n",
        "    # 格式化输出\n",
        "    return {\n",
        "        'status': 'success',\n",
        "        'data': result,\n",
        "        'timestamp': time.time()\n",
        "    }\n",
        "```\n",
        "我需要调用搜索工具找一些相关信息：\n",
        "<tool_call>web_search: {\"query\": \"最佳流式处理架构模式\"}</tool_call>\n",
        "\n",
        "🔍 分析结果...\n",
        "根据我的分析，建议采用以下方案：\n",
        "1. 使用Redis Pub/Sub进行消息分发\n",
        "2. 采用WebSocket建立持久连接\n",
        "3. 使用MongoDB存储会话历史\n",
        "\n",
        "这样可以确保流式数据的实时传输和可靠存储。"
    ]
    
    for chunk in chunks:
        yield chunk
        # 随机延迟模拟真实场景
        await asyncio.sleep(0.3)

# 处理客户端事件示例
def handle_event(event):
    """处理来自WebSocket的事件"""
    print(f"收到事件: {event.type} - {event.payload}")

# 模拟客户端示例
async def simulate_client(session_id: str):
    """模拟客户端与服务器交互"""
    print(f"模拟客户端开始, 会话ID: {session_id}")
    
    # 等待WebSocket服务器启动
    await asyncio.sleep(2)
    
    # 导入websockets库
    import websockets
    
    # 连接到WebSocket服务器
    uri = f"ws://localhost:8765/stream?userId=test_user&sessionId={session_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            # 发送心跳检测
            await websocket.send(json.dumps({
                "type": "ping",
                "timestamp": time.time()
            }))
            
            # 获取历史消息
            await websocket.send(json.dumps({
                "type": "get_history",
                "limit": 10
            }))
            
            # 接收消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    print(f"客户端收到: {data['type']}")
                    
                    if data["type"] == "token":
                        print(f"  内容: {data['payload']['text']}")
                    elif data["type"] == "code_block":
                        print(f"  代码块: {data['payload']['language']}")
                    elif data["type"] == "thinking_step":
                        print(f"  思考: {data['payload']['icon']} {data['payload']['text']}")
                    elif data["type"] == "tool_call":
                        print(f"  工具调用: {data['payload']['tool_name']}")
                    elif data["type"] == "completion":
                        print(f"  完成生成，长度: {len(data['payload']['text'])}")
                    elif data["type"] == "error":
                        print(f"  错误: {data['payload']['message']}")
                except Exception as e:
                    print(f"处理消息错误: {str(e)}")
    except Exception as e:
        print(f"WebSocket连接错误: {str(e)}")

# 完整示例
async def run_example():
    """运行完整示例"""
    # 创建流式管理器
    stream_manager = StreamManager()
    
    # 创建会话
    session_id = stream_manager.create_session(user_id="test_user")
    print(f"已创建会话: {session_id}")
    
    # 创建LLM适配器
    adapter = LLMStreamAdapter(agent_id="test_agent")
    
    # 启动模拟客户端（在单独的任务中）
    client_task = asyncio.create_task(simulate_client(session_id))
    
    # 启动WebSocket服务器（在单独的任务中）
    server = WebSocketServer(host="localhost", port=8765)
    server_task = asyncio.create_task(server.start())
    
    # 等待服务器启动
    await asyncio.sleep(1)
    
    # 保存一些上下文数据
    adapter.save_context(
        session_id=session_id,
        name="user_info",
        value={"name": "测试用户", "preferences": {"theme": "dark"}}
    )
    
    # 流式处理LLM响应
    print("开始处理LLM流式响应...")
    full_text = await adapter.process_llm_stream(
        session_id=session_id,
        stream_generator=mock_llm_response()
    )
    
    print(f"生成完成，总长度: {len(full_text)}")
    
    # 保存对话历史
    adapter.save_context(
        session_id=session_id,
        name="conversation_history",
        value={"messages": [{"role": "assistant", "content": full_text}]}
    )
    
    # 等待客户端接收完成
    await asyncio.sleep(5)
    
    # 关闭会话
    stream_manager.close_session(session_id)
    print(f"已关闭会话: {session_id}")
    
    # 取消任务
    client_task.cancel()
    server_task.cancel()
    
    try:
        await client_task
    except asyncio.CancelledError:
        pass
        
    try:
        await server_task
    except asyncio.CancelledError:
        pass
    
    print("示例运行完成")

if __name__ == "__main__":
    # 当作为脚本运行时，执行示例
    try:
        asyncio.run(run_example())
    except KeyboardInterrupt:
        print("程序被中断")
    finally:
        print("示例已退出") 