<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文生主题系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                            600: '#475569',
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            overflow: auto;
            background-color: #0f172a;
            background-image: 
                radial-gradient(at 40% 20%, #3b82f680 0px, transparent 50%),
                radial-gradient(at 80% 0%, #7c3aed80 0px, transparent 50%),
                radial-gradient(at 0% 50%, #0ea5e980 0px, transparent 50%);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-attachment: fixed;
            color: white;
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        
        .preview-container {
            display: flex;
            flex-direction: column;
            gap: 3rem;
            justify-content: center;
            padding: 2rem;
            max-width: 1800px;
            margin: 0 auto;
        }
        
        .mockup-frame {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 100%;
            height: 650px;
            margin-bottom: 1rem;
        }
        
        .mockup-frame .header {
            height: 2.75rem;
            background: rgba(20, 30, 48, 0.7);
            display: flex;
            align-items: center;
            padding: 0 1.25rem;
        }
        
        .mockup-frame .content {
            height: calc(100% - 2.75rem);
            background: rgba(15, 23, 42, 0.8);
            overflow: hidden;
        }
        
        .mockup-dot {
            height: 0.85rem;
            width: 0.85rem;
            border-radius: 50%;
            margin-right: 0.4rem;
        }

        @keyframes pulse-animation {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .animate-pulse-custom {
            animation: pulse-animation 2s infinite ease-in-out;
        }
        
        /* 新增样式 */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        .feature-card {
            transition: all 0.4s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: 1rem;
            background: linear-gradient(45deg, transparent 50%, rgba(255, 255, 255, 0.1) 60%, transparent 70%);
            z-index: -1;
            transition: all 0.6s ease;
            opacity: 0;
        }
        
        .feature-card:hover::before {
            background-position: 150% 0;
            opacity: 1;
        }
        
        .bg-gradient-tech {
            background: linear-gradient(125deg, #0ea5e9, #7c3aed, #3b82f6);
            background-size: 200% 200%;
            animation: gradientBG 15s ease infinite;
        }
        
        @keyframes gradientBG {
            0% {background-position: 0% 50%;}
            50% {background-position: 100% 50%;}
            100% {background-position: 0% 50%;}
        }
    </style>
</head>
<body class="dark">

<h1 class="text-center text-4xl font-bold pt-8 pb-4 text-white">文生主题系统 - 设计预览</h1>

<!-- 页面预览容器 -->
<div class="preview-container">

    <!-- 新增：主功能入口首页 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">系统首页</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="h-12 w-12 rounded-full bg-gradient-tech flex items-center justify-center float-animation">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h1 class="ml-4 text-3xl font-bold text-white">AI智能创作系统</h1>
                    </div>
                    <div class="flex items-center">
                        <button class="glass-card px-5 py-3 rounded-lg mr-4 hover:bg-purple-700/30 transition-all text-lg">
                            使用指南
                        </button>
                        <button class="glass-card px-5 py-3 rounded-lg hover:bg-purple-700/30 transition-all text-lg">
                            登录/注册
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <!-- 欢迎区域 -->
                        <div class="mb-10 text-center">
                            <h2 class="text-3xl font-bold text-white mb-4">欢迎使用 <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">AI智能创作系统</span></h2>
                            <p class="text-xl text-gray-300">选择您需要的创作功能，开启智能生成之旅</p>
                        </div>
                        
                        <!-- 功能卡片区域 -->
                        <div class="grid grid-cols-3 gap-8 flex-grow">
                            <!-- 文生壁纸功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生壁纸</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">通过文字描述智能生成高清壁纸，支持多种分辨率和风格定制</p>
                                <button class="mt-auto glass-card bg-blue-500/20 hover:bg-blue-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                            
                            <!-- 文生主题功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-purple-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生主题</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">一键生成完整UI主题，包含壁纸、图标、字体与配色方案</p>
                                <button class="mt-auto glass-card bg-purple-500/20 hover:bg-purple-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                            
                            <!-- 文生视频功能卡片 -->
                            <div class="feature-card glass-card rounded-xl p-8 flex flex-col items-center relative overflow-hidden group">
                                <div class="absolute -top-10 -right-10 w-40 h-40 bg-green-500/10 rounded-full blur-2xl transform transition-all duration-500 group-hover:scale-150"></div>
                                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-green-500 to-green-700 flex items-center justify-center mb-6 transform hover:rotate-12 transition-all duration-300 relative z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-3">文生视频</h3>
                                <p class="text-gray-300 text-center text-lg mb-6">基于文字描述生成动态视频内容，支持多种场景与风格</p>
                                <button class="mt-auto glass-card bg-green-500/20 hover:bg-green-500/30 text-white py-3 px-8 rounded-lg transition-all duration-300 text-lg">
                                    立即创建
                                </button>
                            </div>
                        </div>
                        
                        <!-- 底部扩展区域 -->
                        <div class="mt-10 pt-6 border-t border-gray-700/50 flex justify-between items-center">
                            <div>
                                <h3 class="text-xl font-semibold text-white mb-2">更多功能即将推出</h3>
                                <p class="text-gray-400 text-lg">我们正在不断拓展AI创作能力，敬请期待</p>
                            </div>
                            <button class="glass-card bg-dark-700/50 hover:bg-dark-700 text-white py-3 px-6 rounded-lg transition-all duration-300 flex items-center text-lg">
                                <span class="mr-2">加入测试计划</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主页预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">首页</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex justify-between items-center">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clip-rule="evenodd" />
                        </svg>
                        <h1 class="ml-4 text-3xl font-bold text-white">AI文生主题</h1>
                    </div>
                    <div>
                        <button class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 flex items-center text-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            创建主题
                        </button>
                    </div>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-8 text-purple-300">最近生成的主题</h2>
                        
                        <div class="grid grid-cols-3 gap-8 flex-grow">
                            <!-- 主题卡片1 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1614063742148-493c82af9a6c?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">中国风西湖主题</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-03-04</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 主题卡片2 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1561315235-448e9027f3f4?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">科技风未来城市</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-03-02</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- 主题卡片3 -->
                            <div class="glass-card rounded-lg p-6 flex flex-col">
                                <div class="h-48 rounded-lg bg-cover bg-center mb-5" style="background-image: url('https://images.unsplash.com/photo-1478760329108-5c3ed9d495a0?q=80&w=300')"></div>
                                <h3 class="font-semibold text-white text-xl">山水自然风光</h3>
                                <p class="text-base text-gray-300 mt-2">创建于 2024-02-28</p>
                                <div class="mt-auto flex justify-between items-center pt-5">
                                    <span class="text-base bg-green-900/40 text-green-400 py-1.5 px-4 rounded-md">已完成</span>
                                    <button class="text-purple-400 hover:text-purple-300 text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 创建主题页面预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">创建主题</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">创建新主题</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <h2 class="text-3xl font-bold mb-10 text-center text-purple-200">描述您想要的主题</h2>
                        
                        <div class="mb-8">
                            <textarea 
                                class="w-full p-5 bg-dark-800/50 border border-gray-700 focus:border-purple-500 rounded-lg text-white focus:outline-none resize-none h-40 text-lg"
                                placeholder="描述您想要的主题风格，例如：'帮我生成一个中国风的西湖风光的主题'"
                            ></textarea>
                        </div>
                        
                        <div class="mb-8">
                            <p class="text-gray-300 text-lg mb-4">生成速度</p>
                            <div class="flex items-center">
                                <span class="text-base text-gray-400 mr-4">快速</span>
                                <div class="flex-grow relative">
                                    <div class="h-4 bg-dark-700 rounded-full">
                                        <div class="h-full w-1/2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                                    </div>
                                    <div class="absolute h-7 w-7 bg-white rounded-full -top-1.5 border-2 border-purple-600 left-1/2 transform -translate-x-1/2 shadow"></div>
                                </div>
                                <span class="text-base text-gray-400 ml-4">高质量</span>
                            </div>
                        </div>
                        
                        <div class="mb-10">
                            <p class="text-gray-300 text-lg mb-4">参考样式</p>
                            <div class="grid grid-cols-4 gap-6">
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1592609931095-54a2168ae893?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1666697190001-51d9ad1fa9c0?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1664478711535-fd3cc5d1a99a?q=80&w=200')"></div>
                                <div class="rounded-lg h-32 bg-cover bg-center border-2 border-transparent hover:border-purple-500 cursor-pointer" style="background-image: url('https://images.unsplash.com/photo-1557683311-eac922347aa1?q=80&w=200')"></div>
                            </div>
                        </div>
                        
                        <button class="glass-card bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-5 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg text-xl">
                            开始生成
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成进度页面预览 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">生成进度</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">中国风西湖主题 - 生成中</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full flex flex-col">
                        <!-- 总进度 -->
                        <div class="mb-10">
                            <div class="flex justify-between mb-4">
                                <h2 class="text-2xl font-bold text-purple-200">总体进度</h2>
                                <span class="text-purple-300 font-semibold text-xl">45%</span>
                            </div>
                            <div class="h-6 bg-dark-700 rounded-full overflow-hidden">
                                <div class="h-full w-[45%] bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
                            </div>
                        </div>
                        
                        <!-- 当前步骤 -->
                        <div class="mb-10">
                            <h3 class="font-semibold text-purple-200 mb-4 text-xl">当前步骤：UI主题生成</h3>
                            <div class="flex items-center text-lg">
                                <span class="inline-block h-5 w-5 rounded-full bg-purple-500 animate-pulse-custom mr-4"></span>
                                <span class="text-gray-300">预计剩余时间：5分钟</span>
                            </div>
                        </div>
                        
                        <!-- 步骤列表 -->
                        <div class="flex-grow mb-8">
                            <h3 class="font-semibold text-purple-200 mb-6 text-xl">处理步骤</h3>
                            <div class="grid grid-cols-2 gap-x-16 gap-y-8">
                                <!-- 已完成的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-white text-lg">壁纸生成</p>
                                        <p class="text-base text-gray-400">已完成 - 用时 2分30秒</p>
                                    </div>
                                </div>
                                
                                <!-- 当前步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-purple-500 animate-pulse-custom"></div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-white text-lg">UI主题生成</p>
                                        <p class="text-base text-gray-400">进行中 - 45%</p>
                                    </div>
                                </div>
                                
                                <!-- 等待中的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                            <span class="text-base text-gray-400">3</span>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">UI资源切分</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                                
                                <!-- 等待中的步骤 -->
                                <div class="flex items-center">
                                    <div class="relative">
                                        <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                            <span class="text-base text-gray-400">4</span>
                                        </div>
                                        <div class="absolute top-10 left-5 w-0.5 h-16 bg-gray-600"></div>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">并行任务处理</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                                
                                <!-- 最后一个步骤 -->
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center">
                                        <span class="text-base text-gray-400">5</span>
                                    </div>
                                    <div class="ml-5">
                                        <p class="font-medium text-gray-400 text-lg">完成打包</p>
                                        <p class="text-base text-gray-500">等待中</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex justify-between">
                            <button class="py-4 px-7 bg-red-600/30 hover:bg-red-600/50 text-white rounded-lg transition text-lg">
                                终止任务
                            </button>
                            <button class="py-4 px-7 bg-purple-600/30 hover:bg-purple-600/50 text-white rounded-lg transition flex items-center text-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                </svg>
                                刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 壁纸预览页面 -->
    <div class="mockup-frame">
        <div class="header">
            <span class="mockup-dot bg-red-500"></span>
            <span class="mockup-dot bg-yellow-500"></span>
            <span class="mockup-dot bg-green-500"></span>
            <span class="ml-3 text-lg text-gray-300">壁纸预览</span>
        </div>
        <div class="content">
            <div class="h-full flex flex-col">
                <!-- 顶部导航 -->
                <div class="glass-card py-5 px-10 flex items-center">
                    <button class="text-gray-300 hover:text-white mr-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <h1 class="text-3xl font-bold text-white">壁纸预览</h1>
                </div>
                
                <!-- 主要内容 -->
                <div class="flex-grow p-10 overflow-hidden">
                    <div class="glass-card rounded-xl p-10 h-full">
                        <div class="grid grid-cols-5 gap-8 h-full">
                            <!-- 左侧预览图 -->
                            <div class="col-span-3 flex flex-col">
                                <div class="rounded-lg overflow-hidden mb-8 flex-grow bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1614063742148-493c82af9a6c?q=80&w=600')">
                                    <div class="w-full h-full flex items-center justify-center backdrop-blur-sm bg-black/20 hover:backdrop-blur-none hover:bg-transparent transition-all duration-300">
                                        <div class="bg-black/40 px-5 py-2.5 rounded-full text-white text-base font-semibold">点击放大</div>
                                    </div>
                                </div>
                                
                                <!-- 控制按钮 -->
                                <div class="flex justify-between">
                                    <button class="py-4 px-7 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition duration-300 flex items-center text-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                        </svg>
                                        重新生成
                                    </button>
                                    <button class="py-4 px-7 bg-purple-600 hover:bg-purple-500 text-white rounded-lg transition duration-300 text-lg">
                                        继续下一步
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 右侧信息和主色调 -->
                            <div class="col-span-2 flex flex-col">
                                <!-- 壁纸信息 -->
                                <div class="mb-8">
                                    <h2 class="text-2xl font-bold text-purple-200 mb-4">壁纸信息</h2>
                                    <div class="glass-card p-6 rounded-lg">
                                        <div class="grid grid-cols-2 gap-6 text-lg">
                                            <div>
                                                <p class="text-gray-400">尺寸</p>
                                                <p class="text-white">1920 x 1080</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">格式</p>
                                                <p class="text-white">JPG</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">分辨率</p>
                                                <p class="text-white">300dpi</p>
                                            </div>
                                            <div>
                                                <p class="text-gray-400">生成时间</p>
                                                <p class="text-white">2:30</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 主色调展示 -->
                                <div>
                                    <h2 class="text-2xl font-bold text-purple-200 mb-4">主色调</h2>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #2c5282;"></div>
                                            <span class="text-base text-gray-300">#2c5282</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #285e61;"></div>
                                            <span class="text-base text-gray-300">#285e61</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #744210;"></div>
                                            <span class="text-base text-gray-300">#744210</span>
                                        </div>
                                        <div class="flex flex-col items-center">
                                            <div class="h-20 w-20 rounded-lg mb-2" style="background-color: #1a202c;"></div>
                                            <span class="text-base text-gray-300">#1a202c</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

</body>
</html>
