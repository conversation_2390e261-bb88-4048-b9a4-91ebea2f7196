<template>
  <GlassPanel class="p-6">
    <!-- 步骤进度条 -->
    <div class="flex justify-between mb-4">
      <div v-for="(step, index) in steps" :key="index" 
           class="flex flex-col items-center flex-1">
        <div :class="stepClass(index)" 
             class="w-8 h-8 rounded-full flex items-center justify-center mb-2 transition-all">
          <span v-if="currentStep > index">✓</span>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <span class="text-sm" :class="currentStep >= index ? 'text-white' : 'text-gray-500'">{{ step }}</span>
      </div>
    </div>
    
    <!-- 动态进度条 -->
    <div class="h-2 bg-gray-800/50 rounded-full overflow-hidden">
      <div class="h-full bg-gradient-to-r from-info to-info-dark transition-all duration-500" 
           :style="`width: ${progress}%`"></div>
    </div>
  </GlassPanel>
</template>

<script setup>
import { computed } from 'vue';
import GlassPanel from '../GlassPanel.vue';

const props = defineProps({
  steps: {
    type: Array,
    default: () => ['提示词优化', '模型加载', '视频生成', '处理完成']
  },
  currentStep: {
    type: Number,
    default: 0
  },
  progress: {
    type: Number,
    default: 0
  }
});

const stepClass = (index) => {
  if (props.currentStep > index) {
    return 'bg-info text-white';
  } else if (props.currentStep === index) {
    return 'bg-info-dark text-white ring-2 ring-info ring-offset-2 ring-offset-gray-900';
  } else {
    return 'bg-gray-800 text-gray-400';
  }
};
</script> 