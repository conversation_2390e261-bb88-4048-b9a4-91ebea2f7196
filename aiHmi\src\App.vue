<template>
  <div id="app" class="desktop-container">
    <!-- 沉浸式桌面壁纸背景 -->
    <div class="wallpaper-background"></div>

    <!-- 欢迎界面 -->
    <div v-if="showWelcome" class="welcome-screen">
      <!-- VPA欢迎对话框 -->
      <div class="vpa-dialog glassmorphism">
        <div class="dialog-content">
          <h2>你好，我是xx，欢迎来到AI-HMI，</h2>
          <p>我是智能桌面精灵，可以根据场景给您定制专属桌面，试试对我说：</p>

          <div class="quick-actions">
            <button class="action-btn glassmorphism" @click="generateDesktop('commute')">
              生成通勤桌面
            </button>
            <button class="action-btn glassmorphism" @click="generateDesktop('navigation')">
              导航到xx
            </button>
            <button class="action-btn glassmorphism" @click="generateDesktop('alone')">
              帮我规划一个独处的桌面
            </button>
            <button class="action-btn glassmorphism" @click="generateDesktop('gaming')">
              生成游戏桌面
            </button>
            <button class="action-btn glassmorphism" @click="generateDesktop('relax')">
              帮我规划一个放松一日游
            </button>
          </div>

          <div class="ai-lab-info">
            <p>根据您的使用习惯，后续我会持续优化体验，您也可以对我说</p>
            <p><strong>AI-LAB基础到的更多介绍</strong></p>
          </div>
        </div>
      </div>

      <!-- VPA机器人形象 -->
      <div class="vpa-robot">
        <div class="robot-body">
          <div class="robot-head">
            <div class="robot-eyes">
              <div class="eye left-eye"></div>
              <div class="eye right-eye"></div>
            </div>
          </div>
          <div class="robot-arms">
            <div class="arm left-arm"></div>
            <div class="arm right-arm"></div>
          </div>
          <div class="robot-legs">
            <div class="leg left-leg"></div>
            <div class="leg right-leg"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主桌面界面 -->
    <div v-else class="main-desktop">
      <!-- 动态岛 -->
      <div class="dynamic-island">
        <svg class="icon" viewBox="0 0 24 24">
          <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
        </svg>
        {{ currentStatus }}
      </div>

      <!-- 8x4网格容器 -->
      <div class="grid-container">
        <!-- 天气卡片 4x2 -->
        <div class="card weather-card">
          <h3>
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            北京市
          </h3>
          <p class="temperature">
            25°
            <svg class="icon weather-icon" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
          </p>
          <p class="description">晴，西北风3级</p>
        </div>

        <!-- 导航卡片 4x2 -->
        <div class="card navigation-card">
          <h3>
            <svg class="icon" viewBox="0 0 24 24">
              <polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>
            </svg>
            前往：公司
          </h3>
          <p>预计30分钟到达</p>
          <p>500米后左转</p>
        </div>

        <!-- 音乐卡片 4x2 -->
        <div class="card music-card">
          <h3>正在播放：唐诗三百首</h3>
          <p>儿童教育</p>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="music-controls">
            <svg class="icon control-btn" viewBox="0 0 24 24">
              <polygon points="19 20 9 12 19 4 19 20"></polygon>
              <line x1="5" y1="19" x2="5" y2="5"></line>
            </svg>
            <svg class="icon control-btn" viewBox="0 0 24 24">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
            </svg>
            <svg class="icon control-btn" viewBox="0 0 24 24">
              <polygon points="5 4 15 12 5 20 5 4"></polygon>
              <line x1="19" y1="5" x2="19" y2="19"></line>
            </svg>
          </div>
        </div>

        <!-- 待办卡片 4x2 -->
        <div class="card todo-card">
          <h3>
            <svg class="icon" viewBox="0 0 24 24">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            今日待办
          </h3>
          <p>09:30 客户交流</p>
          <p>11:00 项目汇报</p>
        </div>
      </div>

      <!-- VPA数字人 -->
      <div class="vpa-avatar" @click="showWelcome = true">
        <span>VPA助手</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'App',
  setup() {
    const showWelcome = ref(true)
    const currentStatus = ref('AI HMI 智能座舱')
    const isGenerating = ref(false)
    const wallpaperUrl = ref('')

    // 生成壁纸的函数
    const generateWallpaper = async (prompt = '科技感的汽车座舱背景，深蓝色渐变，未来主义风格') => {
      try {
        const taskId = 'wallpaper_' + Date.now()
        console.log('开始生成壁纸，提示词:', prompt)
        const response = await fetch('http://localhost:8002/api/v1/kolors/text-to-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: prompt,
            task_id: taskId
          })
        })

        if (response.ok) {
          const data = await response.json()
          if (data.image_url) {
            wallpaperUrl.value = data.image_url
            console.log('壁纸生成成功:', data.image_url)
            return data.image_url
          }
        } else {
          console.error('壁纸生成失败:', response.status, response.statusText)
          const errorText = await response.text()
          console.error('错误详情:', errorText)
        }
      } catch (error) {
        console.error('调用壁纸生成接口失败:', error)
      }
      return null
    }

    const generateDesktop = async (type) => {
      isGenerating.value = true
      currentStatus.value = '正在为您生成桌面...'

      // 根据场景类型生成对应的壁纸
      let wallpaperPrompt = ''
      switch(type) {
        case 'commute':
          wallpaperPrompt = '通勤场景的汽车座舱背景，清晨阳光，城市道路，温暖色调'
          break
        case 'navigation':
          wallpaperPrompt = '导航场景的汽车座舱背景，GPS地图风格，蓝色科技感'
          break
        case 'alone':
          wallpaperPrompt = '独处场景的汽车座舱背景，宁静夜晚，星空，深蓝紫色调'
          break
        case 'gaming':
          wallpaperPrompt = '游戏场景的汽车座舱背景，霓虹灯效果，赛博朋克风格，紫色和粉色'
          break
        case 'relax':
          wallpaperPrompt = '放松场景的汽车座舱背景，自然风光，绿色植物，柔和光线'
          break
        default:
          wallpaperPrompt = '科技感的汽车座舱背景，深蓝色渐变，未来主义风格'
      }

      // 生成壁纸
      await generateWallpaper(wallpaperPrompt)

      // 模拟AI生成过程
      setTimeout(() => {
        switch(type) {
          case 'commute':
            currentStatus.value = '8:00 前往: XX幼儿园, 预计: 30分到达'
            break
          case 'navigation':
            currentStatus.value = '导航: 前往公司, 预计30分钟到达'
            break
          case 'alone':
            currentStatus.value = '独处模式: 已为您准备安静的桌面'
            break
          case 'gaming':
            currentStatus.value = '游戏模式: 已优化性能设置'
            break
          case 'relax':
            currentStatus.value = '放松模式: 已为您规划一日游路线'
            break
          default:
            currentStatus.value = 'AI HMI 智能座舱'
        }

        isGenerating.value = false
        showWelcome.value = false
      }, 2000)
    }

    // 页面加载时生成默认壁纸
    onMounted(async () => {
      console.log('页面加载，开始生成默认壁纸...')
      await generateWallpaper()
    })

    return {
      showWelcome,
      currentStatus,
      isGenerating,
      wallpaperUrl,
      generateDesktop,
      generateWallpaper
    }
  }
}
</script>

<style>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

/* CSS变量 */
:root {
  --font-family: 'Inter', sans-serif;
  --bg-color: #F2F2F7;
  --card-bg-color: rgba(255, 255, 255, 0.5);
  --primary-text: #000000;
  --secondary-text: #3C3C43;
  --accent-color: #007AFF;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-color: rgba(255, 255, 255, 0.7);
  --card-radius: 24px;
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: var(--bg-color);
}

/* 应用特定样式 */
#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: transparent;
}

/* 桌面容器 */
.desktop-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 沉浸式壁纸背景 */
.wallpaper-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: v-bind('wallpaperUrl ? `url(${wallpaperUrl})` : "url(https://images.unsplash.com/photo-1559251606-c620d2e71b34?q=80&w=1964&auto=format&fit=crop)"');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: background-image 1s ease-in-out;
  z-index: -1;
}

/* 欢迎界面 */
.welcome-screen {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60px;
  position: relative;
}

/* VPA对话框 */
.vpa-dialog {
  max-width: 480px;
  padding: 32px;
  border-radius: 24px;
  color: #333;
}

.dialog-content h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.4;
}

.dialog-content p {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 24px;
  opacity: 0.8;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  text-align: left;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.ai-lab-info {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.4;
}

.ai-lab-info strong {
  font-weight: 600;
}

/* VPA机器人形象 */
.vpa-robot {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 80px;
}

.robot-body {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: robotFloat 3s ease-in-out infinite;
}

.robot-head {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 20px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
}

.robot-eyes {
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
}

.eye {
  width: 16px;
  height: 16px;
  background: #333;
  border-radius: 50%;
  animation: robotBlink 4s infinite;
}

.robot-arms {
  position: absolute;
  top: 80px;
  width: 160px;
  display: flex;
  justify-content: space-between;
}

.arm {
  width: 20px;
  height: 40px;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.robot-legs {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.leg {
  width: 25px;
  height: 50px;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@keyframes robotFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes robotBlink {
  0%, 90%, 100% { transform: scaleY(1); }
  95% { transform: scaleY(0.1); }
}

/* 主桌面界面 */
.main-desktop {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* SVG图标样式 */
.icon {
  width: 1em;
  height: 1em;
  stroke-width: 2;
  stroke: currentColor;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  vertical-align: -0.15em;
}

/* 动态岛 */
.dynamic-island {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--card-bg-color);
  color: var(--primary-text);
  padding: 10px 25px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 16px;
  box-shadow: 0 10px 30px var(--shadow-color);
  backdrop-filter: blur(12px) saturate(1.5);
  border: 1px solid var(--border-color);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 8x4网格容器 */
.grid-container {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 16px;
  padding-top: 60px; /* 为动态岛留出空间 */
}

/* 卡片基础样式 */
.card {
  background: var(--card-bg-color);
  border-radius: var(--card-radius);
  box-shadow: 0 10px 30px var(--shadow-color);
  backdrop-filter: blur(12px) saturate(1.5);
  border: 1px solid var(--border-color);
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  color: var(--primary-text);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px var(--shadow-color);
}

/* 卡片网格布局 */
.weather-card {
  grid-column: 5 / 9;
  grid-row: 1 / 3;
}

.navigation-card {
  grid-column: 5 / 9;
  grid-row: 3 / 5;
}

.music-card {
  grid-column: 1 / 5;
  grid-row: 1 / 3;
}

.todo-card {
  grid-column: 1 / 5;
  grid-row: 3 / 5;
}

/* 卡片内容样式 */
.card h3 {
  margin: 0 0 5px 0;
  font-weight: 700;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card p {
  margin: 0;
  color: var(--secondary-text);
  font-weight: 400;
  font-size: 0.9rem;
}

/* 天气卡片特定样式 */
.temperature {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-top: 10px !important;
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
  color: var(--primary-text) !important;
}

.weather-icon {
  width: 1.5em !important;
  height: 1.5em !important;
  color: var(--accent-color) !important;
}

.description {
  margin-top: auto !important;
}

/* 音乐卡片特定样式 */
.music-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: auto;
  font-size: 1.5rem;
  color: var(--secondary-text);
}

.control-btn {
  width: 1.5em;
  height: 1.5em;
  cursor: pointer;
  transition: color 0.3s ease;
}

.control-btn:hover {
  color: var(--accent-color);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  margin-top: 15px;
  overflow: hidden;
}

.progress-fill {
  width: 30%;
  height: 100%;
  background: var(--accent-color);
  border-radius: 2px;
}

.weather-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0;
}

.temperature {
  font-size: 32px;
  font-weight: bold;
}

.weather-icon {
  font-size: 24px;
}

.weather-detail {
  font-size: 12px;
  opacity: 0.8;
}

.nav-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.route-info {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.next-turn {
  font-size: 14px;
  opacity: 0.8;
}

.music-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.song-title {
  font-size: 12px;
  margin-bottom: 8px;
}

.music-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  font-size: 16px;
}

.todo-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.todo-item {
  font-size: 11px;
  opacity: 0.9;
  padding: 2px 0;
}

/* VPA助手 */
.vpa-avatar {
  position: absolute;
  bottom: 30px;
  left: 40px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  color: white;
  box-shadow: 0 10px 25px rgba(0, 122, 255, 0.4);
  border: 4px solid rgba(255,255,255,0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
}

.vpa-avatar:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 122, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(8, 1fr);
  }

  .weather-card {
    grid-column: 1 / 5;
    grid-row: 1 / 3;
  }

  .navigation-card {
    grid-column: 1 / 5;
    grid-row: 3 / 5;
  }

  .music-card {
    grid-column: 1 / 5;
    grid-row: 5 / 7;
  }

  .todo-card {
    grid-column: 1 / 5;
    grid-row: 7 / 9;
  }

  .vpa-avatar {
    bottom: 20px;
    left: 20px;
    width: 80px;
    height: 80px;
    font-size: 0.8rem;
  }
}
</style>
