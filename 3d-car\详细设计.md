# 车辆3D风阻流线可视化功能详细设计

## 1. 功能概述

本文档详细描述如何在三维车辆展示系统中实现风阻流线可视化效果，模拟小米SU7官网展示风阻系数（Cd 0.195）时的视觉表现。该功能将在车辆模型周围生成动态流动的空气流线，以直观展示空气动力学特性。

## 2. 技术方案

### 2.1 数据结构设计

为存储预设的流线路径点，我们设计以下数据结构：

```typescript
// 定义单条流线的类型
type AirflowLine = {
  points: THREE.Vector3[];  // 路径点数组
  curve?: THREE.CatmullRomCurve3; // 由点生成的曲线
  mesh?: THREE.Line;        // 最终渲染的线条对象
  material?: THREE.Material; // 线条材质
  initialOffset?: number;   // 初始偏移量，用于错开动画
  speed?: number;           // 流动速度，可随机化增加自然感
};

// 存储所有流线的数组
type AirflowLines = AirflowLine[];
```

### 2.2 流线生成方法

我们将采用预设路径点的方式，定义车辆周围的流线路径：

```typescript
/**
 * 生成示例预设路径点
 * 在实际应用中，这些点应根据具体车型进行调整
 */
function generatePresetPathPoints(carModel: THREE.Object3D): AirflowLines {
  const airflowLines: AirflowLines = [];
  const carBoundingBox = new THREE.Box3().setFromObject(carModel);
  const carLength = carBoundingBox.max.z - carBoundingBox.min.z;
  const carWidth = carBoundingBox.max.x - carBoundingBox.min.x;
  const carHeight = carBoundingBox.max.y - carBoundingBox.min.y;
  
  // 车辆中心点
  const carCenter = new THREE.Vector3();
  carBoundingBox.getCenter(carCenter);
  
  // 定义起始位置在车头前方
  const startZ = carBoundingBox.min.z - carLength * 0.2;
  
  // 生成多条流线
  for (let i = 0; i < 20; i++) {
    // 水平分布的参数
    const horizontalOffset = (i % 10) / 10 * carWidth - carWidth / 2;
    // 垂直分布的参数
    const verticalOffset = Math.floor(i / 10) / 2 * carHeight;
    
    // 为每条流线创建路径点
    const points: THREE.Vector3[] = [];
    
    // 创建一系列从前到后的点，模拟气流路径
    for (let j = 0; j < 15; j++) {
      const progress = j / 14; // 0到1的进度值
      
      // 基本路径是从前到后的直线
      const x = carCenter.x + horizontalOffset * (1 + progress * 0.5); // 稍微向外扩散
      
      // 垂直路径根据位置调整，模拟气流绕过车身
      let y = carCenter.y + verticalOffset;
      
      // 中部点稍微抬高，模拟气流绕过车顶
      if (progress > 0.2 && progress < 0.7) {
        const bump = Math.sin(Math.PI * (progress - 0.2) / 0.5) * carHeight * 0.15;
        y += bump;
      }
      
      // z坐标从前到后
      const z = startZ + progress * (carLength * 1.3); // 延伸到车尾后方
      
      points.push(new THREE.Vector3(x, y, z));
    }
    
    // 添加到流线数组
    airflowLines.push({
      points,
      initialOffset: Math.random(), // 随机初始偏移
      speed: 0.5 + Math.random() * 0.5, // 随机速度
    });
  }
  
  return airflowLines;
}
```

### 2.3 视觉效果实现

我们将使用`LineDashedMaterial`结合后期处理的辉光效果（Bloom）来实现流线的发光效果：

```typescript
/**
 * 创建流线视觉效果
 */
function createAirflowVisuals(
  airflowLines: AirflowLines,
  scene: THREE.Scene,
  renderer: THREE.WebGLRenderer,
  camera: THREE.Camera
): void {
  // 创建后期处理器
  const composer = new EffectComposer(renderer);
  composer.addPass(new RenderPass(scene, camera));
  
  // 添加辉光效果
  const bloomPass = new UnrealBloomPass(
    new THREE.Vector2(window.innerWidth, window.innerHeight),
    1.5,   // 强度
    0.4,   // 半径
    0.85   // 阈值
  );
  composer.addPass(bloomPass);
  
  // 为每条流线创建曲线和可视化对象
  airflowLines.forEach((line) => {
    // 创建Catmull-Rom曲线，确保平滑过渡
    line.curve = new THREE.CatmullRomCurve3(line.points);
    
    // 基于曲线创建几何体
    const geometry = new THREE.BufferGeometry().setFromPoints(
      line.curve.getPoints(100) // 生成100个点以确保平滑
    );
    
    // 创建虚线材质，用于动画效果
    line.material = new THREE.LineDashedMaterial({
      color: 0x00ffff,      // 青色
      linewidth: 1,         // 线宽
      scale: 1,             // 比例
      dashSize: 3,          // 虚线段长度
      gapSize: 1,           // 间隙长度
      opacity: 0.8,         // 透明度
      transparent: true,    // 启用透明
      emissive: 0x00ffff,   // 自发光颜色
      emissiveIntensity: 0.8, // 发光强度
    });
    
    // 创建线条对象
    line.mesh = new THREE.Line(geometry, line.material);
    line.mesh.computeLineDistances(); // 计算线段距离，虚线材质需要这个
    
    // 添加到场景
    scene.add(line.mesh);
  });
  
  // 返回composer供动画循环使用
  return composer;
}
```

### 2.4 动态效果实现

我们将通过修改`LineDashedMaterial`的`dashOffset`属性来实现流动效果：

```typescript
/**
 * 更新流线动画
 */
function updateAirflowAnimation(
  airflowLines: AirflowLines,
  deltaTime: number
): void {
  airflowLines.forEach((line) => {
    if (line.material && line.mesh && line.mesh.visible) {
      // 修改dashOffset创建流动效果
      const material = line.material as THREE.LineDashedMaterial;
      material.dashOffset -= deltaTime * (line.speed || 1.0);
      
      // 当偏移值太大时重置，以创建循环动画
      if (material.dashOffset < -100) {
        material.dashOffset = 0;
      }
    }
  });
}
```

## 3. 功能控制接口

### 3.1 初始化函数

```typescript
/**
 * 初始化空气流线
 */
function setupAirflowLines(
  carModel: THREE.Object3D,
  scene: THREE.Scene,
  renderer: THREE.WebGLRenderer,
  camera: THREE.Camera
): {
  airflowLines: AirflowLines;
  composer: EffectComposer;
} {
  // 生成预设路径点
  const airflowLines = generatePresetPathPoints(carModel);
  
  // 创建流线视觉效果
  const composer = createAirflowVisuals(airflowLines, scene, renderer, camera);
  
  return { airflowLines, composer };
}
```

### 3.2 可见性控制函数

```typescript
/**
 * 控制流线可见性
 */
function toggleAirflowVisibility(
  airflowLines: AirflowLines,
  visible: boolean
): void {
  airflowLines.forEach((line) => {
    if (line.mesh) {
      line.mesh.visible = visible;
    }
  });
}
```

### 3.3 销毁函数

```typescript
/**
 * 清理流线资源
 */
function disposeAirflowLines(
  airflowLines: AirflowLines,
  scene: THREE.Scene
): void {
  airflowLines.forEach((line) => {
    if (line.mesh) {
      // 从场景中移除
      scene.remove(line.mesh);
      
      // 释放几何体和材质资源
      if (line.mesh.geometry) {
        line.mesh.geometry.dispose();
      }
      if (line.material) {
        line.material.dispose();
      }
    }
  });
}
```

## 4. 与Vue 3组合式API集成

以下是在Vue 3组件中集成此功能的示例代码：

```typescript
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';

// 导入上面定义的函数
import {
  setupAirflowLines,
  toggleAirflowVisibility,
  updateAirflowAnimation,
  disposeAirflowLines,
  AirflowLines
} from './airflowVisualizer';

export default {
  setup() {
    // 引用和状态
    const canvasRef = ref<HTMLCanvasElement | null>(null);
    const showAirflow = ref(false);
    
    // Three.js 对象
    let scene: THREE.Scene;
    let camera: THREE.PerspectiveCamera;
    let renderer: THREE.WebGLRenderer;
    let controls: OrbitControls;
    let carModel: THREE.Object3D;
    let composer: EffectComposer;
    
    // 流线相关对象
    let airflowLines: AirflowLines;
    
    // 初始化Three.js场景
    const initThreeJS = () => {
      // 创建场景
      scene = new THREE.Scene();
      
      // 创建相机
      camera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
      );
      camera.position.set(5, 3, 5);
      
      // 创建渲染器
      renderer = new THREE.WebGLRenderer({
        canvas: canvasRef.value!,
        antialias: true,
        alpha: true
      });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setPixelRatio(window.devicePixelRatio);
      
      // 创建轨道控制器
      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      
      // 加载车辆模型（示例）
      const loader = new THREE.GLTFLoader();
      loader.load('/models/car.glb', (gltf) => {
        carModel = gltf.scene;
        scene.add(carModel);
        
        // 加载模型后初始化流线
        const { airflowLines: lines, composer: comp } = setupAirflowLines(
          carModel,
          scene,
          renderer,
          camera
        );
        
        airflowLines = lines;
        composer = comp;
        
        // 默认隐藏流线
        toggleAirflowVisibility(airflowLines, showAirflow.value);
      });
      
      // 添加灯光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);
      
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
      directionalLight.position.set(5, 5, 5);
      scene.add(directionalLight);
      
      // 开始动画循环
      animate();
    };
    
    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);
      
      const deltaTime = 0.016; // 约60帧每秒
      
      // 更新控制器
      controls.update();
      
      // 更新流线动画（如果已初始化且可见）
      if (airflowLines && showAirflow.value) {
        updateAirflowAnimation(airflowLines, deltaTime);
      }
      
      // 使用后期处理器渲染（如果已初始化）
      if (composer && showAirflow.value) {
        composer.render();
      } else {
        // 否则使用标准渲染
        renderer.render(scene, camera);
      }
    };
    
    // 监听流线可见性变化
    watch(showAirflow, (newValue) => {
      if (airflowLines) {
        toggleAirflowVisibility(airflowLines, newValue);
      }
    });
    
    // 处理窗口大小变化
    const onWindowResize = () => {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
        
        if (composer) {
          composer.setSize(window.innerWidth, window.innerHeight);
        }
      }
    };
    
    onMounted(() => {
      initThreeJS();
      window.addEventListener('resize', onWindowResize);
    });
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', onWindowResize);
      
      // 清理资源
      if (airflowLines && scene) {
        disposeAirflowLines(airflowLines, scene);
      }
    });
    
    return {
      canvasRef,
      showAirflow,
    };
  },
  template: `
    <div class="car-viewer">
      <canvas ref="canvasRef"></canvas>
      <div class="controls">
        <button @click="showAirflow = !showAirflow">
          {{ showAirflow ? '隐藏风阻流线' : '显示风阻流线' }}
        </button>
      </div>
    </div>
  `
};
```

## 5. 技术方案说明

### 5.1 材质选择

我们选择`LineDashedMaterial`作为流线材质，原因如下：
- 支持虚线效果，可通过修改`dashOffset`轻松实现流动动画
- 结合后期处理的辉光效果，可以实现发光视觉效果
- 比自定义着色器实现简单，但效果依然很好

### 5.2 动画方法

流动效果通过以下方式实现：
1. 为每条流线设置不同的初始`dashOffset`和流动速度，增加自然感
2. 在动画循环中修改`dashOffset`值实现流动效果
3. 使用后期处理的辉光效果（UnrealBloomPass）增强发光效果

### 5.3 性能优化

- 当流线不可见时，跳过动画更新，节省计算资源
- 流线数量和点数可根据实际需求和性能调整
- 可视性设置为false时，避免使用后期处理渲染，减少GPU开销

### 5.4 未来优化方向

- 可以考虑使用WebGL着色器实现更精细的粒子流动效果
- 可以根据车速动态调整流线速度，增强交互感
- 可以探索THREE.InstancedBufferGeometry优化多条流线的渲染性能

## 6. 用户界面设计

### 6.1 风阻系数展示按钮

参照小米SU7官网的风阻系数展示风格，我们设计一个科技感强的风阻系数展示按钮：

```typescript
// 风阻系数显示组件
const AirflowCoefficientButton = defineComponent({
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const toggleAirflow = () => {
      emit('update:modelValue', !props.modelValue);
    };

    return () => (
      <div 
        class={`airflow-coefficient-button ${props.modelValue ? 'active' : ''}`}
        onClick={toggleAirflow}
      >
        <div class="coefficient-title">出色的超低风阻系数</div>
        <div class="coefficient-value">Cd 0.195</div>
        <div class="coefficient-description">
          {props.modelValue ? 
            '风阻系数越低，空气动力学性能越好。点击隐藏风阻流线。' : 
            '风，就是最好的设计师。点击查看风阻流线。'
          }
        </div>
      </div>
    );
  }
});
```

```css
/* 风阻系数按钮样式 */
.airflow-coefficient-button {
  position: absolute;
  top: 20%;
  right: 5%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  color: #f0f0f0;
  padding: 20px;
  width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 100;
}

.airflow-coefficient-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.1);
}

.airflow-coefficient-button.active {
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.2);
}

.coefficient-title {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.8;
}

.coefficient-value {
  font-size: 40px;
  font-weight: 300;
  margin-bottom: 15px;
  background: linear-gradient(90deg, #e0c3a1 0%, #d4a980 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: 'Arial', sans-serif;
}

.coefficient-description {
  font-size: 12px;
  line-height: 1.5;
  opacity: 0.7;
}

/* 当按钮激活时，添加呼吸光效果 */
@keyframes glow {
  0% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.2); }
  50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }
  100% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.2); }
}

.airflow-coefficient-button.active {
  animation: glow 3s infinite;
}
```

### 6.2 在Vue组件中集成按钮

修改之前的Vue组件，集成风阻系数按钮：

```typescript
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { AirflowCoefficientButton } from './AirflowCoefficientButton';

// 导入上面定义的函数
import {
  setupAirflowLines,
  toggleAirflowVisibility,
  updateAirflowAnimation,
  disposeAirflowLines,
  AirflowLines
} from './airflowVisualizer';

export default {
  components: {
    AirflowCoefficientButton
  },
  setup() {
    // 引用和状态
    const canvasRef = ref<HTMLCanvasElement | null>(null);
    const showAirflow = ref(false);
    
    // Three.js 对象
    let scene: THREE.Scene;
    let camera: THREE.PerspectiveCamera;
    let renderer: THREE.WebGLRenderer;
    let controls: OrbitControls;
    let carModel: THREE.Object3D;
    let composer: EffectComposer;
    
    // 流线相关对象
    let airflowLines: AirflowLines;
    
    // 初始化Three.js场景
    const initThreeJS = () => {
      // ... 保持原有的初始化代码不变 ...
    };
    
    // 动画循环
    const animate = () => {
      // ... 保持原有的动画代码不变 ...
    };
    
    // ... 保持其他原有代码不变 ...
    
    return {
      canvasRef,
      showAirflow,
    };
  },
  template: `
    <div class="car-viewer">
      <canvas ref="canvasRef"></canvas>
      
      <!-- 风阻系数按钮 -->
      <AirflowCoefficientButton 
        v-model="showAirflow"
      />
    </div>
  `
};
```

### 6.3 按钮交互效果

按钮交互效果设计：

1. **初始状态**：按钮显示"Cd 0.195"风阻系数，背景半透明暗色，按钮文本提示"点击查看风阻流线"

2. **悬停状态**：按钮微微放大，阴影加深，提升按钮的可点击感

3. **激活状态**：
   - 按钮边缘显示青色高亮边框
   - 添加呼吸光效果，使按钮边缘有轻微的发光动画
   - 按钮文本变为"点击隐藏风阻流线"

4. **点击效果**：
   - 点击时按钮有轻微的按压效果
   - 第一次点击：激活风阻流线显示，按钮切换到激活状态
   - 再次点击：关闭风阻流线显示，按钮恢复到初始状态

5. **响应式设计**：
   - 在小屏幕设备上，按钮尺寸和位置自动调整
   - 按钮在移动设备上会缩小并移到底部

此设计不仅美观且具有科技感，与汽车3D展示的整体风格相符，同时提供清晰的用户反馈和操作指引。

## 7. 实现注意事项与优化建议

### 7.1 流线路径点精度说明

本文档中的`generatePresetPathPoints`函数仅提供了示例实现，生成的路径可能与真实空气流动或参考视频中的精确路径有显著差异。要达到小米SU7官网视频中那种贴合车身轮廓的逼真效果，需注意以下几点：

- **精确的流线路径**：理想情况下，应由专业空气动力学仿真软件（如CFD软件）生成真实的流线数据，或由美术设计师根据车辆几何形状精心设计流线路径。
- **车型特定优化**：文档中示例代码使用简单的数学公式模拟流线，实际项目中应针对特定车型进行优化，确保流线准确贴合车身曲面。
- **流线密度与分布**：高品质效果通常需要在车身关键区域（如前脸、引擎盖、A柱、车顶等）设置更密集的流线，以突出空气动力学特性。
- **数据格式转换**：如果使用专业软件生成流线数据，需要开发适当的转换工具，将其转换为Three.js兼容的格式。

### 7.2 辉光效果(Bloom)的应用范围

当前的实现逻辑中，`composer.render()`仅在`showAirflow.value`为`true`时调用，这意味着辉光效果仅在显示流线时启用。这种实现需要根据项目整体需求考虑以下情况：

#### 辉光效果仅用于流线（当前实现）
```typescript
// 当前实现
if (composer && showAirflow.value) {
  composer.render();
} else {
  renderer.render(scene, camera);
}
```

**优点**：
- 性能更优，不显示流线时避免了后期处理的性能开销
- 界面其他元素不会受辉光效果影响

**缺点**：
- 切换流线显示/隐藏时，整体渲染效果会有明显变化（辉光出现/消失）

#### 辉光效果应用于整个场景（可选方案）
```typescript
// 替代实现
if (composer) {
  composer.render();
} else {
  renderer.render(scene, camera);
}
```

**优点**：
- 一致的视觉效果，即使在切换流线时也不会有渲染风格变化
- 可以为车灯、高光反射等其他场景元素添加辉光效果

**缺点**：
- 持续的性能开销
- 可能需要更复杂的配置来控制哪些对象应用辉光效果

**建议**：根据项目需求和目标平台性能，选择合适的实现方式。如果车辆模型包含需要辉光效果的其他元素（如车灯），建议采用全局辉光方案。

### 7.3 动画deltaTime优化

当前实现中，`animate`函数使用硬编码的deltaTime值（0.016），这在不同刷新率的显示器上可能导致动画速度不一致。为了实现更平滑且帧率无关的动画，建议使用THREE.Clock：

```typescript
// 在initThreeJS中初始化时钟
const clock = new THREE.Clock();

// 在animate函数中使用实际deltaTime
const animate = () => {
  requestAnimationFrame(animate);
  
  // 获取实际帧间隔时间
  const deltaTime = clock.getDelta();
  
  // 更新控制器
  controls.update();
  
  // 更新流线动画（如果已初始化且可见）
  if (airflowLines && showAirflow.value) {
    updateAirflowAnimation(airflowLines, deltaTime);
  }
  
  // 渲染
  if (composer && showAirflow.value) {
    composer.render();
  } else {
    renderer.render(scene, camera);
  }
};
```

这种实现可以确保动画速度在所有设备上保持一致，无论实际帧率如何。

### 7.4 材质和效果参数微调

本文档中提供的UnrealBloomPass和LineDashedMaterial参数仅为起点值，要达到理想的视觉效果，需要根据以下因素进行精细调整：

#### UnrealBloomPass参数调整
```typescript
const bloomPass = new UnrealBloomPass(
  new THREE.Vector2(window.innerWidth, window.innerHeight),
  1.5,   // 强度 - 可调整范围：0.5~2.5
  0.4,   // 半径 - 可调整范围：0.2~1.0
  0.85   // 阈值 - 可调整范围：0.7~0.95
);
```

- **强度(strength)**：控制辉光的亮度，较大值会使辉光更明显，但可能导致过曝
- **半径(radius)**：控制辉光的扩散范围，较大值会使辉光更加柔和和分散
- **阈值(threshold)**：控制哪些像素会产生辉光，较高值意味着只有最亮的像素才会发光

#### LineDashedMaterial参数调整
```typescript
line.material = new THREE.LineDashedMaterial({
  color: 0x00ffff,      // 颜色 - 可尝试不同色调
  linewidth: 1,         // 线宽 - 注：大多数WebGL实现中此值恒为1
  scale: 1,             // 比例 - 可调整范围：0.5~2.0
  dashSize: 3,          // 虚线段长度 - 可调整范围：1~10
  gapSize: 1,           // 间隙长度 - 可调整范围：0.5~5
  opacity: 0.8,         // 透明度 - 可调整范围：0.3~1.0
  transparent: true,    // 启用透明
  emissive: 0x00ffff,   // 自发光颜色 - 应与color匹配或稍浅
  emissiveIntensity: 0.8, // 发光强度 - 可调整范围：0.3~1.5
});
```

调整建议：
1. **流线颜色**：可尝试不同的颜色组合，如青色(0x00ffff)、浅蓝(0x4ff2ff)或白色(0xffffff)
2. **虚线效果**：调整dashSize和gapSize的比例，影响流动效果的视觉感受
3. **透明度与发光**：这两项结合调整，影响流线的可见度和发光强度
4. **流线速度**：在不同的车型或演示场景中，可能需要调整速度以匹配整体节奏

**重要提示**：参数调整应在实际场景中反复测试，根据车辆模型大小、场景光照条件以及整体视觉风格进行微调。

### 7.5 模型加载与初始化时序

在Vue组件集成示例中，流线初始化依赖于车辆模型的成功加载。这一时序关系非常重要，必须确保：

```typescript
// 加载车辆模型（示例）
const loader = new THREE.GLTFLoader();
loader.load('/models/car.glb', (gltf) => {
  carModel = gltf.scene;
  scene.add(carModel);
  
  // 重要：只有在模型完全加载后才初始化流线
  const { airflowLines: lines, composer: comp } = setupAirflowLines(
    carModel,
    scene,
    renderer,
    camera
  );
  
  airflowLines = lines;
  composer = comp;
  
  // 默认隐藏流线
  toggleAirflowVisibility(airflowLines, showAirflow.value);
});
```

**重要注意事项**：
1. 流线生成完全依赖于车辆模型的几何数据，因此必须在模型加载完成后进行
2. 可考虑添加加载状态指示器，提示用户模型和流线正在准备中
3. 大型模型加载可能需要时间，应处理可能的加载错误和超时情况
4. 如果应用中有多个车型可切换，需要在每次切换车型后重新生成流线

### 7.6 资源销毁补充说明

当组件卸载或场景切换时，正确释放资源至关重要。除了已实现的`disposeAirflowLines`函数外，还应考虑：

```typescript
// 扩展资源清理逻辑
onBeforeUnmount(() => {
  window.removeEventListener('resize', onWindowResize);
  
  // 清理流线资源
  if (airflowLines && scene) {
    disposeAirflowLines(airflowLines, scene);
  }
  
  // 清理后期处理相关资源
  if (composer) {
    // 处理所有通道
    composer.passes.forEach(pass => {
      if (pass.dispose) pass.dispose();
    });
  }
  
  // 清理渲染器
  if (renderer) {
    renderer.dispose();
  }
  
  // 清理控制器
  if (controls) {
    controls.dispose();
  }
  
  // 清理场景中的其他对象
  // ...
});
```

**完整清理的好处**：
1. 防止内存泄漏，特别是在单页应用中频繁切换组件时
2. 减少GPU资源占用
3. 确保长时间运行的应用性能稳定

在实际项目中，应根据所使用的Three.js对象和插件，完善资源清理逻辑。
