import { createRouter, create<PERSON>ebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue')
    },
    {
        path: '/doodle',
        name: 'Doodle',
        component: () => import('../views/Doodle.vue')
    },
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue')
    },
    {
        path: '/doodle',
        name: 'Doodle',
        component: () => import('../views/Doodle.vue')
    },
] 