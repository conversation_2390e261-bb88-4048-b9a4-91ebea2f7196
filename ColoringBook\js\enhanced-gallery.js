/**
 * 增强的画廊系统 - 实现吉卜力风格的图片显示和交互
 * 
 * 功能特性：
 * 1. 吉卜力风格画框显示
 * 2. 平滑的图片加载动画
 * 3. 图片加载失败处理和重试机制
 * 4. 响应式画廊布局
 * 5. 图片懒加载和预加载
 * 6. 交互式画框效果
 */

class EnhancedGallery {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            throw new Error(`Gallery container with id "${containerId}" not found`);
        }

        // 配置选项
        this.options = {
            enableLazyLoading: true,
            enablePreloading: true,
            retryAttempts: 3,
            retryDelay: 1000,
            animationDuration: 300,
            placeholderColor: '#f4f1e9',
            errorPlaceholder: '🎨',
            loadingPlaceholder: '✨',
            ...options
        };

        // 状态管理
        this.images = new Map();
        this.loadingImages = new Set();
        this.failedImages = new Set();
        this.observers = new Map();

        // 初始化
        this.init();
    }

    /**
     * 初始化画廊系统
     */
    init() {
        // 设置容器样式
        this.setupContainerStyles();

        // 初始化懒加载观察器
        if (this.options.enableLazyLoading) {
            this.initLazyLoading();
        }

        // 绑定事件监听器
        this.bindEvents();

        console.log('[EnhancedGallery] 画廊系统初始化完成');
    }

    /**
     * 设置容器样式
     */
    setupContainerStyles() {
        // 确保容器有正确的网格布局
        if (!this.container.classList.contains('grid')) {
            this.container.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8';
        }

        // 添加画廊特定样式
        const style = document.createElement('style');
        style.textContent = `
            .enhanced-gallery-frame {
                background-color: #d4c2ad;
                border-radius: 12px;
                padding: 12px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1), inset 0 0 10px rgba(0,0,0,0.1);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                transform: rotate(1deg);
                position: relative;
                overflow: hidden;
            }

            .enhanced-gallery-frame:nth-child(even) {
                transform: rotate(-1deg);
            }

            .enhanced-gallery-frame:hover {
                transform: scale(1.05) rotate(0deg);
                z-index: 10;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15), inset 0 0 15px rgba(0,0,0,0.1);
            }

            .enhanced-gallery-frame.selected {
                box-shadow: 0 0 0 4px #5A7A9E, 0 4px 6px rgba(0,0,0,0.1);
                transform: scale(1.02) rotate(0deg);
            }

            .enhanced-gallery-frame.loading {
                animation: gentle-pulse 2s ease-in-out infinite;
            }

            @keyframes gentle-pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.8; }
            }

            .image-container-enhanced {
                background-color: white;
                border: 1px solid #f4f1e9;
                border-radius: 4px;
                padding: 4px;
                position: relative;
                overflow: hidden;
                aspect-ratio: 1;
            }

            .image-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, #f4f1e9 25%, transparent 25%), 
                           linear-gradient(-45deg, #f4f1e9 25%, transparent 25%), 
                           linear-gradient(45deg, transparent 75%, #f4f1e9 75%), 
                           linear-gradient(-45deg, transparent 75%, #f4f1e9 75%);
                background-size: 20px 20px;
                background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
                color: #c8b8a2;
                font-size: 2rem;
                border-radius: 4px;
            }

            .image-loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(244, 241, 233, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                border-radius: 4px;
                transition: opacity 0.3s ease;
            }

            .loading-spinner-gallery {
                width: 32px;
                height: 32px;
                border: 3px solid #d4c2ad;
                border-top: 3px solid #5B8E7D;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 8px;
            }

            .image-error-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(199, 93, 93, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                border-radius: 4px;
                border: 2px dashed #C75D5D;
            }

            .retry-button {
                background-color: #C75D5D;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 0.75rem;
                cursor: pointer;
                margin-top: 8px;
                transition: all 0.2s ease;
            }

            .retry-button:hover {
                background-color: #b54545;
                transform: translateY(-1px);
            }

            .gallery-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 2px;
                transition: all 0.3s ease;
                opacity: 0;
            }

            .gallery-image.loaded {
                opacity: 1;
            }

            .gallery-image:hover {
                transform: scale(1.02);
            }

            .gallery-controls {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 8px;
                gap: 8px;
            }

            .gallery-btn {
                background-color: #e0d6c7;
                border: 1px solid #c8b8a2;
                color: #5c544b;
                font-weight: 600;
                border-radius: 8px;
                padding: 6px 10px;
                font-size: 0.75rem;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .gallery-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-color: #A3B899;
            }

            .gallery-btn.delete {
                background-color: #C75D5D;
                color: white;
                border-color: #C75D5D;
            }

            .gallery-btn.delete:hover {
                background-color: #b54545;
            }

            /* 响应式设计增强 */
            @media (max-width: 640px) {
                .enhanced-gallery-frame {
                    transform: none !important;
                    margin-bottom: 1rem;
                }
                
                .enhanced-gallery-frame:hover {
                    transform: scale(1.02) !important;
                }
                
                .gallery-controls {
                    flex-wrap: wrap;
                }
                
                .gallery-btn {
                    flex: 1;
                    min-width: 80px;
                    justify-content: center;
                }
            }

            /* 新增画框入场动画 */
            .gallery-frame-enter {
                opacity: 0;
                transform: scale(0.8) rotate(0deg);
                animation: frameEnter 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
            }

            @keyframes frameEnter {
                0% {
                    opacity: 0;
                    transform: scale(0.8) rotate(0deg);
                }
                50% {
                    opacity: 0.7;
                    transform: scale(1.1) rotate(2deg);
                }
                100% {
                    opacity: 1;
                    transform: scale(1) rotate(1deg);
                }
            }

            .gallery-frame-enter:nth-child(even) {
                animation: frameEnterEven 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
            }

            @keyframes frameEnterEven {
                0% {
                    opacity: 0;
                    transform: scale(0.8) rotate(0deg);
                }
                50% {
                    opacity: 0.7;
                    transform: scale(1.1) rotate(-2deg);
                }
                100% {
                    opacity: 1;
                    transform: scale(1) rotate(-1deg);
                }
            }
        `;
        
        if (!document.querySelector('#enhanced-gallery-styles')) {
            style.id = 'enhanced-gallery-styles';
            document.head.appendChild(style);
        }
    }

    /**
     * 初始化懒加载
     */
    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        this.lazyLoadObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px'
            });
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监听选择模式变化
        const selectModeToggle = document.getElementById('select-mode-toggle');
        if (selectModeToggle) {
            selectModeToggle.addEventListener('change', () => {
                this.updateSelectionMode();
            });
        }

        // 监听窗口大小变化
        window.addEventListener('resize', this.debounce(() => {
            this.updateResponsiveLayout();
        }, 250));
    }

    /**
     * 添加图片到画廊
     * @param {Object} imageData - 图片数据
     * @param {string} imageData.url - 图片URL
     * @param {string} imageData.id - 图片ID
     * @param {Object} imageData.metadata - 图片元数据
     */
    addImage(imageData) {
        const { url, id = this.generateId(), metadata = {} } = imageData;
        
        // 创建画框元素
        const frameElement = this.createFrameElement(id, url, metadata);
        
        // 添加到容器
        this.container.appendChild(frameElement);
        
        // 存储图片信息
        this.images.set(id, {
            url,
            metadata,
            element: frameElement,
            loaded: false,
            failed: false
        });

        // 添加入场动画
        frameElement.classList.add('gallery-frame-enter');
        
        // 开始加载图片
        setTimeout(() => {
            this.startImageLoading(id);
        }, 100);

        return id;
    }

    /**
     * 创建画框元素
     * @param {string} id - 图片ID
     * @param {string} url - 图片URL
     * @param {Object} metadata - 元数据
     * @returns {HTMLElement} - 画框元素
     */
    createFrameElement(id, url, metadata) {
        const frameDiv = document.createElement('div');
        frameDiv.className = 'enhanced-gallery-frame';
        frameDiv.dataset.imageId = id;
        
        frameDiv.innerHTML = `
            <div class="image-container-enhanced">
                <div class="image-placeholder">
                    <span>${this.options.loadingPlaceholder}</span>
                </div>
                <div class="image-loading-overlay">
                    <div class="loading-spinner-gallery"></div>
                    <span class="text-xs text-gray-600 font-handwritten">加载中...</span>
                </div>
                <img class="gallery-image" data-src="${url}" alt="生成的图片" loading="lazy">
            </div>
            <div class="gallery-controls">
                <div class="flex gap-2">
                    <button class="gallery-btn download-btn" title="下载">
                        <span>💾</span>
                        <span class="hidden sm:inline">下载</span>
                    </button>
                    <button class="gallery-btn fullscreen-btn" title="全屏查看">
                        <span>🔍</span>
                        <span class="hidden sm:inline">查看</span>
                    </button>
                </div>
                <button class="gallery-btn delete" title="删除">
                    <span>🗑️</span>
                    <span class="hidden sm:inline">删除</span>
                </button>
            </div>
        `;

        // 绑定事件
        this.bindFrameEvents(frameDiv, id);

        return frameDiv;
    }

    /**
     * 绑定画框事件
     * @param {HTMLElement} frameElement - 画框元素
     * @param {string} imageId - 图片ID
     */
    bindFrameEvents(frameElement, imageId) {
        // 点击选择事件
        frameElement.addEventListener('click', (e) => {
            if (e.target.closest('.gallery-btn')) return;
            
            const selectModeToggle = document.getElementById('select-mode-toggle');
            if (selectModeToggle && selectModeToggle.checked) {
                frameElement.classList.toggle('selected');
                this.updatePrintButtonState();
            }
        });

        // 下载按钮
        const downloadBtn = frameElement.querySelector('.download-btn');
        downloadBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.downloadImage(imageId);
        });

        // 全屏查看按钮
        const fullscreenBtn = frameElement.querySelector('.fullscreen-btn');
        fullscreenBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showFullscreen(imageId);
        });

        // 删除按钮
        const deleteBtn = frameElement.querySelector('.delete');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteImage(imageId);
        });
    }

    /**
     * 开始加载图片
     * @param {string} imageId - 图片ID
     */
    startImageLoading(imageId) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo || this.loadingImages.has(imageId)) return;

        this.loadingImages.add(imageId);
        const img = imageInfo.element.querySelector('.gallery-image');
        
        if (this.options.enableLazyLoading && this.lazyLoadObserver) {
            this.lazyLoadObserver.observe(img);
        } else {
            this.loadImage(img);
        }
    }

    /**
     * 加载图片
     * @param {HTMLImageElement} img - 图片元素
     */
    async loadImage(img) {
        const frameElement = img.closest('.enhanced-gallery-frame');
        const imageId = frameElement.dataset.imageId;
        const imageInfo = this.images.get(imageId);
        
        if (!imageInfo) return;

        const loadingOverlay = frameElement.querySelector('.image-loading-overlay');
        const placeholder = frameElement.querySelector('.image-placeholder');
        
        try {
            // 显示加载状态
            frameElement.classList.add('loading');
            loadingOverlay.style.display = 'flex';
            
            // 预加载图片
            await this.preloadImage(imageInfo.url);
            
            // 设置图片源
            img.src = imageInfo.url;
            
            // 等待图片加载完成
            await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
                
                // 超时处理
                setTimeout(() => reject(new Error('Image load timeout')), 10000);
            });

            // 加载成功
            this.handleImageLoadSuccess(imageId, img, loadingOverlay, placeholder);
            
        } catch (error) {
            console.error(`图片加载失败 [${imageId}]:`, error);
            this.handleImageLoadError(imageId, error, loadingOverlay);
        } finally {
            frameElement.classList.remove('loading');
            this.loadingImages.delete(imageId);
        }
    }

    /**
     * 预加载图片
     * @param {string} url - 图片URL
     * @returns {Promise} - 预加载Promise
     */
    preloadImage(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = resolve;
            img.onerror = reject;
            img.src = url;
        });
    }

    /**
     * 处理图片加载成功
     * @param {string} imageId - 图片ID
     * @param {HTMLImageElement} img - 图片元素
     * @param {HTMLElement} loadingOverlay - 加载遮罩
     * @param {HTMLElement} placeholder - 占位符
     */
    handleImageLoadSuccess(imageId, img, loadingOverlay, placeholder) {
        const imageInfo = this.images.get(imageId);
        if (imageInfo) {
            imageInfo.loaded = true;
            imageInfo.failed = false;
        }

        // 隐藏加载状态
        loadingOverlay.style.opacity = '0';
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, this.options.animationDuration);

        // 隐藏占位符
        placeholder.style.opacity = '0';
        setTimeout(() => {
            placeholder.style.display = 'none';
        }, this.options.animationDuration);

        // 显示图片
        img.classList.add('loaded');
        
        // 移除失败状态
        this.failedImages.delete(imageId);
        
        console.log(`[EnhancedGallery] 图片加载成功: ${imageId}`);
    }

    /**
     * 处理图片加载错误
     * @param {string} imageId - 图片ID
     * @param {Error} error - 错误信息
     * @param {HTMLElement} loadingOverlay - 加载遮罩
     */
    handleImageLoadError(imageId, error, loadingOverlay) {
        const imageInfo = this.images.get(imageId);
        if (imageInfo) {
            imageInfo.failed = true;
        }

        this.failedImages.add(imageId);
        
        // 隐藏加载遮罩
        loadingOverlay.style.display = 'none';
        
        // 显示错误状态
        this.showImageError(imageId, error.message);
        
        console.error(`[EnhancedGallery] 图片加载失败: ${imageId}`, error);
    }

    /**
     * 显示图片错误状态
     * @param {string} imageId - 图片ID
     * @param {string} errorMessage - 错误消息
     */
    showImageError(imageId, errorMessage) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo) return;

        const frameElement = imageInfo.element;
        const container = frameElement.querySelector('.image-container-enhanced');
        
        // 移除现有的错误遮罩
        const existingError = container.querySelector('.image-error-overlay');
        if (existingError) {
            existingError.remove();
        }

        // 创建错误遮罩
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'image-error-overlay';
        errorOverlay.innerHTML = `
            <div class="text-2xl mb-2">${this.options.errorPlaceholder}</div>
            <div class="text-xs text-center text-red-600 mb-2">加载失败</div>
            <button class="retry-button">重试</button>
        `;

        // 绑定重试事件
        const retryBtn = errorOverlay.querySelector('.retry-button');
        retryBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.retryImageLoad(imageId);
        });

        container.appendChild(errorOverlay);
    }

    /**
     * 重试图片加载
     * @param {string} imageId - 图片ID
     */
    async retryImageLoad(imageId) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo) return;

        const frameElement = imageInfo.element;
        const errorOverlay = frameElement.querySelector('.image-error-overlay');
        const img = frameElement.querySelector('.gallery-image');
        
        // 移除错误状态
        if (errorOverlay) {
            errorOverlay.remove();
        }
        
        this.failedImages.delete(imageId);
        
        // 重新开始加载
        this.startImageLoading(imageId);
    }

    /**
     * 下载图片
     * @param {string} imageId - 图片ID
     */
    async downloadImage(imageId) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo || !imageInfo.loaded) {
            this.showMessage('图片尚未加载完成', 'warning');
            return;
        }

        try {
            const response = await fetch(imageInfo.url);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `coloring-book-${imageId}-${Date.now()}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            window.URL.revokeObjectURL(url);
            
            this.showMessage('图片下载成功！', 'success');
        } catch (error) {
            console.error('下载失败:', error);
            this.showMessage('图片下载失败，请重试', 'error');
        }
    }

    /**
     * 全屏显示图片
     * @param {string} imageId - 图片ID
     */
    showFullscreen(imageId) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo || !imageInfo.loaded) {
            this.showMessage('图片尚未加载完成', 'warning');
            return;
        }

        // 创建全屏遮罩
        const overlay = document.createElement('div');
        overlay.className = 'fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 cursor-pointer';
        overlay.innerHTML = `
            <div class="relative max-w-full max-h-full p-4">
                <img src="${imageInfo.url}" alt="全屏查看" class="max-w-full max-h-full object-contain">
                <button class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300 transition-colors">&times;</button>
            </div>
        `;

        // 绑定关闭事件
        overlay.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });

        const closeBtn = overlay.querySelector('button');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            document.body.removeChild(overlay);
        });

        // 键盘事件
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(overlay);
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);

        document.body.appendChild(overlay);
    }

    /**
     * 删除图片
     * @param {string} imageId - 图片ID
     */
    deleteImage(imageId) {
        const imageInfo = this.images.get(imageId);
        if (!imageInfo) return;

        if (confirm('确定要删除这张图片吗？')) {
            // 添加删除动画
            const frameElement = imageInfo.element;
            frameElement.style.transition = 'all 0.3s ease';
            frameElement.style.transform = 'scale(0.8) rotate(0deg)';
            frameElement.style.opacity = '0';

            setTimeout(() => {
                if (frameElement.parentNode) {
                    frameElement.parentNode.removeChild(frameElement);
                }
                this.images.delete(imageId);
                this.loadingImages.delete(imageId);
                this.failedImages.delete(imageId);
                this.updatePrintButtonState();
            }, 300);
        }
    }

    /**
     * 更新选择模式
     */
    updateSelectionMode() {
        const selectModeToggle = document.getElementById('select-mode-toggle');
        const isSelectMode = selectModeToggle && selectModeToggle.checked;
        
        if (!isSelectMode) {
            // 清除所有选择
            this.container.querySelectorAll('.enhanced-gallery-frame.selected')
                .forEach(frame => frame.classList.remove('selected'));
        }
        
        this.updatePrintButtonState();
    }

    /**
     * 更新打印按钮状态
     */
    updatePrintButtonState() {
        const printBtn = document.getElementById('print-btn');
        const selectModeToggle = document.getElementById('select-mode-toggle');
        const selectedFrames = this.container.querySelectorAll('.enhanced-gallery-frame.selected');
        
        if (printBtn && selectModeToggle) {
            if (selectModeToggle.checked && selectedFrames.length > 0) {
                printBtn.disabled = false;
                printBtn.style.opacity = '1';
            } else {
                printBtn.disabled = true;
                printBtn.style.opacity = '0.5';
            }
        }
    }

    /**
     * 更新响应式布局
     */
    updateResponsiveLayout() {
        // 根据屏幕大小调整网格列数
        const width = window.innerWidth;
        let columns;
        
        if (width < 640) {
            columns = 1;
        } else if (width < 1024) {
            columns = 2;
        } else {
            columns = 3;
        }
        
        this.container.className = `grid grid-cols-${columns} gap-8`;
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 这里可以集成现有的消息系统
        console.log(`[EnhancedGallery] ${type.toUpperCase()}: ${message}`);
    }

    /**
     * 生成唯一ID
     * @returns {string} - 唯一ID
     */
    generateId() {
        return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} - 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 获取画廊统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        return {
            total: this.images.size,
            loaded: Array.from(this.images.values()).filter(img => img.loaded).length,
            failed: this.failedImages.size,
            loading: this.loadingImages.size
        };
    }

    /**
     * 清空画廊
     */
    clear() {
        this.container.innerHTML = '';
        this.images.clear();
        this.loadingImages.clear();
        this.failedImages.clear();
    }

    /**
     * 销毁画廊
     */
    destroy() {
        if (this.lazyLoadObserver) {
            this.lazyLoadObserver.disconnect();
        }
        
        this.clear();
        
        // 移除样式
        const styles = document.querySelector('#enhanced-gallery-styles');
        if (styles) {
            styles.remove();
        }
    }
}

// 导出增强画廊类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedGallery;
} else if (typeof window !== 'undefined') {
    window.EnhancedGallery = EnhancedGallery;
}