# 小米 SU7 网站特效解析 demo

## 项目概述

这是一个复刻小米SU7汽车官网的3D交互特效演示项目。项目采用现代化的3D Web技术，通过Three.js和其扩展库kokomi.js实现了流畅、沉浸式的3D汽车展示体验。

demo 地址：https://su7-replica.netlify.app/
demo（芙宁娜版）地址：https://su7-replica.netlify.app/#furina
原网站参考：https://gamemcu.com/su7/

## 核心技术

- **Three.js**：WebGL 3D渲染库，是项目的核心引擎
- **kokomi.js**：基于Three.js的扩展库，简化了复杂的3D场景构建
- **GSAP**：用于创建流畅的动画和过渡效果
- **Vite**：现代化前端构建工具，提供快速的开发体验
- **TypeScript**：为JavaScript添加类型系统，提高代码质量和开发效率
- **glTF/GLB**：轻量级3D模型格式，用于加载汽车模型
- **Shader**：自定义的GLSL着色器，用于创建高级视觉效果

## 项目特点

1. **高性能3D渲染**：优化的3D模型和渲染技术，确保在各种设备上流畅运行
2. **交互式汽车模型**：支持鼠标拖拽旋转、缩放查看3D汽车模型
3. **动态环境映射**：实现了真实感的环境光照和反射效果
4. **流畅的过渡动画**：使用GSAP实现流畅的相机动画和场景过渡
5. **沉浸式粒子效果**：模拟风阻和环境氛围的粒子系统
6. **后期处理效果**：包括辉光、调色等视觉增强效果
7. **响应式设计**：自适应不同屏幕尺寸的展示效果
8. **芙宁娜版本**：包含特殊的角色模型和动画效果
9. **车身换色功能**：支持实时更换车身颜色，带有平滑过渡效果

## 项目结构

- `src/`：源代码目录
  - `Experience/`：3D体验核心代码
    - `World/`：包含所有3D场景元素
    - `Shaders/`：着色器代码
    - `Utils/`：工具函数
  - `main.ts`：项目入口文件
  - `style.css`：全局样式

## 主要功能模块

1. **Experience.ts**：整个3D体验的核心控制器
2. **World.ts**：管理所有场景元素，包括汽车模型、环境等
3. **Car.ts**：汽车模型加载和控制
4. **DynamicEnv.ts**：动态环境映射效果
5. **StartRoom.ts**：初始展示场景
6. **Speedup.ts**：加速特效
7. **CameraShake.ts**：相机抖动效果
8. **Furina.ts**：特殊版本的角色展示
9. **ColorPicker.ts**：车身颜色选择器

## 技术实现详解

### Three.js 核心技术

项目基于Three.js实现3D渲染，主要使用了以下核心技术：

1. **Scene & Camera**：场景和摄像机设置
   ```typescript
   // 使用透视相机
   const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 100);
   camera.position.set(0, 0.8, -11);
   
   // 创建场景
   const scene = new THREE.Scene();
   scene.background = new THREE.Color('black');
   ```

2. **材质与光照**：
   ```typescript
   // 使用物理材质
   const bodyMat = new THREE.MeshStandardMaterial({
     color: new THREE.Color('#26d6e9'),
     roughness: 0.2,
     metalness: 0.8,
     envMapIntensity: 5
   });
   ```

3. **后期处理**：
   ```typescript
   // 使用EffectComposer实现后期处理
   const composer = new EffectComposer(renderer);
   const bloomPass = new UnrealBloomPass(
     new THREE.Vector2(window.innerWidth, window.innerHeight),
     1.5, 0.4, 0.85
   );
   ```

### 贴图实现技术

项目中的贴图系统使用了多种纹理技术：

1. **资源加载管理**：
   ```typescript
   // 使用AssetManager统一管理贴图资源
   this.am = new kokomi.AssetManager(this, resources, {
     useMeshoptDecoder: true
   });
   ```

2. **不同类型贴图的处理**：
   ```typescript
   // 环境光遮蔽贴图(AO Map)
   aoMap.flipY = false;
   aoMap.colorSpace = THREE.LinearSRGBColorSpace;
   aoMap.minFilter = THREE.NearestFilter;
   aoMap.magFilter = THREE.NearestFilter;
   aoMap.channel = 1;
   
   // 法线贴图(Normal Map)
   normalMap.colorSpace = THREE.LinearSRGBColorSpace;
   normalMap.wrapS = normalMap.wrapT = THREE.RepeatWrapping;
   
   // 光照贴图(Light Map)
   lightMap.channel = 1;
   lightMap.flipY = false;
   lightMap.colorSpace = THREE.SRGBColorSpace;
   ```

3. **环境贴图技术**：
   ```typescript
   // 创建动态环境贴图
   const { fbo, camera } = useCubeCamera({ resolution: 512 });
   fbo.texture.type = THREE.UnsignedByteType;
   fbo.texture.generateMipmaps = false;
   scene.environment = fbo.texture;
   ```

4. **反射与折射效果**：
   ```typescript
   // 使用CustomShaderMaterial实现反射效果
   const floorCsmMat = new CustomShaderMaterial({
     baseMaterial: floorMat,
     uniforms: floorUniforms,
     vertexShader: floorVertex,
     fragmentShader: floorFrag,
     silent: true,
   });
   ```

### UV展开与贴图应用

模型的UV展开是将3D模型表面展开成2D平面的过程，就像将地球仪表面展开成世界地图那样。项目中的UV展开与贴图系统具有以下特点：

1. **模型的UV展开**：
   - sm_car.gltf模型使用专业的UV展开布局，将车身各部分合理安排在纹理空间内
   - 实现了高效的纹理空间利用，最大化贴图清晰度
   - 采用多张UV贴图结合的方式，各个部件使用独立的贴图

2. **贴图应用技术**：
   - 基础色彩贴图（Base Color Map）：提供模型的基本颜色信息
   - 法线贴图（Normal Map）：增强模型表面细节，如车漆、车门把手等
   - 金属度/粗糙度贴图（Metallic/Roughness Map）：控制车漆反射的效果
   - 环境光遮蔽贴图（Ambient Occlusion Map）：增强阴影效果

3. **动态贴图应用**：
   - 项目中的`decal.png`是一种可动态切换的赛车贴图
   - 通过`car.applySkin('decal')`方法在运行时切换不同的车身外观
   - 这种动态贴图变更不需要重新加载模型，性能更高

4. **定制贴图制作**：
   - 项目包含`create_uv_template.py`工具，可以生成模型的UV展开模板
   - 基于这个模板，可以在Photoshop等设计软件中制作自定义贴图
   - 生成的模板支持三种格式：带颜色标识、纯轮廓和轮廓加文字

### FBX模型与GLTF模型对比

除了主要的sm_car.gltf模型外，项目中还包含了Driving.fbx角色模型，两种格式的区别与应用：

1. **格式区别**：
   - GLTF/GLB：为Web优化的轻量级3D模型格式，使用JSON构造，支持内嵌贴图
   - FBX：传统3D设计格式，支持复杂动画和结构，通常需要转换才能在Web上使用

2. **在项目中的应用**：
   - sm_car.gltf：使用为主要汽车模型，加载速度快、渲染效率高
   - Driving.fbx：用于特殊版本的角色动画，支持更复杂的骨骼动画

## UV映射原理与车身贴图指南

在3D模型中应用贴图是通过UV映射实现的，这是一种将3D模型表面展开到2D平面的技术。对于汽车模型的贴图应用，理解UV映射的工作原理非常重要。

### UV映射基本原理

想象我们需要给一个纸盒子贴上图案。我们有两种选择：

1. **直接在3D盒子上绘制** - 这种方式难以精确控制和设计复杂图案
2. **将盒子展开成平面，绘制图案后再折回去** - 这就是UV映射的基本原理

在3D模型中：
- **U坐标**：对应2D贴图上的水平方向（类似X轴）
- **V坐标**：对应2D贴图上的垂直方向（类似Y轴）
- 每个坐标范围从0到1，表示贴图的相对位置

#### 3D曲面到2D平面的映射变形示意图

以下点阵图展示了一个弯曲的3D表面如何映射到2D平面：

```
       3D曲面(汽车引擎盖)                 2D平面(UV贴图)
    .-'--.-'--.-'--.-'-.           +-----------+
  ,'    /    /    /    /`.         |           |
 /     /    /    /    /   \        |           |
+-----+----+----+----+----+        |           |
|\    |    |    |    |    |        |           |
| \   |    |    |    |    |   ⟹   |           |
|  \  |    |    |    |    |        |           |
|   \ |    |    |    |    |        |           |
|    \|    |    |    |    |        |           |
+-----+----+----+----+----+        +-----------+
```

注意观察：
- 3D曲面上的点阵格子不是均匀的，有些区域挤压，有些区域拉伸
- 映射到2D平面后，点阵变成规则均匀的网格
- 贴图应用时，2D平面上的图案会随着映射关系再次变形以适应3D曲面

当我们在2D平面上绘制贴图时，这种变形关系是反向的：

```
    规则的2D贴图设计                应用到3D曲面后
  +-----+-----+-----+-----+      .---.---.---.---.
  |     |     |     |     |    ,'    /    /    / `.
  |  A  |  B  |  C  |  D  |   /     /    /    /    \
  |     |     |     |     |  +-----+----+----+-----+
  +-----+-----+-----+-----+  |\    |    |    |     |
  |     |     |     |     |  | \   |    |    |     |
  |  E  |  F  |  G  |  H  |  |  \  |    |    |     |
  |     |     |     |     |  |   \ |    |    |     |
  +-----+-----+-----+-----+  |    \| 变 | 形 |     |
                              +-----+----+----+-----+
```

这就解释了为什么在弯曲的车身表面上，贴图会出现不均匀的拉伸或压缩。

```
    UV贴图坐标系             3D模型表面
    +----------+              +------+
    |(0,0) (1,0)|             |      |
    |    ^      |    映射     |  3D  |
    |    |      | ---------> |  汽车 |
    |    v      |             |  模型 |
    |(0,1) (1,1)|             |      |
    +----------+              +------+
```

### 为什么车身贴图会有重复和对称区域？

小米SU7模型的UV映射有一个重要特点：**左右车门共用同一个UV区域**。这种设计有以下优点：

1. **节省贴图空间**：减小贴图文件大小，提高加载速度
2. **保持左右对称**：确保车身两侧图案保持完美对称
3. **简化贴图设计**：只需设计一侧，自动应用到另一侧

这种共享UV区域的设计原理如下图所示：

```
    贴图平面上的一个区域            应用到3D模型上
    +-----------------+            +-------+-------+
    |                 |            |       |       |
    |  车门贴图区域   |    映射    | 左车门 | 右车门 |
    |                 | ---------> |       |       |
    |                 |            |       |       |
    +-----------------+            +-------+-------+
```

### UV映射中的扭曲现象

将弯曲的3D表面展平到2D贴图上不可避免会产生扭曲，就像将橙子皮压平会产生褶皱。在汽车模型中：

1. **弯曲区域**：引擎盖、车顶等区域因表面曲率大，贴图应用后容易产生扭曲
2. **平坦区域**：车门侧面等较平的区域扭曲较小

### 设计汽车贴图的技巧

1. **预补偿扭曲**：
   - 在高度弯曲区域，设计时预先做反向变形补偿
   - 使用`visualization/distortion_visualization.png`查看变形区域

2. **利用对称性**：
   - 设计左右对称的图案，确保在两侧看起来协调
   - 使用`visualization/symmetry_visualization.png`查看对称映射区域

3. **边界处理**：
   - 在UV图边缘保留出血区域(bleed area)，防止像素采样错误
   - 关键图案避免放在UV接缝处，以免出现不连续现象

4. **测试与调整**：
   - 使用`create_test_skin.py`生成测试图案，验证UV映射效果
   - 根据实际效果逐步调整贴图设计

### 项目中的UV工具说明

项目提供了以下UV相关工具：

1. **UV模板生成器**：
   - `create_uv_template_advanced.py`：生成带标记的高精度UV模板
   - `create_precise_uv_mapper.py`：生成精确的UV映射参考

2. **贴图测试工具**：
   - `create_test_skin.py`：创建带网格和标记的测试贴图

3. **可视化工具**：
   - `uv_visualizer.py`：分析并显示UV映射的扭曲和对称区域
   - 生成的可视化图保存在`public/texture/visualization/`目录

使用这些工具可以帮助设计师更精确地创建汽车贴图，减少试错成本。

## 3D模型详解

### 汽车模型（sm_car.gltf）

项目的核心元素是小米SU7汽车的3D模型，使用glTF格式存储，具有以下特点：

1. **模型结构**：
   - 汽车模型由多个部件组成，包括车身、车窗、车轮、车标等
   - 所有几何数据存储在`sm_car_data.bin`二进制文件中（约2MB）
   - 模型采用层次化结构，便于单独控制车轮旋转等动画效果

2. **材质系统**：
   - **Car_body**：车身主材质，默认为蓝绿色（#26d6e9），光滑度高、金属感强
   - **M_body_smoothblack**：车身黑色部分材质
   - **M_ChePai**：车牌专用材质，使用特定贴图
   - **Car_window**：车窗材质，半透明效果
   - **M_wheel**：车轮材质，结合多种贴图实现细节

3. **贴图系统**：
   - 使用多个`.webp`格式贴图（sm_car_img0.webp至sm_car_img8.webp）
   - 贴图用途包括：颜色贴图、法线贴图、金属度/粗糙度贴图等
   - 采用压缩格式减小文件体积，提高加载速度

### UV展开与贴图应用

UV展开是将3D模型表面展开成平面的过程，就像将地球仪表面展开成世界地图那样。项目中的UV展开与贴图系统具有以下特点：

1. **模型的UV展开**：
   - sm_car.gltf模型使用专业的UV展开布局，将车身各部分合理安排在纹理空间内
   - 实现了高效的纹理空间利用，最大化贴图清晰度
   - 采用多张UV贴图结合的方式，各个部件使用独立的贴图

2. **贴图应用技术**：
   - 基础色彩贴图（Base Color Map）：提供模型的基本颜色信息
   - 法线贴图（Normal Map）：增强模型表面细节，如车漆、车门把手等
   - 金属度/粗糙度贴图（Metallic/Roughness Map）：控制车漆反射的效果
   - 环境光遮蔽贴图（Ambient Occlusion Map）：增强阴影效果

3. **动态贴图应用**：
   - 项目中的`decal.png`是一种可动态切换的赛车贴图
   - 通过`car.applySkin('decal')`方法在运行时切换不同的车身外观
   - 这种动态贴图变更不需要重新加载模型，性能更高

4. **定制贴图制作**：
   - 项目包含`create_uv_template.py`工具，可以生成模型的UV展开模板
   - 基于这个模板，可以在Photoshop等设计软件中制作自定义贴图
   - 生成的模板支持三种格式：带颜色标识、纯轮廓和轮廓加文字

### FBX模型与GLTF模型对比

除了主要的sm_car.gltf模型外，项目中还包含了Driving.fbx角色模型，两种格式的区别与应用：

1. **格式区别**：
   - GLTF/GLB：为Web优化的轻量级3D模型格式，使用JSON构造，支持内嵌贴图
   - FBX：传统3D设计格式，支持复杂动画和结构，通常需要转换才能在Web上使用

2. **在项目中的应用**：
   - sm_car.gltf：使用为主要汽车模型，加载速度快、渲染效率高
   - Driving.fbx：用于特殊版本的角色动画，支持更复杂的骨骼动画

## 食用方法

安装依赖

```sh
npm i
```

本地调试

```sh
npm run dev
```

构建

```sh
npm run build
```

预览

```sh
npm run preview
```

## 贡献与反馈

欢迎通过Issues或PR方式提供反馈和改进建议。

## 许可

本项目仅用于学习和研究目的，请勿用于商业用途。
