#!/usr/bin/env python
"""
提示词增强智能体
基于AutoGen框架实现的文生图提示词增强智能体，使用标准MCP工具集成
"""

import os
import logging
import asyncio
import uuid
from typing import Optional, List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入AutoGen相关
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import SseServerParams, mcp_server_tools
from autogen_core import CancellationToken

def get_model_client() -> OpenAIChatCompletionClient:
    """获取配置好的模型客户端"""
    return OpenAIChatCompletionClient(
        model="Qwen2.5-32B-Instruct-AWQ",  
        api_key="1",
        base_url="http://27.159.93.61:8007/v1/",
        model_capabilities={
            "json_output": True,
            "vision": False,
            "function_calling": True,
        },
    )

class PromptEnhancerAgent:
    """文生图提示词增强智能体"""
    
    def __init__(self, mcp_url: str = "http://127.0.0.1:19220"):
        """
        初始化提示词增强智能体
        
        Args:
            mcp_url: MCP服务URL
        """
        self.mcp_url = mcp_url
        self.mcp_tools = None
        self.assistant = None
        logger.info(f"初始化提示词增强智能体，MCP服务: {mcp_url}")
    
    async def init_mcp_tools(self) -> List:
        """
        初始化MCP工具
        
        Returns:
            list: MCP工具列表
        """
        try:
            # 确保MCP URL正确
            mcp_url = self.mcp_url
            if not mcp_url.endswith('/api/v1/sse'):
                if mcp_url.endswith('/'):
                    mcp_url += 'api/v1/sse'
                else:
                    mcp_url += '/api/v1/sse'
            
            logger.info(f"连接到MCP服务: {mcp_url}")
            
            # 构建连接参数
            server_params = SseServerParams(
                url=mcp_url,
                headers={"Content-Type": "application/json", "Accept": "text/event-stream"},
                timeout=30.0
            )
            
            # 获取MCP工具
            logger.info("正在获取MCP工具...")
            tools = await mcp_server_tools(server_params)
            logger.info(f"成功获取MCP工具，共 {len(tools)} 个工具")
            
            # 记录每个获取到的工具
            for i, tool in enumerate(tools):
                tool_name = getattr(tool, "name", "未知工具")
                tool_desc = getattr(tool, "description", "无描述")
                logger.info(f"工具 {i+1}: {tool_name} - {tool_desc}")
            
            self.mcp_tools = tools
            return self.mcp_tools
        except Exception as e:
            logger.error(f"初始化MCP工具失败: {str(e)}")
            logger.exception("详细异常信息:")
            raise
    
    async def create_assistant(self):
        """创建提示词增强助手代理"""
        # 确保MCP工具已初始化
        if not self.mcp_tools:
            await self.init_mcp_tools()
        
        # 创建模型客户端
        model_client = get_model_client()
        
        # 文生图提示词增强系统提示
        system_message = """你是一个专业的图像提示词增强专家，擅长将用户简单的描述转化为详细、富有创意且高质量的文生图提示词。

请按照以下方法增强用户的提示词：

1. 分析与理解：
   - 理解用户描述的核心场景、主题和意图
   - 识别关键元素和潜在的视觉效果

2. 详细扩展内容：
   - 主体元素：详细描述主要人物、物体、场景、环境和背景
   - 艺术风格：指定艺术流派、渲染方式、美术技巧或参考艺术家
   - 视觉效果：照明、色彩方案、色调、饱和度、对比度
   - 技术参数：视角、构图、景深、分辨率、细节程度
   - 氛围与情感：情绪基调、故事性元素、意境表达

3. 结构化输出：
   - 正面提示词(Positive Prompts)：需要包含和强调的元素
   - 负面提示词(Negative Prompts)：需要避免的元素
   - 权重标记：使用适当的权重标记如(word:1.2)强调重要元素

输出的提示词应当结构清晰、细节丰富，能够指导AI生成高质量的图像，并且适合直接复制使用。尽量避免矛盾的描述，保持整体的风格和主题一致性。
"""
        
        # 创建助手代理
        self.assistant = AssistantAgent(
            name="PromptEnhancer",
            system_message=system_message,
            model_client=model_client,
            tools=self.mcp_tools,
            reflect_on_tool_use=True
        )
        
        return self.assistant
    
    async def enhance_prompt(self, prompt: str) -> str:
        """
        增强用户提示词
        
        Args:
            prompt: 用户原始提示词
            
        Returns:
            str: 增强后的提示词
        """
        session_id = f"session-{uuid.uuid4()}"
        logger.info(f"开始增强提示词，会话ID: {session_id}")
        
        try:
            # 确保助手已创建
            if not self.assistant:
                await self.create_assistant()
            
            # 准备任务内容
            task = f"请将以下简单描述转换为详细的文生图提示词：\n\n{prompt}"
            
            # 使用助手处理任务
            response = await self.assistant.on_messages(
                [TextMessage(content=task, source="user")], 
                CancellationToken()
            )
            
            # 返回助手的响应
            enhanced_prompt = response.chat_message.content
            logger.info(f"提示词增强完成")
            
            return enhanced_prompt
        except Exception as e:
            logger.error(f"提示词增强失败: {str(e)}")
            raise
    
    async def enhance_prompt_stream(self, prompt: str) -> None:
        """
        流式增强用户提示词
        
        Args:
            prompt: 用户原始提示词
        """
        session_id = f"session-{uuid.uuid4()}"
        logger.info(f"开始流式增强提示词，会话ID: {session_id}")
        
        try:
            # 确保助手已创建
            if not self.assistant:
                await self.create_assistant()
            
            # 准备任务内容
            task = f"请将以下简单描述转换为详细的文生图提示词：\n\n{prompt}"
            
            # 使用消息流式处理
            async for chunk in self.assistant.on_messages_stream(
                [TextMessage(content=task, source="user")],
                CancellationToken()
            ):
                # 可以在这里处理每个消息块
                pass
                
            logger.info(f"流式提示词增强完成")
        except Exception as e:
            logger.error(f"流式提示词增强失败: {str(e)}")
            raise

async def main():
    """主函数，用于测试"""
    # MCP服务URL，使用本地MCP服务
    mcp_url = os.environ.get("MCP_URL", "http://127.0.0.1:19220")
    
    # 创建提示词增强代理
    enhancer = PromptEnhancerAgent(mcp_url=mcp_url)
    
    # 测试用提示词
    prompt = "一个未来城市的夜景"
    
    # 初始化MCP工具
    await enhancer.init_mcp_tools()
    
    # 创建助手
    await enhancer.create_assistant()
    
    # 测试非流式增强
    logger.info("测试非流式增强提示词...")
    enhanced_prompt = await enhancer.enhance_prompt(prompt)
    logger.info(f"增强后的提示词: {enhanced_prompt}")
    
    logger.info("测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 