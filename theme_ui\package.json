{"name": "theme_ui", "version": "1.0.0", "description": "主题生成系统UI", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "postinstall": "node scripts/copy-assets.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fontsource/inter": "^5.2.5", "axios": "^1.8.1", "chart.js": "^4.4.8", "clsx": "^2.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "howler": "^2.2.4", "markdown-it": "^14.1.0", "pinia": "^3.0.1", "primevue": "^4.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.21.1", "remixicon": "^4.6.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "fs-extra": "^11.2.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "vite": "^6.2.0", "vue-tsc": "^2.0.6"}}