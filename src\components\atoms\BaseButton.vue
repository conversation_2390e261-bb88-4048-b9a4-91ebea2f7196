<template>
  <button
    :class="buttonClasses"
    :disabled="disabled"
    :type="type"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
  >
    <BaseIcon 
      v-if="icon && iconPosition === 'left'" 
      :name="icon" 
      :size="iconSize"
      class="button-icon button-icon-left"
    />
    
    <span v-if="$slots.default" class="button-text">
      <slot />
    </span>
    
    <BaseIcon 
      v-if="icon && iconPosition === 'right'" 
      :name="icon" 
      :size="iconSize"
      class="button-icon button-icon-right"
    />
    
    <div v-if="loading" class="button-loading">
      <div class="loading-spinner"></div>
    </div>
  </button>
</template>

<script>
import { computed, ref } from 'vue'
import BaseIcon from './BaseIcon.vue'

export default {
  name: 'BaseButton',
  components: {
    BaseIcon
  },
  props: {
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'ghost', 'glass', 'danger'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    icon: {
      type: String,
      default: null
    },
    iconPosition: {
      type: String,
      default: 'left',
      validator: (value) => ['left', 'right'].includes(value)
    },
    iconSize: {
      type: String,
      default: '16'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'button',
      validator: (value) => ['button', 'submit', 'reset'].includes(value)
    },
    fullWidth: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const isPressed = ref(false)

    const buttonClasses = computed(() => [
      'base-button',
      `base-button--${props.variant}`,
      `base-button--${props.size}`,
      {
        'base-button--disabled': props.disabled,
        'base-button--loading': props.loading,
        'base-button--full-width': props.fullWidth,
        'base-button--icon-only': props.icon && !props.$slots?.default,
        'base-button--pressed': isPressed.value
      }
    ])

    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }

    const handleMouseDown = () => {
      isPressed.value = true
    }

    const handleMouseUp = () => {
      isPressed.value = false
    }

    const handleTouchStart = () => {
      isPressed.value = true
    }

    const handleTouchEnd = () => {
      isPressed.value = false
    }

    return {
      buttonClasses,
      handleClick,
      handleMouseDown,
      handleMouseUp,
      handleTouchStart,
      handleTouchEnd
    }
  }
}
</script>

<style scoped>
.base-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
}

/* === 尺寸变体 === */
.base-button--small {
  height: 32px;
  padding: 0 var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-width: 64px;
}

.base-button--medium {
  height: 44px;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-md);
  min-width: 88px;
}

.base-button--large {
  height: 56px;
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-width: 112px;
}

/* === 样式变体 === */
.base-button--primary {
  background: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
  box-shadow: var(--shadow-small);
}

.base-button--primary:hover {
  background: color-mix(in srgb, var(--color-primary) 90%, black);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.base-button--secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(var(--blur-light));
}

.base-button--secondary:hover {
  background: var(--bg-primary);
  border-color: var(--color-primary);
}

.base-button--ghost {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid transparent;
}

.base-button--ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(var(--blur-light));
}

.base-button--glass {
  background: rgba(255, 255, 255, var(--transparency-medium));
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  backdrop-filter: 
    blur(var(--blur-medium)) 
    saturate(var(--saturate-medium)) 
    brightness(var(--brightness-medium));
  box-shadow: 
    var(--shadow-small),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.base-button--glass:hover {
  background: rgba(255, 255, 255, calc(var(--transparency-medium) + 0.1));
  backdrop-filter: 
    blur(calc(var(--blur-medium) + 2px)) 
    saturate(calc(var(--saturate-medium) + 0.2));
  transform: translateY(-1px);
}

.base-button--danger {
  background: var(--color-error);
  color: white;
  border: 1px solid var(--color-error);
}

.base-button--danger:hover {
  background: color-mix(in srgb, var(--color-error) 90%, black);
}

/* === 状态样式 === */
.base-button--pressed {
  transform: scale(0.98);
}

.base-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.base-button--loading {
  cursor: wait;
  pointer-events: none;
}

.base-button--loading .button-text,
.base-button--loading .button-icon {
  opacity: 0;
}

.base-button--full-width {
  width: 100%;
}

.base-button--icon-only {
  min-width: auto;
  aspect-ratio: 1;
}

/* === 图标样式 === */
.button-icon {
  flex-shrink: 0;
  transition: transform var(--transition-fast);
}

.button-icon-left {
  margin-right: calc(var(--spacing-sm) / 2);
}

.button-icon-right {
  margin-left: calc(var(--spacing-sm) / 2);
}

/* === 加载动画 === */
.button-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === 焦点样式 === */
.base-button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  .base-button--glass {
    background: rgba(28, 28, 30, var(--transparency-medium));
  }
  
  .base-button--glass:hover {
    background: rgba(28, 28, 30, calc(var(--transparency-medium) + 0.1));
  }
}
</style>
