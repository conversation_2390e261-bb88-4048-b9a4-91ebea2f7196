/* main.css */
/* 导入 Tailwind 基础组件 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --secondary-color: #14b8a6;
  --secondary-hover: #0d9488;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --dark-bg: #0f172a;
  --darker-bg: #0b1120;
  --panel-bg: rgba(30, 41, 59, 0.7);
  --gray-color: #9e9e9e;
  --gray-hover: #757575;
  --light-bg: #f5f5f5;
  --border-color: #ddd;
  --text-color: #333;
  --text-light: #666;
}

/* 基础元素样式 */
body {
  min-height: 100vh;
  background-color: var(--dark-bg);
  color: #e2e8f0;
  font-family: 'Inter', sans-serif;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

/* 自定义效果类 */
@layer components {
  /* 玻璃拟态效果 */
  .glass {
    @apply bg-panel-bg backdrop-blur-md border border-white/10;
  }
  
  .glass-darker {
    @apply bg-darker-bg/80 backdrop-blur-md border border-white/5;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
  }
  
  /* 科技感背景 */
  .tech-bg {
    @apply relative overflow-hidden;
    background: radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.15) 0%, rgba(15, 23, 42, 0) 50%);
  }
  
  .tech-bg::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    z-index: -1;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  /* 卡片样式 */
  .card {
    @apply glass rounded-lg p-4 shadow-lg;
  }
  
  /* 美化按钮 */
  .btn {
    @apply px-4 py-2 rounded-md transition-all duration-300 font-medium;
  }
  
  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-white;
  }
  
  .btn-secondary {
    @apply bg-secondary hover:bg-secondary/80 text-white;
  }
  
  .btn-outline {
    @apply border border-white/20 hover:bg-white/10 backdrop-blur-sm;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    @apply px-2;
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
}

a {
  color: var(--secondary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
} 