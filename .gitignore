theme_ui/node_modules
theme_ui/.env
theme_ui/.env.local
theme_ui/.env.development
theme_ui/.env.production
theme_ui/.env.test
theme_backend/public/
theme_backend/app/api/__pycache__/
theme_backend/app/__pycache__/
theme_backend/app/core/__pycache__/
theme_backend/changan_theme.tar
base_backend/__pycache__
base_backend/app
base_backend/__pycache__/
agents/anegt
agents/agent/prompt_enhancer/venv
agents/agent/prompt_enhancer/venv/Lib/site-packages/pywin32.pth
agents/agent/prompt_enhancer/__pycache__
agents/tools/stream/__pycache__
agents/tools/stream_sdk/venv
agents/tools/stream_sdk/__pycache__
agents/tools/stream_sdk/__pycache__/
theme_backend/agent/__pycache__
node_modules/@types/three/build/three.tsl.d.ts
.windsurfrules
theme_backend/__pycache__/
theme_backend/agent/__pycache__/
3d-car/node_modules
theme_ui/dist
.venv
ColoringBook/TASK_3_IMPLEMENTATION_SUMMARY.md
ColoringBook/TASK_4_IMPLEMENTATION_SUMMARY.md
ColoringBook/TASK_5_IMPLEMENTATION_SUMMARY.md
ColoringBook/test_enhanced_gallery.html
ColoringBook/test_storage_manager.html
ColoringBook/test_task_optimization.html
test_api.py
test_complete_flow.html
test_connection.html
test_direct.py
test_error_handling.html
test_frontend_simulation.html
test_gallery_integration.html
test_simple_coloring_book.py
test_task_management.html
test_api.py
test_direct.py
test_api.py
test_direct.py
test_
TASK_
theme_backend/TASK_6_IMPLEMENTATION_SUMMARY.md
COLORING_BOOK_FIX_SUMMARY.md
coloring_book.log
connection_test_results.txt
test_api.py
test_direct.py
specs
