from fastapi import APIRouter, UploadFile, File, Form, HTTPException
import json
import traceback
from .common import logger, load_workflow, upload_image, send_prompt, wait_for_image

router = APIRouter()
server_name="image_to_image_server"

def build_image_to_image_workflow(prompt: str, task_id: str) -> dict:
    """构建图生图工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("magic_camera.json")

        # 设置提示词
        if "17" not in workflow:
            logger.error("工作流中找不到节点17")
            raise HTTPException(status_code=500, detail="Node 17 not found in workflow")

        workflow["17"]["inputs"]["image"] = prompt
        logger.info(f"设置图片: {prompt}")

        # 设置保存路径
        if "63" not in workflow:
            logger.error("工作流中找不到节点63")
            raise HTTPException(status_code=500, detail="Node 63 not found in workflow")

        return workflow, "63"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
@router.post("/magic-camera", summary="图片转换为图片", description="根据上传的图片生成新的图片")
async def image_to_image(file: UploadFile = File(...),
                         task_id: str = Form(...)):
    try:
        logger.info(f"\n========== 开始转换图片 ==========")
        logger.info(f"文件名: {file.filename}")
        logger.info(f"任务ID: {task_id}")
        # 上传文件到远程服务器
        uploaded_filename = await upload_image(server_name, file.file, "待转换图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")

        # 构建工作流
        workflow, output_node_id = build_image_to_image_workflow(
            uploaded_filename,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的图片URL: {image_url}")

        return {
            "prompt_id": data["prompt_id"],
            "image_url": image_url
        }
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_image接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
