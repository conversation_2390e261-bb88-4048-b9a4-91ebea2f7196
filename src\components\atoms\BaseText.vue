<template>
  <component
    :is="tag"
    :class="textClasses"
    :style="textStyles"
  >
    <slot />
  </component>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'BaseText',
  props: {
    tag: {
      type: String,
      default: 'span',
      validator: (value) => ['span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div'].includes(value)
    },
    variant: {
      type: String,
      default: 'body',
      validator: (value) => [
        'display', 'title', 'headline', 'subheading', 
        'body', 'caption', 'overline', 'button'
      ].includes(value)
    },
    size: {
      type: String,
      default: null,
      validator: (value) => !value || ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'].includes(value)
    },
    weight: {
      type: String,
      default: null,
      validator: (value) => !value || ['light', 'normal', 'medium', 'semibold', 'bold'].includes(value)
    },
    color: {
      type: String,
      default: null,
      validator: (value) => !value || [
        'primary', 'secondary', 'tertiary', 'inverse',
        'success', 'warning', 'error', 'accent'
      ].includes(value)
    },
    align: {
      type: String,
      default: null,
      validator: (value) => !value || ['left', 'center', 'right', 'justify'].includes(value)
    },
    truncate: {
      type: Boolean,
      default: false
    },
    uppercase: {
      type: Boolean,
      default: false
    },
    italic: {
      type: Boolean,
      default: false
    },
    underline: {
      type: Boolean,
      default: false
    },
    gradient: {
      type: Boolean,
      default: false
    },
    glow: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const textClasses = computed(() => [
      'base-text',
      `base-text--${props.variant}`,
      {
        [`base-text--size-${props.size}`]: props.size,
        [`base-text--weight-${props.weight}`]: props.weight,
        [`base-text--color-${props.color}`]: props.color,
        [`base-text--align-${props.align}`]: props.align,
        'base-text--truncate': props.truncate,
        'base-text--uppercase': props.uppercase,
        'base-text--italic': props.italic,
        'base-text--underline': props.underline,
        'base-text--gradient': props.gradient,
        'base-text--glow': props.glow
      }
    ])

    const textStyles = computed(() => ({}))

    return {
      textClasses,
      textStyles
    }
  }
}
</script>

<style scoped>
.base-text {
  font-family: var(--font-family-primary);
  line-height: 1.5;
  transition: all var(--transition-fast);
  margin: 0;
}

/* === 变体样式 === */
.base-text--display {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.base-text--title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.base-text--headline {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
}

.base-text--subheading {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  letter-spacing: 0.01em;
}

.base-text--body {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
}

.base-text--caption {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: 1.4;
  letter-spacing: 0.01em;
}

.base-text--overline {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1.3;
  letter-spacing: 0.08em;
  text-transform: uppercase;
}

.base-text--button {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  line-height: 1.2;
  letter-spacing: 0.02em;
}

/* === 尺寸覆盖 === */
.base-text--size-xs { font-size: var(--font-size-xs); }
.base-text--size-sm { font-size: var(--font-size-sm); }
.base-text--size-md { font-size: var(--font-size-md); }
.base-text--size-lg { font-size: var(--font-size-lg); }
.base-text--size-xl { font-size: var(--font-size-xl); }
.base-text--size-2xl { font-size: var(--font-size-2xl); }
.base-text--size-3xl { font-size: var(--font-size-3xl); }

/* === 字重覆盖 === */
.base-text--weight-light { font-weight: var(--font-weight-light); }
.base-text--weight-normal { font-weight: var(--font-weight-normal); }
.base-text--weight-medium { font-weight: var(--font-weight-medium); }
.base-text--weight-semibold { font-weight: var(--font-weight-semibold); }
.base-text--weight-bold { font-weight: var(--font-weight-bold); }

/* === 颜色变体 === */
.base-text--color-primary { color: var(--text-primary); }
.base-text--color-secondary { color: var(--text-secondary); }
.base-text--color-tertiary { color: var(--text-tertiary); }
.base-text--color-inverse { color: var(--text-inverse); }
.base-text--color-success { color: var(--color-success); }
.base-text--color-warning { color: var(--color-warning); }
.base-text--color-error { color: var(--color-error); }
.base-text--color-accent { color: var(--color-accent); }

/* === 对齐方式 === */
.base-text--align-left { text-align: left; }
.base-text--align-center { text-align: center; }
.base-text--align-right { text-align: right; }
.base-text--align-justify { text-align: justify; }

/* === 修饰符 === */
.base-text--truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.base-text--uppercase {
  text-transform: uppercase;
}

.base-text--italic {
  font-style: italic;
}

.base-text--underline {
  text-decoration: underline;
  text-underline-offset: 2px;
}

/* === 特殊效果 === */
.base-text--gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.base-text--glow {
  text-shadow: 
    0 0 10px currentColor,
    0 0 20px currentColor,
    0 0 30px currentColor;
}

/* === 响应式调整 === */
@media (max-width: 768px) {
  .base-text--display {
    font-size: var(--font-size-2xl);
  }
  
  .base-text--title {
    font-size: var(--font-size-xl);
  }
  
  .base-text--headline {
    font-size: var(--font-size-lg);
  }
}

/* === 深色主题适配 === */
@media (prefers-color-scheme: dark) {
  .base-text--gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  .base-text {
    text-shadow: none;
  }
  
  .base-text--glow {
    text-shadow: none;
    font-weight: var(--font-weight-bold);
  }
}

/* === 减少动画模式 === */
@media (prefers-reduced-motion: reduce) {
  .base-text {
    transition: none;
  }
}
</style>
