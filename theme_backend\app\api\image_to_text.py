from fastapi import APIRouter, UploadFile, File, Form, HTTPException
import json
import traceback
from .common import logger,load_workflow, upload_image, send_prompt, wait_for_text

router = APIRouter()
server_name="image_to_text_server"

def build_text_to_image_workflow(prompt: str, task_id: str) -> dict:
    """构建图生文工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("image_to_text.json")

        # 设置提示词
        if "3" not in workflow:
            logger.error("工作流中找不到节点3")
            raise HTTPException(status_code=500, detail="Node 3 not found in workflow")

        workflow["3"]["inputs"]["image"] = prompt
        logger.info(f"设置图片: {prompt}")

        # 设置保存路径
        if "4" not in workflow:
            logger.error("工作流中找不到节点4")
            raise HTTPException(status_code=500, detail="Node 4 not found in workflow")

        return workflow, "4"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
@router.post("/image-to-text", summary="文件提取文本", description="根据上传的文件提取文本")
async def image_to_text(file: UploadFile = File(...),
                        task_id: str = Form(...)):
    try:
        logger.info(f"\n========== 开始提取文本 ==========")
        logger.info(f"文件名: {file.filename}")
        logger.info(f"任务ID: {task_id}")
        # 上传文件到远程服务器
        uploaded_filename = await upload_image(server_name, file.file, "待识别图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")

        # 调用远程HTTP接口提取文本
        # 构建工作流
        workflow, output_node_id = build_text_to_image_workflow(
            uploaded_filename,
            task_id
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        text = await wait_for_text(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的文本: {text}")

        return {
            "prompt_id": data["prompt_id"],
            "text": text
        }
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_text接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
