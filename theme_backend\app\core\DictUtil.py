"""
图标名称映射工具
用于建立风格化图标和线稿图标的对应关系
"""

def get_icon_mapping():
    """
    获取图标映射字典
    key: 风格化图标名称
    value: 对应的线稿图标名称
    """
    icon_mapping = {
        # 媒体相关
        "icon_0": "com_tinnove_mediacenter",  # 音乐/媒体图标
        "icon_1": "com_autopai_car_dialer",       # 电话图标
        "icon_2": "com_wt_vehiclecenter",     # 汽车图标
        "icon_3": "com_changan_appmarket",    # 锁/安全图标
        #"icon_4": "icon_4",  # 手势/暂无
        #"icon_5": "icon_5",    # 菱形场景图标/暂无
        "icon_6": "com_wt_airconditioner",    # 空调/风扇图标
        "icon_7": "com_autopai_album",    # 导航/地图图标
        "icon_8": "com_incall_apps_personalcenter",        # 头像/个人中心图标
        "icon_9": "com_wtcl_filemanager",     # 文件管理图标
        "icon_10": "com_wt_gamecenter",       # 游戏手柄图标
        
        # 系统功能
        "icon_11": "com_incall_dvr", # 连接/链接图标
        "icon_12": "com_wtcl_electronicdirections",    # 游戏手柄图标
        "icon_13": "com_wt_carcamera",        # 相机图标
        "icon_14": "com_autopai_smart_sound_effect",    # AI/圆形图标
        "icon_15": "com_wt_funbox",    # 客服/对话图标
        #"icon_16": "icon_16", # 暂无
        #"icon_17": "icon_17",      # 暂无
        "icon_18": "com_tinnove_gamezone",    # 分屏/双屏图标
        "icon_19": "com_tinnove_apa", # 个人中心图标
        #"icon_20": "icon_20",           # 暂无
        "icon_21": "com_tinnove_scenemode",   # 场景魔方
        
        # 工具类
        "icon_22": "com_tinnove_cloudcamera", # 声音/音效图标
        #"icon_23": "icon_23",    # 暂无
        "icon_24": "com_tinnove_aispace",    # AI方块图标
        "icon_25": "com_incall_app_drivershealth", # 扫码
        "icon_26": "com_wt_phonelink",        # 手机连接图标
        "icon_27": "com_tinnove_customer",  # 圆形通讯图标
        "icon_28": "com_wt_scene",            # 菱形设置图标
        #"icon_29": "icon_29", # 暂无
        "icon_30": "com_tinnove_carshow",      # 汽车维护图标
        "icon_31": "com_wt_maintenance",      # 设置/齿轮图标
        "icon_32": "com_tinnove_link_client", # 连接客户端图标
        
        # 其他
        "icon_33": "com_tinnove_chrome",   # 链接/连接图标
        "icon_34": "com_tinnove_wecarnavi"    # 邮件/纸飞机图标
    }
    
    return icon_mapping

def get_reverse_mapping():
    """
    获取反向映射字典
    key: 线稿图标名称
    value: 对应的风格化图标名称
    """
    icon_mapping = get_icon_mapping()
    return {v: k for k, v in icon_mapping.items()}
