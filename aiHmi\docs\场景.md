# AI-HMI 场景库 (V1.0)

本文档汇集了 `prd.md` 和 `gemini.md` 中定义的核心场景，旨在通过具体的剧本和原型图，展示AI座舱在不同情境下的智能交互能力。

---

## 场景一：用户早高峰通勤（旗舰版）

*   **用户故事**: 作为一个需要送孩子上学的上班族，我希望VPA能像一个贴心的副驾，不仅能处理导航和日程，还能在途中安抚孩子，并主动帮我处理好通勤路上的早餐和咖啡问题，让一天从容开始。

*   **VPA决策逻辑**:
    *   **初始**: 检测到多名乘客，并通过多模态识别判断出其中有儿童（标签："毛毛"），时间为工作日早高峰。VPA判断当前为“家庭通勤”阶段。
    *   **中途**: 儿童乘客离车后，系统识别到只剩驾驶员一人，且日程表显示上午有重要会议。VPA判断场景切换为“专注通勤”阶段，并需要提供情绪安抚和效率服务。

*   **UI/UX 交互流程与UI布局 (分阶段)**

    *   **阶段A: 家庭出行模式 (送孩子去幼儿园)**
        *   **触发**: 用户和孩子“毛毛”进入车辆。
        *   **VPA/系统行为**: VPA识别到毛毛，主动问候并确认行程：“Hello，主人和毛毛你好。按往常一样先送毛毛上学吧？”。导航自动设定至幼儿园。途中，AI调用百科功能解答毛毛的问题，随后加载儿童教育模块播放视频。
        *   **ASCII 原型图 (修订版)**:
            ```
            +--------------------------------------------------------------------------+
            | [灵动岛: 前往: XX幼儿园, 预计: 15分钟]                                     |
            | +----------------------+  +--------------------+                         |
            | | 儿童教育卡片(8x9)    |  | 百科问答卡片(8x4)    |                         |
            | | (KidEducationCard)   |  | (PediaCard)        |                         |
            | | (正在播放视频...)    |  | "地球是圆的，因为..."|                         |
            | +----------------------+  +--------------------+                         |
            |                        |                                                  |
            |                        |                                     [o.o] < VPA  |
            +--------------------------------------------------------------------------+
            ```

    *   **阶段B: 专注通勤模式 (独自前往公司)**
        *   **触发**: 导航提示“已到达幼儿园”，系统检测到儿童乘客离车。
        *   **VPA/系统行为**: VPA说：“毛毛再见！接下来继续导航到公司。看您上午有会议，来点放松的音乐帮您调整下状态吧？” 同时根据用户习惯，主动询问并下单早餐和咖啡。
        *   **UI 变化**: 儿童教育和百科卡片以动画效果退场。左侧加载音乐控制大卡片，右侧加载订单状态和待办事项卡片。画布层切换为“放松的音乐动态壁纸”。
        *   **ASCII 原型图 (修订版)**:
            ```
            +--------------------------------------------------------------------------+
            | [灵动岛: 前往: 公司, 预计: 25分钟 | 途径: 麦当劳]                      |
            |                                                                          |
            |                      <-- 背景切换为放松的音乐动态壁纸 -->                  |
            |                                                                          |
            | +----------------------+  +--------------------+                         |
            | | 音乐控制卡片(8x9)    |  | 今日待办卡片(8x4)    |                         |
            | | (MusicControlCard)   |  | (TodoCard)         |                         |
            | | (播放: 日落大道)     |  | - 10:00 会议         |                         |
            | +----------------------+  +--------------------+                         |
            |                        |  +--------------------+                         |
            |                        |  | 订单状态卡片(4x2)    |                         |
            |                        |  | (OrderStatusCard)  |                         |
            |                        |  | "麦当劳早餐已下单"   |                         |
            |                        |  +--------------------+                         |
            |                                                            [o.o] < VPA   |
            +--------------------------------------------------------------------------+
            ```

---

## 场景二：下班通勤（家 -> 生鲜超市 -> 家）(修订版)

*   **用户故事**: 辛苦工作一天后，我希望我的VPA能帮我放松下来，并智能地处理回家路上的琐事，比如提醒我买菜，或者帮我提前打开家里的空调。

*   **VPA决策逻辑**:
    *   **触发条件**: 时间为下班高峰（18:30），目的地为“家”，驾驶数据显示用户可能处于疲劳状态。
    *   **AI决策**: 1. 营造放松氛围（切换壁纸、推荐音乐）；2. 提供主动服务（根据智能冰箱数据或用户购物习惯，推荐购买补给）；3. 提供便利（展示智能家居控制面板，实现车家互联）。

*   **UI/UX 交互流程**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: <- 18:30 前往: 家, 路况良好]                                    |
    |                                                                          |
    |                         <-- 画布层: 傍晚城市街景壁纸 -->                   |
    |                                                                          |
    |  +--------------------------------+                                    |
    |  | (VPA主动服务对话框)              |                                    |
    |  | 辛苦一天了, 推荐歌单...          |                                    |
    |  | 冰箱里的鸡蛋不多了, 要买点吗?    |             (o.o)                  |
    |  |  [不用了] [好的, 加入路线]       |           <-- VPA形象 -->            |
    |  +--------------------------------+                                    |
    |                                                                          |
    | +----------------------+ +--------------------------------------------+ |
    | | 音乐控制卡片(4x2)    | | 智能家居卡片(8x4)                          | |
    | | (MusicControlCard)   | | (SmartHomeCard)                            | |
    | | 日落大道... [K||>]   | | [客厅空调:开] [空气净化器:自动]            | |
    | +----------------------+ +--------------------------------------------+ |
    +--------------------------------------------------------------------------+
    ```

---

## 场景三：车内等待/摸鱼 (修订版)

*   **用户故事**: 在等人或者想在车里休息一下的时候，我希望能有一些娱乐选项，比如看会儿视频或者刷刷新闻，让等待的时间不那么无聊。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到车辆挂入P档（驻车）超过1分钟，且车内有乘客。
    *   **AI决策**: 判断用户进入“等待/休息”状态，需要提供娱乐和放松选项。因此，将桌面重新布局，最大化娱乐体验。

*   **UI/UX 交互流程**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [P] <--- 档位挂入P档, 驻车系统已激活                                     |
    |                                                                          |
    |                         <-- 画布层: 宁静湖畔壁纸 -->                     |
    |                                                                          |
    |  +--------------------------------------------------------------------+  |
    |  |                      视频播放器 (16x5)                             |  |
    |  |                      (VideoPlayerCard)                            |  |
    |  |  +--------------------------------------------------------------+  |  |
    |  |  |                      [ > 视频播放中 ]                        |  |  |
    |  |  +--------------------------------------------------------------+  |  |
    |  +--------------------------------------------------------------------+  |
    | (o.o) <--- VPA处于待命状态                                               |
    | +----------------------+ +--------------------+                           |
    | | 新闻摘要卡片(4x2)    | | 环境音卡片(4x2)      |                           |
    | | (NewsDigestCard)     | | (AmbientSoundCard) |                           |
    | | - AI...              | | [雨声] [森林] [冥想] |                           |
    | +----------------------+ +--------------------+                           |
    +--------------------------------------------------------------------------+
    ```

---

## 场景四：雨夜的归途 (修订版)

*   **情景**: 用户深夜下班，外面下着雨，导航回家。用户显得有些疲惫。

*   **VPA决策逻辑**: 当前需要**专注导航**，但要营造一个**平静、温暖、有安全感**氛围来舒缓用户的疲劳。视觉干扰要降到最低。因此，采用极简布局，最大化地展示富有氛围感的背景和特效，仅保留最核心的导航和音乐功能。

*   **渲染计划**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: 回家 - 剩余15分钟]                                              |
    |                                                                          |
    |                                                                          |
    |                   <-- 大面积留空，充分展示背景和雨滴特效 -->               |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    | [o.o] < VPA组件(2x2)        +--------------------+                         |
    | (VPA_Avatar_Widget)         | 音乐控制卡片(4x2)  |                         |
    |                             | (MusicControlCard) |                         |
    |                             +--------------------+                         |
    +--------------------------------------------------------------------------+
    | [画布层: 模糊的城市夜景壁纸] + [氛围层: 动态的雨滴特效]                     |
    +--------------------------------------------------------------------------+
    ```

---

## 场景五：周末家庭出游 (修订版)

*   **用户故事**: 作为一位家长，在周末带家人出游时，我希望车辆能够帮助我安抚孩子，并有效管理行程，让全家人都能享受一个轻松愉快的旅程。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到周末早晨，目的地设置为公园等家庭友好地点，并通过车载摄像头或座椅传感器识别出车内有多名乘客（包括儿童）。
    *   **AI决策**: 判断为“家庭出游”场景，核心任务是服务后排儿童乘客和辅助驾驶员。因此，布局围绕后排娱乐控制和行程便利性功能展开。

*   **UI/UX 交互流程**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: 前往: 森林公园 - 预计45分钟]                                      |
    |                                                                          |
    |                                     导航地图 (路线高亮)                    |
    |                                                                          |
    |  ( rear screen ) --> +--------------------------------+                  |
    |                      |         卡通片播放中...        |                  |
    |                      +--------------------------------+                  |
    |                                                                          |
    | (o.o)                                                                    |
    | +----------------------+ +-----------------------+ +---------------------+ |
    | | 后座娱乐控制(4x2)    | | 查找设施卡片(4x2)     | | 行程提醒卡片(4x2)   | |
    | | (RearSeatControlCard)| | (FacilityFinderCard)  | | (TripReminderCard)  | |
    | | [动画片] [游戏]      | | [查找最近的洗手间]    | | 零食提醒: 30分钟后  | |
    | +----------------------+ +-----------------------+ +---------------------+ |
    +--------------------------------------------------------------------------+
    ```

---

## 场景六：长途高速驾驶 (修订版)

*   **用户故事**: 在长途驾驶时，我希望车辆能帮助我保持警惕，管理驾驶状态，并及时找到必要的休息站，从而使旅程更安全、更轻松。

*   **VPA决策逻辑**:
    *   **触发条件**: 系统检测到车辆在高速公路上已连续行驶超过一小时，或驾驶员疲劳监测系统发出警报。
    *   **AI决策**: 判断为“长途驾驶”场景，核心任务是保障驾驶安全和提供便利信息。布局应简化以减少干扰，突出显示与驾驶状态、续航和前方服务区相关的关键信息。

*   **UI/UX 交互流程**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [灵动岛: G2高速 - 距离下一出口 25km]                                     |
    |                                                                          |
    |                   <-- 简化导航视图，突出前方路线 -->                       |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    | (o.o)                                                                    |
    | +----------------------+ +-----------------------+ +---------------------+ |
    | | 服务区信息卡片(4x2)  | | 驾驶员状态卡片(4x2)   | | 车辆状态卡片(4x2)   | |
    | | (ServiceAreaCard)    | | (DriverStatusCard)    | | (VehicleStatusCard) | |
    | | 下一服务区: 15km     | | 状态: 良好            | | 续航里程: 350km     | |
    | | [加油] [餐饮]        | | [播放提神音乐]        | | [查找充电站]        | |
    | +----------------------+ +-----------------------+ +---------------------+ |
    +--------------------------------------------------------------------------+
    ```

---

## 场景七：访客/代驾模式 (修订版)

*   **用户故事**: 当我把车交给朋友或代驾司机时，我希望能隐藏我的个人信息（如家庭住址、联系人、行程历史），同时为他们提供必要的驾驶功能，确保我的隐私安全。

*   **VPA决策逻辑**:
    *   **触发条件**: 用户通过设置菜单或语音指令（“嘿VPA，开启访客模式”）手动激活。
    *   **AI决策**: 判断进入“访客模式”，核心任务是保护用户隐私。因此，系统会切换到一套预设的、功能受限的极简界面，隐藏所有个人数据和高级设置，只提供基础的导航和车辆控制功能。

*   **UI/UX 交互流程**: (保持不变)

*   **ASCII 原型图 (修订版)**:
    ```
    +--------------------------------------------------------------------------+
    | [访客模式已激活]                                                         |
    |                                                                          |
    |                         <-- 画布层: 中性/默认壁纸 -->                    |
    |                                                                          |
    |  +--------------------------------------------------+                    |
    |  |           临时导航输入模块 (16x2)              |                    |
    |  | 请输入临时目的地: [__________________] [ 前往 ]  |                    |
    |  +--------------------------------------------------+                    |
    |                                                                          |
    | (o.o) < VPA处于受限模式                                                  |
    | +----------------------+ +-----------------------+                       |
    | | 基础音乐卡片(4x2)    | | 基础控制卡片(4x2)     |                       |
    | | (BasicMusicCard)     | | (BasicControlCard)    |                       |
    | | [FM 97.4] [蓝牙音乐] | | [空调] [车窗]         |                       |
    | +----------------------+ +-----------------------+                       |
    +--------------------------------------------------------------------------+
    ```
    3.  **UI 变化**: 桌面切换为通用的默认壁纸。所有个性化卡片被移除。只保留最核心的功能入口，如临时导航、基础媒体播放（如收音机）和空调等车辆基本控制。灵动岛或状态栏会持续显示“访客模式”字样。

---

## 场景八：宠物模式

*   **用户故事**: 当我需要短暂地将我的宠物留在车内时（例如去便利店买东西），我希望车辆能保持一个对宠物友好和安全的温度，并向车外的路人明确展示我的宠物是安全的，车主很快就会回来。

*   **ASCII 原型图 (宠物模式)**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |             +------------------------------------------+                 |
    |             |                                          |                 |
    |             |      我的主人很快就回来！                |                 |
    |             |      车内温度现在是 22°C，很舒适。     |                 |
    |             |                                          |                 |
    |             |      (＾• ω •＾) <--- 可爱宠物动画        |                 |
    |             |                                          |                 |
    |             +------------------------------------------+                 |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    +--------------------------------------------------------------------------+
    | [宠物模式] [锁定车门] [空调: 22°C] [通过手机App监控]                     |
    +--------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 用户在停车后，通过中控屏幕或手机App选择“宠物模式”。
    2.  **VPA/系统行为**: 系统确认激活。VPA会说：“宠物模式已启动。我将保持车内温度在22°C，并锁定车门。您可以通过手机App随时查看车内情况。”
    3.  **UI 变化**: 中控大屏会显示一个醒目的信息页面，告知车外路人宠物的情况。页面上会实时显示车内温度，并可能伴有一个可爱的宠物动画，以减少路人的担忧。同时，用户的手机App会收到通知，并可以远程监控车内温度和摄像头画面。

---

## 场景九：洗车模式

*   **用户故事**: 当我准备进入自动洗车机时，我希望能一键完成所有必要的车辆准备工作，比如关闭车窗、折叠后视镜、关闭自动雨刷等，避免任何意外发生。

*   **ASCII 原型图 (洗车模式)**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                                                                          |
    |             +------------------------------------------+                 |
    |             |                                          |                 |
    |             |               洗车模式已激活             |                 |
    |             |                                          |                 |
    |             |      [✓] 车窗已关闭并锁定                |                 |
    |             |      [✓] 后视镜已折叠                    |                 |
    |             |      [✓] 充电口已锁定                    |                 |
    |             |      [✓] 自动雨刷已禁用                  |                 |
    |             |      [✓] 空调切换为内循环                |                 |
    |             |                                          |                 |
    |             |      准备就绪，可以安全洗车！            |                 |
    |             |                                          |                 |
    |             +------------------------------------------+                 |
    |                                                                          |
    |                                                                          |
    +--------------------------------------------------------------------------+
    | [触摸屏幕任意位置或踩下刹车以退出]                                       |
    +--------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 用户通过车辆设置菜单或语音指令（“嘿VPA，打开洗车模式”）激活。
    2.  **VPA/系统行为**: VPA会确认：“即将进入洗车模式，将为您关闭车窗、折叠后视镜并禁用雨刷。请确认。” 用户确认后，系统执行所有预设操作。
    3.  **UI 变化**: 中控屏幕显示一个清单，清晰地展示所有已完成的准备项目，给用户明确的反馈和安心感。屏幕触摸功能会被临时禁用，以防误触，并提示用户如何退出该模式。

---

## 场景十：浪漫二人世界

*   **用户故事**: 在一个特别的纪念晚上，我希望能和伴侣在车里享受一个不被打扰的浪漫时刻，车辆能主动营造出温馨、私密的氛围。

*   **ASCII 原型图 (浪漫模式)**:
    ```
    +--------------------------------------------------------------------------+
    |                                                                          |
    |                                                                          |
    |                <-- 背景为动态的星空或壁炉火焰视频 -->                    |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    |                                                                          |
    | +----------------------+ +---------------------------------------------+ |
    | | 浪漫爵士乐           | | 氛围灯: [玫瑰粉] [烛光黄] [星空紫]            | |
    | | [ K || > ]           | | [亮度: 50%]                                 | |
    | +----------------------+ +---------------------------------------------+ |
    +--------------------------------------------------------------------------+
    ```

*   **UI/UX 交互流程**:
    1.  **触发**: 系统通过日历事件（如“结婚纪念日”）或用户语音指令（“嘿VPA，开启浪漫模式”）激活。
    2.  **VPA/系统行为**: VPA会说：“好的，正在为您准备一个浪漫的空间。” 同时，系统会调暗车内主照明，激活氛围灯，并根据用户偏好播放浪漫的音乐列表。
    3.  **UI 变化**: 中控屏幕背景切换为动态的星空或壁炉火焰等富有氛围感的视频。界面上只保留必要的音乐控制和氛围灯调节卡片，最大程度地减少信息干扰，让用户专注于享受二人世界。

---

## 新增核心场景

### 11. 智能充电场景

#### 场景描述
用户驾驶电动车到达充电站，系统自动识别充电需求并提供智能化充电服务。

#### 用户故事
- **触发条件**: 车辆电量低于30%，或用户主动搜索充电站
- **用户目标**: 快速找到合适的充电站，高效完成充电
- **期望体验**: 无缝的充电预约、支付和娱乐体验

#### UI/UX交互流程
1. **充电需求检测**: 系统监测电量，主动推荐充电站
2. **充电站导航**: 提供最优路线和实时可用性信息
3. **到站识别**: 自动识别充电站环境，切换充电模式
4. **充电管理**: 实时显示充电状态，提供等待期间的娱乐选项
5. **完成提醒**: 充电完成后提醒用户，提供后续行程建议

#### ASCII原型图
```
+--------------------------------------------------------------------------+
| [灵动岛: 充电中 45% | 预计完成时间: 14:30]                              |
+--------------------------------------------------------------------------+
|                                                                          |
|                        🔌 充电站环境背景                                |
|                     (显示充电桩和周边设施)                              |
|                                                                          |
|  [VPA陪伴小窗]                                                              |
|   (^.^) "充电期间，需要为您播放电影或音乐吗？"                                    |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | ⚡ 充电状态       | | 🎬 娱乐推荐       | | 🛒 附近商店                 | |
| | 当前: 45%        | | [电影] [音乐]     | | [咖啡店] 步行2分钟          | |
| | 功率: 60kW       | | [播客] [有声书]   | | [超市] 步行3分钟            | |
| | 剩余: 25分钟     | | [游戏] [新闻]     | | [餐厅] 步行5分钟            | |
| | 费用: ¥18.5      | |                   | | [预约取餐]                  | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 12. 疲劳驾驶检测与干预场景

#### 场景描述
系统通过多传感器检测驾驶员疲劳状态，主动提供安全干预和休息建议。

#### 用户故事
- **触发条件**: 检测到驾驶员疲劳信号（眼部状态、驾驶行为异常等）
- **用户目标**: 安全驾驶，避免疲劳驾驶风险
- **期望体验**: 及时的疲劳提醒和实用的休息建议

#### UI/UX交互流程
1. **疲劳检测**: 持续监测驾驶员状态
2. **预警提示**: 发现疲劳信号时温和提醒
3. **干预建议**: 提供具体的休息方案
4. **安全辅助**: 增强ADAS功能，提高安全性
5. **休息引导**: 导航至最近的休息区

#### ASCII原型图
```
+--------------------------------------------------------------------------+
| [灵动岛: ⚠️ 疲劳驾驶预警 | 建议立即休息]                                |
+--------------------------------------------------------------------------+
|                                                                          |
|                        ⚠️ 警示色背景渐变                               |
|                     (橙红色渐变，引起注意)                              |
|                                                                          |
|    +--------------------------------------------------+                  |
|    | ⚠️  检测到疲劳驾驶迹象                           |                  |
|    | 您已连续驾驶2小时15分钟                          |                  |
|    | 建议立即休息或更换驾驶员                         |                  |
|    |                                                  |                  |
|    | [查找休息区] [播放提神音乐] [联系家人代驾]       |                  |
|    +--------------------------------------------------+                  |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 🛣️  最近服务区    | | ☕ 提神建议        | | 📞 紧急联系人               | |
| | 距离: 5km        | | [洗脸] [咖啡]     | | [配偶] [朋友]               | |
| | 设施: 全套       | | [休息] [换人]     | | [代驾服务]                  | |
| | 预计: 8分钟      | | [轻度运动]        | | [一键呼叫]                  | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 13. 多用户识别与切换场景

#### 场景描述
车辆能够识别不同用户（主驾驶员、副驾驶员、后排乘客），并提供个性化服务。

#### 用户故事
- **触发条件**: 检测到新用户进入车辆或用户主动切换
- **用户目标**: 获得个性化的车载体验
- **期望体验**: 快速识别，无缝切换个人设置

#### UI/UX交互流程
1. **用户检测**: 通过多种方式识别用户身份
2. **身份确认**: 显示识别结果，允许手动确认或切换
3. **配置加载**: 加载用户个人偏好设置
4. **欢迎界面**: 显示个性化欢迎信息和推荐
5. **权限管理**: 根据用户角色分配功能权限

#### ASCII原型图
```
+--------------------------------------------------------------------------+
| [灵动岛: 用户识别模式 | 检测到新用户]                                   |
+--------------------------------------------------------------------------+
|                                                                          |
|                        👥 中性背景主题                                 |
|                     (简洁的用户选择界面)                               |
|                                                                          |
|              +----------------------------------------+                    |
|              | 👥 选择用户配置文件                    |                    |
|              |                                        |                    |
|              | [👨 张先生]  [👩 李女士]  [👶 儿童模式] |                    |
|              |   主驾驶员    副驾驶员     后排乘客   |                    |
|              |   ✅ 当前     ○ 离线      ○ 未激活   |                    |
|              |                                        |                    |
|              | [🎤 语音识别] [👤 访客模式] [⚙️ 设置]  |                    |
|              |                                        |                    |
|              +----------------------------------------+                    |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 🔍 识别方式       | | 👤 用户偏好        | | 🔒 隐私设置                 | |
| | [人脸] ✅        | | 音乐: 流行        | | 数据共享: 关闭              | |
| | [声纹] ✅        | | 温度: 22°C        | | 位置记录: 仅导航            | |
| | [手机] ○         | | 座椅: 位置3       | | 语音存储: 本地              | |
| | [手动] ○         | | 路线: 避开拥堵    | | [详细设置]                  | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 14. 智能泊车辅助场景

#### 场景描述
系统提供全方位的泊车辅助，包括车位搜索、自动泊车和离车后的车辆管理。

#### 用户故事
- **触发条件**: 接近目的地或用户主动搜索停车位
- **用户目标**: 快速找到合适车位，安全完成泊车
- **期望体验**: 智能化的泊车全流程服务

#### UI/UX交互流程
1. **车位搜索**: 提前搜索目的地附近的可用车位
2. **路线规划**: 规划到达车位的最优路线
3. **泊车辅助**: 提供自动或半自动泊车功能
4. **车位确认**: 确认泊车完成，记录车位信息
5. **离车管理**: 提供车辆状态监控和远程控制

#### ASCII原型图
```
+--------------------------------------------------------------------------+
| [灵动岛: 智能泊车 | 搜索车位中...]                                       |
+--------------------------------------------------------------------------+
|                                                                          |
|                        🅿️ 停车场环境背景                               |
|                     (显示停车场布局和可用车位)                          |
|                                                                          |
|  [VPA头像]                                                              |
|   (o.o) "已为您找到3个可用车位，推荐B2-15号位。"                                    |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 🅿️ 可用车位       | | 🚗 泊车辅助       | | 💰 费用信息                 | |
| | B2-15 (推荐)     | | [自动泊车]        | | 2小时: ¥10                  | |
| | 距离: 50m        | | [辅助泊车]        | | 全天: ¥30                   | |
| | 宽度: 标准       | | [手动泊车]        | | 包月: ¥200                  | |
| |                  | |                   | |                             | |
| | B1-08 (备选)     | | 📹 环视影像       | | 🎫 优惠券                   | |
| | 距离: 80m        | | [前] [后] [左] [右] | | [新用户9折] [使用]          | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

### 15. 紧急情况处理场景

#### 场景描述
系统检测到紧急情况（事故、故障、健康问题等），自动启动应急响应流程。

#### 用户故事
- **触发条件**: 检测到碰撞、气囊弹出、驾驶员异常等紧急信号
- **用户目标**: 获得及时的紧急救援和医疗帮助
- **期望体验**: 快速响应，自动化的救援流程

#### UI/UX交互流程
1. **紧急检测**: 自动检测紧急情况
2. **状态评估**: 评估事故严重程度和人员状况
3. **自动报警**: 自动拨打紧急电话，发送位置信息
4. **现场指导**: 提供急救指导和安全建议
5. **救援协调**: 与救援部门保持联系，提供实时信息

#### ASCII原型图
```
+--------------------------------------------------------------------------+
| [灵动岛: 🚨 紧急情况 | 已自动报警]                                      |
+--------------------------------------------------------------------------+
|                                                                          |
|                        🚨 紧急红色背景                                 |
|                     (闪烁的红色警示背景)                               |
|                                                                          |
|    +--------------------------------------------------+                  |
|    | 🚨 检测到紧急情况                               |                  |
|    | 已自动联系救援服务                              |                  |
|    | 救护车预计15分钟到达                            |                  |
|    |                                                  |                  |
|    | 请保持冷静，按照语音指导操作                     |                  |
|    | [我没事] [需要帮助] [联系家人]                   |                  |
|    +--------------------------------------------------+                  |
|                                                                          |
| +------------------+ +-------------------+ +----------------------------+ |
| | 📍 位置信息       | | 🏥 急救指导        | | 📞 紧急联系                 | |
| | 经度: 116.3974   | | [检查意识]        | | 120: 已拨打                 | |
| | 纬度: 39.9093    | | [检查呼吸]        | | 家人: 正在联系              | |
| | 地址: 朝阳区...  | | [止血方法]        | | 保险: 已通知                | |
| | [发送给救援]     | | [保持体温]        | | [手动拨打]                  | |
| +------------------+ +-------------------+ +----------------------------+ |
+--------------------------------------------------------------------------+
```

---

## 场景串联与智能切换

### 场景感知算法
系统通过以下数据源进行场景识别：
- **时间信息**: 工作日/周末、早晚高峰
- **位置信息**: 家/公司/商场/充电站
- **车辆状态**: 电量、速度、驾驶模式
- **用户行为**: 语音指令、操作习惯
- **环境信息**: 天气、路况、节假日

### 场景切换逻辑
```
场景优先级：
紧急情况 > 疲劳检测 > ADAS预警 > 充电需求 > 常规场景

切换条件：
- 自动切换：基于传感器数据和AI判断
- 手动切换：用户主动选择场景模式
- 渐进切换：通过动画和过渡效果平滑切换
```

### 个性化学习
系统会学习用户的使用习惯，优化场景推荐：
- **时间偏好**: 记录用户在不同时间的场景选择
- **路线偏好**: 学习常用路线和停靠点
- **功能偏好**: 统计最常使用的功能和设置
- **交互偏好**: 适应用户的操作习惯和反馈方式