<template>
  <div class="particle-effect-container" :style="containerStyle" ref="container">
    <!-- 粒子特效区域 -->
    <div class="particles-container">
      <div v-for="particle in particles" :key="particle.id" class="particle" :style="getParticleStyle(particle)">
      </div>
    </div>

    <!-- 渐变遮罩，保持完全透明 -->
    <div class="radial-mask"></div>
  </div>
</template>

<script lang="ts">
// 提供一个常规脚本导出以便其他组件导入
import { defineComponent } from "vue";

export default defineComponent({
  name: "ParticleEffect",
});
</script>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import ParticleEngine, { Particle } from "../utils/ParticleEngine";
import "../styles/particle-effects.css";

// 定义组件属性
const props = defineProps({
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "10rem",
  },
  primaryColor: {
    type: String,
    default: "rgba(99, 102, 241, 0.7)",
  },
  secondaryColor: {
    type: String,
    default: "rgba(59, 130, 246, 0.7)",
  },
  particleColor: {
    type: String,
    default: "rgba(255, 255, 255, 0.6)",
  },
  particleDensity: {
    type: Number,
    default: 50,
  },
  minSize: {
    type: Number,
    default: 0.2,
  },
  maxSize: {
    type: Number,
    default: 1.0,
  },
  speed: {
    type: Number,
    default: 0.3,
  },
});

// 定义暴露给父组件的方法和事件
const emit = defineEmits(["initialized", "destroyed"]);

// 容器样式
const containerStyle = computed(() => ({
  width: props.width,
  height: props.height,
}));

// 组件引用和状态
const container = ref<HTMLElement | null>(null);
const particles = ref<Particle[]>([]);
let particleEngine: ParticleEngine | null = null;

// 获取粒子样式
const getParticleStyle = (particle: Particle) => {
  if (!particleEngine) return {};
  return particleEngine.getParticleStyle(particle);
};

// 初始化粒子引擎
const initParticleEngine = () => {
  if (!container.value) return;

  // 创建粒子引擎实例
  particleEngine = new ParticleEngine(container.value, {
    primaryColor: props.primaryColor,
    secondaryColor: props.secondaryColor,
    particleColor: props.particleColor,
    particleDensity: props.particleDensity,
    minSize: props.minSize,
    maxSize: props.maxSize,
    speed: props.speed,
  });

  // 初始化并启动
  particleEngine.initParticles();
  particleEngine.start();

  // 更新粒子数据到组件
  updateParticlesFromEngine();

  // 通知父组件初始化完成
  emit("initialized");
};

// 从引擎更新粒子数据
const updateParticlesFromEngine = () => {
  if (!particleEngine) return;

  // 定期拉取数据以更新视图
  particles.value = [...particleEngine.getParticles()];

  // 每帧请求更新数据
  requestAnimationFrame(updateParticlesFromEngine);
};

// 监听属性变化
watch(
  [
    () => props.primaryColor,
    () => props.secondaryColor,
    () => props.particleColor,
    () => props.particleDensity,
    () => props.minSize,
    () => props.maxSize,
    () => props.speed,
  ],
  () => {
    if (!particleEngine) return;

    // 更新粒子引擎选项
    particleEngine.updateOptions({
      primaryColor: props.primaryColor,
      secondaryColor: props.secondaryColor,
      particleColor: props.particleColor,
      particleDensity: props.particleDensity,
      minSize: props.minSize,
      maxSize: props.maxSize,
      speed: props.speed,
    });
  }
);

// 生命周期钩子
onMounted(() => {
  initParticleEngine();

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);

  // 销毁粒子引擎
  if (particleEngine) {
    particleEngine.destroy();
    particleEngine = null;
  }

  // 通知父组件已销毁
  emit("destroyed");
});

// 窗口大小变化处理
const handleResize = () => {
  if (particleEngine && container.value) {
    particleEngine.initParticles();
  }
};

// 暴露公共方法
defineExpose({
  // 重新初始化
  reinitialize: () => {
    if (particleEngine) {
      particleEngine.initParticles();
    }
  },
  // 暂停动画
  pause: () => {
    if (particleEngine) {
      particleEngine.stop();
    }
  },
  // 恢复动画
  resume: () => {
    if (particleEngine) {
      particleEngine.start();
    }
  },
});
</script> 