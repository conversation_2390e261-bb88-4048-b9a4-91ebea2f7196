<template>
  <div v-if="show" class="fixed z-50 tooltip-container" :style="tooltipStyle" @mouseleave="$emit('close')">
    <div class="bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-[80%] max-w-2xl h-[250px] flex flex-col">
      <!-- 内容区域 -->
      <div class="flex-1 overflow-y-auto space-y-3 pr-2">
        <!-- 文本内容 -->
        <div class="text-gray-800 text-base leading-relaxed">
          {{ content }}
        </div>

        <!-- 图片预览 -->
        <div v-if="imageUrl" class="relative cursor-pointer" @click="showFullImage = true">
          <img :src="imageUrl" alt="引用图片" class="rounded-lg w-full object-contain max-h-48 hover:opacity-90 transition-opacity">
          <div class="absolute inset-0 bg-gradient-to-t from-white/50 to-transparent rounded-lg"></div>
          <!-- 放大图标 -->
          <div class="absolute top-2 right-2 bg-white/80 rounded-full p-1.5 shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 文档名称（固定在底部） -->
      <div class="flex items-center mt-4 pt-3 border-t border-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span class="text-gray-500 text-sm">{{ documentName }}</span>
      </div>
    </div>

    <!-- 图片放大模态框 -->
    <div v-if="showFullImage" class="fixed inset-0 z-[60] flex items-center justify-center bg-black/80" @click="showFullImage = false">
      <div class="relative max-w-[90vw] max-h-[90vh]">
        <img :src="imageUrl" alt="放大图片" class="max-w-full max-h-[90vh] object-contain">
        <button @click="showFullImage = false" class="absolute top-4 right-4 text-white hover:text-gray-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
  show: boolean;
  content: string;
  documentName: string;
  imageId?: string;
  position: {
    x: number;
    y: number;
  };
}>();

defineEmits<{
  (e: 'close'): void;
}>();

// 控制图片放大显示
const showFullImage = ref(false);

// 计算图片URL
const imageUrl = computed(() => {
  if (!props.imageId) return null;
  return `http://************:18980/v1/document/image/${props.imageId}`;
});

// 计算提示框位置
const tooltipStyle = computed(() => {
  const windowWidth = window.innerWidth;
  const tooltipHeight = 250; // 提示框的固定高度
  const tooltipWidth = Math.min(windowWidth * 0.8, 672); // 最大宽度 2xl (672px)
  
  // 计算水平位置，确保提示框不会超出屏幕
  let left = props.position.x;
  // 如果提示框会超出右边界
  if (left + tooltipWidth / 2 > windowWidth) {
    left = windowWidth - tooltipWidth / 2;
  }
  // 如果提示框会超出左边界
  if (left - tooltipWidth / 2 < 0) {
    left = tooltipWidth / 2;
  }
  
  // 计算垂直位置，确保提示框不会超出屏幕
  let top = props.position.y;
  // 如果提示框会超出顶部，则显示在鼠标下方
  if (top - tooltipHeight - 20 < 0) {
    top = props.position.y + 30; // 在鼠标下方显示
    return {
      left: `${left}px`,
      top: `${top}px`,
      transform: 'translate(-50%, 0)',
      marginTop: '0'
    };
  }
  
  return {
    left: `${left}px`,
    top: `${top}px`,
    transform: 'translate(-50%, -100%)',
    marginTop: '-10px'
  };
});
</script>

<style scoped>
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
