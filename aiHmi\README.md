# AI HMI 智能座舱模拟器

## 项目概述

这是一个为产品经理设计的车载系统桌面模拟器，用于演示AI如何智能组合生成面向C端用户的HMI界面。

## 当前开发状态

### ✅ 已完成功能

#### 1. 项目基础架构
- ✅ Vue 3 + Composition API 项目搭建
- ✅ Pinia 状态管理集成
- ✅ GSAP 动画库集成
- ✅ Axios HTTP 客户端集成

#### 2. 原子组件库
- ✅ BaseButton - 完整按钮组件，支持玻璃拟态效果
- ✅ BaseIcon - SVG图标系统，支持动态加载
- ✅ BaseText - 排版组件，支持多种变体
- ✅ BaseInput - 输入组件，支持验证和玻璃拟态样式

#### 3. 样式系统
- ✅ CSS变量系统 - 透明度层次、玻璃拟态参数
- ✅ 玻璃拟态效果库 - 完整的毛玻璃效果
- ✅ 重置样式 - 汽车优化的基础样式
- ✅ 8x4网格布局系统 - 标准桌面布局

#### 4. 状态管理Store
- ✅ 主题管理Store - 动态主题切换和壁纸整合
- ✅ 布局管理Store - 8x4网格系统和组件布局
- ✅ 壁纸管理Store - 动态壁纸和色彩提取
- ✅ VPA数字人Store - 虚拟个人助手管理
- ✅ AI服务Store - 智能内容生成和场景分析

#### 5. 桌面布局实现
- ✅ 动态岛 (8x1) - 顶部状态栏
- ✅ 天气卡片 (2x2) - 位置、温度、天气状态
- ✅ 导航卡片 (4x2) - 路线信息、预计到达时间
- ✅ 音乐卡片 (2x1) - 播放控制、歌曲信息
- ✅ 待办卡片 (2x1) - 日程安排显示
- ✅ VPA数字人 (透明背景) - 智能助手形象

#### 6. 设计系统实现
- ✅ 玻璃拟态主题 - 默认视觉风格
- ✅ 透明度层次系统 - 4级透明度规范
- ✅ 壁纸整合设计 - 组件与背景和谐共生
- ✅ 响应式设计 - 支持不同屏幕尺寸

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **状态管理**: Pinia
- **动画库**: GSAP
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI
- **样式方案**: CSS Variables + 玻璃拟态

## 开发指南

### 启动开发服务器

```bash
cd aiHmi
npm install
npm run serve
```

访问 http://localhost:8080 查看效果

### 核心设计原则

1. **组件原子化**: 遵循原子设计方法论，构建可复用的组件系统
2. **壁纸整合**: 所有组件与动态壁纸和谐共生，而非简单叠加
3. **透明度层次**: 4级透明度系统，确保视觉层次清晰
4. **8x4网格**: 标准桌面布局网格，所有组件严格按网格定位
5. **玻璃拟态**: 默认视觉风格，营造现代轻盈的质感

### 布局规范

- **动态岛**: 8x1，屏幕顶部持久状态栏
- **天气卡片**: 2x2，显示位置、温度、天气状态
- **导航卡片**: 4x2，显示路线、预计时间、下一步指引
- **音乐卡片**: 2x1，媒体播放控制
- **待办卡片**: 2x1，日程安排显示
- **VPA数字人**: 透明背景，智能助手视觉化身

## 测试说明

项目已通过Playwright进行了基础功能测试：
- ✅ 页面正常加载
- ✅ 8x4网格布局正确显示
- ✅ 玻璃拟态效果正常
- ✅ 响应式设计适配
- ✅ 基础交互功能

## 下一步开发计划

### 🔄 进行中
- 完善原子组件库
- 实现分子组件组合
- 添加交互功能

### 📋 待开发
- VPA数字人动画系统
- AI场景分析引擎
- 动态布局生成
- 主题切换动画
- 语音交互功能
- 后端API集成
