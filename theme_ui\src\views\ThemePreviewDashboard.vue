<template>
  <div class="h-screen w-screen text-white">
    <!-- 主题背景 -->
    <div class="bg-theme"></div>
    
    <!-- 主容器 -->
    <div class="relative h-full w-full p-4 flex flex-col" :style="{
      backgroundImage: `url(${theme?.wallpaper || defaultWallpaper})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    }">
      <!-- 顶部状态栏 -->
      <div class="flex justify-between items-center mb-5">
        <!-- 左侧速度显示 -->
        <div class="flex items-end">
          <div class="text-7xl font-bold">{{ currentSpeed }}</div>
          <div class="flex flex-col ml-2 mb-1.5">
            <span class="text-sm text-gray-300">km/h</span>
            <div class="flex items-center mt-1">
              <span class="bg-green-500 px-2 text-xs font-medium rounded-sm">READY</span>
              <div class="flex items-center ml-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧状态图标 -->
        <div class="flex items-center space-x-4">
          <i class="fas fa-user text-gray-300"></i>
          <i class="fas fa-mobile-alt text-gray-300"></i>
          <i class="fas fa-share-alt text-gray-300"></i>
          <div class="flex items-center">
            <i class="fas fa-network-wired text-gray-300"></i>
            <span class="text-xs ml-1">4G</span>
          </div>
          <i class="fas fa-wifi text-gray-300"></i>
          <i class="fas fa-broadcast-tower text-gray-300"></i>
          <i class="fas fa-shield-alt text-gray-300"></i>
          <div class="text-xl font-medium">{{ currentTime }}</div>
        </div>
      </div>
      
      <!-- 导航栏 -->
      <div class="glass-card rounded-xl mb-5 py-3 px-4">
        <div class="flex justify-center">
          <div class="flex space-x-10">
            <div class="flex flex-col items-center" :class="{'opacity-100': selectedIndex === 0, 'opacity-60': selectedIndex !== 0}">
              <span class="text-white text-sm font-medium" @click="handleItemClick(0)">应用中心</span>
              <div class="h-1 w-full rounded-full mt-2" :class="{'bg-white': selectedIndex === 0, 'bg-transparent': selectedIndex !== 0}"></div>
            </div>
            <div class="flex flex-col items-center" :class="{'opacity-100': selectedIndex === 1, 'opacity-60': selectedIndex !== 1}">
              <span class="text-white text-sm" @click="handleItemClick(1)">小程序</span>
              <div class="h-1 w-full rounded-full mt-2" :class="{'bg-white': selectedIndex === 1, 'bg-transparent': selectedIndex !== 1}"></div>
            </div>
            <div class="flex flex-col items-center" :class="{'opacity-100': selectedIndex === 2, 'opacity-60': selectedIndex !== 2}">
              <span class="text-white text-sm" @click="handleItemClick(2)">HUAWEI Hicar</span>
              <div class="h-1 w-full rounded-full mt-2" :class="{'bg-white': selectedIndex === 2, 'bg-transparent': selectedIndex !== 2}"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 应用图标网格 - 使用一体化毛玻璃背景 -->
      <div class=" rounded-xl p-6 mb-5 flex-1">
        <div class="grid grid-cols-5 gap-6">
          <div v-for="(app, index) in apps" :key="index"
               class="p-3 flex flex-col items-center justify-center hover:bg-dark-600/70 transition-colors rounded-lg cursor-pointer">
            <img v-if="getAppIcon(app.id) !== null" :src="getAppIcon(app.id) || ''" class="w-36 h-36 object-contain mb-2" />
            <i v-else :class="`${app.icon} text-${getIconColor(index)}-200 text-5xl mb-2`"></i>
            <span class="text-xl text-white text-center">{{ app.name }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部工具栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-black h-16 flex justify-between items-center px-5">
      <div class="flex items-center space-x-8">
        <!-- 左侧工具图标 -->
        <button class="p-2">
          <i class="fas fa-cog text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-home text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-truck text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-th-large text-gray-300 text-xl"></i>
        </button>
      </div>
      
      <div class="flex items-center space-x-8">
        <!-- 中间工具图标 -->
        <button class="p-2">
          <i class="fas fa-archive text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-shopping-bag text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-flask text-gray-300 text-xl"></i>
        </button>
      </div>
      
      <div class="flex items-center space-x-6">
        <!-- 右侧控制按钮 -->
        <button class="p-2">
          <i class="fas fa-chevron-left text-gray-300 text-xl"></i>
        </button>
        
        <div class="flex flex-col items-center">
          <div class="text-lg font-medium text-white">28.5</div>
        </div>
        
        <button class="p-2">
          <i class="fas fa-chevron-right text-gray-300 text-xl"></i>
        </button>
        
        <div class="text-sm font-medium text-white mr-1">AUTO</div>
        
        <button class="p-2">
          <i class="fas fa-microphone text-gray-300 text-xl"></i>
        </button>
        
        <button class="p-2">
          <i class="fas fa-wind text-gray-300 text-xl"></i>
        </button>
      </div>
    </div>
    
    <!-- 控制按钮 (浮在界面上的应用按钮) -->
    <div class="fixed top-5 right-5 flex space-x-3 z-10">
      <button @click="$router.back()" class="control-button bg-blue-600/80 hover:bg-blue-700/80 backdrop-blur-sm">
        <i class="fas fa-arrow-left mr-2"></i>
        <span>返回</span>
      </button>
      <button @click="applyTheme" class="control-button bg-green-600/80 hover:bg-green-700/80 backdrop-blur-sm">
        <i class="fas fa-check mr-2"></i>
        <span>应用主题</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { themeApi } from '@/api/themeApi';

// 定义主题数据结构
interface ThemeData {
  id: string;
  name: string;
  wallpaper: string;
  icons: {
    [key: string]: string; // 应用名称: 图标URL
  };
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

// 路由和导航
const route = useRoute();
const router = useRouter();
const taskId = computed(() => route.params.taskId as string);

// 检测是否为测试模式
const isTestMode = computed(() => route.query.test_mode === "true");

// 主题数据
const theme = ref<ThemeData | null>(null);
const loading = ref(false);
const deviceType = ref<'car' | 'mobile'>('car');

// 默认壁纸
const defaultWallpaper = '/images/冰.png';

// 时间状态更新
const currentTime = ref(new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}));
const currentSpeed = ref('120');
let timeInterval: number | null = null;
const apps_list = ref([[
  { id: 'com_tinnove_mediacenter', name: '媒体', icon: 'fas fa-music' },
  { id: 'com_autopai_car_dialer', name: '电话', icon: 'fas fa-phone' },
  { id: 'com_wt_vehiclecenter', name: '车辆设置', icon: 'fas fa-cog' },
  { id: 'com_changan_appmarket', name: '应用市场', icon: 'fas fa-store' },
  { id: 'icon_4', name: '手势', icon: 'fas fa-brain' },
  { id: 'icon_5', name: '场景', icon: 'fas fa-map-marker-alt' },
  { id: 'com_wt_airconditioner', name: '空调', icon: 'fas fa-wind' },
  { id: 'com_autopai_album', name: '地图', icon: 'fas fa-music' },
  { id: 'com_incall_apps_personalcenter', name: '个人中心', icon: 'fas fa-user' },
  { id: 'com_wtcl_filemanager', name: '文件管理', icon: 'fas fa-folder' },
  { id: 'com_wt_gamecenter', name: '游戏', icon: 'fas fa-gamepad' },
  { id: 'com_incall_dvr', name: '行车记录', icon: 'fas fa-video' },
  { id: 'com_wtcl_electronicdirections', name: '游戏', icon: 'fas fa-gamepad' },
  { id: 'com_wt_carcamera', name: '相机', icon: 'fas fa-wind' },
  { id: 'com_autopai_smart_sound_effect', name: 'AI', icon: 'fas fa-image' },
  // 只生成15个，就是上面icon1-icon14
  // // 第一行
  // { id: 'com_tinnove_aispace', name: 'AI空间', icon: 'fas fa-brain' },
  // { id: 'com_tinnove_wecarnavi', name: '导航', icon: 'fas fa-map-marker-alt' },
  // { id: 'com_tinnove_mediacenter', name: '媒体', icon: 'fas fa-music' },
  // { id: 'com_wt_phonelink', name: '手机互联', icon: 'fas fa-mobile-alt' },
  // { id: 'com_autopai_car_dialer', name: '电话', icon: 'fas fa-phone' },
  //
  // // 第二行
  // { id: 'com_wt_gamecenter', name: '游戏', icon: 'fas fa-gamepad' },
  // { id: 'com_wt_airconditioner', name: '空调', icon: 'fas fa-wind' },
  // { id: 'com_wt_scene', name: '场景', icon: 'fas fa-image' },
  // { id: 'com_tinnove_apa', name: '自动泊车', icon: 'fas fa-parking' },
  // { id: 'com_incall_apps_personalcenter', name: '个人中心', icon: 'fas fa-user' },
  //
  // // 第三行
  // { id: 'com_wt_vehiclecenter', name: '车辆设置', icon: 'fas fa-cog' },
  // { id: 'com_wtcl_filemanager', name: '文件管理', icon: 'fas fa-folder' },
  // { id: 'com_tinnove_chrome', name: '浏览器', icon: 'fab fa-chrome' },
  // { id: 'com_changan_appmarket', name: '应用市场', icon: 'fas fa-store' },
  // { id: 'com_incall_dvr', name: '行车记录', icon: 'fas fa-video' },
],
  [
    // 第一行
    { id: 'icon_4', name: '手势', icon: 'fas fa-brain' },
    { id: 'icon_5', name: '场景', icon: 'fas fa-map-marker-alt' },
    { id: 'com_autopai_album', name: '地图', icon: 'fas fa-music' },
    { id: 'com_incall_apps_personalcenter', name: '个人中心', icon: 'fas fa-mobile-alt' },
    { id: 'com_wtcl_filemanager', name: '文件管理', icon: 'fas fa-phone' },

    // 第二行
    { id: 'com_wtcl_electronicdirections', name: '游戏', icon: 'fas fa-gamepad' },
    { id: 'com_wt_carcamera', name: '相机', icon: 'fas fa-wind' },
    { id: 'com_autopai_smart_sound_effect', name: 'AI', icon: 'fas fa-image' },
    { id: 'com_wt_funbox', name: '客服', icon: 'fas fa-parking' },
    { id: 'icon_16', name: 'icon_16', icon: 'fas fa-user' },

    // 第三行
    { id: 'icon_17', name: 'icon_17', icon: 'fas fa-cog' },
    { id: 'com_tinnove_gamezone', name: '分屏', icon: 'fas fa-folder' },
    { id: 'icon_20', name: 'icon_20', icon: 'fab fa-chrome' },
    { id: 'com_tinnove_scenemode', name: '场景魔方', icon: 'fas fa-store' },
  ],
  [
    // 第一行
    { id: 'com_tinnove_cloudcamera', name: '音效', icon: 'fas fa-brain' },
    { id: 'icon_23', name: 'icon_23', icon: 'fas fa-map-marker-alt' },
    { id: 'com_incall_app_drivershealth', name: '扫码', icon: 'fas fa-music' },
    { id: 'com_tinnove_customer', name: '通讯', icon: 'fas fa-mobile-alt' },
    { id: 'icon_29', name: 'icon_29', icon: 'fas fa-phone' },

    // 第二行
    { id: 'com_tinnove_carshow', name: '维护', icon: 'fas fa-gamepad' },
    { id: 'com_wt_maintenance', name: '设置', icon: 'fas fa-wind' },
    { id: 'com_tinnove_link_client', name: '客户端', icon: 'fas fa-image' },

  ]]);
// 应用图标列表 - 保持5x3布局
const apps = ref(apps_list.value[0]);
// 定义选中的索引，默认为第一个
const selectedIndex = ref(0);

// 处理点击事件的函数
const handleItemClick = (index: number) => {
  selectedIndex.value = index;
  apps.value = apps_list.value[index];
};
// 获取应用图标
const getAppIcon = (appId: string): string | null => {
  if (!theme.value || !theme.value.icons) return null;

  // 先尝试直接使用应用ID作为图标名称
  if (theme.value.icons[appId]) {
    return theme.value.icons[appId];
  }

  // 如果没有精确匹配，尝试查找任何包含该ID的图标
  const iconKeys = Object.keys(theme.value.icons);
  for (const key of iconKeys) {
    if (key.includes(appId) || appId.includes(key)) {
      return theme.value.icons[key];
    }
  }

  return null;
};

// 获取备用颜色（当图标无法加载时）
const getFallbackColor = (index: number, opacity: string = "70") => {
  const colors = [
    `rgba(59, 130, 246, 0.${opacity})`, // blue-600
    `rgba(124, 58, 237, 0.${opacity})`, // purple-600
    `rgba(16, 185, 129, 0.${opacity})`, // green-600
    `rgba(245, 158, 11, 0.${opacity})`, // amber-600
    `rgba(239, 68, 68, 0.${opacity})`,  // red-600
    `rgba(6, 182, 212, 0.${opacity})`,  // cyan-600
    `rgba(217, 70, 239, 0.${opacity})`, // fuchsia-600
    `rgba(5, 150, 105, 0.${opacity})`,  // emerald-600
    `rgba(249, 115, 22, 0.${opacity})`, // orange-600
  ];
  return colors[index % colors.length];
};

// 获取图标颜色
const getIconColor = (index: number) => {
  const colors = ['blue', 'purple', 'green', 'amber', 'red', 'cyan', 
                 'fuchsia', 'emerald', 'orange'];
  return colors[index % colors.length];
};

// 应用主题
const applyTheme = () => {
  alert('主题应用功能在实际环境中可用');
};

// 更新时间和模拟车速
const updateTimeAndSpeed = () => {
  currentTime.value = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  
  // 模拟车速变化 - 保持在较高范围以匹配界面设计
  if (Math.random() > 0.7) {
    currentSpeed.value = (100 + Math.floor(Math.random() * 30)).toString();
  }
};

// 加载主题数据
const loadTheme = async () => {
  if (!taskId.value) return;
  
  try {
    loading.value = true;
    
    if (isTestMode.value) {
      // 测试模式：使用本地文件
      console.log("测试模式：加载本地测试文件");
      const basePath = `/test_theme/${taskId.value}/releases`;
      
      // 创建主题数据
      const iconFolder = `${basePath}/icon`;
      
      // 加载颜色配置
      let themeColors = {
        primary: '#3b82f6',
        secondary: '#7c3aed',
        accent: '#0ea5e9'
      };
      
      try {
        // 尝试加载colors.json
        const colorsResponse = await fetch(`${basePath}/colors.json`);
        if (colorsResponse.ok) {
          const colorsData = await colorsResponse.json();
          themeColors = {
            primary: colorsData.button?.normal?.button || '#3b82f6',
            secondary: colorsData.ai_dock_bg_color?.ai_global_focus_color || '#7c3aed',
            accent: colorsData.media_center?.activate?.ai_global_focus_color || '#0ea5e9'
          };
        }
      } catch (colorError) {
        console.warn("颜色配置加载失败，使用默认颜色:", colorError);
      }
      
      // 构建图标映射
      const iconMapping: {[key: string]: string} = {};
      
      // 查找所有已知的图标文件
      const appIds = apps.value.map(app => app.id);
      
      // 将所有可能的图标都添加到映射中
      appIds.forEach(appId => {
        iconMapping[appId] = `${iconFolder}/${appId}.png`;
      });
      
      // 添加额外的已知图标（icon_数字.png格式）
      ['icon_4', 'icon_5', 'icon_16', 'icon_17', 'icon_20', 'icon_23', 'icon_29'].forEach(iconId => {
        iconMapping[iconId] = `${iconFolder}/${iconId}.png`;
      });

      // 设置主题数据
      theme.value = {
        id: taskId.value,
        name: '车机主题预览',
        wallpaper: `${basePath}/wallpaper/wallpaper.jpg`,
        icons: iconMapping,
        colors: themeColors
      };
      
      console.log("测试模式主题加载完成:", theme.value);
    } else {
      // 正常模式：临时使用测试数据
      const baseUrl = "http://localhost:8000"; // 后端服务器地址
      const basePath = `${baseUrl}/ui_source/mainLine_${taskId.value}/releases`;


      // 构建图标映射
      const iconMapping: {[key: string]: string} = {};
      const iconFolder = `${basePath}/icon`;

      // 添加额外的已知图标（icon_数字.png格式）
      ['icon_4', 'icon_5', 'icon_16', 'icon_17', 'icon_20', 'icon_23', 'icon_29'].forEach(iconId => {
        // iconMapping[iconId] = `${iconFolder}/${iconId}.png`;
      });

      for (const apps1 of apps_list.value) {
        // 查找所有已知的图标文件
        const appIds = apps1.map(app => app.id);
        // 将所有可能的图标都添加到映射中
        for (const appId of appIds) {
          const icon_response = await themeApi.getPreviewImageMinioUrl(taskId.value, "icon", `${appId}.png`);
          // console.log("车机主题预览 icon API响应:", response);
          fetch(icon_response, { method: 'get' })
              .then(response => {
                if (response.status === 404) {
                } else {
                  iconMapping[appId] = icon_response;
                }
              })
        }
      }

      const response = await themeApi.getPreviewImageMinioUrl(taskId.value, "wallpaper");
      console.log("车机主题预览 壁纸API响应:", response);
      theme.value = {
        id: taskId.value,
        name: '车机主题预览',
        wallpaper: response,
        icons: iconMapping,
        colors: {
          primary: '#3b82f6',
          secondary: '#7c3aed',
          accent: '#0ea5e9'
        }
      };
    }
  } catch (error) {
    console.error('加载主题失败', error);
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  loadTheme();
  
  // 启动时间更新
  updateTimeAndSpeed();
  timeInterval = window.setInterval(updateTimeAndSpeed, 5000);
});

onUnmounted(() => {
  // 清理定时器
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
/* 主题背景 */
.bg-theme {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  object-fit: cover;
  background-color: #000;
}

/* 防止滚动条出现 */
::-webkit-scrollbar {
  display: none;
}

body {
  overflow: hidden;
  background-color: #000;
}

/* 玻璃卡片效果 */
.glass-card {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 控制按钮样式 */
.control-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 背景颜色类 */
.bg-dark-600\/70 {
  background-color: rgba(30, 41, 59, 0.7);
}

.text-gray-300 {
  color: rgba(209, 213, 219, 1);
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .text-7xl {
    font-size: 5rem;
    line-height: 1;
  }
}

@media (max-width: 1440px) {
  .grid-cols-5 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

@media (max-width: 1024px) {
  .grid-cols-5 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .text-7xl {
    font-size: 4rem;
    line-height: 1;
  }
}
</style> 