# 主题生成系统UI

这是一个基于Vue 3和Tailwind CSS的主题生成系统UI。系统使用了毛玻璃效果和现代化的UI组件，支持深色模式和多种主题颜色。

## 特点

- 基于Vue 3.5+和TypeScript
- 使用Tailwind CSS进行样式设计
- 精美的毛玻璃效果和动画
- 完全本地化的依赖（无CDN依赖）
- 响应式设计
- 深色模式支持
- 多种主题颜色

## 安装

```bash
# 安装依赖
npm install

# 依赖安装完成后会自动运行copy-assets.js脚本，将字体和图标资源复制到本地
```

## 本地资源

该项目使用完全本地化的资源，包括：

- **Remix Icon**: 图标库本地部署
- **Inter字体**: 字体文件本地部署
- **组件样式**: 所有样式使用本地CSS

安装依赖后，`postinstall`脚本会自动将这些资源复制到本地目录，确保应用可以在没有互联网连接的情况下运行。

## 开发

```bash
# 启动开发服务器
npm run dev
```

开发服务器启动后，可以在 [http://localhost:3000](http://localhost:3000) 访问应用。

## 构建

```bash
# 构建生产版本
npm run build
```

构建完成后，生成的文件将存放在 `dist` 目录中。

## 预览构建结果

```bash
# 预览生产构建
npm run preview
```

## 样式组件

项目包含多种可重用的样式组件：

- **GlassCard**: 带有毛玻璃效果的卡片组件
- **GlassPanel**: 毛玻璃效果的面板组件
- **GlassButton**: 毛玻璃效果的按钮样式

### 使用示例

```vue
<!-- 使用毛玻璃卡片 -->
<GlassCard 
  title="文生壁纸" 
  description="通过文字描述智能生成高清壁纸" 
  buttonText="立即创建" 
  color="primary"
>
  <template #icon>
    <i class="ri-palette-line text-2xl text-primary"></i>
  </template>
</GlassCard>

<!-- 使用毛玻璃按钮 -->
<button class="glass-button glass-button-primary">立即创建</button>
```

## 许可

ISC 