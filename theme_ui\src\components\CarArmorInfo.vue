<template>
  <div class="car-armor-info">
    <div class="glass-card p-5 h-full flex flex-col">
      <h3 class="text-xl font-bold text-purple-200 mb-4">车衣信息</h3>

      <div class="flex-grow overflow-y-auto mb-4">
        <!-- 当前应用的车衣信息 -->
        <div class="p-4 bg-dark-800/60 rounded-lg mb-4">
          <h4 class="text-lg font-semibold text-purple-100 mb-2">当前车衣</h4>

          <div v-if="currentArmor">
            <div class="grid grid-cols-[auto_1fr] gap-x-3 gap-y-2 mb-3">
              <div class="text-sm text-gray-400">名称:</div>
              <div class="text-sm text-white font-medium">{{ currentArmor.name }}</div>

              <div class="text-sm text-gray-400">颜色:</div>
              <div class="flex items-center">
                <div class="h-4 w-4 rounded mr-2" :style="{ backgroundColor: currentArmor.color }"></div>
                <span class="text-sm text-white">{{ getColorName(currentArmor.color) }}</span>
              </div>

              <div class="text-sm text-gray-400">材质:</div>
              <div class="text-sm text-white">{{ getMaterialName(currentArmor.material) }}</div>

              <div class="text-sm text-gray-400">特效:</div>
              <div class="text-sm text-white">{{ currentArmor.enableParticles ? '开启' : '关闭' }}</div>
            </div>

            <div class="text-xs text-gray-400 mt-2">
              <div class="flex items-center mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>
                应用时间: {{ formatTime(currentArmor.appliedAt || new Date()) }}
              </div>
            </div>
          </div>

          <div v-else class="text-sm text-gray-400 italic">
            暂无应用的车衣，请在左侧面板选择并应用
          </div>
        </div>

        <!-- 推荐车衣 -->
        <div>
          <h4 class="text-lg font-semibold text-purple-100 mb-2">推荐车衣</h4>

          <div class="space-y-3">
            <div v-for="(armor, index) in recommendedArmors" :key="index" class="p-3 bg-dark-700/40 rounded-lg hover:bg-dark-700/60 transition-colors cursor-pointer" @click="selectRecommendedArmor(armor)">
              <div class="flex items-center">
                <div class="h-6 w-6 rounded mr-3" :style="{ backgroundColor: armor.color }"></div>
                <div>
                  <div class="text-sm font-medium text-white">{{ armor.name }}</div>
                  <div class="text-xs text-gray-400 flex items-center mt-1">
                    <span>{{ getMaterialName(armor.material) }}</span>
                    <span class="mx-1.5">•</span>
                    <span>{{ getColorName(armor.color) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导出/分享 -->
      <div class="mt-auto pt-3 border-t border-gray-700/50">
        <button class="w-full flex items-center justify-center py-2 px-4 rounded-lg bg-dark-700/70 text-white font-medium hover:bg-dark-700/90 transition-colors" @click="shareCarArmor">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
          </svg>
          分享车衣设计
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

const props = defineProps({
  // 当前应用的车衣信息
  currentArmor: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(["select-recommended"]);

// 推荐车衣列表
const recommendedArmors = [
  {
    name: "星云幻甲 - 璀璨银河",
    color: "#8b5cf6",
    material: "glossy",
    enableParticles: true,
    particleCount: 1500,
    rotationSpeed: 2,
  },
  {
    name: "星云幻甲 - 极光之舞",
    color: "#10b981",
    material: "metallic",
    enableParticles: true,
    particleCount: 2000,
    rotationSpeed: 3,
  },
  {
    name: "星云幻甲 - 熔岩烈焰",
    color: "#ef4444",
    material: "glossy",
    enableParticles: true,
    particleCount: 1800,
    rotationSpeed: 1,
  },
  {
    name: "星云幻甲 - 深空蓝晶",
    color: "#3b82f6",
    material: "matte",
    enableParticles: true,
    particleCount: 1200,
    rotationSpeed: 0,
  },
];

// 根据颜色值获取颜色名称
const getColorName = (colorCode: string) => {
  const color = availableColors.find((c) => c.value === colorCode);
  return color ? color.name : "自定义";
};

// 根据材质值获取材质名称
const getMaterialName = (materialCode: string) => {
  const material = availableMaterials.find((m) => m.value === materialCode);
  return material ? material.name : "未知";
};

// 可用颜色列表
const availableColors = [
  { value: "#8b5cf6", name: "紫色" },
  { value: "#3b82f6", name: "蓝色" },
  { value: "#10b981", name: "绿色" },
  { value: "#ef4444", name: "红色" },
  { value: "#f59e0b", name: "黄色" },
  { value: "#ec4899", name: "粉色" },
  { value: "#06b6d4", name: "青色" },
  { value: "#94a3b8", name: "银色" },
];

// 可用材质列表
const availableMaterials = [
  { value: "matte", name: "哑光" },
  { value: "glossy", name: "高光" },
  { value: "metallic", name: "金属" },
];

// 格式化时间
const formatTime = (date: Date) => {
  return format(date, "yyyy-MM-dd HH:mm:ss", { locale: zhCN });
};

// 选择推荐车衣
const selectRecommendedArmor = (armor: any) => {
  emit("select-recommended", armor);
};

// 分享车衣设计
const shareCarArmor = () => {
  // 实际项目中可以实现分享功能
  alert("分享功能开发中，敬请期待！");
};
</script>

<style scoped>
.car-armor-info {
  height: 100%;
}
</style> 